# WLConnectManager 移除完整计划

## 概述

本文档详细记录了移除 WLConnectManager 依赖的完整计划，包括所有需要处理的依赖场景、替代方案和实施步骤。

## 当前进度

### ✅ 已完成的工作

1. **WLPlatformManager 增强**
   - ✅ 添加 `getDensityDpi()` 方法
   - ✅ 添加 `getHuCarInfo()` 方法替代 WLConnectManager.getHuCarInfo()
   - ✅ 添加 `getVehicleType()` 方法

2. **MainActivity 流程修改**
   - ✅ 修改 `onLinkConnected()` 方法，跳过 WLConnectManager
   - ✅ 直接调用 `platformManager.connectedAdapter()`

3. **日志追踪系统**
   - ✅ 在 MainActivity 中添加新流程日志
   - ✅ 在 WLPlatformManager.connectedAdapter() 中添加调用栈追踪
   - ✅ 在 WLConnectManager 中添加旧流程警告日志

## 🚨 待处理的关键依赖

### 1. **车机信息获取场景** - 优先级：🔴 高

#### 1.1 LayoutTypeUtils.isA2R() 方法
**文件位置**: `/app/src/main/java/com/a2ra9k/android/launcher/LayoutTypeUtils.java`

**当前实现**:
```java
WLConnectManager wlConnectManager = (WLConnectManager) SingletonFactory.getInstance().getSingleton(WLConnectManager.NAME);
CarBean huCarInfo = wlConnectManager.getHuCarInfo();
```

**需要修改为**:
```java
WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
CarBean huCarInfo = platformManager.getHuCarInfo();
```

#### 1.2 BaseFloatingDialog.initDialog() 方法
**文件位置**: `/app/src/main/java/com/a2ra9k/android/launcher/service/overlaydialog/floating/BaseFloatingDialog.java`

**当前实现**:
```java
WLConnectManager wlConnectManager = (WLConnectManager) SingletonFactory.getInstance().getSingleton(WLConnectManager.NAME);
CarBean huCarInfo = wlConnectManager.getHuCarInfo();
CAR_SCREEN_WIDTH_PIXEL = huCarInfo.getHuScreenWidth();
CAR_SCREEN_HEIGHT_PIXEL = huCarInfo.getHuScreenHeight();
```

**需要修改为**:
```java
WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
CarBean huCarInfo = platformManager.getHuCarInfo();
CAR_SCREEN_WIDTH_PIXEL = huCarInfo.getHuScreenWidth();
CAR_SCREEN_HEIGHT_PIXEL = huCarInfo.getHuScreenHeight();
```

### 2. **单例工厂注册清理** - 优先级：🟡 中

#### 2.1 BaseModuleManager.initWelinkServiceList()
**文件位置**: `/basemodule/src/main/java/com/autoai/fundrive/basemodule/BaseModuleManager.java`

**需要移除**:
```java
aServiceList.put(WLConnectManager.NAME, WLConnectManager.class.getName());
```

#### 2.2 ModuleManager.initWelinkServiceList()
**文件位置**: `/app/src/main/java/com/a2ra9k/android/launcher/ModuleManager.java`

**需要移除**:
```java
serviceList.put(WLConnectManager.NAME, WLConnectManager.class.getName());
```

### 3. **控制器功能迁移** - 优先级：🟠 低

#### 3.1 MICControl (麦克风控制)
- **当前依赖**: `mWLConnector.getConfiguration().getMicrophoneCapability()`
- **替代方案**: 使用 WLPlatformManager 现有的 `openMicrophone()` 方法
- **影响评估**: 如果应用不使用车机麦克风功能，可以暂时跳过

#### 3.2 TBTControl (导航控制)
- **当前依赖**: `mWLConnector.getConfiguration().getTBTInfoCapability()`
- **替代方案**: 通过 WLProtocolManager 发送导航协议
- **影响评估**: 如果应用不显示导航路口图，可以暂时跳过

#### 3.3 BTPhoneControl (蓝牙电话控制)
- **当前依赖**: `mWLConnector.getConfiguration().getBluetoothPhoneCapability()`
- **替代方案**: 通过协议管理器发送电话控制指令
- **影响评估**: 如果应用不使用车机蓝牙电话，可以暂时跳过

## 📋 实施计划

### 阶段1：核心依赖替换 (必须完成)

**任务清单**:
- [ ] 修改 LayoutTypeUtils.isA2R() 方法
- [ ] 修改 BaseFloatingDialog.initDialog() 方法
- [ ] 清理 BaseModuleManager 中的服务注册
- [ ] 清理 ModuleManager 中的服务注册
- [ ] 验证车机信息获取功能正常

**预期结果**: 应用可以正常启动和运行，车机屏幕信息获取正常

### 阶段2：功能验证和测试

**测试清单**:
- [ ] 车机连接流程测试
- [ ] A2R 布局判断功能测试
- [ ] 浮窗对话框显示测试
- [ ] 录屏功能启动测试
- [ ] 小米手机蓝牙断开测试

**验证要点**:
- [ ] 确认新流程日志输出正确 (【NEW_FLOW】标记)
- [ ] 确认不再出现旧流程日志 (【OLD_FLOW】警告)
- [ ] 确认车机屏幕参数获取正确
- [ ] 确认录屏功能正常启动

### 阶段3：可选功能迁移 (按需实施)

如果应用需要以下功能，可以考虑实施：
- [ ] 麦克风控制功能迁移
- [ ] 导航显示功能迁移
- [ ] 蓝牙电话功能迁移

## 🔍 验证方法

### 1. **日志验证**
在 logcat 中搜索以下关键字：
- `【NEW_FLOW】` - 应该出现，表示使用新流程
- `【OLD_FLOW】` - 不应该出现，表示没有走旧流程
- `【FLOW_TRACE】` - 追踪调用来源

### 2. **功能验证**
- 车机连接成功后录屏正常启动
- A2R 车机布局判断正确
- 浮窗对话框尺寸正确
- 小米手机蓝牙断开时车机状态正确

### 3. **性能验证**
- 应用启动时间没有明显变化
- 内存使用没有异常增长
- 连接建立时间没有明显延长

## ⚠️ 风险评估

### 高风险项
1. **车机信息获取失败** - 可能导致布局错误或浮窗显示异常
2. **时序问题** - 参数可能在获取时还未设置完成

### 中风险项
1. **控制器功能缺失** - 麦克风、导航、电话功能可能不可用
2. **状态管理不一致** - 可能影响其他模块的状态判断

### 低风险项
1. **单例注册残留** - 不会影响功能，但会浪费少量内存

## 📞 应急方案

如果修改后出现问题，可以通过以下方式快速回退：

### 快速回退步骤
1. 恢复 MainActivity.onLinkConnected() 中的原始代码
2. 恢复其他文件中的 WLConnectManager 调用
3. 恢复单例工厂注册
4. 重新编译和测试

### 调试工具
- 通过日志标记快速定位问题
- 通过调用栈追踪确认调用路径
- 通过参数日志确认数据传递

## 📚 相关文档

- [WLPlatformManager 调用时序分析](../docs/WLPlatformManager_调用时序分析.md)
- [项目架构文档](../docs/)

---

**文档版本**: v1.0  
**创建日期**: 2025-01-28  
**负责人**: 开发团队  
**预计完成时间**: 待评估