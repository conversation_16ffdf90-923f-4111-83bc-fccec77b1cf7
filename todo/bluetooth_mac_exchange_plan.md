# Bluetooth MAC Address Exchange Implementation Plan

## Background
This plan outlines the changes needed to implement bidirectional Bluetooth MAC address exchange between phone and car head unit. The implementation will follow these requirements:

1. Phone needs to receive MAC address from car via `onPhoneBTMacAddress` protocol
2. Phone needs to store and manage this MAC address information
3. Phone needs to send its MAC address to car via the `onCarBTConnected` protocol

## Current Implementation Analysis
- Car already sends its Bluetooth MAC address to phone during connection
- Phone stores this MAC in `WLPlatformManager.btMacAddress` with getter `getHuBtMacAddress()`
- Phone detects Bluetooth connection state changes with car and sends info via `sendSpecificHIDDeviceInfo()`
- Current implementation sends connection state but not the phone's MAC address

## Required Changes

### 1. Add Protocol Constant Definitions
需要在两个WLProtocolConfig文件中都添加常量定义：

**在 `basemodule/src/main/java/com/autoai/fundrive/platform/protocol/WLProtocolConfig.java` 中添加：**
```java
// New method for receiving phone MAC address from car
public static final String HU_PROTOCOL_METHOD_PHONE_BT_MAC_ADDRESS = "onPhoneBTMacAddress";
public static final String HU_PROTOCOL_METHOD_FIELD_MAC_ADDRESS = "macAddress";

// Need to add new field for phone MAC address
public static final String MU_PROTOCOL_METHOD_FIELD_PHONE_MAC = "phoneMac";
```

**在 `link_android/linkSdk/src/main/java/com/autoai/avs/linksdk/platform/protocol/WLProtocolConfig.java` 中添加：**
```java
// New method for receiving phone MAC address from car
public static final String HU_PROTOCOL_METHOD_PHONE_BT_MAC_ADDRESS = "onPhoneBTMacAddress";
public static final String HU_PROTOCOL_METHOD_FIELD_MAC_ADDRESS = "macAddress";

// Need to add new field for phone MAC address
public static final String MU_PROTOCOL_METHOD_FIELD_PHONE_MAC = "phoneMac";
```

**注意**: 经过代码分析发现，`MU_PROTOCOL_METHOD_FIELD_BLUETOOTH_PAIRED`字段已经存在且值为"connectedState"，所以不需要重复定义。

### 2. Create Protocol Bean for Receiving Phone MAC
需要在两个模块中都创建Bean类：

**创建 `basemodule/src/main/java/com/autoai/fundrive/platform/protocol/bean/HuPhoneBtMacAddressBean.java`：**
```java
package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 车机端向手机端发送手机蓝牙MAC地址的协议Bean
 */
public class HuPhoneBtMacAddressBean extends BaseProtocolBean {
    private String macAddress;

    public String getMacAddress() {
        return macAddress;
    }

    @Override
    public String toString() {
        return "HuPhoneBtMacAddressBean{" +
                "macAddress='" + macAddress + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.macAddress = extData.optString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_MAC_ADDRESS, "");
    }
}
```

**创建 `link_android/linkSdk/src/main/java/com/autoai/avs/linksdk/platform/protocol/bean/HuPhoneBtMacAddressBean.java`：**
```java
package com.autoai.avs.linksdk.platform.protocol.bean;

import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 车机端向手机端发送手机蓝牙MAC地址的协议Bean
 */
public class HuPhoneBtMacAddressBean extends BaseProtocolBean {
    private String macAddress;

    public String getMacAddress() {
        return macAddress;
    }

    @Override
    public String toString() {
        return "HuPhoneBtMacAddressBean{" +
                "macAddress='" + macAddress + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.macAddress = extData.optString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_MAC_ADDRESS, "");
    }
}
```

### 3. Update HUProtocolControl to Handle New Message
需要在两个HUProtocolControl类的`getCommandBean()`方法中都添加处理逻辑：

**在 `basemodule/src/main/java/com/autoai/fundrive/platform/protocol/control/HUProtocolControl.java` 中：**
```java
case WLProtocolConfig.HU_PROTOCOL_METHOD_PHONE_BT_MAC_ADDRESS:
    mBaseProtocolBean = new HuPhoneBtMacAddressBean();
    break;
```

**在 `link_android/linkSdk/src/main/java/com/autoai/avs/linksdk/platform/protocol/control/HUProtocolControl.java` 中：**
```java
case WLProtocolConfig.HU_PROTOCOL_METHOD_PHONE_BT_MAC_ADDRESS:
    mBaseProtocolBean = new HuPhoneBtMacAddressBean();
    break;
```

### 4. Create Handler for MAC Address Storage
创建 `basemodule/src/main/java/com/autoai/fundrive/platform/protocol/manager/PhoneBtMacAddressManager.java`：
```java
package com.autoai.fundrive.platform.protocol.manager;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import androidx.core.content.ContextCompat;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.SharePreferenceUtil;
import com.autoai.fundrive.commontool.SingletonFactory;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.protocol.bean.BaseProtocolBean;
import com.autoai.fundrive.platform.protocol.bean.HuPhoneBtMacAddressBean;
import com.autoai.fundrive.platform.protocol.listener.HUCommandListener;

/**
 * 手机蓝牙MAC地址管理器
 * 负责接收、存储和提供手机蓝牙MAC地址
 */
public class PhoneBtMacAddressManager implements HUCommandListener {
    private static final String PREF_PHONE_BT_MAC = "phone_bt_mac_address";
    private static final Object LOCK = new Object();

    @Override
    public void onReceiveCommand(BaseProtocolBean baseProtocolBean) {
        if (baseProtocolBean instanceof HuPhoneBtMacAddressBean) {
            HuPhoneBtMacAddressBean bean = (HuPhoneBtMacAddressBean) baseProtocolBean;
            String macAddress = bean.getMacAddress();
            if (!TextUtils.isEmpty(macAddress)) {
                synchronized (LOCK) {
                    try {
                        // 存储MAC地址
                        SharePreferenceUtil.putString(PREF_PHONE_BT_MAC, macAddress);
                        LogManager.i("Received and stored phone BT MAC address: " + macAddress);
                    } catch (Exception e) {
                        LogManager.e("Failed to store phone BT MAC address: " + e.getMessage());
                    }
                }
            } else {
                LogManager.w("Received empty phone BT MAC address");
            }
        }
    }

    /**
     * 获取存储的手机蓝牙MAC地址
     * @return 存储的MAC地址，如果没有则返回空字符串
     */
    public static String getPhoneBtMacAddress() {
        synchronized (LOCK) {
            try {
                return SharePreferenceUtil.getString(PREF_PHONE_BT_MAC, "");
            } catch (Exception e) {
                LogManager.e("Failed to get stored phone BT MAC address: " + e.getMessage());
                return "";
            }
        }
    }
    
    // Add getter to retrieve local MAC address if available, or empty string
    public static String getLocalBluetoothMacAddress(Context context) {
        // 首先尝试获取本地蓝牙MAC地址
        try {
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter != null && context != null) {
                // 检查蓝牙权限
                if (ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH)
                    == PackageManager.PERMISSION_GRANTED) {
                    @SuppressLint("MissingPermission")
                    String address = bluetoothAdapter.getAddress();
                    if (!TextUtils.isEmpty(address) && !"02:00:00:00:00:00".equals(address)) {
                        LogManager.i("Retrieved local Bluetooth MAC: " + address);
                        return address;
                    }
                }
            }
        } catch (Exception e) {
            LogManager.e("Failed to get local Bluetooth MAC: " + e.getMessage());
        }

        // 使用存储的手机MAC地址作为备选方案
        String storedMac = getPhoneBtMacAddress();
        LogManager.i("Using stored phone MAC: " + storedMac);
        return storedMac;
    }
}
```

### 5. Modify MUProtocolControl.sendBluetoothPairedToCar Method
Update method in `MUProtocolControl.java`:
```java
/**
 * 给车机发送蓝牙匹配连接状态消息
 * @param state 蓝牙连接状态
 */
public void sendBluetoothPairedToCar(boolean state) {
    sendBluetoothPairedToCar(state, null);
}

/**
 * 给车机发送蓝牙匹配连接状态消息
 * @param state 蓝牙连接状态
 * @param deviceAddress 设备MAC地址，如果为null则使用存储的或本地的MAC地址
 */
public void sendBluetoothPairedToCar(boolean state, String deviceAddress) {
    LogManager.i("sendBluetoothPairedToCar ---------> state:" + state + ", address:" + deviceAddress);
    try {
        JSONObject extData = new JSONObject();
        extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_CONNECTED_STATE, state ? "1" : "0");
        
        // Get the MAC address - prioritize provided address, then local address, then stored address
        String phoneMac = deviceAddress;
        if (TextUtils.isEmpty(phoneMac)) {
            // 需要传入Context，可以通过WLPlatformManager获取
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            Context context = platformManager.getContext();
            phoneMac = PhoneBtMacAddressManager.getLocalBluetoothMacAddress(context);
        }
        
        extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_PHONE_MAC, phoneMac != null ? phoneMac : "");
        
        String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_BLUETOOTH_PAIRED, extData);
        sendMessageDataCommand(data);
    } catch (Exception e) {
        LogManager.e("sendBluetoothPairedToCar exception:" + e.getMessage());
    }
}
```

### 6. Update WLProtocolManager.sendSpecificHIDDeviceInfo Method
Modify method in `WLProtocolManager.java`:
```java
/**
 * 发送HID设备和互联设备配对状态到车机
 * 
 * @param deviceName 设备名称，如果未找到特定设备则为null
 * @param deviceAddress 设备地址，如果未找到特定设备则为null
 * @param found 是否找到特定HID设备
 */
@Keep
public void sendSpecificHIDDeviceInfo(String deviceName, String deviceAddress, boolean found) {
    LogManager.i("sendHIDPairingStatus ---------> found:" + found + ", deviceName:" + deviceName + ", address:" + deviceAddress);
    muProtocolControl.sendBluetoothPairedToCar(found, deviceAddress);
}
```

### 7. Register Protocol Listener
In `MainActivity.java` or appropriate initialization class:
```java
// Register listener for onPhoneBTMacAddress protocol messages
WLProtocolManager.getInstance().addMethodListener(
    WLProtocolConfig.HU_PROTOCOL_METHOD_PHONE_BT_MAC_ADDRESS, 
    new PhoneBtMacAddressManager()
);
```

## Integration Testing
1. **接收协议测试**
   - 测试接收"onPhoneBTMacAddress"消息
   - 验证MAC地址正确提取和存储
   - 验证后续消息使用存储的MAC地址
   - 测试异常情况（空MAC地址、格式错误等）

2. **发送协议测试**
   - 测试发送"onCarBTConnected"消息
   - 验证正确的格式包含"connectedState"和"phoneMac"字段
   - 测试连接和断开状态
   - 验证MAC地址正确包含
   - 测试权限不足的情况

3. **端到端测试**
   - 验证完整流程：车机发送MAC到手机，手机存储，手机发送回车机
   - 测试AOA和Wi-Fi连接模式
   - 测试多次连接断开的场景
   - 测试应用重启后MAC地址持久化

4. **边界条件测试**
   - 测试蓝牙权限被拒绝的情况
   - 测试蓝牙适配器不可用的情况
   - 测试存储失败的情况
   - 测试并发访问的线程安全性

## Expected Message Formats

### onCarBTConnected (Phone to Car)
```json
{
    "command": {
        "method": "onCarBTConnected",
        "extData": {
            "connectedState": "1",
            "phoneMac": "XX:XX:XX:XX:XX:XX"
        }
    },
    "version": 0,
    "platform": "android",
    "moduleName": "WeLink"
}
```

### onPhoneBTMacAddress (Car to Phone)
```json
{
    "command": {
        "method": "onPhoneBTMacAddress",
        "extData": {
            "macAddress": "XX:XX:XX:XX:XX:XX"
        }
    },
    "moduleName": "WeLink",
    "platform": "android",
    "version": "0"
}
```

## 注意事项和风险评估

### 1. **兼容性考虑**
- 新协议需要与旧版本车机保持兼容
- 如果车机不支持新协议，手机端应该能够正常工作
- 建议添加协议版本检查机制

### 2. **安全性考虑**
- MAC地址属于敏感信息，需要确保安全传输
- 建议添加MAC地址格式验证
- 考虑添加数据加密或签名验证

### 3. **性能考虑**
- 避免频繁的SharedPreferences读写操作
- 考虑添加内存缓存机制
- 确保MAC地址获取不会阻塞主线程

### 4. **错误处理**
- 添加完善的异常处理机制
- 提供降级方案（如果无法获取MAC地址）
- 添加重试机制

### 5. **日志和调试**
- 添加详细的日志记录便于调试
- 注意不要在生产环境中打印敏感信息
- 建议添加开发者选项控制日志级别

### 6. **测试覆盖**
- 确保在不同Android版本上测试
- 测试不同品牌手机的兼容性
- 测试各种网络环境和连接状态
``` 