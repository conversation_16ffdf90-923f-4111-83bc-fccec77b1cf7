package com.autoai.welink.autoproxy.server;

import com.autoai.welink.autoproxy.server.code.AutoServerLink;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * Created by gao<PERSON> on 2018/12/25.
 */

public class WLAutoServerLink {

    AutoServerLink mAutoServerLink = null;

    ExecutorService masterThreadExecutor = Executors.newSingleThreadExecutor();//线程池
    ExecutorService touchEventThreadExecutor = Executors.newSingleThreadExecutor();//线程池
    ExecutorService voiceDataThreadExecutor = Executors.newSingleThreadExecutor();//线程池

    public WLAutoServerLink(AutoServerLink autoServerLink) {
        mAutoServerLink = autoServerLink;
    }

    public void resumeMirrorScreen() {
        mAutoServerLink.resumeMirrorScreen();
    }

    public void pauseMirrorScreen() {
        mAutoServerLink.pauseMirrorScreen();
    }

    public byte[] read(String filename) {
        return mAutoServerLink.read(filename);
    }

    public boolean create(String filename, byte[] buffer) {
        return mAutoServerLink.create(filename, buffer);
    }

    public void remove(String filename) {
        mAutoServerLink.remove(filename);
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        masterThreadExecutor.execute(() -> mAutoServerLink.disconnect());
    }

    /**
     * 获取connectStr
     * @return 返回connectStr
     */
    public String getLinkConnectStr() {
        return mAutoServerLink.getLinkConnectStr();
    }

    /**
     * 获取能力cap
     * @return 返回cap
     */
    public long getLinkConnectCap() {
        return mAutoServerLink.getLinkConnectCap();
    }

    /**
     * 设置 WLAutoDataListener接收数据监听
     * @param dataLister WLAutoDataListener监听
     */
    public void setAutoDataListener(WLAutoDataListener dataLister) {
        mAutoServerLink.setAutoDataListener(dataLister);
    }

    /**
     * 发送数据
     * @param type 发送的数据类型
     * @param bytes 发送的二进制数据
     */
    public void sendData(final int type, final byte[] bytes) {
        sendData(type, bytes, null);
    }

    /**
     * 发送数据
     * @param type 发送的数据类型
     * @param bytes 发送的二进制数据
     * @param bytes2 发送的第二段二进制数据
     */
    public void sendData(final int type, final byte[] bytes, final byte[] bytes2) {
        mAutoServerLink.sendDataByAIDL(type, bytes,bytes2);
    }
}
