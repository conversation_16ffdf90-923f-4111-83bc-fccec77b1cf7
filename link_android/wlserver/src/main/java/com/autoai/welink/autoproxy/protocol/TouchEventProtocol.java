package com.autoai.welink.autoproxy.protocol;

import android.view.MotionEvent;

import com.autoai.welink.autoproxy.AppConnectListener;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

//回控协议
public class TouchEventProtocol extends ProtocolBase {
    public TouchEventProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {

    }

    public void onTouchEvent(MotionEvent event) {
        AnFileLog.e("fatal4","wlserver onTouchEvent "+event);
        int action = event.getAction();
        int pointerCount = event.getPointerCount();
        int raw_x = (int) event.getX();
        int raw_y = (int) event.getY();
        String json = "{}";
        JSONObject obj = new JSONObject();
        try {
            obj.put("pointerCount", pointerCount);
            if (pointerCount == 1) {
                obj.put("action", action);
                obj.put("raw_x", raw_x);
                obj.put("raw_y", raw_y);
            } else if (pointerCount > 1) {
                JSONArray array_x = new JSONArray();
                JSONArray array_y = new JSONArray();
                for (int i = 0; i < pointerCount; i++) {
                    try {
                        array_x.put((int) event.getX(i));
                        array_y.put((int) event.getY(i));
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
//                action &= 0xFF;
//                if (action == MotionEvent.ACTION_POINTER_DOWN)
//                    action = MotionEvent.ACTION_DOWN;
//                if (action == MotionEvent.ACTION_POINTER_UP)
//                    action = MotionEvent.ACTION_UP;
                obj.put("array_x", array_x);
                obj.put("array_y", array_y);
                obj.put("action", action);
            }
            json = obj.toString();
            AnFileLog.e("fatal4","wlserver onTouchEvent json="+json);
            if (transmisson != null) {
                transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_TOUCH_EVENT, json.getBytes());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
