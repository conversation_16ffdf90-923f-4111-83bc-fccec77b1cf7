package com.autoai.welink.autoproxy.protocol;

import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.server.WLAutoDataListener;
import com.autoai.welink.autoproxy.server.WLAutoServerLink;
import com.autoai.welink.autoproxy.statemanager.StateController;

public class Transmisson {
    ProtocolMgr protocolMgr;
    public StateController stateController;
    public String connectStr;

    public ProtocolMgr getProtocolMgr() {
        return protocolMgr;
    }

    private WLAutoServerLink wlAutoServerLink;
    public WLAutoDataListener wlAutoDataListener = new WLAutoDataListener() {
        @Override
        public void onDisconnected() {
            protocolMgr.onDisconnected();
            stateController.onDisconnected();
            disconnect();
            wlAutoServerLink = null;
        }

        @Override
        public void onError(int type) {
            protocolMgr.onDisconnected();
            stateController.onError(type);
            wlAutoServerLink = null;
        }

        @Override
        public void onReciveData(int type,byte[] data) {
            protocolMgr.onReciveData(type, data);
            //AnFileLog.e("onReciveData data.length=" + data.length);
//            try {
//                int packsize = data.length;
//                if (data.length < 4) return;//数据包异常,长度不够
//                //解析数据,分出type和数据包
//                ByteArrayInputStream bais = new ByteArrayInputStream(data);
//                DataInputStream bis = new DataInputStream(bais);
//                int type = bis.readInt();
//                byte[] pack = new byte[packsize - 4];//申请数据数据包的缓冲区
//                int readsize = bis.read(pack);
//                if (readsize != packsize - 4) return;//数据包异常,长度不对
//                protocolMgr.onReciveData(type, pack);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
            return;
        }
    };


    public Transmisson(String connectStr, StateController state, AppConnectListener listener, WLAutoServerLink wlAutoServerLink) {
        this.stateController = state;
        this.wlAutoServerLink = wlAutoServerLink;
        this.wlAutoServerLink.setAutoDataListener(wlAutoDataListener);
        this.protocolMgr = new ProtocolMgr(this, listener);
        this.protocolMgr.onConnected();
        this.connectStr = connectStr;
    }

    public byte[] read(String filename) {
        if (wlAutoServerLink != null) {
            return wlAutoServerLink.read(filename);
        }

        return null;
    }

    public boolean create(String filename, byte[] buffer) {
        if (wlAutoServerLink != null) {
            return wlAutoServerLink.create(filename, buffer);
        }

        return false;
    }

    public void remove(String filename) {
        if (wlAutoServerLink != null) {
            wlAutoServerLink.remove(filename);
        }
    }

    public void disconnect() {
        if (wlAutoServerLink != null) {
            wlAutoServerLink.disconnect();
        }
    }

    public void sendData(final int type, final byte[] bytes) {
        AnFileLog.e("wlserver-protocol","sendData type="+type+",bytes.length="+bytes.length);
        if (wlAutoServerLink != null) {
            wlAutoServerLink.sendData(type, bytes);
        }
    }
}
