package com.autoai.welink.autoproxy.statemanager;

public class AppState {
    public static final int AppState_NULL = 0;                //初始状态
    public static final int AppState_CONNECT = 1 << 0;         //链接状态
    public static final int AppState_DISCONNECT = 1 << 1;      //断开状态
    public static final int AppState_FOREGROUND = 1 << 2;      //APP在前台
    public static final int AppState_BACKGROUND = 1 << 3;      //APP在后台
    public static final int AppState_INACTIVEMUSIC = 1 << 4;   //APP音乐未激活
    public static final int AppState_ACTIVEMUSIC = 1 << 5;     //APP音乐激活
    public static final int AppState_ACTIVESCREEN = 1 << 6;    //APP激活录屏
    public static final int AppState_INACTIVESCREEN = 1 << 7;  //APP录屏未激活

    private int value = AppState_NULL;

    public int getValue() {
        return value;
    }

    public void setValue(int state) {
        if (state == AppState_CONNECT && hasState(AppState_DISCONNECT)) {
            value ^= AppState_DISCONNECT;
        }

        if (state == AppState_DISCONNECT && hasState(AppState_CONNECT)) {
            value ^= AppState_CONNECT;
        }

        if (state == AppState_FOREGROUND && hasState(AppState_BACKGROUND)) {
            value ^= AppState_BACKGROUND;
        }

        if (state == AppState_BACKGROUND && hasState(AppState_FOREGROUND)) {
            value ^= AppState_FOREGROUND;
        }

        if (state == AppState_INACTIVEMUSIC && hasState(AppState_ACTIVEMUSIC)) {
            value ^= AppState_ACTIVEMUSIC;
        }

        if (state == AppState_ACTIVEMUSIC && hasState(AppState_INACTIVEMUSIC)) {
            value ^= AppState_INACTIVEMUSIC;
        }

        if (state == AppState_INACTIVESCREEN && hasState(AppState_ACTIVESCREEN)) {
            value ^= AppState_ACTIVESCREEN;
        }

        if (state == AppState_ACTIVESCREEN && hasState(AppState_INACTIVESCREEN)) {
            value ^= AppState_INACTIVESCREEN;
        }


        value = (value | state);
    }

    public boolean hasState(int state) {
        return (value & state) != 0;
    }
}
