package com.autoai.welink.autoproxy.protocol;


import com.autoai.welink.autoproxy.AppConnectListener;

//协议管理类
public class ProtocolMgr {
    //协议类型
    public static final int TRANSIMISSON_TYPE_TOUCH_EVENT = 3;//投屏回控
    public static final int TRANSIMISSON_TYPE_BACKGROUND = 5;//前后台状态
    public static final int TRANSIMISSON_TYPE_MUSIC = 11;//音乐
    public static final int TRANSIMISSON_TYPE_NAVI = 12;//导航
    public static final int TRANSIMISSON_TYPE_COMMAND = 13;//车机命令
    public static final int TRANSIMISSON_TYPE_ACTION = 14;//action消息
    public static final int TRANSIMISSON_TYPE_MICROPHONE = 15;//车机麦克风
    public static final int TRANSIMISSON_TYPE_SOUND = 16;//声音


    public ProtocolMgr(Transmisson transmisson, AppConnectListener appListener) {
        touchEventProtocol = new TouchEventProtocol(transmisson, appListener);
        backgroundStatusProtocol = new BackgroundStatusProtocol(transmisson, appListener);
        musicProtocol = new MusicProtocol(transmisson, appListener);
        commandProtocol = new CommandProtocol(transmisson,  null);
        actionProtocol = new ActionProtocol(transmisson, appListener);
        naviProtocol = new NaviProtocol(transmisson, appListener);
        microphoneProtocol = new MicrophoneProtocol(transmisson,  null);
        soundProtocol = new SoundProtocol(transmisson,  appListener);
    }

    //=========================================================
    //投屏回控协议
    TouchEventProtocol touchEventProtocol;

    public TouchEventProtocol getTouchEventProtocol() {
        return touchEventProtocol;
    }

    //=========================================================
    //前后台协议
    BackgroundStatusProtocol backgroundStatusProtocol;

    public BackgroundStatusProtocol getBackgroundStatusProtocol() {
        return backgroundStatusProtocol;
    }

    //=========================================================
    //投屏控制协议
    MusicProtocol musicProtocol;

    public MusicProtocol getMusicProtocol() {
        return musicProtocol;
    }


    //=========================================================
    //车机命令协议协议
    CommandProtocol commandProtocol;

    public CommandProtocol getCommandProtocol() {
        return commandProtocol;
    }

    //=========================================================
    //action协议协议
    ActionProtocol actionProtocol;
    public ActionProtocol getActionProtocol() {
        return actionProtocol;
    }

    //=========================================================
    //导航协议协议
    NaviProtocol naviProtocol;
    public NaviProtocol getNaviProtocol() {return naviProtocol;}

    //麦克风协议
    MicrophoneProtocol microphoneProtocol;

    public MicrophoneProtocol getMicrophoneProtocol() {
        return microphoneProtocol;
    }


    //声音协议
    SoundProtocol soundProtocol;

    public SoundProtocol getSoundProtocol() {
        return soundProtocol;
    }
    //=========================================================
    public void onReciveData(int type, byte[] data) {
        AnFileLog.e("wlserver-protocol","ProtocolMgr onReciveData type=" + type + ",data.length=" + data.length);
        //解析数据包,或者按类型分发供具体协议解析数据包
        switch (type) {
            case 0:
                break;
            case TRANSIMISSON_TYPE_TOUCH_EVENT:
                break;
            case TRANSIMISSON_TYPE_BACKGROUND:
                if (backgroundStatusProtocol != null) {
                    backgroundStatusProtocol.onReciveData(data);
                }
                break;
            case TRANSIMISSON_TYPE_MUSIC:
                if (musicProtocol != null) {
                    musicProtocol.onReciveData(data);
                }
                break;
            case TRANSIMISSON_TYPE_NAVI:
                if (naviProtocol != null) {
                    naviProtocol.onReciveData(data);
                }
                break;
            case TRANSIMISSON_TYPE_ACTION:
                if (actionProtocol != null) {
                    actionProtocol.onReciveData(data);
                }
                break;
            case TRANSIMISSON_TYPE_SOUND:
                if (soundProtocol != null) {
                    soundProtocol.onReciveData(data);
                }
                break;
            case TRANSIMISSON_TYPE_COMMAND:
                if (commandProtocol != null) {
                    commandProtocol.onReciveData(data);
                }
        }
    }

    public boolean onConnected() {
        if (backgroundStatusProtocol != null) {
            backgroundStatusProtocol.onConnected();
        }
        if (musicProtocol != null) {
            musicProtocol.onConnected();
        }
        if (commandProtocol != null) {
            commandProtocol.onConnected();
        }
        if (actionProtocol != null) {
            actionProtocol.onConnected();
        }
        if (naviProtocol != null) {
            naviProtocol.onConnected();
        }
        if (microphoneProtocol != null) {
            microphoneProtocol.onConnected();
        }
        if (soundProtocol != null) {
            soundProtocol.onConnected();
        }
        return false;
    }

    public void onDisconnected() {
        if (backgroundStatusProtocol != null) {
            backgroundStatusProtocol.onDisconnected();
        }
        if (musicProtocol != null) {
            musicProtocol.onDisconnected();
        }
        if (commandProtocol != null) {
            commandProtocol.onDisconnected();
        }
        if (actionProtocol != null) {
            actionProtocol.onDisconnected();
        }
        if (naviProtocol != null) {
            naviProtocol.onDisconnected();
        }
        if (microphoneProtocol != null) {
            microphoneProtocol.onDisconnected();
        }
        if (soundProtocol != null) {
            soundProtocol.onDisconnected();
        }
    }
}
