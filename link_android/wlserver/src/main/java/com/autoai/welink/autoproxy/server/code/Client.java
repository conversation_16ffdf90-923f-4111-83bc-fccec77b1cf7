package com.autoai.welink.autoproxy.server.code;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.IBinder;
import android.os.MemoryFile;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.os.SharedMemory;
import android.system.ErrnoException;
import android.view.Surface;

import com.autoai.welink.auto.client.IClientService;
import com.autoai.welink.autoproxy.protocol.AnFileLog;
import com.autoai.welink.autoproxy.server.WLAutoDataListener;
import com.autoai.welink.autoproxy.statemanager.StateController;
import com.autoai.welink.autoproxy.statemanager.StateManager;

import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.nio.channels.FileLock;
import java.nio.channels.OverlappingFileLockException;
import java.util.ArrayList;
import java.util.Map;


public class Client {
    static class TimeLimitMemory {
        public final long date = System.currentTimeMillis();
        public final SharedMemory sharedMemory;
        public final MemoryFile memoryFile;

        public TimeLimitMemory(SharedMemory sharedMemory) {
            this.sharedMemory = sharedMemory;
            this.memoryFile = null;
        }

        public TimeLimitMemory(MemoryFile memoryFile) {
            this.memoryFile = memoryFile;
            this.sharedMemory = null;
        }
    }
    public static Map<String, TimeLimitMemory> memoryList;
    public static final Object memoryListLock = new Object();
    public static StateManager stateManager;

    private final ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceDisconnected(ComponentName name) {
            bBind = false;

            synchronized (clientServiceLock) {
                clientService = null;
            }

            if (listener != null) {
                listener.onDisconnected();
            }
        }

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            if (!bBind) {
                return;
            }

            synchronized (clientServiceLock) {
                clientService = IClientService.Stub.asInterface(service);
            }

            synchronized (constructorSync) {
                constructorSync.notify();
            }
        }
    };

    public Client(Context context, String packageName) throws Exception {
        this.context = context;
        Intent intent = new Intent();

        intent.setAction("com.autoai.welink.auto.client.IClientService");
        intent.setPackage(packageName);

        bBind = true;

        synchronized (constructorSync) {
            if (!context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)) {
                throw new Exception("MemoryFile failed");
            }
            constructorSync.wait(300);
        }
    }

    public void setDataListener(WLAutoDataListener listener) {
        this.listener = listener;
    }

    public void sendData(String connectStr, int type, byte[] bytes){
        synchronized (clientServiceLock) {
            if (clientService != null) {
                try {
                    AnFileLog.e("aidl_s","sendData clientService="+clientService);
                    clientService.sendData(connectStr, type, bytes);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    //wlserver处理wlclient发来的数据
    static public void onDataRecv(String connectStr, int type, byte[] bytes){
        AnFileLog.e("aidl_s","onDataRecv connectStr="+connectStr+",type="+type+",bytes.length="+bytes.length+",stateManager="+stateManager);
        if (stateManager != null){
            StateController stateController = stateManager.findController(connectStr);
            if (stateController != null){
                if (stateController.transmisson != null) {
                    if (stateController.transmisson.wlAutoDataListener != null) {
                        AnFileLog.e("aidl_s","onDataRecv onReciveData ok");
                    }
                        stateController.transmisson.wlAutoDataListener.onReciveData(type, bytes);
                }
            }
        }
    }

    public void release() {
        listener = null;

        synchronized (clientServiceLock) {
            if (clientService != null) {
                clientService = null;
                context.unbindService(serviceConnection);
            }
        }
    }

    public static boolean create(String filename, byte[] buffer) {
        synchronized (memoryListLock) {
            if (memoryList == null) {
                return false;
            }

            //每次新建内存文件前，先检查是否有过期内存文件，如果有则删除，以保证运行期间Connector不读数据出现内存泄漏 -->
            ArrayList<String> keyList = new ArrayList<>();
            for (Map.Entry<String, TimeLimitMemory> entry: memoryList.entrySet()) {
                TimeLimitMemory memory = entry.getValue();
                if (memory != null && (System.currentTimeMillis() - memory.date) > 1000) {
                    keyList.add(entry.getKey());
                }
            }
            for (String key: keyList) {
                TimeLimitMemory memory = memoryList.get(key);
                if (memory != null) {
                    AnFileLog.e("aidl_s", "ClearTimeOutMemory");
                    if (memory.sharedMemory != null) {
                        memory.sharedMemory.close();
                    } else if (memory.memoryFile != null) {
                        memory.memoryFile.close();
                    }
                    memoryList.remove(key);
                }
            }
            // <--

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                SharedMemory sharedMemory = null;
                try {
                    sharedMemory = SharedMemory.create(filename, buffer.length);
                } catch (ErrnoException e) {
                    e.printStackTrace();
                }
                if (sharedMemory == null) {
                    return false;
                }

                ByteBuffer byteBuffer = null;
                try {
                    byteBuffer = sharedMemory.mapReadWrite();
                } catch (ErrnoException e) {
                    e.printStackTrace();
                }
                if (byteBuffer == null) {
                    sharedMemory.close();
                    return false;
                }

                byteBuffer.put(buffer);
                SharedMemory.unmap(byteBuffer);

                memoryList.put(filename, new TimeLimitMemory(sharedMemory));
            } else {
                if (memoryList == null) {
                    return false;
                }

                MemoryFile memoryFile = null;
                try {
                    memoryFile = new MemoryFile(filename, buffer.length);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (memoryFile == null) {
                    return false;
                }

                FileDescriptor fd = null;
                try {
                    @SuppressLint("DiscouragedPrivateApi") Method method = MemoryFile.class.getDeclaredMethod("getFileDescriptor");
                    fd = (FileDescriptor) method.invoke(memoryFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fd == null) {
                    memoryFile.close();
                    return false;
                }

                FileOutputStream fos = new FileOutputStream(fd);
                FileLock fl = null;
                try {
                    fl = fos.getChannel().lock(0L, buffer.length, false);
                    memoryFile.writeBytes(buffer, 0, 0, buffer.length);
                    fl.release();
                    fos.flush();
                    fos.close();
                } catch (Exception e) {
                    memoryFile.close();

                    try {
                        if (fl != null) {
                            fl.release();
                        }
                        fos.close();
                    } catch (IOException e1) {
                        e1.printStackTrace();
                    }

                    AnFileLog.e("aidl_s", "MemoryFile-Exception: " + filename);
                    e.printStackTrace();

                    return false;
                }

                memoryList.put(filename, new TimeLimitMemory(memoryFile));
            }
            return true;
        }
    }

    public static void remove(String filename) {
        synchronized (memoryListLock) {
            if (memoryList != null) {
                TimeLimitMemory memory = memoryList.get(filename);

                if (memory == null) {
                    return;
                }

                if (memory.sharedMemory != null) {
                    memory.sharedMemory.close();
                } else if (memory.memoryFile != null) {
                    memory.memoryFile.close();
                }
                memoryList.remove(filename);
            }
        }
    }

    public byte[] read(String connectStr, String filename) {
        int size = getClientLength(connectStr, filename);
        if (size == 0) {
            return null;
        }

        byte[] buffer = new byte[size];

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            SharedMemory sharedMemory = null;
            ByteBuffer byteBuffer = null;

            sharedMemory = getClientSharedMemory(connectStr, filename);
            if (sharedMemory == null) {
                return null;
            }

            try {
                byteBuffer = sharedMemory.mapReadOnly();
            } catch (ErrnoException e) {
                e.printStackTrace();
            }

            if (byteBuffer == null) {
                sharedMemory.close();
                return null;
            }

            byteBuffer.get(buffer);
            SharedMemory.unmap(byteBuffer);
            sharedMemory.close();
        } else {
            ParcelFileDescriptor pfd = null;
            FileDescriptor fd = null;

            pfd = getClientFileDescriptor(connectStr, filename);
            if (pfd == null) {
                return null;
            }

            fd = pfd.getFileDescriptor();
            if (fd == null) {
                return null;
            }

            try {
                FileInputStream fis = new FileInputStream(fd);
                FileLock fl = fis.getChannel().lock(0L, size, true);

                try {
                    fis.getChannel().position(0);
                } catch (IOException e) {
                    AnFileLog.e("aidl_s", "MemoryFile-Exception-channel.position" + filename);
                }

                fis.read(buffer, 0, size);
                fl.release();
                fis.close();
                pfd.close();
            } catch (IOException e) {
                AnFileLog.e("aidl_s", "MemoryFile-Exception1: " + filename);
                e.printStackTrace();
            } catch (OverlappingFileLockException e) {
                AnFileLog.e("aidl_s", "MemoryFile-Exception2: " + filename);
                return null;
            }

        }
        return buffer;
    }

    public void pauseMirrorScreen(String connectStr) {
        synchronized (clientServiceLock) {
            if (clientService != null) {
                try {
                    clientService.pauseMirrorScreen(connectStr);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void resumeMirrorScreen(String connectStr) {
        synchronized (clientServiceLock) {
            if (clientService != null) {
                try {
                    clientService.resumeMirrorScreen(connectStr);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    static public Surface getSurface(String connectStr) {
        if (stateManager == null) {
            return null;
        }

        if (stateManager.wlAutoServer.hasRandomStr(connectStr)) {
            return new Surface(stateManager.getVideoSurfaceTexture());
        } else {
            return null;
        }
    }

    private ParcelFileDescriptor getClientFileDescriptor(String connectStr, String filename) {
        ParcelFileDescriptor pfd = null;

        synchronized (clientServiceLock) {
            if (clientService != null) {
                try {
                    pfd = clientService.getFileDescriptor(connectStr, filename);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        return pfd;
    }

    private SharedMemory getClientSharedMemory(String connectStr, String filename) {
        SharedMemory sharedMemory = null;

        synchronized (clientServiceLock) {
            if (clientService != null) {
                try {
                    sharedMemory = clientService.getSharedMemory(connectStr, filename);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        return sharedMemory;
    }

    private int getClientLength(String connectStr, String filename) {
        int length = 0;

        synchronized (clientServiceLock) {
            if (clientService != null) {
                try {
                    length = clientService.length(connectStr, filename);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        return length;
    }

    private IClientService clientService = null;
    private final Context context;
    private WLAutoDataListener listener;
    private final Object clientServiceLock = new Object();
    private final Object constructorSync = new Object();
    private boolean bBind;
}
