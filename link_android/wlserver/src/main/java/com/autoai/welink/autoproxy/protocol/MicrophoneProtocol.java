package com.autoai.welink.autoproxy.protocol;

import com.autoai.welink.autoproxy.AppConnectListener;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by gao<PERSON> on 2019/8/7.
 */

public class MicrophoneProtocol extends ProtocolBase {
    public MicrophoneProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    public void sendVoice(byte[] pcm, int rate, int bit, int channel){

        //写文件
        String fileName = null;
        int count = 0;
        if (null != pcm) {
            do {
                if (fileName == null) {
                    fileName = getRandomFileName();
                } else {
                    fileName += count;
                }
            } while (!transmisson.create(fileName, pcm) && ++count < 3);
        }

        if (count >= 3) {
            return;
        }

        JSONObject obj = new JSONObject();
        try {
            obj.put("Rate", rate);
            obj.put("Bit", bit);
            obj.put("Channel", channel);
            obj.put("FileName", fileName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MICROPHONE, json.getBytes());
    }

    public  String getRandomFileName() {

        //获取当前时间+随机6位字符串
        String randomStr = getRandomString(6);//"aeviiq";
        long nowTime = System.currentTimeMillis();
        return randomStr+nowTime;
    }

    private static final String string = "abcdefghijklmnopqrstuvwxyz";
    /**
     * 生成随机字符串
     * @param length 随机字符串长度
     */
    private static String getRandomString(int length){
        StringBuffer sb = new StringBuffer();
        int len = string.length();
        for (int i = 0; i < length; i++) {
            sb.append(string.charAt(getRandom(len)));
        }

        return sb.toString();
    }

    private static int getRandom(int lenght) {

        return (int)(Math.random() * lenght);
    }

    @Override
    public void onReciveData(byte[] data) {

        try {
            JSONObject jsonObject = new JSONObject(new String(data));

            if (null == jsonObject || !jsonObject.has("FileName")) {
                return;
            }

            String fileName = jsonObject.optString("FileName");
            transmisson.remove(fileName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {

    }
}
