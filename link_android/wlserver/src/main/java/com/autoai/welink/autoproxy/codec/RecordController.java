package com.autoai.welink.autoproxy.codec;

import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Surface;

import com.autoai.welink.autoproxy.protocol.AnFileLog;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/*
h264编码的实现部分
* */
public class RecordController {
    WLCodecListener codecListener;//视频数据输出
//    Lock lock = new ReentrantLock();
    MediaCodec mMediaCodec;
    int width, height, densityDpi, fps;
    Rect connectorRect;
    SurfaceRecorder surfaceRecorder = new SurfaceRecorder();
    private Handler workhandler;
    private HandlerThread mDecoderThread;

    public RecordController(/*Context context*/) {
//		this.context = context;
    }

    public void setWH(int width, int height, Rect connectorRect, int densityDpi, int fps) {
        AnFileLog.e("codec", "RecordController setWH w=" + width + ",h=" + height + ",densityDpi=" + densityDpi + ",fps=" + fps);
        this.width = width;
        this.height = height;
        this.connectorRect = connectorRect;
        this.densityDpi = densityDpi;
        this.fps = fps;
        createCodec();
    }

    public boolean checkDisplay(int width, int height, int densityDpi) {
        this.width = width;
        this.height = height;
        this.densityDpi = densityDpi;
        boolean isDisplayValid = createCodec();
        release();
        return isDisplayValid;
    }

    public void updateConnectorLeftTop(int x, int y) {
        connectorRect.left = x;
        connectorRect.top = y;

        surfaceRecorder.updateConnectorLeftTop(x, y);
    }

    protected boolean configureMedia(boolean usecq) {
        MediaFormat mediaFormat = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, width, height);
        mediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, width * height * 3);
        mediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, fps);
        mediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            mediaFormat.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileBaseline);
            mediaFormat.setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel31);
        }

        if (usecq) {
            mediaFormat.setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CQ);
        }

        AnFileLog.e("codec", "configureMedia width=" + width + ",height=" + height + ",screenDensity=" + densityDpi + ",usecq=" + usecq);
        try {
            AnFileLog.e("codec", "Build.MODEL: " + Build.MODEL);
//            if (Build.MODEL.startsWith("Pixel")) {
//                //Pixel 3a 、Pixel 3a XL两部手机硬编码不输出I帧，因此采用软编码器替代
//                //最新的验证结果, Pixel3 Pixel 4XL 均有此问题, 因此暂时强制所有 Pixel 手机均走软编码器, 后续如果发现特定机型支持硬编码的话, 再单独放开
//                //参考：https://github.com/pedroSG94/rtmp-rtsp-stream-client-java/issues/381
//                mMediaCodec = MediaCodec.createByCodecName("c2.android.avc.encoder");
//            } else {
//                mMediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_VIDEO_AVC);
//            }
//            mMediaCodec = createEncoder();
//            mMediaCodec = MediaCodec.createByCodecName("OMX.google.h264.encoder");
            mMediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_VIDEO_AVC);
            mMediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);

            surfaceRecorder.start(mMediaCodec.createInputSurface());
            surfaceRecorder.setVideoSurfaceTexture(width, height, connectorRect);
            if (mDecoderThread == null) {
                mDecoderThread = new HandlerThread("AsyncMediaDecoderThread");
                mDecoderThread.start();
                workhandler = new Handler(mDecoderThread.getLooper());
            }
            mMediaCodec.setCallback(callback, workhandler);
            workhandler.post(() -> mMediaCodec.start());
            stop();

            return true;
        } catch (Exception e) {
//			e.printStackTrace();
            AnFileLog.e("codec", "configureMedia got Exception=" + e);
        }
        return false;
    }

    private MediaCodec createEncoder() throws IOException {
        MediaCodec mediaCodec;
        try {
            /*
               在 Android 系统中，Google 提供了一些内置的软编解码器：

              1. OMX.google.h264.encoder,

              2. OMX.google.h264.decoder,

              3. OMX.google.acc.encoder,

              4. OMX.google.acc.decoder

              统一使用OMX.google.h264.encoder软编码可以适配所有设备
              软编码编码速度虽然要慢一点，但是压缩率要高
             */
            mediaCodec = MediaCodec.createByCodecName("OMX.google.h264.encoder");
        }catch (Exception e) {
            /*
                防止个别厂商自定义
             */
            mediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_VIDEO_AVC);
        }
        return mediaCodec;
    }

    private int mFps = 0;
    private long mLastTime = 0;
    private long mLastTime2 = 0;

    private void getFps() {
        mFps++;
        long timeStamp = System.currentTimeMillis();
        if (timeStamp - mLastTime >= 1000) {
            Log.e("ENCODE", "fps ==>" + mFps);
            mFps = 0;
            mLastTime = timeStamp;
        }
    }

    private int mFps1 = 0;
    private long mLastTime1 = 0;

    private void getFps1() {
        mFps1++;
        long timeStamp = System.currentTimeMillis();
        if (timeStamp - mLastTime1 >= 1000) {
            Log.e("ENCODE_1", "fps ==>" + mFps1);
            mFps1 = 0;
            mLastTime1 = timeStamp;
        }
    }


    MediaCodec.Callback callback = new MediaCodec.Callback() {
        @Override
        public void onInputBufferAvailable(MediaCodec codec, int index) {
            AnFileLog.e("codec", "onInputBufferAvailable");
        }

        @Override
        public void onOutputBufferAvailable(MediaCodec codec, int index, MediaCodec.BufferInfo info) {
            AnFileLog.e("codec", "onOutputBufferAvailable out " + isPause);
            getFps();
            try {
//                lock.lock();
                ByteBuffer encodedData = codec.getOutputBuffer(index);
                if ((info.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                    info.size = 0;
                }
                if (info.size == 0) {
                    AnFileLog.e("codec", "info.size == 0, drop it.");
                    encodedData = null;
                }
                if (encodedData != null) {
                    encodedData.position(info.offset);
                    encodedData.limit(info.offset + info.size);
                    byte[] outData = new byte[info.size];
                    encodedData.get(outData);

                    if (!isPause) {
                        if (!mustIFrame || getFrameType(outData) == 5) {
                            mustIFrame = false;
                            if (codecListener != null) {
                                codecListener.onVideoTime(info.presentationTimeUs);
                                codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, outData);
                                getFps1();
                            }
                        } else {
                            surfaceRecorder.reset();
                            if(mMediaCodec != null && System.currentTimeMillis() - mLastTime2 > 2000) {
                                mLastTime2 = System.currentTimeMillis();
                                Bundle params = new Bundle();
                                params.putInt(MediaCodec.PARAMETER_KEY_REQUEST_SYNC_FRAME, 0);
                                mMediaCodec.setParameters(params);
                            }
                        }
                    }
                }
                codec.releaseOutputBuffer(index, false);
            } catch (Exception e) {
                AnFileLog.e("codec", "onOutputBufferAvailable got Exception=" + e.toString());
            } finally {
//                lock.unlock();
            }
        }

        //return SPS: 7, PPS: 8, IDR: 5, P: 1，-1为无效帧
        private int getFrameType(byte[] data) {
            if (data == null || data.length < 5) {
                return -1;
            }

            if (data[0] == 0x0 && data[1] == 0x0 && data[2] == 0x0 && data[3] == 0x1) {
                return (data[4] & 0x1F);
            } else {
                return -1;
            }
        }

        @Override
        public void onError(MediaCodec codec, MediaCodec.CodecException e) {
            AnFileLog.e("codec", "onError" + e.toString());
        }

        @Override
        public void onOutputFormatChanged(MediaCodec codec, MediaFormat format) {
            AnFileLog.e("codec", "onOutputFormatChanged in");
            if (codecListener != null) {
                codecListener.onVideoFormatChange(format);
            }
            if (!isPause) {
                byte[] sps;
                byte[] pps;
                try {
                    sps = format.getByteBuffer("csd-0").array();
                    pps = format.getByteBuffer("csd-1").array();
                } catch (Exception e) {
                    e.printStackTrace();
                    return;
                }
                if (codecListener != null) {
                    codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, sps);
                    codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, pps);
                }
            }
            AnFileLog.e("codec", "onOutputFormatChanged out");
        }
    };

    protected void resetOutputFormat() {
        AnFileLog.e("codec", "resetOutputFormat");

        // should happen before receiving buffers, and should only happen once
        MediaFormat newFormat = mMediaCodec.getOutputFormat();

        if (newFormat == null ||
            newFormat.getByteBuffer("csd-0") == null ||
            newFormat.getByteBuffer("csd-1") == null) {
            return;
        }

        if (!isPause) {
            byte[] sps;
            byte[] pps;
            try {
                sps = newFormat.getByteBuffer("csd-0").array();
                pps = newFormat.getByteBuffer("csd-1").array();
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }
            codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, sps);
            codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, pps);
        }
    }

    private void recordStop() {
        AnFileLog.e("codec", "RecordController recordStop");
        surfaceRecorder.stop();
        release();
    }

    protected void release() {
        if (mMediaCodec != null && workhandler != null) {
//            lock.lock();
            workhandler.post(() -> {
                mMediaCodec.stop();
                mMediaCodec.release();
                mMediaCodec = null;
                AnFileLog.e("codec", "mMediaCodec.release() ");
            });
//            lock.unlock();

        }
    }

    public void setCodeListener(WLCodecListener listener) {
        codecListener = listener;
    }

    boolean createCodec() {
        AnFileLog.e("codec", "RecordController createCodec");
        if (configureMedia(true) ||
            configureMedia(false)) {
            return true;
        }

        return false;
    }

    private boolean isPause = false;
    private boolean mustIFrame = false;

    public void stop() {
        AnFileLog.e("codec", "RecordController stop");
        isPause = true;
        surfaceRecorder.pause();
    }

    public void start() {
        AnFileLog.e("codec", "RecordController start");
        isPause = false;
        mustIFrame = true;
        resetOutputFormat();
        surfaceRecorder.resume();
    }

    public void destory() {
        AnFileLog.e("codec", "RecordController destory");
        recordStop();
    }

    public SurfaceTexture enableExternal(Rect rect, boolean forceFullScreen) {
        return surfaceRecorder.enableExternal(rect, forceFullScreen);
    }

    public void disableExternal() {
        surfaceRecorder.disableExternal();
    }

    public SurfaceTexture showOverlaySurfaceTexture() {
        return surfaceRecorder.showOverlaySurfaceTexture();
    }

    public void hideOverlaySurfaceTexture() {
        surfaceRecorder.hideOverlaySurfaceTexture();
    }

    public SurfaceTexture getVideoSurfaceTexture() {
        return surfaceRecorder.getVideoSurfaceTexture();
    }

    public void record() {
        surfaceRecorder.record();
    }
}
