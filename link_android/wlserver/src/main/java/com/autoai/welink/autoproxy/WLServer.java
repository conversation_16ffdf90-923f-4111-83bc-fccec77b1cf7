package com.autoai.welink.autoproxy;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.view.MotionEvent;

import com.autoai.welink.autoproxy.codec.RecordController;
import com.autoai.welink.autoproxy.protocol.AnFileLog;
import com.autoai.welink.autoproxy.server.BuildConfig;
import com.autoai.welink.autoproxy.server.code.Client;
import com.autoai.welink.autoproxy.statemanager.StateManager;

public final class WLServer {
    /***
     * 应用能力
     */
    public static final int WL_CAP_HARDWARE        = 1 << 0;
    public static final int WL_CAP_DISPLAY         = 1 << 1;
    public static final int WL_CAP_SOUND           = 1 << 2;
    public static final int WL_CAP_TBTINFO         = 1 << 3;
    public static final int WL_CAP_MUSIC           = 1 << 4;
    public static final int WL_CAP_MICROPHONE      = 1 << 5;
    public static final int WL_CAP_BLUETOOTHPHONE  = 1 << 6;

    /***
     * 获取功能库版本号
     *
     * @return 返回功能库版本号字符串
     */
    public static String getVersion() {
        return BuildConfig.VERSION_NAME + "/" + BuildConfig.BUILD_TYPE;
    }

    public static boolean checkDisplay(int width, int height, int densityDpi) {
        RecordController recordController = new RecordController();
        boolean bDisplayValid = recordController.checkDisplay(width, height, densityDpi);
        recordController.destory();
        return bDisplayValid;
    }

    /**
     * WLServer 构造函数
     *
     * @param context Application Context
     * @param wlConfiguration 互联配置信息
     * @param listener 状态回调
     */
    public WLServer(Context context, WLConfiguration wlConfiguration, AppConnectListener listener) {
        AnFileLog.init(context);

        wlConfiguration.setServerCodec(true);//强制在server端编码
        stateMgr = new StateManager(context, wlConfiguration, listener);
        Client.stateManager = stateMgr;
    }

    /**
     * 释放 WLServer 资源
     */
    public void release() {
        Client.stateManager = null;
        stateMgr.release();
    }

    StateManager stateMgr;

    /**
     * 分配连接字符串, 格式如：welink://:47529/connect?ver=1&check=vxcirb
     *
     * @param packageName 应用包名
     * @param cap 连接字符串对应的能力
     * @return 连接字符串，连接字符串是App连接WeLink的凭证，也是WLServer标识App的唯一ID
     */
    public String assign(String packageName, int cap) {
        return stateMgr.wlAutoServer.getConnectStr(packageName, cap, "127.0.0.1");
    }

    /**
     * 撤销连接字符串，并断开对应的App连接
     *
     * @param connectStr 连接字符串
     */
    public void revoke(String connectStr) {
        stateMgr.revoke(connectStr);
    }

    /**
     * 开始投屏
     */
    public void start() {
        stateMgr.start();
    }

    /**
     * 停止投屏
     */
    public void stop() {
        stateMgr.stop();
    }

    /**
     * 发送触控事件，触控消息将发送到正在投屏的app
     */
    public boolean touch(MotionEvent motionEvent) {
        if (motionEvent.getPointerCount()<2) {
            return stateMgr.sendTouch(stateMgr.getMirrorScreenApp(), motionEvent);
        }
        return stateMgr.sendTouch2(stateMgr.getMirrorScreenApp(), motionEvent);
    }

    /**
     * 发送命令
     *
     * @param connectStr 连接字符串
     * @param command 命令字符串，具体格式见文档《车机命令》
     */
    public void command(String connectStr, String command) {
        stateMgr.sendCommand(connectStr, command);
    }

    /**
     * 设置已连接的App投屏
     *
     * @param connectStr 对应App的连接字符串，null代表没有App投屏
     * @param x,y Connector的显示左上角位置
     */
    public void mirror(String connectStr, int x, int y) {
        stateMgr.setMirrorScreenApp(connectStr, x, y);
    }

//    /**
//     * 启动App
//     * @param packagename App包名
//     */
//    public void startApp(String packagename) {
//    }

    /**
     * 允许external层
     *
     * @param rect external层显示范围
     * @param forceFullScreen  区域内的内容强制全屏显示(强制缩放到全屏显示会使得rect中的偏移位置不再起作用,只有宽高起作用,非等比例缩放)
     * @return 代表external的SurfaceTexture
     */
    public SurfaceTexture enableExternal(Rect rect,boolean forceFullScreen) {
        return stateMgr.enableExternal(rect,forceFullScreen);
    }

    /**
     * 禁用external层
     */
    public void disableExternal() {
        stateMgr.disableExternal();
    }

    /**
     * 显示overlay层，该层可以悬浮显示在第三方App投屏界面之上
     *
     * @return 代表overlay的SurfaceTexture
     */
    public SurfaceTexture showOverlaySurfaceTexture() {
        return stateMgr.showOverlaySurfaceTexture();
    }

    /**
     * 隐藏overlay层
     */
    public void hideOverlaySurfaceTexture() {
        stateMgr.hideOverlaySurfaceTexture();
    }

    /**
     * 获取当前激活音乐App对应的连接字符串
     *
     * 能够被激活的音乐App有三类:
     * 1.手机前台的音乐App
     * 2.投屏状态的音乐App
     * 3.WeLink设定激活音乐App
     *
     * @return 连接字符串，如果返回null，说明目前没有音乐App被激活
     */
    public String getActivedMusic() {
        return stateMgr.getActivedMusicApp();
    }

    /**
     * 设定激活音乐App
     *
     * @param connectStr 连接字符串
     */
    public void activateMusic(String connectStr) {
        stateMgr.activateMusic(connectStr);
    }

    /**
     * 发送声音开始播放状态
     *
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     */
    public void sendSoundBegin(String connectStr, int soundID) {
        stateMgr.sendSoundBegin(connectStr, soundID);
    }

    /**
     * 发送声音播放完毕状态
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     */
    public void sendSoundComplete(String connectStr, int soundID) {
        stateMgr.sendSoundComplete(connectStr, soundID);
    }

    /**
     * 播放即将完成，发送下一条数据
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     */
    public void sendSoundPrepare(String connectStr, int soundID) {
        stateMgr.sendSoundPrepare(connectStr, soundID);
    }

    /**
     * 发送声音打断状态
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     * @param playTime 打断前已播放的时长，单位: 毫秒
     * @param totalTime 声音的总时长，单位: 毫秒
     */
    public void sendSoundInterrupt(String connectStr, int soundID, long playTime, long totalTime) {
        stateMgr.sendSoundInterrupt(connectStr, soundID, playTime, totalTime);
    }

    /**
     * 发送声音拒绝状态
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     * @param waitingTime 重复发送前需要等待的时长，单位: 毫秒
     */
    public void sendSoundReject(String connectStr, int soundID, long waitingTime) {
        stateMgr.sendSoundReject(connectStr, soundID, waitingTime);
    }

    /**
     * 发送语音指令
     *
     * @param connectStr 连接字符串
     * @param pcm 声音数据
     * @param rate 采样率
     * @param bit 采样精度
     * @param channel 声道数
     */
    public void voice(String connectStr, byte[] pcm, int rate, int bit, int channel) {
        if (null == pcm) {
            return;
        }

        stateMgr.sendVoice(connectStr, pcm, rate, bit, channel);
    }

    public static void enableLogCat(boolean enable) {
        AnFileLog.enableLogCat(enable);
    }

    public static void enableLogFile(boolean enable) {
        AnFileLog.enableLogFile(enable);
    }
}
