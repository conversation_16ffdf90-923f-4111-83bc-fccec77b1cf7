package com.autoai.welink.autoproxy.codec;

import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.opengl.EGL14;
import android.opengl.EGLConfig;
import android.opengl.EGLContext;
import android.opengl.EGLDisplay;
import android.opengl.EGLExt;
import android.opengl.EGLSurface;
import android.view.Surface;

import com.autoai.welink.autoproxy.protocol.AnFileLog;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;

public class CodecInputSurface implements SurfaceTexture.OnFrameAvailableListener {
    private static final int EGL_RECORDABLE_ANDROID = 0x3142;

    private EGLDisplay mEGLDisplay = EGL14.EGL_NO_DISPLAY;
    private EGLContext mEGLContext = EGL14.EGL_NO_CONTEXT;
    private EGLSurface mEGLSurface = EGL14.EGL_NO_SURFACE;
    private SurfaceTexture stConnector;
    private SurfaceTexture stExternal;
    private SurfaceTexture stOverlay;
    private Object clearConnectorLock = new Object();
    private Object clearExternalLock = new Object();
    private Object clearOverlayLock = new Object();
    private Surface mSurface;
    private final Object mConnectorFrameSyncObject = new Object();
    private final Object mExternalFrameSyncObject = new Object();
    private final Object mOverlayFrameSyncObject = new Object();
    private int mConnectorFrameAvailable = 0;
    private int mExternalFrameAvailable = 0;
    private int mOverlayFrameAvailable = 0;
    private TextureRenderer mTextureRender;
    private boolean isShowOverlay = false;
    private boolean isShowExternal = false;

    private long debugTimingFrame = 0;

    /**
     * Creates a CodecInputSurface from a Surface.
     */
    public CodecInputSurface(/*Surface surface*/) {
//        if (surface == null) {
//            throw new NullPointerException();
//        }
//        mSurface = surface;
        eglSetup();

    }

    public void createRender() {
        mTextureRender = new TextureRenderer();
        mTextureRender.surfaceCreated();

        stConnector = null;
        stExternal = null;
        stOverlay = null;
    }

    /**
     * Prepares EGL.  We want a GLES 2.0 context and a surface that supports recording.
     */
    private void eglSetup() {
        mEGLDisplay = EGL14.eglGetDisplay(EGL14.EGL_DEFAULT_DISPLAY);
        if (mEGLDisplay == EGL14.EGL_NO_DISPLAY) {
            throw new RuntimeException("unable to get EGL14 display");
        }
        int[] version = new int[2];
        if (!EGL14.eglInitialize(mEGLDisplay, version, 0, version, 1)) {
            throw new RuntimeException("unable to initialize EGL14");
        }

        // Configure EGL for recording and OpenGL ES 2.0.
        int[] attribList = {
                EGL14.EGL_RED_SIZE, 8,
                EGL14.EGL_GREEN_SIZE, 8,
                EGL14.EGL_BLUE_SIZE, 8,
                EGL14.EGL_ALPHA_SIZE, 8,
                EGL14.EGL_RENDERABLE_TYPE, EGL14.EGL_OPENGL_ES2_BIT,
                EGL_RECORDABLE_ANDROID, 1,
                EGL14.EGL_NONE
        };
        int[] numConfigs = new int[1];
        EGL14.eglChooseConfig(mEGLDisplay, attribList, 0, configs, 0, configs.length,
                numConfigs, 0);
        checkEglError("eglCreateContext RGB888+recordable ES2");

        // Configure context for OpenGL ES 2.0.
        int[] attrib_list = {
                EGL14.EGL_CONTEXT_CLIENT_VERSION, 2,
                EGL14.EGL_NONE
        };
        mEGLContext = EGL14.eglCreateContext(mEGLDisplay, configs[0], EGL14.EGL_NO_CONTEXT,
                attrib_list, 0);
        checkEglError("eglCreateContext");

        // Create a window surface, and attach it to the Surface we received.
//        mEGLSurface = EGL14.eglCreateWindowSurface(mEGLDisplay, configs[0], mSurface, surfaceAttribs, 0);
        checkEglError("eglCreateWindowSurface");
    }
    EGLConfig[] configs = new EGLConfig[1];
    int[] surfaceAttribs = {
            EGL14.EGL_NONE
    };
    public void createEGLSurface(Surface surface){
        mSurface = surface;
        mEGLSurface = EGL14.eglCreateWindowSurface(mEGLDisplay, configs[0], mSurface, surfaceAttribs, 0);
    }

    /**
     * Makes our EGL context and surface current.
     */
    public void makeCurrent() {
        EGL14.eglMakeCurrent(mEGLDisplay, mEGLSurface, mEGLSurface, mEGLContext);
        checkEglError("eglMakeCurrent");
    }

    private int width;
    private int height;
    private Rect connectorRect;
    private Rect externalRect;

    public void setConnectorSurfaceTexture(int width, int height, Rect connectorRect) {
        this.width = width;
        this.height = height;
        this.connectorRect = connectorRect;
    }

    public void updateConnectorLeftTop(int x, int y) {
        connectorRect.left = x;
        connectorRect.top = y;
    }

    public SurfaceTexture getConnectorSurfaceTexture() {
        synchronized (clearConnectorLock) {
            if (stConnector != null) {
                stConnector.release();
            }

            synchronized (mConnectorFrameSyncObject) {
                mConnectorFrameAvailable = 0;
            }

            int texID = mTextureRender.getConnectorTextureId();
            stConnector = new SurfaceTexture(texID, false);
            stConnector.setOnFrameAvailableListener(this);
            stConnector.setDefaultBufferSize(connectorRect.width(), connectorRect.height());
        }

        return stConnector;
    }

    public SurfaceTexture enableExternal(Rect rect,boolean forceFullScreen) {
        disableExternal();
        bForceFullScreen=forceFullScreen;//每次enableExternal后都会更新一次状态

        int texID = mTextureRender.getExternalTextureId();
        externalRect = rect;
        stExternal = new SurfaceTexture(texID, false);
        stExternal.setOnFrameAvailableListener(this);
        stExternal.setDefaultBufferSize(externalRect.width(), externalRect.height());

//        Surface surface = new Surface(stExternal);
//        Canvas canvas = surface.lockCanvas(null);
//        canvas.drawARGB(0, 0, 0, 0);
//        surface.unlockCanvasAndPost(canvas);
//        surface.release();

        isShowExternal = true;

        return stExternal;
    }

    public void disableExternal() {
        isShowExternal = false;

        synchronized (clearExternalLock) {
            if (stExternal != null) {
                // Android Q之前，被进程间共享的SurfaceTexture如果不释放，就无法再分配Surface
                stExternal.release();
                stExternal = null;

                synchronized (mExternalFrameSyncObject) {
                    mExternalFrameAvailable = 0;
                }
            }
        }
    }

    public SurfaceTexture showOverlaySurfaceTexture() {
        hideOverlaySurfaceTexture();

        int texID = mTextureRender.getOverlayTextureId();
        stOverlay = new SurfaceTexture(texID, false);
        stOverlay.setOnFrameAvailableListener(this);
        stOverlay.setDefaultBufferSize(width, height);

//        Surface surface = new Surface(stOverlay);
//        Canvas canvas = surface.lockCanvas(null);
//        canvas.drawARGB(0, 0, 0, 0);
//        surface.unlockCanvasAndPost(canvas);
//        surface.release();

        isShowOverlay = true;

        return stOverlay;
    }

    public void hideOverlaySurfaceTexture() {
        isShowOverlay = false;

        synchronized (clearOverlayLock) {
            if (stOverlay != null) {
                // Android Q之前，被进程间共享的SurfaceTexture如果不释放，就无法再分配Surface
                stOverlay.release();
                stOverlay = null;

                synchronized (mOverlayFrameSyncObject) {
                    mOverlayFrameAvailable = 0;
                }
            }
        }
    }

    @Override
    public void onFrameAvailable(SurfaceTexture st) {
        if (st == stConnector) {
            AnFileLog.e("wlserver", "onFrameAvailable -- connector");
            synchronized (mConnectorFrameSyncObject) {
                ++mConnectorFrameAvailable;
            }
        } else if (st == stExternal) {
            AnFileLog.e("wlserver", "onFrameAvailable -- external");
            synchronized (mExternalFrameSyncObject) {
                ++mExternalFrameAvailable;
            }
        } else if (st == stOverlay) {
            AnFileLog.e("wlserver", "onFrameAvailable -- overlay");
            synchronized (mOverlayFrameSyncObject) {
                ++mOverlayFrameAvailable;
            }
        }
    }

    public boolean awaitNewImage() {
        boolean bUpdate = false;

        synchronized (clearConnectorLock) {
            if (mConnectorFrameAvailable > 0) {
                do {
                    stConnector.updateTexImage();

                    synchronized (mConnectorFrameSyncObject) {
                        --mConnectorFrameAvailable;
                    }
                }
                while (mConnectorFrameAvailable > 0);

                bUpdate = true;
            }
        }

        synchronized (clearExternalLock) {
            if (mExternalFrameAvailable > 0) {
                AnFileLog.e("wlserver", "clearExternalLock");
                do {
                    AnFileLog.e("wlserver", "updateTexImage");
                    stExternal.updateTexImage();

                    synchronized (mExternalFrameSyncObject) {
                        --mExternalFrameAvailable;
                    }
                }
                while (mExternalFrameAvailable > 0);

                bUpdate = true;
            }
        }

        synchronized (clearOverlayLock) {
            if (mOverlayFrameAvailable > 0) {
                do {
                    stOverlay.updateTexImage();

                    synchronized (mOverlayFrameSyncObject) {
                        --mOverlayFrameAvailable;
                    }
                }
                while (mOverlayFrameAvailable > 0);

                bUpdate = true;
            }
        }

        if (!bUpdate && AnFileLog.isEnable()) {
            if (System.currentTimeMillis() - debugTimingFrame >= 1000) {
                debugTimingFrame = System.currentTimeMillis();
                bUpdate = true;
            }
        }

        return bUpdate;
    }

    private boolean bForceFullScreen = false;
    public void drawImage() {
        if (isShowExternal) {
            if (bForceFullScreen){
                mTextureRender.drawFrame(connectorRect, connectorRect, isShowOverlay, width, height);
            }else {
                mTextureRender.drawFrame(connectorRect, externalRect, isShowOverlay, width, height);
            }
        } else {
            mTextureRender.drawFrame(connectorRect, null, isShowOverlay, width, height);
        }
    }

    /**
     * Discards all resources held by this class, notably the EGL context.  Also releases the
     * Surface that was passed to our constructor.
     */
    public void release() {
        if (stConnector != null) {
            stConnector.release();
            stConnector = null;
        }

        if (stExternal != null) {
            stExternal.release();
            stExternal = null;
        }

        if (stOverlay != null) {
            stOverlay.release();
            stOverlay = null;
        }

        if (mEGLDisplay != EGL14.EGL_NO_DISPLAY) {
            EGL14.eglMakeCurrent(mEGLDisplay, EGL14.EGL_NO_SURFACE, EGL14.EGL_NO_SURFACE,
                    EGL14.EGL_NO_CONTEXT);
            EGL14.eglDestroyContext(mEGLDisplay, mEGLContext);
            mEGLContext = EGL14.EGL_NO_CONTEXT;

            EGL14.eglReleaseThread();
            EGL14.eglTerminate(mEGLDisplay);
            EGL14.eglDestroySurface(mEGLDisplay, mEGLSurface);
            mEGLSurface = EGL14.EGL_NO_SURFACE;

            mEGLDisplay = EGL14.EGL_NO_DISPLAY;

            mSurface = null;
        }

        if (mTextureRender != null) {
            mTextureRender.release();
        }
    }

    /**
     * Calls eglSwapBuffers.  Use this to "publish" the current frame.
     */
    public boolean swapBuffers() {
        if (mEGLSurface==null || mSurface==null) {
            return false;
        }
        boolean result = EGL14.eglSwapBuffers(mEGLDisplay, mEGLSurface);
        checkEglError("eglSwapBuffers");
        return result;
    }

    /**
     * Sends the presentation time stamp to EGL.  Time is expressed in nanoseconds.
     */
    public void setPresentationTime(long nsecs) {
        if (mEGLSurface==null || mSurface==null) {
            return;
        }
        EGLExt.eglPresentationTimeANDROID(mEGLDisplay, mEGLSurface, nsecs);
        checkEglError("eglPresentationTimeANDROID");
    }

    /**
     * Checks for EGL errors.  Throws an exception if one is found.
     */
    private void checkEglError(String msg) {
        int error;
        if ((error = EGL14.eglGetError()) != EGL14.EGL_SUCCESS) {
            throw new RuntimeException(msg + ": EGL error: 0x" + Integer.toHexString(error));
        }
    }
}
