package com.autoai.welink.autoproxy.protocol;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.statemanager.AppState;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by gao<PERSON> on 2019/2/22.
 */

public class MusicProtocol extends ProtocolBase {
    private static final String PROTOCOL_MUSIC_CANCEL_SUCCESS = "cancel_success";
    private static final String PROTOCOL_MUSIC_PLAY_SUCCESS = "play_success";
    private static final String PROTOCOL_MUSIC_READFILE_SUCCESS = "read_file_success";
    private static final String PROTOCOL_MUSIC_REQUEST_SHEET = "request_sheet";
    private static final String PROTOCOL_MUSIC_SEARCH = "search";
    private static final String PROTOCOL_MUSIC_LIST = "music_list";

    private static final String PROTOCOL_MUSIC_START = "start";
    private static final String PROTOCOL_MUSIC_STOP = "stop";
    private static final String PROTOCOL_MUSIC_PAUSE = "pause";
    private static final String PROTOCOL_MUSIC_RESUME = "resume";

    private static final String PROTOCOL_MUSIC_ID3 = "ID3";
    private static final String PROTOCOL_MUSIC_PCM = "PCM";
    private static final String PROTOCOL_MUSIC_ORDER = "order";
    private static final String PROTOCOL_MUSIC_CANCEL = "cancel";
    private static final String PROTOCOL_MUSIC_REGISTER = "register";
    private static final String PROTOCOL_MUSIC_REQUEST_FOCUS = "request_focus";


    public MusicProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    //心跳协议,无需与功能库有关联,只需要收发本协议的数据并处理即可
    //=========================================================
    //每个协议默认都需要接收自己协议的数据包,此处传入的数据包只是协议自身的数据,不会包含底层模块自行增加的各种协议头
    @Override
    public void onReciveData(byte[] data) {
        try {
            JSONObject jsonObject = new JSONObject(new String(data));

            if (null == jsonObject) {
                return;
            }

            //读文件
            byte[] bytes = null;
            String fileName = null;
            if (jsonObject.has("FileName")) {
                fileName = jsonObject.optString("FileName");
                bytes = transmisson.read(fileName);
            }

            String type = jsonObject.optString("Type");
            switch (type) {
                case PROTOCOL_MUSIC_ID3:
                    Bitmap bitmap = null;
                    if (null != bytes) {
                        bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);
                    }

                    appListener.onMusicID3(jsonObject.optString("Source"),
                            jsonObject.optString("Artist"),
                            jsonObject.optString("Title"),
                            jsonObject.optString("Album"),
                            jsonObject.optString("Lyric"),
                            jsonObject.optInt("LyricType"),
                            jsonObject.optInt("Duration"),
                            bitmap);
                    break;
                case PROTOCOL_MUSIC_REGISTER:

                    if (jsonObject.optBoolean("IsCancelRegister")) {
                        appListener.onMusicUnregister(transmisson.connectStr);
                        transmisson.stateController.setState(AppState.AppState_INACTIVEMUSIC);
                    } else {
                        appListener.onMusicRegister(transmisson.connectStr);
                    }
                    break;
                case PROTOCOL_MUSIC_ORDER:
                    appListener.onMusicOrder(jsonObject.optInt("Order"));
                    break;
                case PROTOCOL_MUSIC_PCM:
                    //pcm
                    appListener.onMusicPCM(jsonObject.optLong("Position"),
                            bytes);
                    break;
                case PROTOCOL_MUSIC_CANCEL_SUCCESS:
                    //pcm
                    transmisson.stateController.setState(AppState.AppState_INACTIVEMUSIC);
                    break;
//            else if (type.equals(PROTOCOL_MUSIC_LIST)) {
//                appListener.onMusicSheet(transmisson.connectStr, jsonObject.optString("Content"), jsonObject.optString("Sheet"));
//            }
                case PROTOCOL_MUSIC_REQUEST_FOCUS:
                    //通知musicPlay
                    transmisson.stateController.setState(AppState.AppState_ACTIVEMUSIC);
                    break;
                case PROTOCOL_MUSIC_START:
                    appListener.onMusicStart(
                            jsonObject.optLong("TotalLen"),
                            jsonObject.optInt("Rate"),
                            jsonObject.optInt("Bit"),
                            jsonObject.optInt("Channel"));
                    break;
                case PROTOCOL_MUSIC_STOP:
                    //通知musicPlay停止播放
                    appListener.onMusicStop();
                    break;
                case PROTOCOL_MUSIC_PAUSE:
                    //通知musicPlay暂停播放
                    appListener.onMusicPause();
                    break;
                case PROTOCOL_MUSIC_RESUME:
                    //通知musicPlay恢复播放
                    appListener.onMusicResume();
                    break;
            }

            if (null != fileName) {
                readFileSuccess(fileName);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知新音乐App开始发送PCM
     */
    private void readFileSuccess(String fileName) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_READFILE_SUCCESS);
            obj.put("FileName", fileName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {

    }

    //byte[]转int
    private static int byteArrayToInt(byte[] b) {
        return   b[3] & 0xFF |
            (b[2] & 0xFF) << 8 |
            (b[1] & 0xFF) << 16 |
            (b[0] & 0xFF) << 24;
    }

    /**
     * 通知新音乐App开始发送PCM
     */
    public void playSuccess() {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_PLAY_SUCCESS);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
    }

    /**
     * 通知旧音乐App停止发送PCM
     */
    public void cancelPlayState() {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_CANCEL);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
    }

    /**
     * 请求音乐列表
     */
    public void requestMusicSheet(String id) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_REQUEST_SHEET);
            obj.put("ID", id);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
    }

    /*
    * 搜索
    */
    public void searchMusicSheet(String content) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_SEARCH);
            obj.put("Content", content);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
    }
}
