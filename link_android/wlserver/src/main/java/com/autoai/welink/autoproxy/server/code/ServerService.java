package com.autoai.welink.autoproxy.server.code;

import java.io.FileDescriptor;
import java.lang.reflect.Method;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.os.MemoryFile;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.os.SharedMemory;
import android.view.Surface;

import com.autoai.welink.autoproxy.server.IServerService;

public class ServerService extends Service {
    @Override
    public IBinder onBind(Intent intent) {
        return stub;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
    }

    private final IServerService.Stub stub = new IServerService.Stub() {
        @Override
        public ParcelFileDescriptor getFileDescriptor(String connectStr, String filename) {
            if (Client.stateManager == null || !Client.stateManager.wlAutoServer.hasRandomStr(connectStr)) {
                return null;
            }
            synchronized (Client.memoryListLock) {
                if (Client.memoryList != null) {
                    Client.TimeLimitMemory memory = Client.memoryList.get(filename);
                    if (memory != null) {
                        MemoryFile memoryFile = memory.memoryFile;
                        if (memoryFile == null) {
                            return null;
                        }

                        ParcelFileDescriptor pfd = null;
                        try {
                            @SuppressLint("DiscouragedPrivateApi") Method method = MemoryFile.class.getDeclaredMethod("getFileDescriptor");
                            FileDescriptor fd = (FileDescriptor) method.invoke(memoryFile);

                            pfd = ParcelFileDescriptor.dup(fd);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        return pfd;
                    }
                }

                return null;
            }
        }

        @Override
        public SharedMemory getSharedMemory(String connectStr, String filename) {
            if (Client.stateManager == null || !Client.stateManager.wlAutoServer.hasRandomStr(connectStr)) {
                return null;
            }
            synchronized (Client.memoryListLock) {
                if (Client.memoryList != null) {
                    Client.TimeLimitMemory memory = Client.memoryList.get(filename);
                    if (memory != null) {
                        return memory.sharedMemory;
                    }
                }

                return null;
            }
        }

        @Override
        public int length(String connectStr, String filename) {
            if (Client.stateManager == null || !Client.stateManager.wlAutoServer.hasRandomStr(connectStr)) {
                return 0;
            }
            synchronized (Client.memoryListLock) {
                if (Client.memoryList != null) {
                    Client.TimeLimitMemory memory = Client.memoryList.get(filename);
                    if (memory != null) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                            SharedMemory sharedMemory = memory.sharedMemory;
                            if (sharedMemory == null) {
                                return 0;
                            }
                            return sharedMemory.getSize();
                        } else {
                            MemoryFile memoryFile = memory.memoryFile;
                            if (memoryFile == null) {
                                return 0;
                            }
                            return memoryFile.length();
                        }
                    }
                }

                return 0;
            }
        }

        @Override
        public Surface getSurface(String connectStr) {
            return Client.getSurface(connectStr);
        }

        @Override
        public void sendData(String connectStr,int type, byte[] bytes) throws RemoteException {
            Client.onDataRecv(connectStr,type,bytes);
        }
    };

}
