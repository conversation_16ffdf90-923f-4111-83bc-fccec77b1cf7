package com.autoai.welink.autoproxy.protocol;

import com.autoai.welink.autoproxy.AppConnectListener;

import org.json.JSONException;
import org.json.JSONObject;

//车机命令
public class CommandProtocol extends ProtocolBase {
    public CommandProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    public void sendCommand(String command){
        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_COMMAND, command.getBytes());
    }

    @Override
    public void onReciveData(byte[] data) {
        String command = new String(data);
        try {
            JSONObject jsonObject = new JSONObject(command);
            if (jsonObject.has("Request") &&
                "Disconnect".equals(jsonObject.optString("Request"))) {
                transmisson.stateController.onDisconnected();
                transmisson.disconnect();
                return;
            }
        }
        catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {

    }
}
