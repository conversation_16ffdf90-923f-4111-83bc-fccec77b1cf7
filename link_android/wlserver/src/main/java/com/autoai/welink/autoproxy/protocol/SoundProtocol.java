package com.autoai.welink.autoproxy.protocol;

import com.autoai.welink.autoproxy.AppConnectListener;

import org.json.JSONException;
import org.json.JSONObject;

public class SoundProtocol extends ProtocolBase {
    private static final String SOUND_ID = "SoundID";
    private static final String SOUND_MARK = "Mark";
    private static final String SOUND_RATE = "Rate";
    private static final String SOUND_BIT = "Bit";
    private static final String SOUND_CHANNEL = "Channel";

    private static final String SOUND_BEGIN = "Begin";
    private static final String SOUND_COMPLETE = "Complete";
    private static final String SOUND_PREPARE = "Prepare";
    private static final String SOUND_INTERRUPT = "Interrupt";
    private static final String SOUND_REJECT = "Reject";
    private static final String SOUND_DELETE_FILE = "DeleteFile";

    public SoundProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    @Override
    public void onReciveData(byte[] data) {
        try {
            JSONObject jsonObject = new JSONObject(new String(data));
            if (null == jsonObject) {
                return;
            }

            //读文件
            byte[] bytes = null;
            String fileName = null;
            if (jsonObject.has("FileName")) {
                fileName = jsonObject.optString("FileName");
                bytes = transmisson.read(fileName);
            }

            appListener.onSoundPCM(transmisson.connectStr,
                jsonObject.optInt(SOUND_ID),
                jsonObject.optString(SOUND_MARK),
                bytes,
                jsonObject.optInt(SOUND_RATE),
                jsonObject.optInt(SOUND_BIT),
                jsonObject.optInt(SOUND_CHANNEL));
            if (null != fileName) {
                readFileSuccess(jsonObject.optInt(SOUND_ID), fileName);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知新音乐App开始发送PCM
     */
    private void readFileSuccess(int soundID, String fileName) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type",SOUND_DELETE_FILE);
            obj.put(SOUND_ID, soundID);
            obj.put("FileName", fileName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_SOUND, json.getBytes());
        }
    }

    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {

    }

    //byte[]转int
    private static int byteArrayToInt(byte[] b) {
        return   b[3] & 0xFF |
            (b[2] & 0xFF) << 8 |
            (b[1] & 0xFF) << 16 |
            (b[0] & 0xFF) << 24;
    }

    /**
     * 发送声音开始播放状态
     *
     * @param soundID Sound ID
     */
    public void sendSoundBegin(int soundID) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type",SOUND_BEGIN);
            obj.put(SOUND_ID, soundID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_SOUND, json.getBytes());
        }
    }

    /**
     * 发送声音播放完毕状态
     * @param soundID Sound ID
     */
    public void sendSoundComplete(int soundID) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type",SOUND_COMPLETE);
            obj.put(SOUND_ID, soundID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_SOUND, json.getBytes());
        }
    }

    /**
     * 发送声音播放完毕状态
     * @param soundID Sound ID
     */
    public void sendSoundPrepare(int soundID) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type",SOUND_PREPARE);
            obj.put(SOUND_ID, soundID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_SOUND, json.getBytes());
        }
    }

    /**
     * 发送声音打断状态
     * @param soundID Sound ID
     * @param playTime 打断前已播放的时长，单位: 毫秒
     * @param totalTime 声音的总时长，单位: 毫秒
     */
    public void sendSoundInterrupt(int soundID, long playTime, long totalTime) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type",SOUND_INTERRUPT);
            obj.put(SOUND_ID, soundID);
            obj.put("PlayTime",playTime);
            obj.put("TotalTime",totalTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_SOUND, json.getBytes());
        }
    }

    /**
     * 发送声音拒绝状态
     * @param soundID Sound ID
     * @param waitingTime 重复发送前需要等待的时长，单位: 毫秒
     */
    public void sendSoundReject(int soundID, long waitingTime) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type",SOUND_REJECT);
            obj.put(SOUND_ID, soundID);
            obj.put("WaitingTime",waitingTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_SOUND, json.getBytes());
        }
    }
}
