package com.autoai.welink.autoproxy.server.code;

import com.autoai.welink.auto.client.WLAutoConnectListener;
import com.autoai.welink.autoproxy.protocol.AnFileLog;
import com.autoai.welink.autoproxy.server.WLAutoDataListener;
import com.autoai.welink.autoproxy.server.WLAutoServerLink;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

import static com.autoai.welink.autoproxy.protocol.ProtocolMgr.TRANSIMISSON_TYPE_COMMAND;


/**
 * Created by gao<PERSON> on 2018/12/26.
 */
public class AutoServerLink extends Thread {
    private Client client;
//    private final Object clientLock = new Object();

    private WLAutoDataListener dataListener;
    Socket inSocket;
    AutoServer autoServer;

    int selectVersion;
    String connectStr;
    String ramdomStr;
    String packageName;
    String clientIp;

    /*
    0. 校验成功
    1. WeLink服务无效，有可能是WeLink App没有运行或者没有连接车机
    2. 连接字符串不正确
    3. WLConnector版本太老，无法连接当前的WeLink服务
    4. 重复连接
    5. 连接WeLink服务失败
   */
    static final int CONNECT_CORRECT = WLAutoConnectListener.CONNECT_NOT_ERROR;
    static final int CONNECT_WELINK_APP_ERROR = WLAutoConnectListener.CONNECT_WELINK_APP_ERROR;
    static final int CONNECT_STR_ERROR = WLAutoConnectListener.CONNECT_STR_ERROR;
    static final int CONNECT_APP_VER_ERROR = WLAutoConnectListener.CONNECT_APP_VER_ERROR;
    static final int CONNECT_REPEAT_ERROR = WLAutoConnectListener.CONNECT_REPEAT_ERROR;
    static final int CONNECT_SERVICE_ERROR = WLAutoConnectListener.CONNECT_SERVICE_ERROR;
    static final int CONNECT_RESOLUTION_ERROR = WLAutoConnectListener.CONNECT_RESOLUTION_ERROR;


    static final int MAGIC_NUM = 0x78967896;
    static final int LINK_TYPE = 1000000;
    static final int INT_TYPE_LEN = 4; //int 类型数据长度
    static final int LONG_TYPE_LEN = 8; //long类型数据长度
    static final int HEAD_DATA_LEN = INT_TYPE_LEN + INT_TYPE_LEN + LONG_TYPE_LEN + INT_TYPE_LEN; //数据头组成int（magicNum）+int（type）+ long（发送数据距离上一次发送数据的时间差）+int（数据长度）
    byte[] headData = new byte[HEAD_DATA_LEN];

    ByteArrayOutputStream reciveHeadData = new ByteArrayOutputStream(HEAD_DATA_LEN);
    int reciveHeadDataLen = 0;
    ByteArrayOutputStream actualReciveData = new ByteArrayOutputStream(1024 * 1024 * 4);
    int actualReciveDataLen = 0;
    byte[] reciveDataMagicNumByte = new byte[INT_TYPE_LEN];
    int reciveDataMagicNum = 0;
    byte[] reciveDataTypeByte = new byte[INT_TYPE_LEN];
    int reciveDataType = 0;
    byte[] reciveDataTimeByte = new byte[LONG_TYPE_LEN];
    long reciveDataTime = 0;
    byte[] reciveDataLenByte = new byte[INT_TYPE_LEN];
    int reciveDataLen = 0;

    byte[] serverData = new byte[1024 * 1024 * 4];
    Boolean isHeadData = true;
    boolean isDisconnected = false;

    int socketCap = 0;

    public AutoServerLink (Socket socket, AutoServer server, String ip) {
        inSocket = socket;
        autoServer = server;
        clientIp = ip;
    }

    private void release() {
        AnFileLog.e("wlserver", "release");
        isDisconnected = true;

        if (client != null) {
            client.release();
            client = null;
        }

        if (null != ramdomStr && autoServer.hasRandomStr(ramdomStr)) {
            autoServer.removeConnectStr(ramdomStr);
            autoServer.addUnConnectStr(connectStr, ramdomStr, socketCap, packageName);
        }
    }

    /**
     * 断开连接
     * {"Request":"Disconnect"}
     */
    public void disconnect() {
        AnFileLog.e("wlserver", "disconnect");
        JSONObject obj = new JSONObject();
        try {
            obj.put("Request","Disconnect");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        client.sendData(ramdomStr, TRANSIMISSON_TYPE_COMMAND, obj.toString().getBytes());
        //释放socket資源
        socketClose();
    }

    /**
     * 获取connectStr
     * @return 返回connectStr
     */
    public String getLinkConnectStr() {
        return connectStr;
    }

    /**
     * 获取connectStr
     * @return 返回connectStr
     */
    public long getLinkConnectCap() {
        return socketCap;
    }

    /**
     * 获取MemoryShare
     */
//    public Client getClient() {
//        return client;
//    }

    /**
     * 设置 WLAutoDataListener接收数据监听
     * @param listener WLAutoDataListener监听
     */
    public void setAutoDataListener(WLAutoDataListener listener) {
        dataListener = listener;
    }

    ReentrantLock lock = new ReentrantLock();
    public void sendDataByAIDL(int type, byte[] bytes,byte[] bytes2) {
        AnFileLog.e("aidl_s","sendDataByAIDL connectStr="+connectStr+",type="+type+",bytes.length="+bytes.length+",client="+client);
        byte[] bb=bytes;
        if (bytes2!=null) {
            try {
                ByteArrayOutputStream bos=new ByteArrayOutputStream();
                bos.write(intToByteArray(bytes.length));
                bos.write(bytes);
                bos.write(bytes2);
                bb=bos.toByteArray();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        if (client != null) {
            client.sendData(ramdomStr, type, bb);
        }
    }

    public void resumeMirrorScreen() {
        if (client != null) {
            client.resumeMirrorScreen(ramdomStr);
        }
    }

    public byte[] read(String filename) {
        if (client != null) {
            return client.read(ramdomStr, filename);
        }

        return null;
    }

    public boolean create(String filename, byte[] buffer) {
        if (client != null) {
            return Client.create(filename, buffer);
        }

        return false;
    }

    public void remove(String filename) {
        if (client != null) {
            Client.remove(filename);
        }
    }

    public void pauseMirrorScreen() {
        if (client != null) {
            client.pauseMirrorScreen(ramdomStr);
        }
    }
        /**
		 * 发送数据
		 * @param type 发送的数据类型
		 * @param bytes 发送的二进制数据
		 */
    private void sendData(int type, byte[] bytes) {
        lock.lock();
        if (inSocket != null) {
            try {
                inSocket.getOutputStream().write(getHead(type, bytes.length));
                inSocket.getOutputStream().write(bytes);
                inSocket.getOutputStream().flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        lock.unlock();
    }

    /**
     * 发送的数据添加数据头
     * int（magicNum）+int（type）+ long（发送数据距离上一次发送数据的时间差）+int（数据长度）
     * @param type linkType以上是link内部数据，以下是外部数据
     * @param bytes 发送的数据
     * @return
     */
    long startTime1 = System.currentTimeMillis();
    private  byte[] getHead(int type, int bytesLen) {
        System.arraycopy(intToByteArray(MAGIC_NUM),0,headData,0,INT_TYPE_LEN);
        System.arraycopy(intToByteArray(type),0,headData,INT_TYPE_LEN,INT_TYPE_LEN);

        long coastTime = System.currentTimeMillis() - startTime1;
        startTime1 = System.currentTimeMillis();
        byte[] a = longToBytes(coastTime);
        System.arraycopy(a,0,headData,INT_TYPE_LEN+INT_TYPE_LEN,LONG_TYPE_LEN);
       AnFileLog.e("AAAASDF","*** server senddata type = "+type+", Len = "+bytesLen);

        byte[] length = intToByteArray(bytesLen);
        System.arraycopy(length,0,headData,INT_TYPE_LEN+INT_TYPE_LEN+LONG_TYPE_LEN,INT_TYPE_LEN);

        return headData;
    }

    //byte 数组与 long 的相互转换
    ByteBuffer sendBuffer = ByteBuffer.allocate(LONG_TYPE_LEN);
    public byte[] longToBytes(long x) {
        sendBuffer.clear();
        sendBuffer.putLong(0, x);
        return sendBuffer.array();
    }

    ByteBuffer receiveBuffer = ByteBuffer.allocate(LONG_TYPE_LEN);
    public long bytesToLong(byte[] bytes) {
        receiveBuffer.clear();
        receiveBuffer.put(bytes, 0, bytes.length);
        receiveBuffer.flip();//need flip
        return receiveBuffer.getLong();
    }

    /**
     * int转byte[]
     */
    private static byte[] intToByteArray(int a) {
        return new byte[] {
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }

    /**
     * 校验移除socket连接的校验字符串
     * {"ConnectStr":"asdbnh","RandomStr":"asdbnh","AppType":"navi"}
     * @param str 连接的校验字符串
     *            格式：type+name+随机字符串
     * @return 校验是否成功码
     */
    private int checkConnectStr (String str) {
        List<JSONObject> connectCheckList = autoServer.getConnectStrList();
        Iterator<JSONObject> it = connectCheckList.iterator();
        while(it.hasNext()){
            JSONObject obj = (JSONObject)it.next();
            String ramdomString = obj.optString("RandomStr");
            if(str.equals(ramdomString)){
                boolean firstConnect = obj.optBoolean("FirstConnect");
                if (!firstConnect) {
                    AnFileLog.e("AAAASDF","*** server checkConnectStr CONNECT_REPEAT_ERROR ");
                    return CONNECT_REPEAT_ERROR;
                }

                ramdomStr = str;
                connectStr = obj.optString("ConnectStr");
                packageName = obj.optString("PackageName");
                socketCap = obj.optInt("CAP");
                autoServer.removeConnectStr(ramdomStr);
                autoServer.addConnectedStr(connectStr, ramdomStr, socketCap, packageName);

                AnFileLog.e("AAAASDF","*** server checkConnectStr CONNECT_CORRECT ");
                return  CONNECT_CORRECT;
            }
        }

        AnFileLog.e("AAAASDF","*** server checkConnectStr CONNECT_STR_ERROR ");
        return CONNECT_STR_ERROR;
    }

    @Override
    public void run() {
        super.run();
        int len = -1;

        try {
            while (null != inSocket && (len = inSocket.getInputStream().read(serverData)) != -1) {
                AnFileLog.e("wlserver", "socket read: " + len);

                //头数据
                if (isHeadData) {
                    handleClientHeadData(len,0);
                }
                else {
                    handleClientData(len,0);
                }

                len = -1;
            }
        } catch (IOException e1) {
            AnFileLog.e("wlserver", e1.getMessage());
            e1.printStackTrace();
        }

        AnFileLog.e("wlserver", "AutoServerLink exit 1: " + inSocket + ", " + len);
        lock.lock();
        if (inSocket != null) {
            try {
                inSocket.close();
                inSocket = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        lock.unlock();
        AnFileLog.e("wlserver", "AutoServerLink exit 2");
    }

    private void socketClose () {
        AnFileLog.e("wlserver", "socketClose");
        lock.lock();
        if (inSocket != null) {
            try {
                inSocket.close();
                inSocket = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        lock.unlock();

        release();
    }

    long startTime2 = System.currentTimeMillis();
    private void handleClientHeadData(int totalLen, int offside) {
        int remainHeadDataLen = HEAD_DATA_LEN - reciveHeadDataLen;
        int remainDatalen = totalLen - remainHeadDataLen;
        if (remainDatalen < 0) {
            remainHeadDataLen = totalLen;
        }

        reciveHeadData.write(serverData, offside, remainHeadDataLen);
        reciveHeadDataLen += remainHeadDataLen;

        if (HEAD_DATA_LEN == reciveHeadDataLen) {

            System.arraycopy(reciveHeadData.toByteArray(), 0, reciveDataMagicNumByte, 0, INT_TYPE_LEN);
            System.arraycopy(reciveHeadData.toByteArray(), INT_TYPE_LEN, reciveDataTypeByte, 0, INT_TYPE_LEN);
            System.arraycopy(reciveHeadData.toByteArray(), INT_TYPE_LEN + INT_TYPE_LEN, reciveDataTimeByte, 0, LONG_TYPE_LEN);
            System.arraycopy(reciveHeadData.toByteArray(), INT_TYPE_LEN + INT_TYPE_LEN + LONG_TYPE_LEN, reciveDataLenByte, 0, INT_TYPE_LEN);

            reciveDataMagicNum = byteArrayToInt(reciveDataMagicNumByte);
            if (MAGIC_NUM != reciveDataMagicNum) {
                return;
            }

            reciveDataType = byteArrayToInt(reciveDataTypeByte);
            reciveDataTime = bytesToLong(reciveDataTimeByte);
            reciveDataLen = byteArrayToInt(reciveDataLenByte);

            long coastTime = System.currentTimeMillis() - startTime2;
            startTime2 = System.currentTimeMillis();

            //清空
            reciveHeadData.reset();
            reciveHeadDataLen = 0;
            resetArray(reciveDataMagicNumByte);
            reciveDataMagicNum = 0;
            resetArray(reciveDataTypeByte);
            resetArray(reciveDataTimeByte);
            reciveDataTime = 0;
            resetArray(reciveDataLenByte);
            isHeadData = false;
            actualReciveDataLen = 0;

            if (remainDatalen > 0) {
                handleClientData(remainDatalen, offside + remainHeadDataLen);
            }
        }
    }

    private void handleClientData(int totalLen, int offside) {
        int remainDataLen = reciveDataLen - actualReciveDataLen;
        int remainHeadDataLen = totalLen - remainDataLen;
        if (remainHeadDataLen < 0) {
            remainDataLen = totalLen;
        }
        actualReciveData.write(serverData, offside, remainDataLen);
        actualReciveDataLen += remainDataLen;

        if (reciveDataLen == actualReciveDataLen) {
            handleSocketInPutStream(actualReciveData.toByteArray());

            //清空
            actualReciveData.reset();
            actualReciveDataLen = 0;
            reciveDataLen = 0;
            reciveDataType = 0;
            isHeadData = true;

            if (remainHeadDataLen > 0) {
                handleClientHeadData(remainHeadDataLen, offside + remainDataLen);
            }
        }
    }

    private  void handleSocketInPutStream(byte[] clientSentence) {
        AnFileLog.e("AAAASDF","*** server handleSocketInPutStream  type = "+reciveDataType+", dataLen = " + clientSentence.length);

        if (reciveDataType >= LINK_TYPE) {
            try {
                JSONObject jsonObject = new JSONObject(new String(clientSentence));
                if (null == jsonObject) {
                    return;
                }
                AnFileLog.e("AAAASDF","*** server handleSocketInPutStream  " + jsonObject);

                if (jsonObject.has("Request")
                    && "Connect".equals(jsonObject.optString("Request"))) {
                    checkClientSocketIsValid(jsonObject);
                }

                if (jsonObject.has("Connect") &&
                    handleConnectResult(jsonObject)) {
                    ///确认Connector接受分辨率后，再向Server发送连接成功消息
                    autoServer.getLinkListener().onConnected(new WLAutoServerLink(this), socketCap);
                    client.setDataListener(dataListener);
                }

                if (jsonObject.has("Request")
                    && "Disconnect".equals(jsonObject.optString("Request"))) {

                    if (!isDisconnected) {
                        if (null != dataListener){
                            dataListener.onDisconnected();
                        }
                        socketClose();
                    }
                }


            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        else {
            //处理其他数据
            if (null != dataListener) {
                dataListener.onReciveData(reciveDataType, clientSentence);
            }
        }
    }

    //将byte数组置空
    private static byte[] resetArray(byte[] a){
        byte[] b2 = new byte[a.length];
        for(int i=0;i<a.length;i++) {
            a[i] = b2[i];
        }
        return a;
    }

    //byte[]转int
    private static int byteArrayToInt(byte[] b) {
        return   b[3] & 0xFF |
                (b[2] & 0xFF) << 8 |
                (b[1] & 0xFF) << 16 |
                (b[0] & 0xFF) << 24;
    }

    int touchEventPort = 0;
    Socket touchEventSocket;

    /**
     * 连接是否成功
     * {"Connect”:”Success",”TouchEventPort”:12345,”VideoPort”:12345,”AudioPort”:12345,”VoiceDataPort”:12345}
     * {"Connect”:”Fail"}
     * @param jsonObject
     */
    private boolean handleConnectResult(JSONObject jsonObject) {
        AnFileLog.e("wlserver", "handleConnectResult");
        String connectResult = jsonObject.optString("Connect");
        if ("Success".equals(connectResult)) {
            touchEventPort = jsonObject.optInt("TouchEventPort");

            try {
                touchEventSocket = new Socket(clientIp, touchEventPort);

            } catch (IOException e) {
                e.printStackTrace();
            }

            if (autoServer.isServerCodec) {
                try {
                    client = new Client(autoServer.ctx, packageName);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (client == null) {
                    if (!isDisconnected) {
                        ResponseConnectFail(CONNECT_SERVICE_ERROR);

                        if (autoServer.getLinkListener() != null) {
                            autoServer.getLinkListener().onError(getLinkConnectStr(), CONNECT_SERVICE_ERROR);
                        }
                        socketClose();

                        return false;
                    }
                }
            }
        }
        else if ("Fail".equals(connectResult)){

            //连接失败
            if (!isDisconnected) {
                if (autoServer.getLinkListener() != null) {
                    autoServer.getLinkListener().onError(getLinkConnectStr(), CONNECT_RESOLUTION_ERROR);
                }
                socketClose();

                return false;
            }
        }

        return true;
    }

    /**
     * 校验socket是否正确
     * @param jsonObject 格式：
     *  {"Request":"connect","MaxVersion":5,"MinVersion":1,”RandomStr”:”asdfghn”}
     */
    private void checkClientSocketIsValid(JSONObject jsonObject) {
        int maxVersion = jsonObject.optInt("MaxVersion");
        int minVersion = jsonObject.optInt("MinVersion");
        String randomStr = jsonObject.optString("RandomStr");

        //选择版本号
        selectVersion = getSelectVersion(maxVersion,minVersion);
        if (-1 == selectVersion) {
            ResponseConnectFail(CONNECT_APP_VER_ERROR);
            return;
        }

        //校验字符串
        int checkStrCode = 0;
        if (null != randomStr) {
            checkStrCode = checkConnectStr(randomStr);
        }

        if (CONNECT_CORRECT == checkStrCode ) {
            responseConnectInfo(selectVersion);
        }
        else {
            ResponseConnectFail(checkStrCode);
        }
    }

    private int getSelectVersion (int maxVer, int minVer) {
        int serverMaxVersion = autoServer.getMaxVersion();
        int serverMinVersion = autoServer.getMinVersion();

        for(int i = maxVer; i >= minVer; i--){
            for(int j = serverMaxVersion; j >= serverMinVersion; j--) {
                if (i == j) {
                    return i;
                }
            }
        }

        return -1;
    }

    /**
     * 确定采用的协议版本号、车机分辨率
     * {“Response":"Connect","ServerVersion":5,"HUScreenWidth":100,"HUScreenHeight":100,"Dpi":180,"Fps":30,"Pcm":2,"IsSupportWiFiChannel":false, "IsServerCodec":false, "PackageName":"com.autoai.welink.launcher"}
     * @param version 确认连接的版本号
     */
    private void responseConnectInfo(int version) {

        JSONObject obj = new JSONObject();
        try {
            obj.put("Response","Connect");
            obj.put("ServerVersion",version);
            obj.put("HUScreenWidth",autoServer.getHUScreenWidth());
            obj.put("HUScreenHeight",autoServer.getHUScreenHeight());
            obj.put("Dpi",autoServer.getDpi());
            obj.put("Fps",autoServer.getFps());
            obj.put("Pcm", autoServer.getPcm());
            obj.put("PcmCache", autoServer.getPcmCache());
            obj.put("IsSupportWiFiChannel",autoServer.getIsSupportWiFiChannel());
            obj.put("IsServerCodec",autoServer.getIsServerCodec());
            obj.put("PackageName", autoServer.getPackageName());

            obj.put("VehicleType", autoServer.getVehicleType());
            obj.put("VehicleID", autoServer.getVehicleID());
            obj.put("UserID", autoServer.getUserID());
            obj.put("CAP", socketCap);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        sendData(LINK_TYPE, obj.toString().getBytes());
    }

    /**
     * 应用类型不对，不同意建立连接，连接失败
     * {“Response":"CONNECT","ErrorCode”:101}
     * @param errorCode 连接失败code
     */
    private void ResponseConnectFail(int errorCode) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Response","Connect");
            obj.put("ErrorCode",errorCode);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        sendData(LINK_TYPE, obj.toString().getBytes());
    }
}
