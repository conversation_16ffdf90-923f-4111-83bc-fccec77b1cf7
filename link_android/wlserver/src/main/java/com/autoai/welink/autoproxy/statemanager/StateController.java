package com.autoai.welink.autoproxy.statemanager;

import android.view.MotionEvent;

import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.protocol.Transmisson;
import com.autoai.welink.autoproxy.server.WLAutoServerLink;

import static com.autoai.welink.autoproxy.statemanager.AppState.AppState_ACTIVEMUSIC;

public class StateController {
    public Transmisson transmisson;                                  //APP传输对象
    private WLAutoServerLink wlAutoServerLink;
    private AppState currentState;                            //APP当前状态
    private StateManager stateMgr;
    private long cap;
    private AppConnectListener appConnectListener;
    private String connectStr;
    private boolean isSupportMusicSheet = false;

    public StateController(long cap, String connectStr, StateManager stateMgr, AppConnectListener listener, WLAutoServerLink wlAutoServerLink) {
        this.transmisson = new Transmisson(connectStr, this, listener, wlAutoServerLink);
        this.appConnectListener = listener;
        this.connectStr = wlAutoServerLink.getLinkConnectStr();
        this.stateMgr = stateMgr;
        this.cap = cap;
        this.wlAutoServerLink = wlAutoServerLink;
        currentState = new AppState();
        currentState.setValue(AppState.AppState_CONNECT);
    }

    public void setState(int state) {
        if (!currentState.hasState(state) || state == AppState_ACTIVEMUSIC) { //请求音乐焦点都有响应
            currentState.setValue(state);
            stateMgr.stateChange(StateController.this, state);
        }
    }

    public int getCurrentState() {
        return currentState.getValue();
    }

    public void onDisconnected() {
        this.setState(AppState.AppState_DISCONNECT);
        appConnectListener.onDisconnected(connectStr);
    }

    public void onError(int type) {
        stateMgr.stateChange(StateController.this, AppState.AppState_NULL);
        appConnectListener.onError(connectStr, type);
    }


    public void sendForeground() {
        transmisson.getProtocolMgr().getBackgroundStatusProtocol().onForeground();
    }

    public void sendBackground() {
        transmisson.getProtocolMgr().getBackgroundStatusProtocol().onBackground();
    }

    public void sendTouch(MotionEvent motionEvent) {
        transmisson.getProtocolMgr().getTouchEventProtocol().onTouchEvent(motionEvent);
    }

    public void sendPauseRecord(boolean isServerCodec) {
        wlAutoServerLink.pauseMirrorScreen();
//        transmisson.getProtocolMgr().getVideoRecorderControlProtocol().pauseRecord();
    }

    public void sendResumeRecord(boolean isServerCodec) {
        wlAutoServerLink.resumeMirrorScreen();

//        transmisson.getProtocolMgr().getVideoRecorderControlProtocol().resumeRecord();
    }

    public void sendCommand(String command) {
        transmisson.getProtocolMgr().getCommandProtocol().sendCommand(command);
    }

    public void sendMusicCancel() {
        transmisson.getProtocolMgr().getMusicProtocol().cancelPlayState();
    }

    public void sendMusicPlay() {
        appConnectListener.onMusicFocus(connectStr);
        transmisson.getProtocolMgr().getMusicProtocol().playSuccess();
    }

    public void requestMusicSheet(String id) {
        transmisson.getProtocolMgr().getMusicProtocol().requestMusicSheet(id);
    }

    public void searchMusicSheet(String content) {
        transmisson.getProtocolMgr().getMusicProtocol().searchMusicSheet(content);
    }

    public String getConnectStr(){return connectStr;}

    public void sendVoice(byte[] pcm, int rate, int bit, int channel) {
        transmisson.getProtocolMgr().getMicrophoneProtocol().sendVoice(pcm, rate, bit, channel);
    }

    public void setIsSupportMusicSheet(Boolean isSupportMusicSheet) {
        this.isSupportMusicSheet = isSupportMusicSheet;
    }

    public boolean getIsSupportMusicSheet() {
        return  this.isSupportMusicSheet;
    }


    public void sendSoundBegin(int soundID) {
        transmisson.getProtocolMgr().getSoundProtocol().sendSoundBegin(soundID);
    }

    public void sendSoundComplete(int soundID) {
        transmisson.getProtocolMgr().getSoundProtocol().sendSoundComplete(soundID);
    }

    public void sendSoundPrepare(int soundID) {
        transmisson.getProtocolMgr().getSoundProtocol().sendSoundPrepare(soundID);
    }

    public void sendSoundInterrupt(int soundID, long playTime, long totalTime) {
        transmisson.getProtocolMgr().getSoundProtocol().sendSoundInterrupt(soundID, playTime, totalTime);
    }

    public void sendSoundReject(int soundID, long waitingTime) {
        transmisson.getProtocolMgr().getSoundProtocol().sendSoundReject(soundID, waitingTime);
    }
}
