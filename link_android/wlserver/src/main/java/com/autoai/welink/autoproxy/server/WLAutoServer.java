package com.autoai.welink.autoproxy.server;

import android.content.Context;

import com.autoai.welink.autoproxy.WLConfiguration;
import com.autoai.welink.autoproxy.server.code.AutoServer;

/**
 * Created by g<PERSON><PERSON> on 2018/11/14.
 */
public class WLAutoServer extends AutoServer {

    /**
     * 构造接口
     * @param linkListener APP连接成功返回一个socket对象监听
     * @param wlConfiguration 配置信息
     * @param maxVer 最大版本号
     * @param minVer 最小版本号
     */
    public WLAutoServer(Context context, WLAutoServerLinkListener linkListener, WLConfiguration wlConfiguration, int maxVer, int minVer) {
        super(context, linkListener,wlConfiguration, maxVer, minVer);
    }

    /**
     * 释放資源
     */
    @Override
    public  void release () {
        super.release();
    }

    /**
     * 分派导航类App连接字符串
     *
     * @param ip   监听IP
     * @param cap 连接字符串对应的能力
     * @return 连接字符串
     */
    @Override
    public String getConnectStr (String packageName, int cap, String ip) {
        return super.getConnectStr(packageName, cap, ip);
    }

    /**
     * 回收连接字符串，并断开对应的App连接
     *
     * @param str 指定的连接字符串
     * @return 回收成功或失败
     */
    @Override
    public boolean removeConnectStr(String str) {
        return  super.removeConnectStr(str);
    }
}
