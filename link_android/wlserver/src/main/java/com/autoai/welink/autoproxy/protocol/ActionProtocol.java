package com.autoai.welink.autoproxy.protocol;

import com.autoai.welink.autoproxy.AppConnectListener;

import org.json.JSONException;
import org.json.JSONObject;

public class ActionProtocol extends ProtocolBase {

    public static final int ACTION_RETURN_LAUNCHER = 0;//返回launcher
    public static final int ACTION_FOREGROUND_SELF = 1;//将App调入前台
    public static final int ACTION_OPEN_VR = 2;//打开语音识别
    public static final int ACTION_ENTER_HOME = 3;//进入HOME
    public static final int ACTION_QUIT_HOME = 4;//退出HOME

    public static final String PROTOCOL_STRING_VER = "Ver";
    public static final String PROTOCOL_STRING_ACT = "Act";
    public static final String PROTOCOL_STRING_VER_VALUE = "v0.1.0";

    public ActionProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    @Override
    public void onReciveData(byte[] data) {
        if (data != null) {
            String string = new String(data);
            try {
                JSONObject js_action = new JSONObject(string);
                if (js_action != null) {
                    String ver = js_action.getString(PROTOCOL_STRING_VER);
                    if (!ver.equals(PROTOCOL_STRING_VER_VALUE)) {
                        return;
                    }

                    int type = js_action.getInt(PROTOCOL_STRING_ACT);
                    appListener.onAction(transmisson.connectStr, type);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {

    }
}
