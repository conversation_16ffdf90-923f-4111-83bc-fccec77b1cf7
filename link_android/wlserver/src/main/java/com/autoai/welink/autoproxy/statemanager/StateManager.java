package com.autoai.welink.autoproxy.statemanager;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.media.MediaFormat;
import android.util.Log;
import android.view.MotionEvent;

import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.WLConfiguration;
import com.autoai.welink.autoproxy.codec.RecordController;
import com.autoai.welink.autoproxy.codec.WLCodecListener;
import com.autoai.welink.autoproxy.protocol.AnFileLog;
import com.autoai.welink.autoproxy.server.R;
import com.autoai.welink.autoproxy.server.WLAutoServer;
import com.autoai.welink.autoproxy.server.WLAutoServerLink;
import com.autoai.welink.autoproxy.server.WLAutoServerLinkListener;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;


public class StateManager {
    private AppConnectListener appConnectListener;
    private Map<String, StateController> linkMap = new HashMap<>();
    public WLAutoServer wlAutoServer;

    private StateController foregroundSC;
    private StateController activeScreenSC;
    private StateController willActiveScreenSC;
    private boolean bChangeScreenActive;

    private StateController activeMusicSC;
    private StateController willActiveMusicSC;
    private boolean bCancelMusicActive;

//    private RecordController recordController;
    private Object lock;
    private boolean bH264Record = false;

    private final int FPS;
    private int screenWidth, screenHeight;
    private Rect connectorRect;
    private final boolean isServerCodec;

    private WLAutoServerLinkListener wlAutoServerLinkListener = new WLAutoServerLinkListener() {
        @Override
        public void onConnected(WLAutoServerLink serverLink,  long cap) {
            StateController state = new StateController(cap, serverLink.getLinkConnectStr(), StateManager.this, appConnectListener, serverLink);
            linkMap.put(state.getConnectStr(), state);
            appConnectListener.onConnected(serverLink.getLinkConnectStr());
        }

        @Override
        public void onError(String connectStr, int type) {
            appConnectListener.onError(connectStr, type);
        }
    };

    public StateManager(Context context, WLConfiguration wlConfiguration, AppConnectListener listener) {
        screenWidth = wlConfiguration.getHUScreenWidth();
        screenHeight = wlConfiguration.getHUScreenHeight();
        connectorRect = wlConfiguration.getConnectorRect();
        FPS = wlConfiguration.getFps();
        Log.e("auto-fps", "fps: " + FPS);
        this.isServerCodec = wlConfiguration.isServerCodec();

        appConnectListener = listener;
        wlAutoServer = new WLAutoServer(context, wlAutoServerLinkListener, wlConfiguration, MAX_VER, MIN_VER);
//        recordController = new RecordController();
        lock = new Object();
      /*  recordController.setWH(screenWidth, screenHeight, connectorRect, wlConfiguration.getDensityDpi(), wlConfiguration.getFps());
        recordController.setCodeListener(new WLCodecListener() {
            @Override
            public void onCallBack(int type, byte[] bytes) {
                if (appConnectListener != null) {
                    appConnectListener.onVideoFrame(bytes);
                }
            }

            @Override
            public void onVideoFormatChange(MediaFormat mediaFormat) {
                if (appConnectListener != null) {
                    appConnectListener.onVideoFormatChange(mediaFormat);
                }
            }

            @Override
            public void onVideoTime(long time) {
                if (appConnectListener != null) {
                    appConnectListener.onVideoTime(time);
                }
            }

            @Override
            public void onCodecStatusCallBack(int type, Object object) {

            }

            @Override
            public void onScreenNoUpdate() {

            }
        });

        if (isServerCodec) {
            new Thread() {
                @Override
                public void run() {
                    while (true) {
                        synchronized (lock) {
                            if (recordController == null) {
                                break;
                            }

                            if (bH264Record) {
                                recordController.record();
                            }

                        }

                        try {
                            sleep(1000 / FPS);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }.start();
        }
        */

    }

    public void release() {
        if (linkMap != null) {
            Iterator<Map.Entry<String, StateController>> iterator = linkMap.entrySet().iterator();
            while (iterator.hasNext()) {
                String connectStr = iterator.next().getValue().getConnectStr();
                StateController sc = findController(connectStr);

                if (sc != null) {
                    sc.transmisson.disconnect();
                    wlAutoServer.removeConnectStr(connectStr);
                }

                iterator.remove();
            }
        }

        wlAutoServer.release();

       /* synchronized (lock) {
            recordController.destory();
            recordController = null;
        }*/
    }

    public void stateChange(StateController sc, int state) {
        switch (state) {
            case AppState.AppState_CONNECT: {

            }
            break;

            case AppState.AppState_DISCONNECT: {
                if (foregroundSC != null && foregroundSC.getConnectStr().equals(sc.getConnectStr())) {
                    foregroundSC = null;
                }

                if (activeScreenSC != null && activeScreenSC.getConnectStr().equals(sc.getConnectStr())) {
                    activeScreenSC = null;
                }

                if (activeMusicSC != null && activeMusicSC.getConnectStr().equals(sc.getConnectStr())) {
                    activeMusicSC = null;
                }

                if (willActiveScreenSC != null && willActiveScreenSC.getConnectStr().equals(sc.getConnectStr())) {
                    willActiveScreenSC = null;
                    if (bChangeScreenActive) {
                        bChangeScreenActive = false;
                    }
                }

                if (willActiveMusicSC != null && willActiveMusicSC.getConnectStr().equals(sc.getConnectStr())) {
                    willActiveMusicSC = null;
                    if (bCancelMusicActive) {
                        bCancelMusicActive = false;
                    }
                }

                linkMap.remove(sc.getConnectStr());
            }
            break;

            case AppState.AppState_BACKGROUND: {
                 /*connector已经切到后台
                1.通知appConnectListener到后台
                */
                foregroundSC = null;
                appConnectListener.onBackground(sc.getConnectStr());
            }
            break;

            case AppState.AppState_FOREGROUND: {
                /*connector已经切到前台
                1.通知appConnectListener到前台
                */
                foregroundSC = sc;
                appConnectListener.onForeground(foregroundSC.getConnectStr());
            }
            break;

            case AppState.AppState_ACTIVEMUSIC: {
                if(bCancelMusicActive) {
                    willActiveMusicSC = sc;
                } else {
                    if (activeMusicSC != null) {
                        if (!activeMusicSC.getConnectStr().equals(sc.getConnectStr())) {
                            activeMusicSC.sendMusicCancel();
                            activeMusicSC = null;
                            willActiveMusicSC = sc;
                            bCancelMusicActive = true;
                        } else {
                            sc.sendMusicPlay();
                            activeMusicSC = sc;
                            bCancelMusicActive = false;
                        }
                    } else {
                        sc.sendMusicPlay();
                        activeMusicSC = sc;
                        bCancelMusicActive = false;
                    }
                }
            }
            break;

            case AppState.AppState_INACTIVEMUSIC: {
                activeMusicSC = null;
                if(willActiveMusicSC != null){
                    willActiveMusicSC.sendMusicPlay();
                    activeMusicSC = willActiveMusicSC;
                    willActiveMusicSC = null;
                }
                bCancelMusicActive = false;
            }
            break;

            case AppState.AppState_ACTIVESCREEN: {
                //激活sc录屏
                activeScreenSC = sc;
                willActiveScreenSC = null;
                bChangeScreenActive = false;
            }
            break;

            case AppState.AppState_INACTIVESCREEN: {
                //停止sc录屏
                activeScreenSC = null;

                if (willActiveScreenSC != null) {
//                    willActiveScreenSC.sendResumeRecord(isServerCodec);
                } else {
                    bChangeScreenActive = false;
                }
            }
            break;

            case AppState.AppState_NULL: {
                //出现error，做断开处理
                if (foregroundSC != null && foregroundSC.getConnectStr().equals(sc.getConnectStr())) {
                    foregroundSC = null;
                }

                if (activeScreenSC != null && activeScreenSC.getConnectStr().equals(sc.getConnectStr())) {
                    activeScreenSC = null;
                }

                if (activeMusicSC != null && activeMusicSC.getConnectStr().equals(sc.getConnectStr())) {
                    activeMusicSC = null;
                }

                if (willActiveScreenSC != null && willActiveScreenSC.getConnectStr().equals(sc.getConnectStr())) {
                    willActiveScreenSC = null;
                }

                if (bChangeScreenActive) {
                    bChangeScreenActive = false;
                }

                if (willActiveMusicSC != null && willActiveMusicSC.getConnectStr().equals(sc.getConnectStr())) {
                    willActiveMusicSC = null;
                    if (bCancelMusicActive) {
                        bCancelMusicActive = false;
                    }
                }

                linkMap.remove(sc.getConnectStr());
            }
        }
    }

    public boolean sendTouch(String connectStr, MotionEvent motionEvent) {
        AnFileLog.e("fatal4","wlserver sendTouch2 "+motionEvent);
        StateController sc = findController(connectStr);

        if (sc != null) {
            motionEvent.offsetLocation(-connectorRect.left, -connectorRect.top);
            MotionEvent me = MotionEvent.obtain(motionEvent.getDownTime(), motionEvent.getEventTime(), motionEvent.getAction(),
                motionEvent.getX(), motionEvent.getY(), motionEvent.getPressure(), motionEvent.getSize(), motionEvent.getMetaState(),
                motionEvent.getXPrecision(), motionEvent.getYPrecision(), motionEvent.getDeviceId(),
                motionEvent.getEdgeFlags());

            if (me.getX() >= 0 && me.getX() < connectorRect.width() && me.getY() >= 0 && me.getY() < connectorRect.height()) {
                me.setSource(motionEvent.getSource());
                sc.sendTouch(me);
                return true;
            }
        }
        return false;
    }
    public boolean sendTouch2(String connectStr, MotionEvent motionEvent) {
        AnFileLog.e("fatal4","wlserver sendTouch2 "+motionEvent);
        StateController sc = findController(connectStr);

        if (sc != null) {
            motionEvent.offsetLocation(-connectorRect.left, -connectorRect.top);
            int pointerCount=motionEvent.getPointerCount();
            MotionEvent.PointerProperties[] pointerProperties = new MotionEvent.PointerProperties[pointerCount];
            MotionEvent.PointerCoords[] pointerCoords = new MotionEvent.PointerCoords[pointerCount];

            for (int i = 0; i < pointerCount; i++) {
                MotionEvent.PointerProperties pps = new MotionEvent.PointerProperties();
                pps.id = i;
                pps.toolType = MotionEvent.TOOL_TYPE_FINGER;
                pointerProperties[i] = pps;

                MotionEvent.PointerCoords pcs = new MotionEvent.PointerCoords();
                pcs.pressure = motionEvent.getPressure();
                pcs.x = motionEvent.getX(i);
                pcs.y = motionEvent.getY(i);
                pointerCoords[i] = pcs;
                if (pcs.x >= 0 && pcs.x < connectorRect.width() && pcs.y  >= 0 && pcs.y  < connectorRect.height()) {
                }else{
                    AnFileLog.e("fatal4","sendTouch2 return false pcs="+pcs.x+","+pcs.y+",connectorRect w="+connectorRect.width()+",h="+connectorRect.height());
                    return false;
                }
            }

            MotionEvent me = MotionEvent.obtain(motionEvent.getDownTime(), motionEvent.getEventTime(), motionEvent.getAction(),
                pointerCount, pointerProperties, pointerCoords,
                motionEvent.getMetaState(), motionEvent.getButtonState(),
                motionEvent.getXPrecision(), motionEvent.getYPrecision(), motionEvent.getDeviceId(),
                motionEvent.getEdgeFlags(),motionEvent.getSource(),motionEvent.getFlags());

            if (me.getX() >= 0 && me.getX() < connectorRect.width() && me.getY() >= 0 && me.getY() < connectorRect.height()) {
                me.setSource(motionEvent.getSource());
                sc.sendTouch(me);
                return true;
            }else{
                AnFileLog.e("fatal4","sendTouch2 return false "+motionEvent+",connectorRect w="+connectorRect.width()+",h="+connectorRect.height());
            }
        }
        return false;
    }
    public void sendCommand(String connectStr, String command) {
        StateController sc = findController(connectStr);

        if (sc != null){
            sc.sendCommand(command);
        }
    }

    public void revoke(String connectStr) {

        StateController sc = findController(connectStr);

        if (sc != null) {
            sc.transmisson.disconnect();
            wlAutoServer.removeConnectStr(connectStr);
            linkMap.remove(connectStr);
        }
    }

    public boolean activeMirrorScreen(String connectStr){
        if(bChangeScreenActive) {
            return false;
        }

        if(connectStr != null) {
            //1.判断connectStr是否已连接
            StateController sc = findController(connectStr);
            if (sc != null) {
                if (activeScreenSC != null && !activeScreenSC.getConnectStr().equals(connectStr)) {
                    //当前active的app与需要active的app不同
                    //1.关闭当前app的投屏
                    //2.等待投屏结束消息后启动新app的投屏
                    activeScreenSC.sendPauseRecord(isServerCodec);
                    activeScreenSC = null;
                    willActiveScreenSC = sc;
                    bChangeScreenActive = true;
                } else if (activeScreenSC == null) {
                    //当前没有active的app
                    //1.发送消息通知app开始投屏
                    sc.sendResumeRecord(isServerCodec);
                    willActiveScreenSC = null;
                    bChangeScreenActive = true;
                }
                //active的app已启动录屏
                return true;
            }
            return false;
        }
        else{
            //关闭当前active的app
            if (activeScreenSC != null) {
                activeScreenSC.sendPauseRecord(isServerCodec);
                activeScreenSC = null;
            }
            return true;
        }
    }

    public StateController findController(String connectStr) {
        if (connectStr == null) {
            return null;
        }

        return linkMap.get(connectStr);
    }

    public String getActivedMusicApp() {
        if (activeMusicSC != null)
        {
            return activeMusicSC.getConnectStr();
        }
        return null;
    }

    public void activateMusic(String connectStr) {
        stateChange(findController(connectStr), AppState.AppState_ACTIVEMUSIC);
    }

    public void requestMusicSheet(String connectStr, String id) {
        if (findController(connectStr).getIsSupportMusicSheet()) {
            activeMusicSC.requestMusicSheet(id);
        }
    }

    public void searchMusicSheet(String connectStr, String content) {
        if (findController(connectStr).getIsSupportMusicSheet()) {
            activeMusicSC.searchMusicSheet(content);
        }
    }

    public void start() {
       /* if (!bH264Record) {
            bH264Record = true;
            recordController.start();
        }*/
    }

    public void stop() {
      /*  if (bH264Record) {
            bH264Record = false;
            recordController.stop();
        }*/
    }

    public void setMirrorScreenApp(String connectStr, int x, int y) {
        if (activeScreenSC != null) {
            activeScreenSC.sendPauseRecord(isServerCodec);
            activeScreenSC = null;
        }

        if (connectStr == null) {
            return;
        }

        StateController sc = findController(connectStr);
        if (sc != null) {
            sc.sendResumeRecord(isServerCodec);
            activeScreenSC = sc;
        }

        if (x != Integer.MAX_VALUE) {
            connectorRect.left = x;
        }
        if (y != Integer.MAX_VALUE) {
            connectorRect.top = y;
        }
//        recordController.updateConnectorLeftTop(connectorRect.left, connectorRect.top);
    }

    public SurfaceTexture enableExternal(Rect rect,boolean forceFullScreen) {
//        return recordController.enableExternal(rect,forceFullScreen);
        return null;
    }

    public void disableExternal() {
//        recordController.disableExternal();
    }

    public String getMirrorScreenApp() {
        if (activeScreenSC == null) {
            return null;
        }

        return activeScreenSC.getConnectStr();
    }

    public SurfaceTexture showOverlaySurfaceTexture() {
//        return recordController.showOverlaySurfaceTexture();
        return null;
    }

    public void hideOverlaySurfaceTexture() {
//        recordController.hideOverlaySurfaceTexture();
    }

    public SurfaceTexture getVideoSurfaceTexture() {
//        return recordController.getVideoSurfaceTexture();
        return null;
    }

    static StateController sc_lastTime = null;
    public void sendVoice(String connectStr, byte[] pcm, int rate, int bit, int channel) {
        StateController sc = findController(connectStr);

        if( sc != null){

            if (null != sc_lastTime && sc_lastTime != sc) {
                sc_lastTime.sendVoice(null, 0, 0, 0);
            }
            sc.sendVoice(pcm, rate, bit, channel);
            sc_lastTime = sc;
        }
    }
//    private void startWelinkTimer() {
//        callWelinkTimer = new Timer();
//        callWelinkTimer.schedule(new TimerTask() {
//            public void run() {
//                if (welinkSC != null) {
//                    //send到前台消息数据到client端
//                    welinkSC.sendFroground();
//                }
//            }
//        }, 200);
//    }
//
//    private void cancelWelinkTimer() {
//        callWelinkTimer.cancel();
//    }

    public void sendSoundBegin(String connectStr, int soundID) {
        StateController ctr = findController(connectStr);
        if (null != ctr) {
            ctr.sendSoundBegin(soundID);
        }
    }

    public void sendSoundComplete(String connectStr, int soundID) {
        StateController ctr = findController(connectStr);
        if (null != ctr) {
            ctr.sendSoundComplete(soundID);
        }
    }

    public void sendSoundPrepare(String connectStr, int soundID) {
        StateController ctr = findController(connectStr);
        if (null != ctr) {
            ctr.sendSoundPrepare(soundID);
        }
    }

    public void sendSoundInterrupt(String connectStr, int soundID, long playTime, long totalTime) {
        StateController ctr = findController(connectStr);
        if (null != ctr) {
            ctr.sendSoundInterrupt(soundID, playTime, totalTime);
        }
    }

    public void sendSoundReject(String connectStr, int soundID, long waitingTime) {
        StateController ctr = findController(connectStr);
        if (null != ctr) {
            ctr.sendSoundReject(soundID, waitingTime);
        }
    }

    public String getFroground() {
        if (foregroundSC != null) {
            return foregroundSC.getConnectStr();
        }

        return null;
    }

    private static final int MAX_VER = 1;
    private static final int MIN_VER = 1;
}
