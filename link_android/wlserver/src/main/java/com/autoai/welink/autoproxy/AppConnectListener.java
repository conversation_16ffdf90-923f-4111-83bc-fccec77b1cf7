package com.autoai.welink.autoproxy;

import android.graphics.Bitmap;
import android.media.MediaFormat;

/**
 * <AUTHOR>
 */
public interface AppConnectListener {
    /**
     * App连接成功
     *
     * @param connectStr 连接字符串
     */
    void onConnected(String connectStr);

    /**
     * App切换至前台
     *
     * @param connectStr 连接字符串
     */
    void onForeground(String connectStr);

    /**
     * App切换至后台
     *
     * @param connectStr 连接字符串
     */
    void onBackground(String connectStr);

    /**
     * App断开连接
     *
     * @param connectStr 连接字符串
     */
    void onDisconnected(String connectStr);

    /**
     * App返回连接失败
     *
     * @param connectStr 连接字符串
     * @param errorCode  错误码
     *                   1. WeLink服务无效，有可能是WeLink App没有运行或者没有连接车机
     *                   2. 连接字符串不正确
     *                   3. WLConnector版本太老，无法连接当前的WeLink服务
     *                   4. 重复连接
     *                   5. 连接WeLink服务失败
     *                   6. App不支持该分辨率
     */
    void onError(String connectStr, int errorCode);

    /**
     * 活动触发通知
     *
     * @param connectStr 发送者的连接字符串
     * @param action     action id -- 参照文档《ActionID》
     */
    void onAction(String connectStr, int action);


    /**
     * 视频格式发生改变，在编码器开始工作时调用
     *
     * @param mediaFormat 视频格式
     */
    void onVideoFormatChange(MediaFormat mediaFormat);

    /**
     * 接收到视频帧数据的时间点
     *
     * @param time 当前帧数据对应的时间点
     */
    void onVideoTime(long time);

    /**
     * 接收到视频帧数据
     *
     * @param frame 帧数据
     */
    void onVideoFrame(byte[] frame);

    /**
     * 播放器注册，播放器注册才可能成为激活音乐App
     *
     * @param connectStr 连接字符串
     */
    void onMusicRegister(String connectStr);

    /**
     * 播放器获取焦点
     *
     * @param connectStr 连接字符串
     */
    void onMusicFocus(String connectStr);

    /**
     * 播放器取消注册，播放器取消注册将变为非激活音乐App
     *
     * @param connectStr 连接字符串
     */
    void onMusicUnregister(String connectStr);

    /**
     * 音乐ID3信息更新
     *
     * @param source    音源
     * @param artist    演唱者(创建者)
     * @param title     歌曲名(电台名)
     * @param album     专辑名
     * @param lyric     歌词，可以为null
     * @param lyricType 歌词格式类型 1:LRC, 2:QRC ,0:歌词为null
     * @param duration  时长 单位:秒
     * @param cover     歌曲封面，可以为null
     */
    void onMusicID3(String source, String artist, String title, String album, String lyric, int lyricType, int duration, Bitmap cover);

    /**
     * 音乐播放顺序更新
     *
     * @param order 播放顺序：参见《车机命令》，0: 循环播放、1: 顺序播放、2: 随机播放、3: 单曲循环播放
     */
    void onMusicOrder(int order);

    /**
     * 接收到音乐音频数据
     *
     * @param position 数据的位置
     * @param pcm      声音数据, 为null则暂停播放，length为0则表示正在缓冲数据
     */
    void onMusicPCM(long position, byte[] pcm);

    /**
     * 接收到音乐音频数据
     *
     * @param totalLen 声音数据总长度
     * @param rate     采样率
     * @param bit      采样精度
     * @param channel  声道数
     */
    void onMusicStart(long totalLen, int rate, int bit, int channel);

    /**
     * 停止音乐播放
     */
    void onMusicStop();

    /**
     * 暂停音乐播放
     */
    void onMusicPause();

    /**
     * 恢复音乐播放
     */
    void onMusicResume();

    /**
     * 接收到导航提示声音数据
     *
     * @param connectStr 发送者的连接字符串
     * @param soundID    Sound ID
     * @param mark       声音标识
     * @param pcm        声音数据
     * @param rate       采样率
     * @param bit        采样精度
     * @param channel    声道数
     */
    void onSoundPCM(String connectStr, int soundID, String mark, byte[] pcm, int rate, int bit, int channel);

    /**
     * 开始TBT导航
     *
     * @param connectStr 发送者的连接字符串
     */
    void onNaviTBTBegin(String connectStr);

    /**
     * 接收到导航TBT信息
     *
     * @param connectStr      发送者的连接字符串
     * @param currentRoadName 当前道路名称
     * @param roadName        下一路口名称
     * @param roadDistance    下一路口距离，米
     * @param roadTurnIcon    下一个路口TBT图标ID
     * @param remainDistance  距目的地的剩余距离，小余等于0则导航结束，单位：米
     * @param remainTime      预估到达目的地的所剩时间，单位：分
     */
    void onNaviTBT(String connectStr, String currentRoadName, String roadName, int roadDistance, int roadTurnIcon, int remainDistance, int remainTime);

    /**
     * 结束TBT导航
     *
     * @param connectStr 发送者的连接字符串
     */
    void onNaviTBTEnd(String connectStr);

    /**
     * 接收到交通摄像头提示
     *
     * @param connectStr 发送者的连接字符串
     */
    void onTrafficCamera(String connectStr);
}
