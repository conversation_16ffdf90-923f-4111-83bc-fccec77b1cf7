package com.autoai.welink.autoproxy.protocol;

import com.autoai.welink.autoproxy.AppConnectListener;

import org.json.JSONException;
import org.json.JSONObject;

public class NaviProtocol extends ProtocolBase {
    private static final String PROTOCOL_NAVITYPE_START_TBT = "navi_start_tbt";
    private static final String PROTOCOL_NAVITYPE_STOP_TBT = "navi_stop_tbt";
    private static final String PROTOCOL_NAVITYPE_UPDATE_TBT = "navi_update_tbt";
    private static final String PROTOCOL_NAVITYPE_CAMERA = "navi_camera";

    private static final String PROTOCOL_NAVI_STR_TYPE = "Type";
    private static final String PROTOCOL_NAVI_STR_CROADNAME = "CRoadName";
    private static final String PROTOCOL_NAVI_STR_ROADNAME = "RoadName";
    private static final String PROTOCOL_NAVI_STR_ROADDISTANCE = "RoadDistance";
    private static final String PROTOCOL_NAVI_STR_TURNICON = "TurnIcon";
    private static final String PROTOCOL_NAVI_STR_REMAINDISTANCE = "RemainDistance";
    private static final String PROTOCOL_NAVI_STR_REMAINTIME = "RemainTime";

    public NaviProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    @Override
    public void onReciveData(byte[] data) {
        try {
            JSONObject jsonObject = new JSONObject(new String(data));

            if (null == jsonObject) {
                return;
            }

            String type = jsonObject.optString(PROTOCOL_NAVI_STR_TYPE);
            switch (type) {
                case PROTOCOL_NAVITYPE_UPDATE_TBT:
                    appListener.onNaviTBT(transmisson.connectStr,
                            jsonObject.optString(PROTOCOL_NAVI_STR_CROADNAME),
                            jsonObject.optString(PROTOCOL_NAVI_STR_ROADNAME),
                            jsonObject.optInt(PROTOCOL_NAVI_STR_ROADDISTANCE),
                            jsonObject.optInt(PROTOCOL_NAVI_STR_TURNICON),
                            jsonObject.optInt(PROTOCOL_NAVI_STR_REMAINDISTANCE),
                            jsonObject.optInt(PROTOCOL_NAVI_STR_REMAINTIME)
                    );
                    break;
                case PROTOCOL_NAVITYPE_START_TBT:
                    appListener.onNaviTBTBegin(transmisson.connectStr);
                    break;
                case PROTOCOL_NAVITYPE_STOP_TBT:
                    appListener.onNaviTBTEnd(transmisson.connectStr);
                    break;
                case PROTOCOL_NAVITYPE_CAMERA:
                    appListener.onTrafficCamera(transmisson.connectStr);
                    break;
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {

    }

    //byte[]转int
    private static int byteArrayToInt(byte[] b) {
        return   b[3] & 0xFF |
            (b[2] & 0xFF) << 8 |
            (b[1] & 0xFF) << 16 |
            (b[0] & 0xFF) << 24;
    }
}
