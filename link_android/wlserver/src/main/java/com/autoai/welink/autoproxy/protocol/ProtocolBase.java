package com.autoai.welink.autoproxy.protocol;


import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.server.WLAutoServerLink;
import com.autoai.welink.autoproxy.server.WLAutoServerLinkListener;

//协议基类,每个协议实现都需要自此类继承
public abstract class ProtocolBase implements WLAutoServerLinkListener{
    //每个协议默认都会持有一个传输对象,用来发送数据
    Transmisson transmisson;
    AppConnectListener appListener;

    public ProtocolBase(Transmisson transmisson, AppConnectListener appListener) {
        this.transmisson = transmisson;
        this.appListener = appListener;
    }

    //=========================================================
    //每个协议默认都需要接收自己协议的数据包,此处传入的数据包只是协议自身的数据,不会包含底层模块自行增加的各种协议头
    public void onReciveData(byte[] data) {
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    public abstract boolean onConnected();

//    @Override
    public void onDisconnected() {

    }

    @Override
    public void onConnected(WLAutoServerLink serverLink, long cap) {

    }

    @Override
    public void onError(String connectStr, int tyep) {

    }
}
