package com.autoai.welink.autoproxy.protocol;

import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.statemanager.AppState;

//前后台状态协议
public class BackgroundStatusProtocol extends ProtocolBase {
    public BackgroundStatusProtocol(Transmisson transmisson, AppConnectListener appListener) {
        super(transmisson, appListener);
    }

    //前后台状态变化协议
    //本使用的协议字符串
    public static final String PROTOCOL_STRING_ONBACKGROUND = "onBackground";
    public static final String PROTOCOL_STRING_ONFOREGROUND = "onForeground";
    public static final String PROTOCOL_STRING_ONLOCKSCREEN = "onLockscreen";
    public static final String PROTOCOL_STRING_ONUNLOCKSCREEN = "onUnlockscreen";

    //=========================================================
    //每个协议默认都需要接收自己协议的数据包,此处传入的数据包只是协议自身的数据,不会包含底层模块自行增加的各种协议头
    @Override
    public void onReciveData(byte[] data) {
        String string = new String(data);
        switch (string) {
            case PROTOCOL_STRING_ONBACKGROUND:
                AnFileLog.e("wlserver-protocol", "recv protocol string=" + PROTOCOL_STRING_ONBACKGROUND + ", connectStr=" + transmisson.stateController.getConnectStr());
                transmisson.stateController.setState(AppState.AppState_BACKGROUND);
                break;
            case PROTOCOL_STRING_ONFOREGROUND:
                AnFileLog.e("wlserver-protocol", "recv protocol string=" + PROTOCOL_STRING_ONFOREGROUND + ", connectStr=" + transmisson.stateController.getConnectStr());
                transmisson.stateController.setState(AppState.AppState_FOREGROUND);
                break;
            case PROTOCOL_STRING_ONLOCKSCREEN:
                AnFileLog.e("wlserver-protocol", "recv protocol string=" + PROTOCOL_STRING_ONLOCKSCREEN + ", connectStr=" + transmisson.stateController.getConnectStr());
                break;
            case PROTOCOL_STRING_ONUNLOCKSCREEN:
                AnFileLog.e("wlserver-protocol", "recv protocol string=" + PROTOCOL_STRING_ONUNLOCKSCREEN + ", connectStr=" + transmisson.stateController.getConnectStr());
                break;
        }
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected() {
        return false;
    }

    @Override
    public void onDisconnected() {
    }

    public void onForeground() {
        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONFOREGROUND.getBytes());
    }

    public void onBackground() {
        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONBACKGROUND.getBytes());
    }

    public void onLockscreen() {
        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONLOCKSCREEN.getBytes());
    }

    public void onUnlockscreen() {
        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONUNLOCKSCREEN.getBytes());
    }
}
