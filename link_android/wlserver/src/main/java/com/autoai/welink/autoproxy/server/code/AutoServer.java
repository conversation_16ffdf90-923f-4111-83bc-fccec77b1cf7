package com.autoai.welink.autoproxy.server.code;

import android.content.Context;
import android.os.Build;
import android.os.MemoryFile;
import android.os.SharedMemory;

import com.autoai.welink.autoproxy.WLConfiguration;
import com.autoai.welink.autoproxy.protocol.AnFileLog;
import com.autoai.welink.autoproxy.server.WLAutoServerLinkListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by gaole on 2018/11/14.
 */

public class AutoServer {
    List<JSONObject> connectCheckList = new ArrayList<>();
    boolean socketServerIsStart = false;
    int port = 0;
    WLAutoServerLinkListener linkListener;
    AutoServer autoserver;
    int HUScreenWidth;
    int HUScreenHeight;
    int maxVersion;
    int minVersion;
    boolean isSupportWiFiChannel;
    boolean isServerCodec;
    int dpi;
    int Fps;
    int Pcm, Pcm_Cache;
    String VehicleType="",VehicleID="",UserID="";//车辆标识,用户标识,以支持第三方app个性化
    Context ctx;
    final Object lockObject = new Object();

    /**
     * 构造接口
     * @param listener APP连接成功返回一个socket对象监听
     * @param wlConfiguration 配置信息
     * @param maxVer 最大版本号
     * @param minVer 最小版本号
     */
    public AutoServer(Context context, WLAutoServerLinkListener listener, WLConfiguration wlConfiguration, int maxVer, int minVer){
        ctx = context;
        linkListener = listener;
        HUScreenWidth = wlConfiguration.getConnectorRect().width();
        HUScreenHeight = wlConfiguration.getConnectorRect().height();
        maxVersion = maxVer;
        minVersion = minVer;
        isSupportWiFiChannel = wlConfiguration.isSupportWiFiChannel();
        dpi = wlConfiguration.getDensityDpi();
        Fps = wlConfiguration.getFps();
        Pcm = wlConfiguration.getPcm();
        Pcm_Cache = wlConfiguration.getPcmCache();
        isServerCodec = wlConfiguration.isServerCodec();
        VehicleType =wlConfiguration.getVehicleType();
        VehicleID =wlConfiguration.getVehicleID();
        UserID =wlConfiguration.getUserID();

        Client.memoryList = new HashMap<>();

        if (!socketServerIsStart) {
            socketServerIsStart = true;

            synchronized (lockObject) {
                startSocketServer();
                try {
                    lockObject.wait();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        autoserver = this;
    }

    /**
     * 释放資源
     */
    public void release() {
        synchronized (Client.memoryListLock) {
            if (Client.memoryList != null) {
                Iterator<Map.Entry<String, Client.TimeLimitMemory>> iterator = Client.memoryList.entrySet().iterator();
                while (iterator.hasNext()) {
                    Client.TimeLimitMemory memory = iterator.next().getValue();
                    if (memory != null) {
                        if (memory.sharedMemory != null) {
                            memory.sharedMemory.close();
                        } else if (memory.memoryFile != null) {
                            memory.memoryFile.close();
                        }
                    }
                    iterator.remove();
                }

                Client.memoryList = null;
            }
        }

        HUScreenWidth = 0;
        HUScreenHeight = 0;
        socketServerIsStart = false;
        connectCheckList.clear();
    }

    //welink://:47529/connect?ver=1&check=vxcirb
    public String getConnectStr(String pkName, int cap, String ip) {
        StringBuffer connectStr = new StringBuffer();
        connectStr.append("welink://");
//        connectStr.append(ip);
        connectStr.append(":");
        connectStr.append(port);
        connectStr.append("/connect?ver=1&check=");

        String randomStr = getRandomString(6);
        connectStr.append(randomStr);

        addUnConnectStr(connectStr.toString(), randomStr, cap, pkName);

        return connectStr.toString();
    }

    /**
     * 添加socket连接的校验字符串
     * @param randomStr 随机字符串
     * @param cap 连接字符串对应的能力
     *            格式：
     *            {"ConnectStr":"asdbnh","RandomStr":"asdbnh","CAP":0x1234}
     */
    public void addUnConnectStr (String connectStr, String randomStr, int cap, String pkName) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("ConnectStr",connectStr);
            obj.put("RandomStr",randomStr);
            obj.put("CAP",cap);
            obj.put("FirstConnect",true);
            obj.put("PackageName",pkName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        connectCheckList.add(obj);
       AnFileLog.e("AAAASDF","*** server addUnConnectStr "+connectCheckList);
    }

    public void addConnectedStr (String connectStr, String randomStr, long cap, String pkName) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("ConnectStr",connectStr);
            obj.put("RandomStr",randomStr);
            obj.put("CAP",cap);
            obj.put("FirstConnect",false);
            obj.put("PackageName",pkName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        connectCheckList.add(obj);
       AnFileLog.e("AAAASDF","*** server addConnectedStr "+connectCheckList);
    }

    /**
     * 移除socket连接的校验字符串
     * @param str 连接的校验字符串
     *            格式：type+name+随机字符串
     * @return 移除成功或失败
     */
    public boolean removeConnectStr (String str) {
        Iterator<JSONObject> it = connectCheckList.iterator();
        while(it.hasNext()){
            JSONObject obj = (JSONObject)it.next();
            String ramdomStr = obj.optString("RandomStr");
            if(str.equals(ramdomStr)){
                it.remove();
               AnFileLog.e("AAAASDF","*** server removeConnectStr success ");
                return  true;
            }
        }

        return false;
    }

    public boolean hasRandomStr(String str) {
        for (JSONObject obj : connectCheckList) {
            String randomStr = obj.optString("RandomStr");
            if (str.equals(randomStr)) {
                AnFileLog.e("AAAASDF", "*** server hasConnectStr");
                return true;
            }
        }

        return false;
    }

    public List<JSONObject> getConnectStrList() {
        return connectCheckList;
    }

    public int getHUScreenWidth() {
        return HUScreenWidth;
    }

    public int getHUScreenHeight() {
        return HUScreenHeight;
    }

    public WLAutoServerLinkListener getLinkListener() {
        return linkListener;
    }

    public int getMaxVersion() {return maxVersion; }
    public int getMinVersion() {return minVersion; }

    public boolean getIsSupportWiFiChannel() {
        return isSupportWiFiChannel;
    }

    public boolean getIsServerCodec() {
        return isServerCodec;
    }

    public int getDpi() {
        return  dpi;
    }

    public int getFps() {
        return Fps;
    }

    public int getPcm() {
        return Pcm;
    }

    public int getPcmCache() {
        return Pcm_Cache;
    }

    public String getVehicleType() { return VehicleType; }

    public String getVehicleID() {
        return VehicleID;
    }

    public String getUserID() {
        return UserID;
    }

    public String getPackageName() {
        return ctx.getPackageName();
    }

    private static final String string = "abcdefghijklmnopqrstuvwxyz";
    /**
     * 生成随机字符串
     * @param length 随机字符串长度
     */
    private static String getRandomString(int length){
        StringBuffer sb = new StringBuffer();
        int len = string.length();
        for (int i = 0; i < length; i++) {
            sb.append(string.charAt(getRandom(len)));
        }

        return sb.toString();
    }

    private static int getRandom(int length) {

        return (int)(Math.random() * length);
    }

    /**
     * 启动socket server
     */
    private void startSocketServer() {
        new Thread(() -> {
            try {
                ServerSocket serverSocket= new ServerSocket(0);
                port = serverSocket.getLocalPort();
                AnFileLog.e("AAAASDF","*** server startSocketServer port = "+serverSocket.getLocalPort());

                synchronized (lockObject) {
                    lockObject.notify();
                }

                // 持续等待请求
                while(socketServerIsStart) {
                    // 等待客户端请求,无请求则闲置;有请求到来时,返回一个对该请求的socket连接
                    Socket clientSocket = serverSocket.accept();
                    String clientIP = clientSocket.getInetAddress().getHostAddress();
                    new AutoServerLink(clientSocket, autoserver, clientIP).start();
                }
                serverSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }).start();
    }
}
