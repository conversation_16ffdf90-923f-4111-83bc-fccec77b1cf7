package com.autoai.welink.autoproxy;

import android.graphics.Rect;

/**
 * 提供三投协议中的一些配置信息
 */
public class WLConfiguration {
    /**
     * 连接版本号.
     */
    int connectVersion;
    /**
     * connector绘制范围
     */
    Rect connectorRect;
    /**
     * 屏幕宽
     */
    int HUScreenWidth;
    /**
     * 屏幕高
     */
    int HUScreenHeight;
    /**
     * 车机屏幕密度.
     */
    int densityDpi;
    /**
     * 投屏帧率.
     */
    int fps;
    /**
     * 音乐PCM每包时间长度（毫秒）.
     */
    int pcm;
    /**
     * 音乐PCM数据缓存时长(毫秒），缓存大则音乐播放不会中断，但会导致歌曲切换、停止、暂停响应慢.
     */
    int pcmCache;
    /**
     * 是否支持Wi-Fi车机通道, 建议只在Wi-Fi手车互联下打开, 因为这会导致手机即便连接Wi-Fi也仍然使用移动网络.
     */
    boolean isSupportWiFiChannel;
    /**
     * WLServer负责H264的Codec.
     */
    boolean isServerCodec;
    /**
     * 车辆类别,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL).
     */
    String VehicleType;
    /**
     * 车辆标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL).
     */
    String VehicleID;
    /**
     * 用户标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL).
     */
    String UserID;

    /**
     * Instantiates a new Wl configuration.
     *
     * @param HUScreenWidth        车机屏幕宽
     * @param HUScreenHeight       车机屏幕高
     * @param connectorRect        Connector显示范围, null是车机屏幕范围
     * @param densityDpi           车机屏幕密度
     * @param fps                  投屏帧率
     * @param pcm                  音乐PCM每包时间长度（毫秒）
     * @param pcmCache             音乐PCM数据缓存时长(毫秒），缓存大则音乐播放不会中断，但会导致歌曲切换、停止、暂停响应慢
     * @param VehicleType          车辆类别,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL)
     * @param VehicleID            车辆标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL)
     * @param UserID               用户标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL)
     */
    public WLConfiguration(int HUScreenWidth, int HUScreenHeight, Rect connectorRect, int densityDpi, int fps, int pcm, int pcmCache, String VehicleType, String VehicleID, String UserID) {
        this.connectVersion = 0;

        this.HUScreenWidth = HUScreenWidth;
        this.HUScreenHeight = HUScreenHeight;

        if (connectorRect == null) {
            connectorRect = new Rect(0, 0, HUScreenWidth, HUScreenHeight);
        }
        this.connectorRect = connectorRect;

        this.densityDpi = densityDpi;
        this.fps = fps;
        this.pcm = pcm;
        this.pcmCache = pcmCache;
        this.isSupportWiFiChannel = false;
        this.isServerCodec = true;
        this.VehicleType = VehicleType;
        this.VehicleID = VehicleID;
        this.UserID = UserID;
    }

    /**
     * Gets Connector屏幕范围.
     *
     * @return connector屏幕范围 connector screen rect
     */
    public Rect getConnectorRect() {
        return connectorRect;
    }

    /**
     * Gets 车机屏幕宽度.
     *
     * @return 车机屏幕宽度 hu screen width
     */
    public int getHUScreenWidth() {
        return HUScreenWidth;
    }

    /**
     * Gets 车机屏幕高度.
     *
     * @return 车机屏幕高度 hu screen height
     */
    public int getHUScreenHeight() {
        return HUScreenHeight;
    }

    /**
     * Gets 车机屏幕密度.
     *
     * @return 车机屏幕密度 density dpi
     */
    public int getDensityDpi() {
        return densityDpi;
    }

    /**
     * Gets 投屏帧率.
     *
     * @return 投屏帧率 fps
     */
    public int getFps() {
        return fps;
    }

    /**
     * Gets 音乐PCM每包时间长度（毫秒）.
     *
     * @return 音乐PCM每包时间长度 （毫秒）
     */
    public int getPcm() {
        return pcm;
    }

    /**
     * Gets 音乐PCM数据缓存时长(毫秒）.
     *
     * @return 音乐PCM数据缓存时长(毫秒 ） ， 缓存大则音乐播放不会中断 ， 但会导致歌曲切换 、 停止 、 暂停响应慢 pcm cache pcm cache
     */
    public int getPcmCache() {
        return pcmCache;
    }

    /**
     * 是否支持Wi-Fi车机通道.
     *
     * @return 是否支持Wi -Fi车机通道, 建议只在Wi-Fi手车互联下打开, 因为这会导致手机即便连接Wi-Fi也仍然使用移动网络
     */
    public boolean isSupportWiFiChannel() {
        return isSupportWiFiChannel;
    }

    /**
     * Gets 车辆类别.
     *
     * @return 车辆类别, 以支持第三方app个性化, 没有时默认为空字符串(是 " ", 不是NULL) vehicle id
     */
    public String getVehicleType() {
        return VehicleType;
    }

    /**
     * Gets 车辆标识.
     *
     * @return 车辆标识, 以支持第三方app个性化, 没有时默认为空字符串(是 " ", 不是NULL) vehicle id
     */
    public String getVehicleID() {
        return VehicleID;
    }

    /**
     * Gets 用户标识.
     *
     * @return 用户标识, 以支持第三方app个性化, 没有时默认为空字符串(是 " ", 不是NULL) user id
     */
    public String getUserID() {
        return UserID;
    }

    /**
     * Gets 连接版本号.
     *
     * @return 连接版本号 connect version
     */
    public int getConnectVersion() {
        return connectVersion;
    }

    /**
     * 是否WLServer负责H264的Codec.
     *
     * @return 是否WLServer负责H264的Codec boolean
     */
    public boolean isServerCodec() {
        return isServerCodec;
    }

    /**
     * Sets WLServer负责H264的Codec.
     *
     * @param serverCodec 是否WLServer负责H264的Codec
     */
    public void setServerCodec(boolean serverCodec) {
        isServerCodec = serverCodec;
    }
}
