package com.autoai.welink.autoproxy.protocol.commandmanager;

import org.json.JSONException;
import org.json.JSONObject;

public class commandMgr {
    public static final String PROTOCOL_STRING_VER = "Ver";
    public static final String PROTOCOL_STRING_CMD = "Cmd";
    public static final String PROTOCOL_STRING_PARM = "Parm";
    public static final String PROTOCOL_STRING_VER_VALUE = "v0.1.0";

    //num:代表播放第n条，第0条代表当前首
    public static String musicPlay(int num) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Play");
        obj.put(PROTOCOL_STRING_PARM, num);

        return obj.toString();
    }

    public static String musicPause() throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Pause");

        return obj.toString();
    }

    public static String musicStop() throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Stop");

        return obj.toString();
    }

    //location:代表Seek的位置，位置计算公式：(采样率 * 声道数 * 采样精度 / 8) * 秒数
    public static String musicSeek(long location) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Seek");
        obj.put(PROTOCOL_STRING_PARM, location);

        return obj.toString();
    }

    public static String musicPrevious() throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Previous");

        return obj.toString();
    }

    public static String musicNext() throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Next");

        return obj.toString();
    }


    //order:0: 循环播放、1: 顺序播放、2: 随机播放、3: 单曲循环播放
    public static String musicOrder(int order) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Order");
        obj.put(PROTOCOL_STRING_PARM, order);

        return obj.toString();
    }

    //searchStr:关键字间以空格间隔
    public static String allSearch(String searchStr) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Search");
        obj.put(PROTOCOL_STRING_PARM, searchStr);

        return obj.toString();
    }

    //mode：1:代表将要进入Home模式（导航App应该只显示地图层，音乐App应该只显示歌曲封面），0:将要退出Home模式
    public static String allHome(int mode) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Home");
        obj.put(PROTOCOL_STRING_PARM, mode);

        return obj.toString();
    }

    //mode：1:代表打开路况，0:代表关闭路况
    public static String naviTraffic(int mode) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Traffic");
        obj.put(PROTOCOL_STRING_PARM, mode);

        return obj.toString();
    }

    //zoom：代表地图缩放百分比，负值为缩小，正值为放大
    public static String naviZoom(int zoom) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Zoom");
        obj.put(PROTOCOL_STRING_PARM, zoom);

        return obj.toString();
    }

    //lon：目的地精度，lat：目的地纬度
    public static String naviStart(double lon, double lat) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Start");

        JSONObject posobj = new JSONObject();
        posobj.put("lon", lon);
        posobj.put("lat", lat);

        obj.put(PROTOCOL_STRING_PARM, posobj);

        return obj.toString();
    }

    public static String naviEnd() throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "End");

        return obj.toString();
    }

    //mode:1:代表开启路况播报，0:代表关闭路况播报
    public static String naviBroadcast(int mode) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Broadcast");
        obj.put(PROTOCOL_STRING_PARM, mode);

        return obj.toString();
    }

    //mode:0:代表白天模式，1:代表夜间模式，2:代表3D地图模式，3:代表2D地图模式
    public static String naviModel(int mode) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Model");
        obj.put(PROTOCOL_STRING_PARM, mode);

        return obj.toString();
    }

    public static String naviRoute() throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Route");

        return obj.toString();
    }

    public static String allText(String str) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
        obj.put(PROTOCOL_STRING_CMD, "Text");
        obj.put(PROTOCOL_STRING_PARM, str);

        return obj.toString();
    }
}


