package com.autoai.welink.autoproxy.codec;

import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.view.Surface;

import java.util.concurrent.Semaphore;


public class SurfaceRecorder {
    final int MIN_FORCE_DRAW = 6;
    final int RESUME_FORCE_DRAW = 90;

    Surface mSurface;

    public void start(Surface surface) {
        mSurface = surface;
        mStart = true;
        mGLInitObject = new Semaphore(0);

        mRenderThread = new Thread(this::render);

        mRenderThread.start();

        try {
            mGLInitObject.acquire(1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setVideoSurfaceTexture(int width, int height, Rect connectorRect) {
        mInputSurface.setConnectorSurfaceTexture(width, height, connectorRect);
    }

    public void updateConnectorLeftTop(int x, int y) {
        if (mInputSurface != null) {
            mInputSurface.updateConnectorLeftTop(x, y);
        }
    }

    public SurfaceTexture getVideoSurfaceTexture() {
        return mInputSurface.getConnectorSurfaceTexture();
    }

    public SurfaceTexture showOverlaySurfaceTexture() {
        synchronized (mForceDrawLock) {
            if (mForceDraw < MIN_FORCE_DRAW) {
                mForceDraw = MIN_FORCE_DRAW;
            }
        }

        return mInputSurface.showOverlaySurfaceTexture();
    }

    public void hideOverlaySurfaceTexture() {
        mInputSurface.hideOverlaySurfaceTexture();

        synchronized (mForceDrawLock) {
            if (mForceDraw < MIN_FORCE_DRAW) {
                mForceDraw = MIN_FORCE_DRAW;
            }
        }
    }

    public SurfaceTexture enableExternal(Rect rect,boolean forceFullScreen) {
        synchronized (mForceDrawLock) {
            mVFR = false;

            if (mForceDraw < MIN_FORCE_DRAW) {
                mForceDraw = MIN_FORCE_DRAW;
            }
        }

        return mInputSurface.enableExternal(rect,forceFullScreen);
    }

    public void disableExternal() {
        mInputSurface.disableExternal();

        synchronized (mForceDrawLock) {
            mVFR = true;

            if (mForceDraw < MIN_FORCE_DRAW) {
                mForceDraw = MIN_FORCE_DRAW;
            }
        }
    }

    public void pause(){
        mPause = true;
    }

    public void resume(){
        mPause = false;

        synchronized (mForceDrawLock) {
            mForceDraw = RESUME_FORCE_DRAW;
        }
    }

    public void reset() {
        synchronized (mForceDrawLock) {
            mForceDraw = RESUME_FORCE_DRAW;
        }
        synchronized (mLock) {
            mLock.notify();
        }
    }

    void create(Surface surface)
    {
        mInputSurface = new CodecInputSurface();
        mInputSurface.createEGLSurface(surface);
        mInputSurface.makeCurrent();
        mInputSurface.createRender();
    }

    public void record() {
        synchronized (mLock) {
            mLock.notify();
        }
    }

    public void stop() {
        if (mRenderThread != null) {
            mStart = false;

            synchronized (mLock) {
                mLock.notify();
            }

            try {
                mRenderThread.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        mRenderThread = null;
    }

    private void render() {
        create(mSurface);
        mGLInitObject.release();

        while (mStart) {
            try {
                synchronized (mLock) {
                    mLock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (mInputSurface.awaitNewImage()) {
                synchronized (mForceDrawLock) {
                    if (mForceDraw < MIN_FORCE_DRAW) {
                        mForceDraw = MIN_FORCE_DRAW;
                    }
                }
            }

            synchronized (mForceDrawLock) {
                if (mVFR) {
                    if (mForceDraw <= 0) {
                        continue;
                    }

                    --mForceDraw;
                }
            }

            if (!mPause) {
//                mInputSurface.drawImage();
//                mInputSurface.setPresentationTime(System.nanoTime());
//                mInputSurface.swapBuffers();
            }
        }

        if (mInputSurface != null) {
            mInputSurface.release();
            mInputSurface = null;
        }
    }

    private CodecInputSurface mInputSurface;
    private boolean mStart;
    private final Object mLock = new Object();
    private Thread mRenderThread = null;
    private Semaphore mGLInitObject;
    private boolean mVFR = true;
    private Integer mForceDraw = 0;
    private final Object mForceDrawLock = new Object();
    private boolean mPause =false;
}
