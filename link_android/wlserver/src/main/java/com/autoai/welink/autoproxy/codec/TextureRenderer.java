package com.autoai.welink.autoproxy.codec;

import android.graphics.Rect;
import android.opengl.GLES11Ext;
import android.opengl.GLES20;

import com.autoai.welink.autoproxy.protocol.AnFileLog;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import java.nio.ShortBuffer;


public class TextureRenderer {
    private final String vertexShaderCode =
        "attribute vec4 vPosition;\n" +
            "attribute vec2 inputTextureCoordinate;\n" +
            "varying vec2 textureCoordinate;\n" +
            "void main() {\n" +
            "  gl_Position = vPosition;\n" +
            "  textureCoordinate = inputTextureCoordinate;\n" +
            "}";

    private final String fragmentShaderCode =
        "#extension GL_OES_EGL_image_external : require\n" +
            "precision mediump float;\n" +
            "varying vec2 textureCoordinate;\n" +
            "uniform samplerExternalOES sTexture;\n" +
            "void main() {\n" +
            "  gl_FragColor = texture2D(sTexture, textureCoordinate);\n" +
            "}";

    private final float[] overlaySquareCoords = {-1.0f, 1.0f, -1.0f, -1.0f, 1.0f, -1.0f, 1.0f, 1.0f};
    private final float[] textureVertices = {0f, 0f, 0f, 1f, 1f, 1f, 1f, 0f,};
    private final short[] drawOrder = {0, 1, 2, 0, 2, 3}; // order to draw vertices
    private final int COORDS_PER_VERTEX = 2;
    private final int vertexStride = COORDS_PER_VERTEX * 4; // 4 bytes per

    private FloatBuffer vertexBuffer, textureVerticesBuffer;
    private ShortBuffer drawListBuffer;

    private int mProgram = 0;
    private int vertexShader = 0;
    private int fragmentShader = 0;
    private int texConnector;
    private int texExternal;
    private int texOverlay;
    private int mPositionHandle;
    private int mTextureCoordHandle;

    public TextureRenderer() {
    }

    public int getConnectorTextureId() {
        return texConnector;
    }

    public int getExternalTextureId() {
        return texExternal;
    }

    public int getOverlayTextureId() {
        return texOverlay;
    }

    private int mFps = 0;
    private long mLastTime = 0;

    private void getFps() {
        mFps++;
        long timeStamp = System.currentTimeMillis();
        if (timeStamp - mLastTime >= 1000) {
            AnFileLog.e("TextureRenderer", "fps ==>" + mFps);
            mFps = 0;
            mLastTime = timeStamp;
        }
    }


    public void drawFrame(Rect connectorRect, Rect externalRect, boolean isShowOverlay, int HUScreenWidth, int HUScreenHeight) {
        if (mProgram == 0) {
            return;
        }

        getFps();

        GLES20.glClearColor(0, 0, 0, 0);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
        GLES20.glActiveTexture(GLES20.GL_TEXTURE0);

        GLES20.glEnableVertexAttribArray(mTextureCoordHandle);
        GLES20.glVertexAttribPointer(mTextureCoordHandle, COORDS_PER_VERTEX,
            GLES20.GL_FLOAT, false, vertexStride, textureVerticesBuffer);

        GLES20.glBindBuffer(GLES20.GL_ELEMENT_ARRAY_BUFFER, 0);

        GLES20.glEnableVertexAttribArray(mPositionHandle);

        GLES20.glEnable(GLES20.GL_BLEND);
        GLES20.glBlendFunc(GLES20.GL_ONE, GLES20.GL_ONE_MINUS_SRC_ALPHA);

        GLES20.glUseProgram(mProgram);


        /*********** Connector ***********/
        float[] connectorSquareCoords = {
            -1.0f + (2.0f / HUScreenWidth * connectorRect.left),
            1.0f - (2.0f / HUScreenHeight * connectorRect.top),
            -1.0f + (2.0f / HUScreenWidth * connectorRect.left),
            1.0f - (2.0f / HUScreenHeight * connectorRect.bottom),
            -1.0f + (2.0f / HUScreenWidth * connectorRect.right),
            1.0f - (2.0f / HUScreenHeight * connectorRect.bottom),
            -1.0f + (2.0f / HUScreenWidth * connectorRect.right),
            1.0f - (2.0f / HUScreenHeight * connectorRect.top)
        };
        vertexBuffer.put(connectorSquareCoords);
        vertexBuffer.position(0);
        GLES20.glVertexAttribPointer(mPositionHandle, COORDS_PER_VERTEX,
            GLES20.GL_FLOAT, false, vertexStride, vertexBuffer);
        GLES20.glBindBuffer(GLES20.GL_ARRAY_BUFFER, 0);
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texConnector);
        GLES20.glDrawElements(GLES20.GL_TRIANGLES, drawOrder.length,
            GLES20.GL_UNSIGNED_SHORT, drawListBuffer);
        /********************************/

        /*********** External ***********/
        if (externalRect != null) {
            float[] externalSquareCoords = {
                -1.0f + (2.0f / HUScreenWidth * externalRect.left),
                1.0f - (2.0f / HUScreenHeight * externalRect.top),
                -1.0f + (2.0f / HUScreenWidth * externalRect.left),
                1.0f - (2.0f / HUScreenHeight * externalRect.bottom),
                -1.0f + (2.0f / HUScreenWidth * externalRect.right),
                1.0f - (2.0f / HUScreenHeight * externalRect.bottom),
                -1.0f + (2.0f / HUScreenWidth * externalRect.right),
                1.0f - (2.0f / HUScreenHeight * externalRect.top)
            };
            vertexBuffer.put(externalSquareCoords);
            vertexBuffer.position(0);
            GLES20.glVertexAttribPointer(mPositionHandle, COORDS_PER_VERTEX,
                GLES20.GL_FLOAT, false, vertexStride, vertexBuffer);
            GLES20.glBindBuffer(GLES20.GL_ARRAY_BUFFER, 0);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texExternal);
            GLES20.glDrawElements(GLES20.GL_TRIANGLES, drawOrder.length,
                GLES20.GL_UNSIGNED_SHORT, drawListBuffer);
        }
        /*******************************/

        /*********** Overlay ***********/
        if (isShowOverlay) {
            vertexBuffer.put(overlaySquareCoords);
            vertexBuffer.position(0);
            GLES20.glVertexAttribPointer(mPositionHandle, COORDS_PER_VERTEX,
                GLES20.GL_FLOAT, false, vertexStride, vertexBuffer);
            GLES20.glBindBuffer(GLES20.GL_ARRAY_BUFFER, 0);
            GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texOverlay);
            GLES20.glDrawElements(GLES20.GL_TRIANGLES, drawOrder.length,
                GLES20.GL_UNSIGNED_SHORT, drawListBuffer);
        }
        /******************************/

        if (AnFileLog.isEnable()) {
            GLES20.glEnable(GLES20.GL_SCISSOR_TEST);
            GLES20.glScissor(0, 0, 100, 100);
            GLES20.glClearColor((float) Math.random(), (float) Math.random(), (float) Math.random(), 0);
            GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
            GLES20.glDisable(GLES20.GL_SCISSOR_TEST);
        }

        GLES20.glDisable(GLES20.GL_BLEND);
        GLES20.glDisableVertexAttribArray(mPositionHandle);
        GLES20.glDisableVertexAttribArray(mTextureCoordHandle);
    }

    public void surfaceCreated() {
        createProgram();

        int[] textures = new int[1];

        GLES20.glGenTextures(1, textures, 0);
        texConnector = textures[0];
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texConnector);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_NEAREST);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_S, GLES20.GL_CLAMP_TO_EDGE);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_T, GLES20.GL_CLAMP_TO_EDGE);

        GLES20.glGenTextures(1, textures, 0);
        texExternal = textures[0];
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texExternal);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_NEAREST);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_S, GLES20.GL_CLAMP_TO_EDGE);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_T, GLES20.GL_CLAMP_TO_EDGE);

        GLES20.glGenTextures(1, textures, 0);
        texOverlay = textures[0];
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texOverlay);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_NEAREST);
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_S, GLES20.GL_CLAMP_TO_EDGE);
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_T, GLES20.GL_CLAMP_TO_EDGE);
    }

    public void release() {
        GLES20.glDeleteProgram(mProgram);
        mProgram = 0;
        GLES20.glDeleteShader(vertexShader);
        GLES20.glDeleteShader(fragmentShader);
        int[] textures = new int[1];

        textures[0] = texConnector;
        GLES20.glDeleteTextures(1, textures, 0);

        textures[0] = texExternal;
        GLES20.glDeleteTextures(1, textures, 0);

        textures[0] = texOverlay;
        GLES20.glDeleteTextures(1, textures, 0);
    }

    private int loadShader(int type, String shaderCode) {
        int shader = GLES20.glCreateShader(type);

        GLES20.glShaderSource(shader, shaderCode);
        GLES20.glCompileShader(shader);

        return shader;
    }

    private void createProgram() {
        // initialize vertex byte buffer for shape coordinates
        ByteBuffer bb = ByteBuffer.allocateDirect(overlaySquareCoords.length * 4);
        bb.order(ByteOrder.nativeOrder());
        vertexBuffer = bb.asFloatBuffer();

        // initialize byte buffer for the draw list
        ByteBuffer dlb = ByteBuffer.allocateDirect(drawOrder.length * 2);
        dlb.order(ByteOrder.nativeOrder());
        drawListBuffer = dlb.asShortBuffer();
        drawListBuffer.put(drawOrder);
        drawListBuffer.position(0);

        ByteBuffer bb2 = ByteBuffer.allocateDirect(textureVertices.length * 4);
        bb2.order(ByteOrder.nativeOrder());
        textureVerticesBuffer = bb2.asFloatBuffer();
        textureVerticesBuffer.put(textureVertices);
        textureVerticesBuffer.position(0);

        vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, vertexShaderCode);
        fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, fragmentShaderCode);

        mProgram = GLES20.glCreateProgram();
        GLES20.glAttachShader(mProgram, vertexShader);
        GLES20.glAttachShader(mProgram, fragmentShader);
        GLES20.glLinkProgram(mProgram);
        mPositionHandle = GLES20.glGetAttribLocation(mProgram, "vPosition");
        mTextureCoordHandle = GLES20.glGetAttribLocation(mProgram, "inputTextureCoordinate");
    }
}
