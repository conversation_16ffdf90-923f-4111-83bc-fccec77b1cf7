<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.autoai.welink.autoproxy.server" >
    <uses-permission android:name="android.permission.INTERNET" />
    <application>
        <service
            android:name="com.autoai.welink.autoproxy.server.code.ServerService"
            android:process=":wlprimary"
            android:exported="false">
            <intent-filter>
                <action android:name="com.autoai.welink.autoproxy.server.IServerService" />
            </intent-filter>
        </service>
    </application>
</manifest>

