android {
    apply from: "../config.gradle"
    apply from: "../versions.gradle"

    compileSdkVersion project.COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
//        consumerProguardFiles "consumer-rules.pro"

        buildConfigField "long", "VERSION_CODE", String.valueOf(versionCode)
        buildConfigField "String", "VERSION_NAME", "\"" + versionName + "\""
    }

    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}