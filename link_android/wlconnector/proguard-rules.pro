# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-keep public class com.autoai.welink.auto.WLConfiguration { public *; }
-keep public interface com.autoai.welink.auto.WLConnectListener { public *; }
-keep public class com.autoai.welink.auto.WLConnector { public *; }
-keep public interface com.autoai.welink.auto.WLMusic { public *; }
-keep public interface com.autoai.welink.auto.WLMusic$Callback { public *; }
-keep public interface com.autoai.welink.auto.WLSound { public *; }
-keep public interface com.autoai.welink.auto.WLSound$Callback { public *; }
-keep public interface com.autoai.welink.auto.WLMicrophone { public *; }
-keep public interface com.autoai.welink.auto.WLMicrophone$Callback { public *; }
-keep public interface com.autoai.welink.auto.WLBluetoothPhone { public *; }
-keep public interface com.autoai.welink.auto.WLBluetoothPhone$Callback { public *; }
-keep public interface com.autoai.welink.auto.WLTBTInfo { public *; }

-keep public interface com.autoai.welink.auto.client.WLAutoConnectListener { public *; }

-keepattributes Exceptions, InnerClasses
-keep public interface com.autoai.welink.auto.client.IClientService { *; }
-keep public class com.autoai.welink.auto.client.IClientService$Stub { *; }
-keep public interface com.autoai.welink.autoproxy.server.IServerService { *; }
-keep public class com.autoai.welink.autoproxy.server.IServerService$Stub { *; }

-repackageclasses com.autoai.welink.auto
