package com.autoai.welink.auto;

import android.content.Context;

import com.autoai.welink.auto.v1.AnFileLog;
import com.autoai.welink.auto.v1.Connector;
import com.autoai.welink.auto.connector.BuildConfig;

public abstract class WLConnector {
	/***
	 * 禁止包外构建WLConnector对象，只能使用connect构建WLConnector的单例对象
	 * @param context Application Context
	 */
	protected WLConnector(Context context) {
	}

	/***
	 * 获取功能库版本号
	 * @return 返回功能库版本号字符串
	 */
	public static String getVersion() {
		return BuildConfig.VERSION_NAME + "/" + BuildConfig.BUILD_TYPE;
	}

    /**
     * App 连接 WeLink 服务
     *
     * @param context Application Context
     * @param connectStr 连接字符串，格式如：welink://:47529/connect?ver=1&check=vxcirb
     * @param listener 连接状态回调
     * @return false表示还有WLConnector的实例未释放
     */
    public static boolean connect(Context context, String connectStr, WLConnectListener listener) {
        AnFileLog.init(context);

        return Connector.connect(context, connectStr, listener);
    }

	/**
	 * 主动断开于WeLink app的连接, 也会触发onDisconnnected回调
	 */
	public abstract void disconnect();

	/**
	 * 释放WLConnector对象
	 */
	public abstract void release();

    /**
     * 查询连接配置信息
     * @return 返回连接配置信息供查询
     */
    public abstract WLConfiguration getConfiguration();

    public static void enableLogCat(boolean enable) {
        AnFileLog.enableLogCat(enable);
    }

    public static void enableLogFile(boolean enable) {
        AnFileLog.enableLogFile(enable);
    }
}
