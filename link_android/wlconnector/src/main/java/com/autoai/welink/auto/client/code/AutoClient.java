package com.autoai.welink.auto.client.code;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.MemoryFile;
import android.os.ParcelFileDescriptor;
import android.os.SharedMemory;

import com.autoai.welink.auto.client.WLAutoConnectListener;
import com.autoai.welink.auto.client.WLAutoDataListener;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.AnFileLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.FileDescriptor;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.util.concurrent.CountDownLatch;

import static com.autoai.welink.auto.client.WLAutoConnectListener.CONNECT_WELINK_APP_ERROR;
import static java.lang.Thread.sleep;

/**
 * Created by gaole on 2018/11/14.
 */

public class AutoClient {
    private final WLAutoConnectListener connectListener;
    private String connectStr_all;
    private String randomStr;
    private int serverPort;
    private final String serverIP = "127.0.0.1";
    private int maxVer;
    private int minVer;
    private final Context mContext;
    private WLAutoDataListener dataListener;
    private Socket clientSocket;
    private final Object clientSocketLock = new Object();
    private boolean isConnectAddressCorrect = false;

    private static final int MAGIC_NUM = 0x78967896;
    private static final int LINK_TYPE = 1000000;
    private static final int TRANSIMISSON_TYPE_COMMAND = 13;//车机命令
    private static final int INT_TYPE_LEN = 4; //int 类型数据长度
    private static final int LONG_TYPE_LEN = 8; //long类型数据长度
    private static final int HEAD_DATA_LEN = INT_TYPE_LEN + INT_TYPE_LEN + LONG_TYPE_LEN + INT_TYPE_LEN; //数据头组成int（magicNum）+int（type）+ long（发送数据距离上一次发送数据的时间差）+int（数据长度）
    private final byte[] headData = new byte[HEAD_DATA_LEN];
    private boolean isDisconnected = false;

    private static Server server;
    private final int threadSleepTime = 1;

    /**
     * 创建
     */
    public AutoClient(Context context, WLAutoConnectListener lister) {
        mContext = context;
        connectListener = lister;
    }


    private void release() {
        isDisconnected = true;

        if (server != null) {
            server.release(mContext);
            server = null;
        }
    }

    /**
     * 开始连接
     *
     * @param connectStr 校验字符串 welink://:47529/connect?ver=1&check=vxcirb
     * @param maxVersion WelinkConnect SDK 支持的最大版本号
     * @param minVersion WelinkConnect SDK 支持的最小版本号
     */
    public void autoConnect(String connectStr, int maxVersion, int minVersion) {
        AnFileLog.e("AAAASDF","client autoConnect  " + connectStr);
        connectStr_all=connectStr;
        if (null == connectStr || 0 > maxVersion || 0 > minVersion) {
            return;
        }

        maxVer = maxVersion;
        minVer = minVersion;

        Uri uri = Uri.parse(connectStr);

        if (uri != null) {
            String scheme = uri.getScheme();
            String function = uri.getPath();
            String version = uri.getQueryParameter("ver");

            if (scheme.equals("welink") && function.equals("/connect") && version.equals("1")) {
                serverPort = uri.getPort();
                randomStr = uri.getQueryParameter("check");

                if (serverPort > 0) {
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                clientSocket = new Socket(serverIP, serverPort);
                                //请求建立连接
                                requestConnect();
                                new SocketThread(clientSocket).start();

                            } catch (IOException e) {
                                e.printStackTrace();
                            }

                        }
                    }).start();

                    int timeOutCount = 0;
                    while (!isConnectAddressCorrect) {
                        try {
                            sleep(threadSleepTime);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        timeOutCount++;
                        //设置超时2s
                        if (2000 < timeOutCount*threadSleepTime) {
                            break;
                        }
                    }
                }
            }
        }

        if (!isConnectAddressCorrect && !isDisconnected) {
            connectListener.onError(CONNECT_WELINK_APP_ERROR);
            socketClose();
        }
    }

    /**
     * 断开连接
     * {"Request":"Disconnect"}
     */
    public void disconnect() {
        AnFileLog.e("aidl_c","disconnect");
        JSONObject obj = new JSONObject();
        try {
            obj.put("Request", "Disconnect");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (server != null)
            server.sendData(connectStr_all, TRANSIMISSON_TYPE_COMMAND, obj.toString().getBytes());
        //释放socket資源
        socketClose();
    }

    /**
     * 添加 WLAutoDataListener监听
     *
     * @param listener WLAutoDataListener监听
     */
    public void setAutoDataListener(WLAutoDataListener listener) {
        dataListener = listener;
        Server.setWLAutoDataListener(listener);
    }

    /**
     * 发送数据
     *
     * @param type  发送的数据类型
     * @param bytes 发送的二进制数据
     */
    public void sendDataByAIDL(int type, byte[] bytes, byte[] bytes2) {
        AnFileLog.e("aidl_c","sendDataByAIDL type="+type+",bytes.length="+bytes.length+",server="+server+",connectStr="+connectStr_all);

        byte[] bb=bytes;
        if (bytes2!=null) {
            try {
                ByteArrayOutputStream bos=new ByteArrayOutputStream();
                bos.write(intToByteArray(bytes.length));
                bos.write(bytes);
                bos.write(bytes2);
                bb=bos.toByteArray();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        if (server != null) {
            server.sendData(connectStr_all, type, bb);
        }
    }

    public void remove(String filename) {
        if (server != null) {
            server.remove(filename);
        }
    }

    public boolean create(String filename, byte[] buffer) {
        if (server != null) {
            return server.create(filename, buffer);
        }

        return false;
    }

    public byte[] read(String filename) {
        if (server != null) {
            return server.read(filename);
        }

        return null;
    }

    public boolean isMirrorScreen() {
        if (server != null) {
            return server.isMirrorScreen();
        }

        return false;
    }

    public static ParcelFileDescriptor getFileDescriptor(String connectStr, String filename) {
        if (server != null) {
            return server.getFileDescriptor(connectStr, filename);
        }

        return null;
    }

    public static SharedMemory getSharedMemory(String connectStr, String filename) {
        if (server != null) {
            return server.getSharedMemory(connectStr, filename);
        }

        return null;
    }

    public static int length(String connectStr, String filename) {
        if (server != null) {
            return server.length(connectStr, filename);
        }

        return 0;
    }

    public static void pauseMirrorScreen(String connectStr) {
        if (server != null) {
            server.pauseMirrorScreen(connectStr);
        }
    }

    public static void resumeMirrorScreen(String connectStr) {
        if (server != null) {
            server.resumeMirrorScreen(connectStr);
        }
    }

    public static void onDataRecv(String connectStr, int type, byte[] bytes) {
        if (server != null) {
            server.onDataRecv(connectStr, type, bytes);
        }
    }

    private void sendData(int type, byte[] bytes, byte[] bytes2) {
        synchronized (clientSocketLock) {
            if (clientSocket != null) {
                try {
                    if (bytes2 != null) {
                        clientSocket.getOutputStream().write(getHead(type, INT_TYPE_LEN + bytes.length + bytes2.length));
                        clientSocket.getOutputStream().write(intToByteArray(bytes.length));
                        clientSocket.getOutputStream().write(bytes);
                        clientSocket.getOutputStream().write(bytes2);
                        clientSocket.getOutputStream().flush();
                    } else {
                        clientSocket.getOutputStream().write(getHead(type, bytes.length));
                        clientSocket.getOutputStream().write(bytes);
                        clientSocket.getOutputStream().flush();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 发送数据
     *
     * @param type  发送的数据类型
     * @param bytes 发送的二进制数据
     */
    private void sendData(int type, byte[] bytes) {
        sendData(type, bytes, null);
    }

    /**
     * 发送的数据添加数据头
     * int（magicNum）+int（type）+ long（发送数据距离上一次发送数据的时间差）+int（数据长度）
     *
     * @param type linkType以上是link内部数据，以下是外部数据
     * @param bytes 发送的数据
     * @return
     */
    private static long startTime1 = System.currentTimeMillis();

    private byte[] getHead(int type, int byteLen) {
        System.arraycopy(intToByteArray(MAGIC_NUM), 0, headData, 0, INT_TYPE_LEN);
        System.arraycopy(intToByteArray(type), 0, headData, INT_TYPE_LEN, INT_TYPE_LEN);

        long coastTime = System.currentTimeMillis() - startTime1;
        startTime1 = System.currentTimeMillis();
        byte[] a = longToBytes(coastTime);
        System.arraycopy(a, 0, headData, INT_TYPE_LEN + INT_TYPE_LEN, LONG_TYPE_LEN);
        AnFileLog.e("AAAASDF","client senddata type = " + type + ", Len = " + byteLen);

        System.arraycopy(intToByteArray(byteLen), 0, headData, INT_TYPE_LEN + INT_TYPE_LEN + LONG_TYPE_LEN, INT_TYPE_LEN);

        return headData;
    }

    //byte 数组与 long 的相互转换
    private static final ByteBuffer sendBuffer = ByteBuffer.allocate(LONG_TYPE_LEN);

    private static byte[] longToBytes(long x) {
        sendBuffer.clear();
        sendBuffer.putLong(0, x);
        return sendBuffer.array();
    }

    private static final ByteBuffer reciveBuffer = ByteBuffer.allocate(LONG_TYPE_LEN);

    private static long bytesToLong(byte[] bytes) {
        reciveBuffer.clear();
        reciveBuffer.put(bytes, 0, bytes.length);
        reciveBuffer.flip();//need flip
        return reciveBuffer.getLong();
    }

    /**
     * int转byte[]
     */
    private static byte[] intToByteArray(int a) {
        return new byte[]{
            (byte) ((a >> 24) & 0xFF),
            (byte) ((a >> 16) & 0xFF),
            (byte) ((a >> 8) & 0xFF),
            (byte) (a & 0xFF)
        };
    }

    /**
     * 请求建立连接
     * {"Request”:"Connect","MaxVersion":5,"MinVersion":5,"AppType":"navi”,”RandomStr”:”asdfghn”}
     */
    private void requestConnect() {
        try {
            JSONObject obj = new JSONObject();
            obj.put("Request", "Connect");
            obj.put("MaxVersion", maxVer);
            obj.put("MinVersion", minVer);
            obj.put("RandomStr", randomStr);

            sendData(LINK_TYPE, obj.toString().getBytes());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //将byte数组置空
    private static byte[] resetArray(byte[] a) {
        byte[] b2 = new byte[a.length];
        for (int i = 0; i < a.length; i++) {
            a[i] = b2[i];
        }
        return a;
    }

    //byte[]转int
    private static int byteArrayToInt(byte[] b) {
        return b[3] & 0xFF |
            (b[2] & 0xFF) << 8 |
            (b[1] & 0xFF) << 16 |
            (b[0] & 0xFF) << 24;
    }

    private void socketClose() {
        AnFileLog.e("AAAASDF","socketClose");
        synchronized (clientSocketLock) {
            if (null != clientSocket) {
                try {
                    clientSocket.close();
                    clientSocket = null;
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        release();
    }

    class SocketThread extends Thread {
       final Socket inSocket;
        final byte[] serverData = new byte[1024 * 1024 * 4];
        Boolean isHeadData = true;

        final ByteArrayOutputStream reciveHeadData = new ByteArrayOutputStream(HEAD_DATA_LEN);
        int reciveHeadDataLen = 0;
        final ByteArrayOutputStream actualReciveData = new ByteArrayOutputStream(1024 * 1024 * 4);
        int actualReciveDataLen = 0;
        final  byte[] reciveDataMagicNumByte = new byte[INT_TYPE_LEN];
        int reciveDataMagicNum = 0;
        final  byte[] reciveDataTypeByte = new byte[INT_TYPE_LEN];
        int reciveDataType = 0;
        final byte[] reciveDataTimeByte = new byte[LONG_TYPE_LEN];
        long reciveDataTime = 0;
        final byte[] reciveDataLenByte = new byte[INT_TYPE_LEN];
        int reciveDataLen = 0;

         public SocketThread(Socket socket) {
            inSocket = socket;
            isDisconnected = false;
        }

        @Override
        public void run() {
            super.run();
            int len = -1;
            try {
                while (null != inSocket && (len = inSocket.getInputStream().read(serverData)) != -1) {
                    AnFileLog.e("AAAASDF", "socket read: " + len);
                    isConnectAddressCorrect = true;
                    //头数据
                    if (isHeadData) {
                        handleClientHeadData(len, 0);
                    } else {

                        handleClientData(len, 0);
                    }

                    len = -1;
                }
            } catch (IOException e1) {
                e1.printStackTrace();
            }

            AnFileLog.e("AAAASDF", "AutoClient exit 1: " + inSocket + ", " + len);
            synchronized (clientSocketLock) {
                if (null != clientSocket) {
                    try {
                        clientSocket.close();
                        clientSocket = null;
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            AnFileLog.e("AAAASDF", "AutoClient exit 2");
        }

        long startTime2 = System.currentTimeMillis();

        private void handleClientHeadData(int totalLen, int offside) {
            int remainHeadDataLen = HEAD_DATA_LEN - reciveHeadDataLen;
            int remainDatalen = totalLen - remainHeadDataLen;
            if (remainDatalen < 0) {
                remainHeadDataLen = totalLen;
            }

            reciveHeadData.write(serverData, offside, remainHeadDataLen);
            reciveHeadDataLen += remainHeadDataLen;

            if (HEAD_DATA_LEN == reciveHeadDataLen) {

                System.arraycopy(reciveHeadData.toByteArray(), 0, reciveDataMagicNumByte, 0, INT_TYPE_LEN);
                System.arraycopy(reciveHeadData.toByteArray(), INT_TYPE_LEN, reciveDataTypeByte, 0, INT_TYPE_LEN);
                System.arraycopy(reciveHeadData.toByteArray(), INT_TYPE_LEN + INT_TYPE_LEN, reciveDataTimeByte, 0, LONG_TYPE_LEN);
                System.arraycopy(reciveHeadData.toByteArray(), INT_TYPE_LEN + INT_TYPE_LEN + LONG_TYPE_LEN, reciveDataLenByte, 0, INT_TYPE_LEN);

                reciveDataMagicNum = byteArrayToInt(reciveDataMagicNumByte);
                if (MAGIC_NUM != reciveDataMagicNum) {
                    return;
                }

                reciveDataType = byteArrayToInt(reciveDataTypeByte);
                reciveDataTime = bytesToLong(reciveDataTimeByte);
                reciveDataLen = byteArrayToInt(reciveDataLenByte);

                long coastTime = System.currentTimeMillis() - startTime2;
                startTime2 = System.currentTimeMillis();

                //清空
                reciveHeadData.reset();
                reciveHeadDataLen = 0;
                resetArray(reciveDataMagicNumByte);
                reciveDataMagicNum = 0;
                resetArray(reciveDataTypeByte);
                resetArray(reciveDataTimeByte);
                reciveDataTime = 0;
                resetArray(reciveDataLenByte);
                isHeadData = false;
                actualReciveDataLen = 0;

                if (remainDatalen > 0) {
                    handleClientData(remainDatalen, offside + remainHeadDataLen);
                }
            }
        }

        private void handleClientData(int totalLen, int offside) {
            int remainDataLen = reciveDataLen - actualReciveDataLen;
            int remainHeadDataLen = totalLen - remainDataLen;
            if (remainHeadDataLen < 0) {
                remainDataLen = totalLen;
            }
            actualReciveData.write(serverData, offside, remainDataLen);
            actualReciveDataLen += remainDataLen;

            if (reciveDataLen == actualReciveDataLen) {
                handleSocketInPutStream(actualReciveData.toByteArray());

                //清空
                actualReciveData.reset();
                actualReciveDataLen = 0;
                reciveDataLen = 0;
                reciveDataType = 0;
                isHeadData = true;

                if (remainHeadDataLen > 0) {
                    handleClientHeadData(remainHeadDataLen, offside + remainDataLen);
                }
            }
        }

        private void handleSocketInPutStream(byte[] clientSentence) {
            AnFileLog.e("AAAASDF","client recivedata type = " + reciveDataType + ", Len = " + clientSentence.length);

            if (reciveDataType >= LINK_TYPE) {
                try {
                    JSONObject jsonObject = new JSONObject(new String(clientSentence));
                    if (null == jsonObject) {
                        return;
                    }
                    AnFileLog.e("AAAASDF","client handleSocketInPutStream  " + jsonObject);

                    if (jsonObject.has("Response")
                        && jsonObject.optString("Response").equals("Connect")) {
                        handleConnectResult(jsonObject);
                    }

                    if (jsonObject.has("Request")
                        && jsonObject.optString("Request").equals("Disconnect")) {

                        if (!isDisconnected) {
                            connectListener.onDisconnected();
                            socketClose();
                        }
                    }

                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else {
                //处理其他数据
                dataListener.onReciveData(reciveDataType, clientSentence);
            }
        }
    }

    private boolean isServerCodec;

    /**
     * 连接是否成功
     * server端确定采用的协议版本号、车机分辨率{“Response":"CONNECT","ServerVersion":5,"HUScreenWidth":100,"HUScreenHeight":100,"IsSupportWiFiChannel":false, "Dpi":123, "IsServerCodec":false}
     * 应用类型不对，不同意建立连接，连接失败 {“Response":"CONNECT","ErrorCode”:101}
     *
     * @param jsonObject
     */
    private void handleConnectResult(JSONObject jsonObject) {
        AnFileLog.e("AAAASDF", "handleConnectResult");
        if (jsonObject.has("ErrorCode")) {
            if (!isDisconnected) {
                connectListener.onError(jsonObject.optInt("ErrorCode"));
                socketClose();
            }
        } else {
            int serverVersion = jsonObject.optInt("ServerVersion");
            int HUScreenWidth = jsonObject.optInt("HUScreenWidth");
            int HUScreenHeight = jsonObject.optInt("HUScreenHeight");
            int dpi = jsonObject.optInt("Dpi");
            int fps = jsonObject.optInt("Fps");
            int pcm = jsonObject.optInt("Pcm");
            int pcmCache = jsonObject.optInt("PcmCache");
            boolean isSupportWiFiChannel = jsonObject.optBoolean("IsSupportWiFiChannel");
            boolean isServerCodec = jsonObject.optBoolean("IsServerCodec");
            String packageName = jsonObject.optString("PackageName");
            int cap = jsonObject.optInt("CAP");

            String VehicleType = "";
            String VehicleID = "";
            String UserID = "";
            if (jsonObject.has("VehicleType"))
                VehicleType = jsonObject.optString("VehicleType");
            if (jsonObject.has("VehicleID"))
                VehicleID = jsonObject.optString("VehicleID");
            if (jsonObject.has("UserID"))
                UserID = jsonObject.optString("UserID");

            try {
                server = new Server(mContext, connectListener, packageName, randomStr);
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (server != null) {
                WLConfigurationImp wlConfiguration = new WLConfigurationImp(serverVersion, HUScreenWidth, HUScreenHeight, dpi, fps, pcm, pcmCache, isSupportWiFiChannel, isServerCodec, VehicleType, VehicleID, UserID, cap);

                if (!connectListener.onConnected(wlConfiguration)) {
                    server.release(mContext);
                    server = null;
                }

                AnFileLog.e("AAAASDF", "AutoClient exit 11: " + clientSocket);
                synchronized (clientSocketLock) {
                    if (null != clientSocket) {
                        try {
                            clientSocket.close();
                            clientSocket = null;
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
                AnFileLog.e("AAAASDF", "AutoClient exit 2");
            } else {
                connectResult(false);
            }
        }
    }

    private int touchEventPort = 0;
    private ServerSocket touchEventSocket = null;
    private Socket serverTouchEventSocket;

    /**
     * 回复server端是否连接成功
     * {"Connect”:”Success",”TouchEventPort”:12345,”VideoPort”:12345,”AudioPort”:12345, ”VoiceDataPort”:12345}
     * {"Connect”:”Fail”}
     *
     * @param isSuccess
     */
    public void connectResult(boolean isSuccess) {
        try {
            JSONObject obj = new JSONObject();

            if (isSuccess) {
                createDataSocket();

                obj.put("Connect", "Success");
                obj.put("TouchEventPort", touchEventPort);

            } else {
                obj.put("Connect", "Fail");
            }

            sendData(LINK_TYPE, obj.toString().getBytes());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private final CountDownLatch socketCountDownLockObject = new CountDownLatch(1);
    private void createDataSocket() {
        createTouchEventDataSocket();

        try {
           socketCountDownLockObject.await();

        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void createTouchEventDataSocket() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    if (!isServerCodec) {
                        touchEventSocket = new ServerSocket(0);
                    } else {
                        touchEventSocket = new ServerSocket(0, 10, InetAddress.getByName("127.0.0.1"));
                    }
                    touchEventPort = touchEventSocket.getLocalPort();

                    socketCountDownLockObject.countDown();

                    serverTouchEventSocket = touchEventSocket.accept();
                    new TouchEventSocketThread(serverTouchEventSocket).start();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    class TouchEventSocketThread extends Thread {
        final byte[] videoData = new byte[1024 * 1024 * 4];
        boolean isVideoHeadData = true;
        final int videoDataHeadLen = INT_TYPE_LEN + INT_TYPE_LEN;//数据总长度+数据类型
        final ByteArrayOutputStream videoDataHeadStream = new ByteArrayOutputStream(videoDataHeadLen);
        final ByteArrayOutputStream videoDataStream = new ByteArrayOutputStream(1024*1024);
        int reciveVideoDataHeadLen = 0;
        int reciveVideoDataLen = 0;
        final byte[] videoDataLenByte = new byte[INT_TYPE_LEN];
        int videoDataLen = 0;
        final byte[] dataTypeByte = new byte[INT_TYPE_LEN];
        int dataType = 0;
        final  Socket touchEventInSocket;

        public TouchEventSocketThread(Socket socket) {
            touchEventInSocket = socket;
        }

        @Override
        public void run() {
            super.run();
            int len = -1;
            try {
                while (null != touchEventInSocket && (len = touchEventInSocket.getInputStream().read(videoData)) != -1) {
//                    头数据
                    if (isVideoHeadData) {
                        handleVideoDataHead(len, 0);
                    }
                    else {
                        handleVideoData(len, 0);
                    }

                    len = -1;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        private void handleVideoDataHead(int totalLen, int offside) {
            int needDataLen = videoDataHeadLen - reciveVideoDataHeadLen;
            int remainDataLen = totalLen - needDataLen;
            if (remainDataLen >= 0) {
                videoDataHeadStream.write(videoData, offside, needDataLen);
                System.arraycopy(videoDataHeadStream.toByteArray(), 0, videoDataLenByte, 0, INT_TYPE_LEN);
                System.arraycopy(videoDataHeadStream.toByteArray(), INT_TYPE_LEN, dataTypeByte, 0, INT_TYPE_LEN);

                videoDataLen = byteArrayToInt(videoDataLenByte);
                dataType = byteArrayToInt(dataTypeByte);
                AnFileLog.e("BAAAASDF","client TouchEventSocketThread videoDataLen  " + videoDataLen+", type = "+dataType);

                isVideoHeadData = false;
                videoDataStream.reset();
                reciveVideoDataLen = 0;

                if (remainDataLen >0) {
                    handleVideoData(remainDataLen, offside+needDataLen);
                }
            }
            else {
                videoDataHeadStream.write(videoData, offside, totalLen);
                reciveVideoDataHeadLen += totalLen;
            }
        }

        private void handleVideoData(int totalLen, int offside) {
            int needDataLen = videoDataLen - reciveVideoDataLen;
            int remainDataLen = totalLen - needDataLen;
            if (remainDataLen >= 0) {
                videoDataStream.write(videoData, offside,needDataLen);

                //处理Video数据
                if (null != dataListener) {
                    dataListener.onReciveData(dataType, videoDataStream.toByteArray());
                }

                isVideoHeadData = true;
                videoDataHeadStream.reset();
                resetArray(videoDataLenByte);
                resetArray(dataTypeByte);
                reciveVideoDataHeadLen = 0;
                videoDataLen = 0;
                dataType = 0;

                if (remainDataLen > 0) {
                    handleVideoDataHead(remainDataLen,offside + needDataLen);
                }
            }
            else {
                videoDataStream.write(videoData, offside, totalLen);
                reciveVideoDataLen += totalLen;
            }
        }
    }

    /**
     * 将得到的int类型的IP转换为String类型
     *
     * @param ip
     * @return
     */
    public static String intIP2StringIP(int ip) {
        return (ip & 0xFF) + "." +
            ((ip >> 8) & 0xFF) + "." +
            ((ip >> 16) & 0xFF) + "." +
            (ip >> 24 & 0xFF);
    }
}
