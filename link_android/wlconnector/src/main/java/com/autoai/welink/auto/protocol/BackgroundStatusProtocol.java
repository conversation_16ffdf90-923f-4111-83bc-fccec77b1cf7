package com.autoai.welink.auto.protocol;

import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.AnFileLog;
import com.autoai.welink.auto.v1.BackgroundNotify;

//前后台状态协议
public class BackgroundStatusProtocol extends ProtocolBase implements BackgroundNotify.Listener {
	public BackgroundStatusProtocol(Transmisson transmisson) {
		super(transmisson);
	}

	//前后台状态变化协议
	//本使用的协议字符串
	private static final String PROTOCOL_STRING_ONBACKGROUND = "onBackground";
    private static final String PROTOCOL_STRING_ONFOREGROUND = "onForeground";
    private static final String PROTOCOL_STRING_ONLOCKSCREEN = "onLockscreen";
    private static final String PROTOCOL_STRING_ONUNLOCKSCREEN = "onUnlockscreen";
	//=========================================================
	//每个协议默认都需要接收自己协议的数据包,此处传入的数据包只是协议自身的数据,不会包含底层模块自行增加的各种协议头
	@Override
	public void onReciveData(byte[] data) {
		String string = new String(data);
		if (string.equals(PROTOCOL_STRING_ONBACKGROUND)) {
			AnFileLog.e("wlconnector-protocol","recv protocol string="+PROTOCOL_STRING_ONBACKGROUND+", connectStr=" + transmisson.connectStr);
		} else if (string.equals(PROTOCOL_STRING_ONFOREGROUND)) {
			AnFileLog.e("wlconnector-protocol","recv protocol string="+PROTOCOL_STRING_ONFOREGROUND+", connectStr=" + transmisson.connectStr);
		} else if (string.equals(PROTOCOL_STRING_ONLOCKSCREEN)) {
			AnFileLog.e("wlconnector-protocol","recv protocol string="+PROTOCOL_STRING_ONLOCKSCREEN+", connectStr=" + transmisson.connectStr);
		} else if (string.equals(PROTOCOL_STRING_ONUNLOCKSCREEN)) {
			AnFileLog.e("wlconnector-protocol","recv protocol string="+PROTOCOL_STRING_ONUNLOCKSCREEN+", connectStr=" + transmisson.connectStr);
		}
	}

	//每个协议默认都需要响应网络状态,只处理自己关心的状态变化
	@Override
	public boolean onConnected(WLConfigurationImp wlConfiguration) {
		AnFileLog.e("wlconnector-protocol","BackgroundNotify-onHeartbeat");
		if (isForeground)
			transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONFOREGROUND.getBytes());
		else
			transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONBACKGROUND.getBytes());
		return false;
	}

	@Override
	public void onDisconnected() {

	}

	@Override
	public void onError(int errorCode) {

	}

	boolean isForeground =true;
	//=========================================================
	//实现BackgroundNotify.Listener
	@Override
	public void onForeground() {
		AnFileLog.e("wlconnector-protocol","BackgroundNotify-onForeground");
		isForeground =true;
		transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONFOREGROUND.getBytes());
	}

	@Override
	public void onBackground() {
		AnFileLog.e("wlconnector-protocol","BackgroundNotify-onBackground");
		isForeground =false;
		transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONBACKGROUND.getBytes());
	}

	@Override
	public void onLockscreen() {
		AnFileLog.e("wlconnector-protocol","BackgroundNotify-onLockscreen");
		transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONLOCKSCREEN.getBytes());
	}

	@Override
	public void onUnlockscreen() {
		AnFileLog.e("wlconnector-protocol","BackgroundNotify-onUnlockscreen");
		transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_BACKGROUND, PROTOCOL_STRING_ONUNLOCKSCREEN.getBytes());
	}
}
