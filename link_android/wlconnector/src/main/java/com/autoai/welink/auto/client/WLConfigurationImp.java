package com.autoai.welink.auto.client;

import android.app.Dialog;

import com.autoai.welink.auto.WLBluetoothPhone;
import com.autoai.welink.auto.WLConfiguration;
import com.autoai.welink.auto.WLMicrophone;
import com.autoai.welink.auto.WLMusic;
import com.autoai.welink.auto.WLSound;
import com.autoai.welink.auto.WLTBTInfo;

/**
 * 提供三投协议中的一些配置信息,供连接成功后只读查询各个模块需要的配置信息.
 */
public class WLConfigurationImp implements WLConfiguration {

    /***
     * 应用能力
     */
    private static final int WL_CAP_HARDWARE        = 1 << 0;
    private static final int WL_CAP_DISPLAY         = 1 << 1;
    private static final int WL_CAP_SOUND           = 1 << 2;
    private static final int WL_CAP_TBTINFO         = 1 << 3;
    private static final int WL_CAP_MUSIC           = 1 << 4;
    private static final int WL_CAP_MICROPHONE      = 1 << 5;
    private static final int WL_CAP_BLUETOOTHPHONE  = 1 << 6;

    private Dialog dialog;
    private WLMusic music;
    private WLTBTInfo tbtInfo;
    private WLSound sound;
    private WLMicrophone microphone;

    /**
     * 连接版本号.
     */
    private final int connectVersion;
    /**
     * 车机屏幕宽.
     */
    private final int HUScreenWidth;
    /**
     * 车机屏幕高.
     */
    private final int HUScreenHeight;
    /**
     * 车机屏幕密度.
     */
    private final int densityDpi;
    /**
     * 投屏帧率.
     */
    private final int fps;
    /**
     * 音乐PCM每包时间长度（毫秒）.
     */
    private final int pcm;
    /**
     * 音乐PCM数据缓存时长(毫秒），缓存大则音乐播放不会中断，但会导致歌曲切换、停止、暂停响应慢.
     */
    private final int pcmCache;
    /**
     * 是否支持Wi-Fi车机通道, 建议只在Wi-Fi手车互联下打开, 因为这会导致手机即便连接Wi-Fi也仍然使用移动网络.
     */
    private final boolean isSupportWiFiChannel;
    /**
     * WLServer负责H264的Codec.
     */
    private  final boolean isServerCodec;
    /**
     * 车辆类别,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL).
     */
    private final String VehicleType;
    /**
     * 车辆标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL).
     */
    private final String VehicleID;
    /**
     * 用户标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL).
     */
    private final String UserID;
    /*
     * 应用程序能力
     */
    private final int cap;

    /**
     * Instantiates a new Wl configuration.
     *
     * @param connectVersion       连接版本号
     * @param HUScreenWidth        车机屏幕宽
     * @param HUScreenHeight       车机屏幕高
     * @param densityDpi           车机屏幕密度
     * @param fps                  投屏帧率
     * @param pcm                  音乐PCM每包时间长度（毫秒）
     * @param pcmCache             音乐PCM数据缓存时长(毫秒），缓存大则音乐播放不会中断，但会导致歌曲切换、停止、暂停响应慢
     * @param isSupportWiFiChannel 是否支持Wi-Fi车机通道, 建议只在Wi-Fi手车互联下打开, 因为这会导致手机即便连接Wi-Fi也仍然使用移动网络
     * @param isServerCodec        WLServer负责H264的Codec
     * @param VehicleType            车辆类别,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL)
     * @param VehicleID            车辆标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL)
     * @param UserID               用户标识,以支持第三方app个性化,没有时默认为空字符串(是"",不是NULL)
     */
    public WLConfigurationImp(int connectVersion, int HUScreenWidth, int HUScreenHeight, int densityDpi, int fps, int pcm, int pcmCache, boolean isSupportWiFiChannel, boolean isServerCodec,String VehicleType, String VehicleID, String UserID, int cap) {
        this.dialog =  null;
        this.sound = null;
        this.music = null;
        this.tbtInfo = null;
        this.microphone = null;

        this.connectVersion = connectVersion;
        this.HUScreenWidth = HUScreenWidth;
        this.HUScreenHeight = HUScreenHeight;
        this.densityDpi = densityDpi;
        this.fps = fps;
        this.pcm = pcm;
        this.pcmCache = pcmCache;
        this.isSupportWiFiChannel = isSupportWiFiChannel;
        this.isServerCodec = isServerCodec;
        this.VehicleType = VehicleType;
        this.VehicleID = VehicleID;
        this.UserID = UserID;
        this.cap = cap;
    }

    public void setDisplayCapability(Dialog dialog) {
        this.dialog = dialog;
    }
    /**
     * 获取车机显示能力
     */
    public Dialog getDisplayCapability() {

        if((this.cap & WL_CAP_DISPLAY) == WL_CAP_DISPLAY)
            return dialog;

        return null;
    }

    public void setSoundCapability(WLSound sound) {
        this.sound = sound;
    }
    /**
     * 获取车机语音提示能力
     */
    public WLSound getSoundCapability() {

        if((this.cap & WL_CAP_SOUND) == WL_CAP_SOUND)
            return sound;

        return  null;
    }

    public void setTBTInfoCapability(WLTBTInfo tbtInfo) {
        this.tbtInfo = tbtInfo;
    }
    /**
     * 获取车机TBTInfo展示能力
     */
    public WLTBTInfo getTBTInfoCapability() {

        if((this.cap & WL_CAP_TBTINFO) == WL_CAP_TBTINFO)
            return tbtInfo;

        return  null;
    }

    public void setMusicCapability(WLMusic music) {
        this.music = music;
    }
    /**
     * 获取车机音乐播放能力
     */
    public WLMusic getMusicCapability() {

        if((this.cap & WL_CAP_MUSIC) == WL_CAP_MUSIC)
            return music;

        return  null;
    }

    public void setMicrophoneCapability(WLMicrophone microphone) {
        this.microphone = microphone;
    }
    /**
     * 获取车机音乐播放能力
     */
    public WLMicrophone getMicrophoneCapability() {
        if((this.cap & WL_CAP_MICROPHONE) == WL_CAP_MICROPHONE)
            return microphone;

        return  null;
    }

    public WLBluetoothPhone getBluetoothPhoneCapability() {
        return null;
    }

    /**
     * Gets 车机屏幕宽.
     *
     * @return 车机屏幕宽 hu screen width
     */
    public int getHUScreenWidth() {
        return HUScreenWidth;
    }

    /**
     * Gets 车机屏幕高.
     *
     * @return 车机屏幕高 hu screen height
     */
    public int getHUScreenHeight() {
        return HUScreenHeight;
    }

    /**
     * Gets 车机屏幕密度.
     *
     * @return 车机屏幕密度 density dpi
     */
    public int getHUDensityDpi() {
        return densityDpi;
    }

    /**
     * Gets 投屏帧率.
     *
     * @return 投屏帧率 fps
     */
    public int getFps() {
        return fps;
    }

    /**
     * Gets 音乐PCM每包时间长度（毫秒）.
     *
     * @return 音乐PCM每包时间长度 （毫秒）
     */
    public int getPcm() {
        return pcm;
    }

    /**
     * Gets 音乐PCM数据缓存时长(毫秒）.
     *
     * @return 音乐PCM数据缓存时长(毫秒 ） ， 缓存大则音乐播放不会中断 ， 但会导致歌曲切换 、 停止 、 暂停响应慢 pcm cache
     */
    public int getPcmCache() {
        return pcmCache;
    }

    /**
     * 是否支持Wi-Fi车机通道.
     *
     * @return 是否支持Wi -Fi车机通道, 建议只在Wi-Fi手车互联下打开, 因为这会导致手机即便连接Wi-Fi也仍然使用移动网络
     */
    public boolean isSupportWiFiChannel() {
        return isSupportWiFiChannel;
    }

    /**
     * Gets 车辆类别.
     *
     * @return 车辆类别, 以支持第三方app个性化, 没有时默认为空字符串(是 " ", 不是NULL) vehicle id
     */
    public String getVehicleType() {
        return VehicleType;
    }

    /**
     * Gets 车辆标识.
     *
     * @return 车辆标识, 以支持第三方app个性化, 没有时默认为空字符串(是 " ", 不是NULL) vehicle id
     */
    public String getVehicleID() {
        return VehicleID;
    }

    /**
     * Gets 用户标识.
     *
     * @return 用户标识, 以支持第三方app个性化, 没有时默认为空字符串(是 " ", 不是NULL) user id
     */
    public String getUserID() {
        return UserID;
    }

    /**
     * Gets 连接版本号.
     *
     * @return 连接版本号
     */
    public int getConnectVersion() {
        return connectVersion;
    }

    /**
     * 是否WLServer负责H264的Codec.
     *
     * @return 是否WLServer负责H264的Codec
     */
    public boolean isServerCodec() {
        return isServerCodec;
    }
}
