package com.autoai.welink.auto;

public interface WLTBTInfo {
    /**
     * 启动TBT
     */
    void startTBT();

    /**
     * 停止TBT
     */
    void stopTBT();

    /**
     * 更新TBT信息
     * @param currentRoadName 当前道路名称
     * @param roadName 下一个路口名称
     * @param roadDistance 下一个路口距离，单位：米
     * @param roadTurnIcon 下一个路口TBT图标ID
     * @param remainDistance 距目的地的剩余距离，小余等于0则导航结束，单位：米
     * @param remainTime 预估到达目的地的所剩时间，单位：分
     */
    void updateTBT(String currentRoadName, String roadName, int roadDistance, int roadTurnIcon, int remainDistance, int remainTime);

    /**
     * 电子眼提醒
     */
    void notifyCamera();
}
