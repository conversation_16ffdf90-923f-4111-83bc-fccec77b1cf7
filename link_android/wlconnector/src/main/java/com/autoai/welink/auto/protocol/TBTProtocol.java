package com.autoai.welink.auto.protocol;

import android.os.Handler;
import android.os.Looper;

import com.autoai.welink.auto.WLTBTInfo;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.MainHandler;

import org.json.JSONException;
import org.json.JSONObject;

public class TBTProtocol  extends ProtocolBase implements WLTBTInfo {
    private static final String PROTOCOL_NAVITYPE_START_TBT = "navi_start_tbt";
    private static final String PROTOCOL_NAVITYPE_STOP_TBT = "navi_stop_tbt";
    private static final String PROTOCOL_NAVITYPE_UPDATE_TBT = "navi_update_tbt";
    private static final String PROTOCOL_NAVITYPE_CAMERA = "navi_camera";

    private static final String PROTOCOL_NAVI_STR_TYPE = "Type";
    private static final String PROTOCOL_NAVI_STR_CROADNAME = "CRoadName";
    private static final String PROTOCOL_NAVI_STR_ROADNAME = "RoadName";
    private static final String PROTOCOL_NAVI_STR_ROADDISTANCE = "RoadDistance";
    private static final String PROTOCOL_NAVI_STR_TURNICON = "TurnIcon";
    private static final String PROTOCOL_NAVI_STR_REMAINDISTANCE = "RemainDistance";
    private static final String PROTOCOL_NAVI_STR_REMAINTIME = "RemainTime";

    public TBTProtocol(Transmisson transmisson) {
        super(transmisson);
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected(WLConfigurationImp wlConfiguration) {
        return false;
    }

    @Override
    public void onDisconnected() {

    }

    @Override
    public void onError(int errorCode) {

    }

    @Override
    public void startTBT() {
        MainHandler.getInstance().post(this::startTBT_imp);
    }
    public void startTBT_imp() {
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_NAVI_STR_TYPE, PROTOCOL_NAVITYPE_START_TBT);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_NAVI, obj.toString().getBytes());
        }
    }

    @Override
    public void stopTBT() {
        MainHandler.getInstance().post(this::stopTBT_imp);
    }
    public void stopTBT_imp() {
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_NAVI_STR_TYPE, PROTOCOL_NAVITYPE_STOP_TBT);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_NAVI, obj.toString().getBytes());
        }
    }

    @Override
    public void updateTBT(String currentRoadName, String roadName, int roadDistance, int roadTurnIcon, int remainDistance, int remainTime) {
        MainHandler.getInstance().post(() -> updateTBT_imp(currentRoadName, roadName, roadDistance, roadTurnIcon, remainDistance, remainTime));
    }
    public void updateTBT_imp(String currentRoadName, String roadName, int roadDistance, int roadTurnIcon, int remainDistance, int remainTime) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_NAVI_STR_TYPE, PROTOCOL_NAVITYPE_UPDATE_TBT);
            obj.put(PROTOCOL_NAVI_STR_CROADNAME, currentRoadName);
            obj.put(PROTOCOL_NAVI_STR_ROADNAME, roadName);
            obj.put(PROTOCOL_NAVI_STR_ROADDISTANCE, roadDistance);
            obj.put(PROTOCOL_NAVI_STR_TURNICON, roadTurnIcon);
            obj.put(PROTOCOL_NAVI_STR_REMAINDISTANCE, remainDistance);
            obj.put(PROTOCOL_NAVI_STR_REMAINTIME, remainTime);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_NAVI, obj.toString().getBytes());
        }
    }

    @Override
    public void notifyCamera() {
        MainHandler.getInstance().post(this::notifyCamera_imp);
    }
    public void notifyCamera_imp() {
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_NAVI_STR_TYPE, PROTOCOL_NAVITYPE_CAMERA);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_NAVI, obj.toString().getBytes());
        }
    }
}
