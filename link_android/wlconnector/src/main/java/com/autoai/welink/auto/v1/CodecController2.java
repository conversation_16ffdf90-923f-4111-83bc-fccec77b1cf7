package com.autoai.welink.auto.v1;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.os.Build;
import android.view.Display;
import android.view.Surface;
import android.view.WindowManager;

import com.autoai.welink.auto.client.code.Server;

public class CodecController2 implements CodecController {
	private final WLCodecListener listener;
	private final Context context;

	public CodecController2(Connector wlConnector,Context context,WLCodecListener listener) {
		this.listener = listener;
		this.context=context;
//		recordController=new RecordController2(context);
	}
	//=========================================================
//	public RecordController2 recordController;
	private long frame_index = 0;//启动后,发送的帧的index
	static public boolean add_debug_byte = false;
	static public boolean only_record_opengl = false;
	static private Rect videoRect = new Rect(0, 0, 1280, 720);//绘制图片到视频,绘制的尺寸
	static private int width = 1280;//vd(含presestation)和视频都使用统一的尺寸
	static private int height = 720;
	static private int dpi=320;
	static private boolean isRunning = false;
	static private boolean isPause = false;
	static private int phone_type = 0;//0为一般手机的处理方式,1为GalaxyS6

	private void checkPhoneType() {
		String string1 = Build.MANUFACTURER;
		String string2 = Build.MODEL;
		if (string1.equals("samsung") && string2.equals("SM-G9208"))
			phone_type = 1;
	}

	public boolean isCodecAlive() {

	    return isRunning && !isPause;
    }

    public void recordStart() {
//        recordController.recordStart();
    }

	public void start(String connectStr) {
		AnFileLog.e("v1","start() ---> " + "isRunning = [" + isRunning + "]");
		if (isRunning) return;
		isRunning = true;
		isPause = false;//每次开始后都要重置暂停状态
		frame_index = 0;
		setBitmapOnCar(null);
		checkPhoneType();
		AnFileLog.e("v1", "baseCodecController.setWH(" + width + "," + height);
//		recordController.setCodeListener(new WLCodecListener() {
//			@Override
//			public void onCallBack(int type, byte[] bytes) {
////				AnFileLog.e("WLCodecListener onCallBack type=" + type + ",size=" + bytes.length);
//				if (listener == null) return;
//				long ms = System.currentTimeMillis();
//				if (add_debug_byte) {
//					ByteBuffer bb = ByteBuffer.allocate(bytes.length + 16);
//					bb.put(bytes);
//					bb.putLong(frame_index++);
//					bb.putLong(ms);
//					listener.onCallBack(type, bb.array());
//				} else {
//					listener.onCallBack(type, bytes);
//				}
//			}
//
//			@Override
//			public void onCodecStatusCallBack(int type, Object object) {
//				if (listener != null) {
//					listener.onCodecStatusCallBack(type, object);
//				}
//			}
//
//			@Override
//			public void onScreenNoUpdate() {
//				if (listener != null) listener.onScreenNoUpdate();
//			}
////		});
//
//		recordController.setWH(width, height,dpi);
//		recordController.createCodec();

		createVD(null, connectStr, width, height, dpi);

		AnFileLog.e("v1","mHandler.sendEmptyMessageDelayed(1, 15000); in start");
//		recordController.codecListener.onCodecStatusCallBack(WLCodecListener.CODEC_STATUS_START, null);
	}

	public void stop() {
		if (!isRunning) return;
		isRunning = false;
//		recordController.stop();
//        MainHandler.getInstance().postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                if (captureDialog != null) {
//                    captureDialog.hide();
//                    captureDialog = null;
//                }
//            }
//        }, 1);
	}

    public void release(){
	    if (captureDialog != null) {
	        captureDialog.dismiss();
	        captureDialog = null;
        }

        if (virtualDisplay!=null) {
            virtualDisplay.release();
            virtualDisplay = null;
        }
    }

	// 扩展屏相关,vd
    private VirtualDisplay virtualDisplay;
    private MirrorDialog captureDialog;//投屏ui

    private void createVD(Surface surface, String connectStr, int width, int height, int densityDpi) {
		DisplayManager displayManager = (DisplayManager) context.getSystemService(Context.DISPLAY_SERVICE);
		virtualDisplay = displayManager.createVirtualDisplay(connectStr, width, height, densityDpi, surface, DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_OWN_CONTENT_ONLY);
//		recordController.virtualDisplay = virtualDisplay;
		AnFileLog.e("wlconnector", "createVirtualDisplay w=" + width + ",h=" + height + ",dpi=" + densityDpi);
		captureDialog = new MirrorDialog(context, virtualDisplay.getDisplay());
		captureDialog.init(width, height);

        Server.virtualDisplay = virtualDisplay;
        Server.dialog = captureDialog;
//		captureDialog.show();
	}
	public void showPresentation(){
		captureDialog.show();
	}

	public void pause() {
		if (isPause) return;
		isPause = true;
//		recordController.pause();
//		surfaceRecorder.pause();
	}

	public void resume() {
		if (!isPause) return;
		isPause = false;
//		recordController.resume();
//		surfaceRecorder.resume();
	}

	public void setWH(int width, int height,int dpi) {
		this.width = width;
		this.height = height;
		this.dpi=dpi;
		videoRect = new Rect(0, 0, width, height);
	}

    public MirrorDialog getCaptureDialog() {
        return captureDialog;
    }

	public void setVideoFPS(int fps) {
	}
    private void setBitmapOnCar(Bitmap bitmap) {
	}
}
