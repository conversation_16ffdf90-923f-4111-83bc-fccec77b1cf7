package com.autoai.welink.auto;

import android.graphics.Bitmap;

public interface WLMusic {
	interface Callback {
        /**
         * 获取音频焦点
         *
         * 能够获取音频焦点的音乐App有三类:
         * 1.手机前台的音乐App
         * 2.投屏状态的音乐App
         * 3.WeLink设定当前焦点音乐App
         */
        void onEnableFocus();

        /**
         * 禁用音频焦点
         */
        void onDisableFocus();

        /**
         * 音乐播放进度
         * @param position 播放进度
         */
        void onTick(long position);
    }

    /**
     * 注册音乐播放器
     * 注册的音乐播放器可以获得音频焦点
     *
     * @param callback 事件回调，null则取消注册
     */
    void register(Callback callback);

    /**
     * 请求音频焦点
     *
     * 以下2中情况请求音频焦点可以成功：
     * 1.手机前台的音乐App
     * 2.投屏状态的音乐App
     */
    void requestFocus();

    /**
     * 开始播放状态
     *
     * @param totalLen 数据总长度（毫秒）, 计算方法：(rate * channel * bit / 8) * 1l * 总毫秒数 / 1000，-1代表直播
     * @param rate 采样率
     * @param bit 采样精度
     * @param channel 声道数
     * @return false 未获得焦点，true 命令成功
     */
    boolean start(long totalLen, int rate, int bit, int channel);

    /**
     * 结束播放状态
     *
     * @return false 未获得焦点，true 命令成功
     */
    boolean stop();

    /**
     * 暂停播放
     *
     * @return false 未获得焦点，true 命令成功
     */
    boolean resume();

    /**
     * 恢复播放
     *
     * @return false 未获得焦点，true 命令成功
     */
    boolean pause();

    /*************** update类函数 ***************/

	/**
	 * 更新ID3信息
     *
     * @param source 音源
     * @param artist 演唱者
     * @param title 歌曲名
     * @param album 专辑名
     * @param lyric 歌词，可以为null
     * @param lyricType 歌词格式类型 1:LRC, 2:QRC ,0:歌词为null
     * @param duration 时长，单位:秒
     * @param cover 歌曲封面，可以为null
     *
     * @return false 未处于播放状态，true 命令成功
     */
	boolean updateID3(String source, String artist, String title, String album, String lyric, int lyricType, int duration, Bitmap cover);

    /**
     * 更新播放顺序
     *
     * @param order 播放顺序：参见《车机命令》，0: 循环播放、1: 顺序播放、2: 随机播放、3: 单曲循环播放
     *
     * @return false 未处于播放状态，true 命令成功
     */
	boolean updateOrder(int order);

    /**
     * 更新PCM数据，PCM数据需要以实际播放进度更新
     *
     * @param position 数据位置, -1代表直播
     * @param pcm pcm数据
     *
     * @return false 未处于播放状态，true 命令成功
     */
    boolean updatePCM(long position, byte[] pcm);
}
