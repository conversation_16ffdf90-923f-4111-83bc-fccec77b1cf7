package com.autoai.welink.auto.client;

import android.content.Context;

import com.autoai.welink.auto.client.code.AutoClient;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by gao<PERSON> on 2018/11/14.
 */

public class WLAutoClient {
   private final AutoClient autoClient;
   private final ExecutorService masterThreadExecutor = Executors.newSingleThreadExecutor();//线程池
    ExecutorService videoThreadExecutor = Executors.newSingleThreadExecutor();//线程池
    ExecutorService soundThreadExecutor = Executors.newSingleThreadExecutor();//线程池

    /**
     * 创建
     * @param context 上下文
     * @param connectLister 连接状态的回调
     */
    public WLAutoClient(Context context, WLAutoConnectListener connectLister) {
        autoClient = new AutoClient(context,connectLister);
    }

    /**
     * 开始连接
     * @param connectStr 校验字符串
     * @param maxVersion WelinkConnect SDK 支持的最大版本号,如0,1，2...
     * @param minVersion WelinkConnect SDK 支持的最小版本号,如0,1，2...
     */
    public void connect (String connectStr, int maxVersion, int minVersion) {
        autoClient.autoConnect(connectStr, maxVersion, minVersion);
    }

    /////// 解决：首先发送同意连接的消息，再发送其他协议消息
    public void connectResult(boolean isSuccess) {
        autoClient.connectResult(isSuccess);
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        masterThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                autoClient.disconnect();
            }
        });
    }

    public void remove(String filename) {
        autoClient.remove(filename);
    }

    public boolean create(String filename, byte[] buffer) {
        return autoClient.create(filename, buffer);
    }

    public byte[] read(String filename) {
        return autoClient.read(filename);
    }

    public boolean isMirrorScreen() {
        return autoClient.isMirrorScreen();
    }

    /**
     * 设置 WLAutoDataListener接收数据监听
     * @param dataLister WLAutoDataListener监听
     */
    public void setAutoDataListener(WLAutoDataListener dataLister) {
        autoClient.setAutoDataListener(dataLister);
    }

    /**
     * 发送数据
     * @param type 发送的数据类型
     * @param bytes 发送的二进制数据
     */
    public void sendData(final int type, final byte[] bytes) {
        sendData(type, bytes, null);
    }

    /**
     * 发送数据
     * @param type 发送的数据类型
     * @param bytes 发送的二进制数据
     * @param bytes2 发送的第二段二进制数据
     */
    private void sendData(final int type, final byte[] bytes, final byte[] bytes2) {
        autoClient.sendDataByAIDL(type, bytes, bytes2);
    }
}
