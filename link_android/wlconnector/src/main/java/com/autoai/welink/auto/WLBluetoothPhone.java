package com.autoai.welink.auto;

public interface WLBluetoothPhone {
    interface Callback {
        /**
         * 已拨打
         */
        void onDialed();

        /**
         * 拒绝拨打
         */
        void onRefused();
    }

    /**
     * 通过车机蓝牙拨打电话
     * @param phoneNumber 电话号码
     * @param callback 拨打状态
     */
    void dial(String phoneNumber, Callback callback);

    /**
     * 通过车机蓝牙挂断电话
     */
    void hangUp();
}
