package com.autoai.welink.auto.client;



/**
 * Created by gao<PERSON> on 2018/11/14.
 */

public interface WLAutoConnectListener {

    /*
    0. 没有错误
    1. WeLink服务无效，有可能是WeLink App没有运行或者没有连接车机
    2. 连接字符串不正确
    3. WLConnector版本太老，无法连接当前的WeLink服务
    4. 重复连接
    5. 连接WeLink服务失败
    6. App不支持该分辨率
    */
    int CONNECT_NOT_ERROR = 0;
    int CONNECT_WELINK_APP_ERROR = 1;
    int CONNECT_STR_ERROR = 2;
    int CONNECT_APP_VER_ERROR = 3;
    int CONNECT_REPEAT_ERROR = 4;
    int CONNECT_SERVICE_ERROR = 5;
    int CONNECT_RESOLUTION_ERROR = 6;

    /**
     * 连接失败
     * @param errorCode 失败码
     */
    void onError(int errorCode);

    /**
     * 连接成功
     * @param wlConfiguration 连接配置信息
     * @return 是否支持该分辨率连接
     */
    boolean onConnected(WLConfigurationImp wlConfiguration);

    /**
     * 连接断开
     */
    void onDisconnected();
}

