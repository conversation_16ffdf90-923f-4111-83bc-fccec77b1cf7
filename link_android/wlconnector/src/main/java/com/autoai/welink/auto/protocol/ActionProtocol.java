package com.autoai.welink.auto.protocol;

//import com.autoai.welink.auto.WLConfiguration;
import com.autoai.welink.auto.client.WLConfigurationImp;

import org.json.JSONException;
import org.json.JSONObject;

public class ActionProtocol extends ProtocolBase{
    //action类型
    private static final int ACTION_RETURN_LAUNCHER = 0;//返回launcher
    private static final int ACTION_FOREGROUND_SELF = 1;//将App调入前台
    private static final int ACTION_OPEN_SR = 2;//打开语音识别
    private static final int ACTION_REQUEST_VOICE = 3;//请求车机语音
    private static final int ACTION_STOP_VOICE = 4;//停止车机语音

    private static final String PROTOCOL_STRING_VER = "Ver";
    private static final String PROTOCOL_STRING_ACT = "Act";
    private static final String PROTOCOL_STRING_VER_VALUE = "v0.1.0";

    public ActionProtocol(Transmisson transmisson) {
        super(transmisson);
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected(WLConfigurationImp wlConfiguration) {
        return false;
    }

    @Override
    public void onDisconnected() {
    }

    @Override
    public void onError(int errorCode) {

    }

    public void returnLauncherAction(){
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
            obj.put(PROTOCOL_STRING_ACT, ACTION_RETURN_LAUNCHER);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_ACTION, obj.toString().getBytes());
    }

    public void foregroundSelfAction(){
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
            obj.put(PROTOCOL_STRING_ACT, ACTION_FOREGROUND_SELF);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_ACTION, obj.toString().getBytes());
    }

    public void openSRAction(){
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
            obj.put(PROTOCOL_STRING_ACT, ACTION_OPEN_SR);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_ACTION, obj.toString().getBytes());
    }

    public void requestVoiceData(){
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
            obj.put(PROTOCOL_STRING_ACT, ACTION_REQUEST_VOICE);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_ACTION, obj.toString().getBytes());
    }

    public void stopVoiceData(){
        JSONObject obj = new JSONObject();
        try {
            obj.put(PROTOCOL_STRING_VER, PROTOCOL_STRING_VER_VALUE);
            obj.put(PROTOCOL_STRING_ACT, ACTION_STOP_VOICE);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_ACTION, obj.toString().getBytes());
    }
}
