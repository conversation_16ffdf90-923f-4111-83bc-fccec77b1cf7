package com.autoai.welink.auto.protocol;

import com.autoai.welink.auto.WLConnectListener;
import com.autoai.welink.auto.client.WLConfigurationImp;

import org.json.JSONException;
import org.json.JSONObject;

public class CommandProtocol extends ProtocolBase{
    public CommandProtocol(Transmisson transmisson) {
        super(transmisson);
    }

    private WLConnectListener welinkListener;
    public void setWelinkListener(WLConnectListener listener){
        welinkListener = listener;
    }

    @Override
    public void onReciveData(byte[] data) {
            String command = new String(data);
            try {
                JSONObject jsonObject = new JSONObject(command);
                if (jsonObject.has("Request") &&
                    jsonObject.optString("Request").equals("Disconnect")) {
                    transmisson.disconnect();
                    return;
                }
            }
            catch (JSONException e) {
                e.printStackTrace();
            }

            if(welinkListener != null)
                welinkListener.onCommand(command);
    }
    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected(WLConfigurationImp wlConfiguration) {
        return false;
    }

    @Override
    public void onDisconnected() {
    }

    @Override
    public void onError(int errorCode) {

    }
}
