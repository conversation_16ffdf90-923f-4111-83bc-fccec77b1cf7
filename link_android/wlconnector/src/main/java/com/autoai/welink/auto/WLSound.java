package com.autoai.welink.auto;

public interface WLSound {
    interface Callback {
        /**
         * 开始播放
         *
         * @param mark 声音标识
         */
        void onBegin(String mark);

        /**
         * 播放即将完成，发送下一条数据
         *
         * @param mark 声音标识
         */
        void onPrepare(String mark);

        /**
         * 播放完成
         *
         * @param mark 声音标识
         */
        void onComplete(String mark);

        /**
         * 播放被打断
         *
         * @param mark 声音标识
         * @param playTime 被打断的播放时间, 单位: 毫秒
         * @param totalTime 播放声音的总时间，单位: 毫秒
         */
        void onInterrupt(String mark, long playTime, long totalTime);

        /**
         * 拒绝播放
         *
         * @param mark 声音标识
         * @param waitingTime 播放被拒绝，建议重新播放等待的时长，单位: 毫秒
         */
        void onReject(String mark, long waitingTime);
    }

    /**
     * 播放声音
     *
     * @param mark 声音标识
     * @param pcm 声音数据
     * @param rate 采样率
     * @param bit 采样精度
     * @param channel 声道数
     * @param callback 播放状态
     */
    void play(String mark, byte[] pcm, int rate, int bit, int channel, Callback callback);
}
