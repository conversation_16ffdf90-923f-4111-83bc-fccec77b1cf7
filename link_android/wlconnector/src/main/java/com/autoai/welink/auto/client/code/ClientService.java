package com.autoai.welink.auto.client.code;

import java.io.FileDescriptor;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.os.MemoryFile;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.os.SharedMemory;

import com.autoai.welink.auto.client.IClientService;

public class ClientService extends Service {
    @Override
    public IBinder onBind(Intent intent) {
        return stub;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
    }

    private final IClientService.Stub stub = new IClientService.Stub() {
        final ExecutorService sendDataThreadExecutor = Executors.newSingleThreadExecutor();

        @Override
        public ParcelFileDescriptor getFileDescriptor(String connectStr, String filename) {
            return AutoClient.getFileDescriptor(connectStr, filename);
        }

        @Override
        public SharedMemory getSharedMemory(String connectStr, String filename) {
            return AutoClient.getSharedMemory(connectStr, filename);
        }

        @Override
        public int length(String connectStr, String filename) {
            return AutoClient.length(connectStr, filename);
        }

        @Override
        public void pauseMirrorScreen(String connectStr) {
            AutoClient.pauseMirrorScreen(connectStr);
        }

        @Override
        public void resumeMirrorScreen(String connectStr) {
            AutoClient.resumeMirrorScreen(connectStr);
        }

        @Override
        public void sendData(String connectStr, int type, byte[] bytes) throws RemoteException {
            sendDataThreadExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    AutoClient.onDataRecv(connectStr, type, bytes);
                }
            });
        }
    };
}
