package com.autoai.welink.auto;

public interface WLMicrophone {
    interface Callback {
        /**
         * 车机语音数据
         * @param pcm 声音数据
         * @param rate 采样率
         * @param bit 采样精度
         * @param channel 声道数
         */
        void onSound(byte[] pcm, int rate, int bit, int channel);

        /**
         * 车机语音发送完成通知，车机语音数据发送超过一定时间后会自动结束
         */
        void onCompleted();
    }

    /**
     * 请求车机声音数据
     */
    void request(Callback cb);

    /**
     * 停止接受车机声音数据
     */
    void stop();
}
