package com.autoai.welink.auto.client.code;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.IBinder;
import android.os.MemoryFile;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.os.SharedMemory;
import android.system.ErrnoException;
import android.view.Surface;
import android.hardware.display.VirtualDisplay;

import com.autoai.welink.auto.client.WLAutoConnectListener;
import com.autoai.welink.auto.client.WLAutoDataListener;
import com.autoai.welink.auto.v1.AnFileLog;
import com.autoai.welink.autoproxy.server.IServerService;

import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.nio.channels.FileLock;
import java.nio.channels.OverlappingFileLockException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;


public class Server {
    public static VirtualDisplay virtualDisplay;
    public static Dialog dialog;

    static class TimeLimitMemory {
        public final long date = System.currentTimeMillis();
        public final SharedMemory sharedMemory;
        public final MemoryFile memoryFile;

        public TimeLimitMemory(SharedMemory sharedMemory) {
            this.sharedMemory = sharedMemory;
            this.memoryFile = null;
        }

        public TimeLimitMemory(MemoryFile memoryFile) {
            this.memoryFile = memoryFile;
            this.sharedMemory = null;
        }
    }
    private Map<String, TimeLimitMemory> memoryList;

    private  final ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceDisconnected(ComponentName name) {
            bBind = false;

            synchronized (serverServiceLock) {
                serverService = null;
            }

            if (listener != null) {
                listener.onDisconnected();
            }
        }

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            if (!bBind) return;

            synchronized (serverServiceLock) {
                serverService = IServerService.Stub.asInterface(service);
            }

            synchronized (constructorSync) {
                constructorSync.notify();
            }
        }
    };

    public Server(Context context, WLAutoConnectListener listener, String packageName, String randomStr) throws Exception {
        this.listener = listener;
        this.randomStr = randomStr;
        Intent intent = new Intent();

        intent.setAction("com.autoai.welink.autoproxy.server.IServerService");
        intent.setPackage(packageName);

        memoryList = new HashMap<>();
        bBind = true;

        synchronized (constructorSync) {
            if (!context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)) {
                throw new Exception("SurfaceShared failed");
            }
            constructorSync.wait(300);
        }
    }

    public synchronized void release(Context context) {
        listener = null;

        synchronized (serverServiceLock) {
            if (serverService != null) {
                serverService = null;
                context.unbindService(serviceConnection);
            }
        }

        virtualDisplay = null;
        dialog = null;

        if (memoryList != null) {
            Iterator<Map.Entry<String, TimeLimitMemory>> iterator = memoryList.entrySet().iterator();
            while (iterator.hasNext()) {
                TimeLimitMemory memory = iterator.next().getValue();
                if (memory != null) {
                    if (memory.sharedMemory != null) {
                        memory.sharedMemory.close();
                    } else if (memory.memoryFile != null) {
                        memory.memoryFile.close();
                    }
                }
                iterator.remove();
            }

            memoryList = null;
        }
    }

    public synchronized boolean create(String filename, byte[] buffer) {
        if (memoryList == null) return false;

        //每次新建内存文件前，先检查是否有过期内存文件，如果有则删除，以保证运行期间Server不读数据出现内存泄漏 -->
        ArrayList<String> keyList = new ArrayList<>();
        for (Map.Entry<String, TimeLimitMemory> entry: memoryList.entrySet()) {
            TimeLimitMemory memory = entry.getValue();
            if (memory != null && (System.currentTimeMillis() - memory.date) > 1000) {
                keyList.add(entry.getKey());
            }
        }
        for (String key: keyList) {
            TimeLimitMemory memory = memoryList.get(key);
            if (memory != null) {
                AnFileLog.e("aidl_c", "ClearTimeOutMemory");
                if (memory.sharedMemory != null) {
                    memory.sharedMemory.close();
                } else if (memory.memoryFile != null) {
                    memory.memoryFile.close();
                }
                memoryList.remove(key);
            }
        }
        // <--

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            SharedMemory sharedMemory = null;
            try {
                sharedMemory = SharedMemory.create(filename, buffer.length);
            } catch (ErrnoException e) {
                e.printStackTrace();
            }
            if (sharedMemory == null) {
                return false;
            }

            ByteBuffer byteBuffer = null;
            try {
                byteBuffer = sharedMemory.mapReadWrite();
            } catch (ErrnoException e) {
                e.printStackTrace();
            }
            if (byteBuffer == null) {
                sharedMemory.close();
                return false;
            }

            byteBuffer.put(buffer);
            SharedMemory.unmap(byteBuffer);

            memoryList.put(filename, new TimeLimitMemory(sharedMemory));
        } else {
            MemoryFile memoryFile = null;
            try {
                memoryFile = new MemoryFile(filename, buffer.length);
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (memoryFile == null) {
                return false;
            }

            FileDescriptor fd = null;
            try {
                @SuppressLint("DiscouragedPrivateApi") Method method = MemoryFile.class.getDeclaredMethod("getFileDescriptor");
                fd = (FileDescriptor) method.invoke(memoryFile);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fd == null) {
                memoryFile.close();
                return false;
            }

            FileOutputStream fos = new FileOutputStream(fd);
            FileLock fl = null;
            try {
                fl = fos.getChannel().lock(0L, buffer.length, false);
                memoryFile.writeBytes(buffer, 0, 0, buffer.length);
                fl.release();
                fos.flush();
                fos.close();
            } catch (Exception e) {
                memoryFile.close();

                try {
                    if (fl != null) {
                        fl.release();
                    }
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }

                AnFileLog.e("aidl_c", "MemoryFile-Exception: " + filename);
                e.printStackTrace();
                return false;
            }

            memoryList.put(filename, new TimeLimitMemory(memoryFile));
        }
        return true;
    }

    public synchronized void remove(String filename) {
        if (memoryList != null) {
            TimeLimitMemory memory = memoryList.get(filename);

            if (memory == null) return;

            if (memory.sharedMemory != null) {
                memory.sharedMemory.close();
            } else if (memory.memoryFile != null) {
                memory.memoryFile.close();
            }
            memoryList.remove(filename);
        }
    }

    public byte[] read(String filename) {
        int size = getServerLength(filename);
        if (size == 0) {
            return null;
        }

        byte[] buffer = new byte[size];
        if (buffer == null) {
            return null;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            SharedMemory sharedMemory = null;
            ByteBuffer byteBuffer = null;

            sharedMemory = getServerSharedMemory(filename);
            if (sharedMemory == null) {
                return null;
            }

            try {
                byteBuffer = sharedMemory.mapReadOnly();
            } catch (ErrnoException e) {
                e.printStackTrace();
            }

            if (byteBuffer == null) {
                sharedMemory.close();
                return null;
            }

            byteBuffer.get(buffer);
            SharedMemory.unmap(byteBuffer);
            sharedMemory.close();
            return buffer;
        } else {
            ParcelFileDescriptor pfd = null;
            FileDescriptor fd = null;

            pfd = getServerFileDescriptor(filename);
            if (pfd == null) {
                return null;
            }

            fd = pfd.getFileDescriptor();
            if (fd == null) {
                return null;
            }

            try {
                FileInputStream fis = new FileInputStream(fd);
                FileLock fl = fis.getChannel().lock(0L, size, true);

                try {
                    fis.getChannel().position(0);
                } catch (IOException e) {
                    AnFileLog.e("aidl_c", "MemoryFile-Exception-channel.position" + filename);
                }

                fis.read(buffer, 0, size);
                fl.release();
                fis.close();
                pfd.close();
            } catch (IOException e) {
                AnFileLog.e("aidl_c", "MemoryFile-Exception1: " + filename);
                e.printStackTrace();
            } catch (OverlappingFileLockException e) {
                AnFileLog.e("aidl_c", "MemoryFile-Exception2: " + filename);
                return null;
            }

            return buffer;
        }
    }

    public void sendData(String connectStr_all, int type, byte[] bytes){
        synchronized (serverServiceLock) {
            if (serverService != null) {
                try {
                    AnFileLog.e("aidl_c","sendData serverService="+serverService);
                    serverService.sendData(connectStr_all, type, bytes);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    //wlclient处理wlserver发来的数据
    public void onDataRecv(String connectStr, int type, byte[] bytes){
        if (this.randomStr == null || !this.randomStr.equals(connectStr)) {
            return;
        }

        AnFileLog.e("aidl_c","onDataRecv type="+type+",bytes.length="+bytes.length+",dataListener="+dataListener);
        if (dataListener!=null){
            dataListener.onReciveData(type,bytes);
        }
    }

    public void pauseMirrorScreen(String connectStr) {
        if (this.randomStr == null || !this.randomStr.equals(connectStr)) {
            return;
        }

        Surface surface = virtualDisplay.getSurface();
        virtualDisplay.setSurface(null);

        if (surface != null) {
            surface.release();
        }
    }

    public void resumeMirrorScreen(String connectStr) {
        if (this.randomStr == null || !this.randomStr.equals(connectStr)) {
            return;
        }

        virtualDisplay.setSurface(getServerSurface());
    }

    public boolean isMirrorScreen() {
        return virtualDisplay.getSurface() != null;
    }

    public synchronized ParcelFileDescriptor getFileDescriptor(String connectStr, String filename) {
        if (this.randomStr == null || !this.randomStr.equals(connectStr)) {
            return null;
        }

        if (memoryList != null) {
            Server.TimeLimitMemory memory = memoryList.get(filename);
            if (memory != null) {
                MemoryFile memoryFile = memory.memoryFile;
                if (memoryFile == null) return null;

                ParcelFileDescriptor pfd = null;
                try {
                    @SuppressLint("DiscouragedPrivateApi") Method method = MemoryFile.class.getDeclaredMethod("getFileDescriptor");
                    FileDescriptor fd = (FileDescriptor) method.invoke(memoryFile);

                    pfd = ParcelFileDescriptor.dup(fd);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                return pfd;
            }
        }

        return null;
    }

    public synchronized SharedMemory getSharedMemory(String connectStr, String filename) {
        if (this.randomStr == null || !this.randomStr.equals(connectStr)) {
            return null;
        }

        if (memoryList != null) {
            Server.TimeLimitMemory memory = memoryList.get(filename);
            if (memory != null) {
                return memory.sharedMemory;
            }
        }

        return null;
    }

    public synchronized int length(String connectStr, String filename) {
        if (this.randomStr == null || !this.randomStr.equals(connectStr)) {
            return 0;
        }

        if (memoryList != null) {
            Server.TimeLimitMemory memory = memoryList.get(filename);
            if (memory != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                    SharedMemory sharedMemory = memory.sharedMemory;
                    if (sharedMemory == null) return 0;

                    return sharedMemory.getSize();
                } else {
                    MemoryFile memoryFile = memory.memoryFile;
                    if (memoryFile == null) return 0;

                    return memoryFile.length();
                }
            }
        }

        return 0;
    }

    private ParcelFileDescriptor getServerFileDescriptor(String filename) {
        ParcelFileDescriptor pfd = null;

        synchronized (serverServiceLock) {
            if (serverService != null) {
                try {
                    pfd = serverService.getFileDescriptor(randomStr, filename);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        return pfd;
    }

    private SharedMemory getServerSharedMemory(String filename) {
        SharedMemory sharedMemory = null;

        synchronized (serverServiceLock) {
            if (serverService != null) {
                try {
                    sharedMemory = serverService.getSharedMemory(randomStr, filename);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        return sharedMemory;
    }

    private int getServerLength(String filename) {
        int length = 0;

        synchronized (serverServiceLock) {
            if (serverService != null) {
                try {
                    length = serverService.length(randomStr, filename);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        return length;
    }

    private Surface getServerSurface() {
        Surface surface = null;

        synchronized (serverServiceLock) {
            if (serverService != null) {
                try {
                    surface = serverService.getSurface(randomStr);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        return surface;
    }

    public static void setWLAutoDataListener(WLAutoDataListener listener){
        dataListener=listener;
    }

    private IServerService serverService = null;
    private WLAutoConnectListener listener;
    private final String randomStr;
    private final Object serverServiceLock = new Object();
    private final Object constructorSync = new Object();
    private boolean bBind;
    private static WLAutoDataListener dataListener;
}
