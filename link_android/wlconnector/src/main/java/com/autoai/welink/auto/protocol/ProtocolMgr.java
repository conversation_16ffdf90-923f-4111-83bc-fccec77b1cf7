package com.autoai.welink.auto.protocol;

import android.app.Dialog;
import android.view.View;

import com.autoai.welink.auto.WLMusic;
import com.autoai.welink.auto.WLSound;
import com.autoai.welink.auto.WLTBTInfo;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.AnFileLog;

//协议管理类
public class ProtocolMgr {
    //协议类型
    public static final int TRANSIMISSON_TYPE_TOUCH_EVENT = 3;//投屏回控
    public static final int TRANSIMISSON_TYPE_BACKGROUND = 5;//前后台状态
    public static final int TRANSIMISSON_TYPE_MUSIC = 11;//音乐
    public static final int TRANSIMISSON_TYPE_NAVI = 12;//导航
    private static final int TRANSIMISSON_TYPE_COMMAND = 13;//车机命令
    public static final int TRANSIMISSON_TYPE_ACTION = 14;//action消息
    public static final int TRANSIMISSON_TYPE_MICROPHONE = 15;//车机麦克风
    public static final int TRANSIMISSON_TYPE_SOUND = 16;//声音

    public ProtocolMgr(Transmisson transmisson) {
        touchEventProtocol = new TouchEventProtocol(transmisson);
        backgroundStatusProtocol = new BackgroundStatusProtocol(transmisson);
        musicProtocol = new MusicProtocol(transmisson);
        commandProtocol = new CommandProtocol(transmisson);
        actionProtocol = new ActionProtocol(transmisson);
        tbtProtocol = new TBTProtocol(transmisson);
        soundProtocol = new SoundProtocol(transmisson);
        microphoneProtocol = new MicrophoneProtocol(transmisson);
    }

    //=========================================================
    //投屏回控协议
    private final TouchEventProtocol touchEventProtocol;

    public void setDialog(Dialog dialog) {
        touchEventProtocol.setDialog(dialog);
    }

    //=========================================================
    //前后台协议
    private  final BackgroundStatusProtocol backgroundStatusProtocol;

    public BackgroundStatusProtocol getBackgroundStatusProtocol() {
        return backgroundStatusProtocol;
    }

    //=========================================================
    private final MusicProtocol musicProtocol;

    public WLMusic getWLMusic() {
        return musicProtocol;
    }

    //=========================================================
    private final CommandProtocol commandProtocol;

    public CommandProtocol getCommandProtocol() {
        return commandProtocol;
    }

    //=========================================================
    private final ActionProtocol actionProtocol;

    public ActionProtocol getActionProtocol() {
        return actionProtocol;
    }

    //=========================================================
    private final SoundProtocol soundProtocol;

    public WLSound getWLSound() {
        return soundProtocol;
    }

    //=========================================================
    private final TBTProtocol tbtProtocol;

    public WLTBTInfo getWLTBTInfo() {
        return tbtProtocol;
    }

    //=========================================================
   private final MicrophoneProtocol microphoneProtocol;

    public MicrophoneProtocol getMicrophoneProtocol() {
        return microphoneProtocol;
    }

    //=========================================================
    public void onReciveData(int type, byte[] data) {
        AnFileLog.e("wlconnector-protocol","onReciveData type=" + type + ",data.length=" + data.length);
        //解析数据包,或者按类型分发供具体协议解析数据包
        switch (type) {
            case 0:
                break;
            case TRANSIMISSON_TYPE_TOUCH_EVENT:
                if (touchEventProtocol != null)
                    touchEventProtocol.onReciveData(data);
                break;
            case TRANSIMISSON_TYPE_BACKGROUND:
                if (backgroundStatusProtocol != null)
                    backgroundStatusProtocol.onReciveData(data);
                break;
            case TRANSIMISSON_TYPE_MUSIC:
                if (musicProtocol != null)
                    musicProtocol.onReciveData(data);
                break;
            case TRANSIMISSON_TYPE_COMMAND:
                if (commandProtocol != null)
                    commandProtocol.onReciveData(data);
                break;
            case TRANSIMISSON_TYPE_MICROPHONE:
                if (microphoneProtocol != null)
                    microphoneProtocol.onReciveData(data);
                break;
            case TRANSIMISSON_TYPE_SOUND:
                if (soundProtocol != null)
                    soundProtocol.onReciveData(data);
                break;
        }
    }

    public boolean onConnected(WLConfigurationImp wlConfiguration) {
        if (touchEventProtocol != null)
            touchEventProtocol.onConnected(wlConfiguration);
        if (backgroundStatusProtocol != null)
            backgroundStatusProtocol.onConnected(wlConfiguration);
        if (musicProtocol != null)
            musicProtocol.onConnected(wlConfiguration);
        if (commandProtocol != null)
            commandProtocol.onConnected(wlConfiguration);
        if (actionProtocol != null)
            actionProtocol.onConnected(wlConfiguration);
        if (soundProtocol != null)
            soundProtocol.onConnected(wlConfiguration);
        if (tbtProtocol != null)
            tbtProtocol.onConnected(wlConfiguration);
        if (microphoneProtocol != null)
            microphoneProtocol.onConnected(wlConfiguration);
        return false;
    }

    public void onDisconnected() {
        if (touchEventProtocol != null)
            touchEventProtocol.onDisconnected();
        if (backgroundStatusProtocol != null)
            backgroundStatusProtocol.onDisconnected();
        if (musicProtocol != null)
            musicProtocol.onDisconnected();
        if (commandProtocol != null)
            commandProtocol.onDisconnected();
        if (actionProtocol != null)
            actionProtocol.onDisconnected();
        if (soundProtocol != null)
            soundProtocol.onDisconnected();
        if (tbtProtocol != null)
            tbtProtocol.onDisconnected();
        if (microphoneProtocol != null)
            microphoneProtocol.onDisconnected();
    }

    public void onError(int errorCode) {
        if (touchEventProtocol != null)
            touchEventProtocol.onError(errorCode);
        if (backgroundStatusProtocol != null)
            backgroundStatusProtocol.onError(errorCode);
        if (musicProtocol != null)
            musicProtocol.onError(errorCode);
        if (commandProtocol != null)
            commandProtocol.onError(errorCode);
        if (actionProtocol != null)
            actionProtocol.onError(errorCode);
        if (soundProtocol != null)
            soundProtocol.onError(errorCode);
        if (tbtProtocol != null)
            tbtProtocol.onError(errorCode);
        if (microphoneProtocol != null)
            microphoneProtocol.onError(errorCode);
    }
}
