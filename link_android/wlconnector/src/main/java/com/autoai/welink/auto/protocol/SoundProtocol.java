package com.autoai.welink.auto.protocol;

import com.autoai.welink.auto.WLSound;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.MainHandler;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

public class SoundProtocol extends ProtocolBase implements WLSound {
    private static final String SOUND_ID = "SoundID";
    private static final String SOUND_MARK = "Mark";
    private static final String SOUND_RATE = "Rate";
    private static final String SOUND_BIT = "Bit";
    private static final String SOUND_CHANNEL = "Channel";

    private static final String SOUND_BEGIN = "Begin";
    private static final String SOUND_COMPLETE = "Complete";
    private static final String SOUND_PREPARE = "Prepare";
    private static final String SOUND_INTERRUPT = "Interrupt";
    private static final String SOUND_REJECT = "Reject";
    private static final String SOUND_DELETE_FILE = "DeleteFile";

    public SoundProtocol(Transmisson transmisson) {
        super(transmisson);
    }

    @Override
    public void onReciveData(byte[] data) {
        try {
            JSONObject jsonObject = new JSONObject(new String(data));
            if (null == jsonObject) {
                return;
            }

            String type = jsonObject.optString("Type");
            if (type.equals(SOUND_DELETE_FILE)) {
                if (!jsonObject.has("FileName")) {
                    return;
                }

                String fileName = jsonObject.optString("FileName");
                transmisson.remove(fileName);
            }
            else {
                int index = jsonObject.optInt(SOUND_ID);
                Object[] array = callBacklist.get(index);

                if (null == array || null == array[1]) {
                    return;
                }

                Callback callback = (Callback) array[1];

                if (type.equals(SOUND_BEGIN)) {
                    callback.onBegin((String)array[2]);
                }
                else if (type.equals(SOUND_COMPLETE)) {
                    callback.onComplete((String)array[2]);
                    callBacklist.remove(index);
                }
                else if (type.equals(SOUND_PREPARE)) {
                    callback.onPrepare((String)array[2]);
                }
                else if (type.equals(SOUND_INTERRUPT)) {
                    callback.onInterrupt((String)array[2], jsonObject.optLong("PlayTime"), jsonObject.optLong("TotalTime"));
                    callBacklist.remove(index);
                }
                else if (type.equals(SOUND_REJECT)) {
                    callback.onReject((String)array[2], jsonObject.optLong("WaitingTime"));
                    callBacklist.remove(index);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected(WLConfigurationImp wlConfiguration) {
        return false;
    }

    @Override
    public void onDisconnected() {

    }

    @Override
    public void onError(int errorCode) {

    }

    HashMap<Integer, Object[]> callBacklist = new HashMap<>();
    int index = 0;
    /**
     * 播放声音
     *
     * @param mark 声音标识
     * @param pcm 声音数据
     * @param rate 采样率
     * @param bit 采样精度
     * @param channel 声道数
     * @param callback 播放状态
     */
    public void play(String mark, byte[] pcm, int rate, int bit, int channel, Callback callback){
        if (pcm != null && pcm.length <= 0) {
            return;
        }
        MainHandler.getInstance().post(() -> play_imp(mark, pcm, rate, bit, channel, callback));
    }
    public void play_imp(String mark, byte[] pcm, int rate, int bit, int channel, Callback callback){
        if (null != callback) {
            Object[] array = new Object[3];
            index = (index + 1)%Integer.MAX_VALUE;
            array[0] = index;
            array[1] = callback;
            array[2] = mark;
            callBacklist.put(index, array);
        }

        //写文件
        String fileName = null;
        int count = 0;
        if (null != pcm) {
            do {
                if (fileName == null) {
                    fileName = getRandomFileName();
                } else {
                    fileName += count;
                }
            } while (!transmisson.create(fileName, pcm) && ++count < 3);
        }

        if (count >= 3) {
            return;
        }

        JSONObject obj = new JSONObject();
        try {
            obj.put(SOUND_ID, index);
            obj.put(SOUND_MARK, mark);
            obj.put(SOUND_RATE, rate);
            obj.put(SOUND_BIT, bit);
            obj.put(SOUND_CHANNEL, channel);
            obj.put("FileName", fileName);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_SOUND, obj.toString().getBytes());
        }
    }
}
