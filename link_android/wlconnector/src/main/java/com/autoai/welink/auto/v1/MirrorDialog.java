package com.autoai.welink.auto.v1;

import android.app.Presentation;
import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.Window;
import android.view.WindowManager;

import com.autoai.welink.auto.connector.R;

import java.lang.reflect.Method;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;


public class MirrorDialog extends Presentation {
    private final Display display;

	public MirrorDialog(Context context, Display display) {
        super(context.createDisplayContext(display), display, R.style.presentationDialog);
        this.display = display;

        //针对小米特定手机适配
        Window window = getWindow();
        if (window != null) {
            window.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION);
            window.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        }

        if(MiuiUtil.isMIUI()) {
            //针对小米刘海适配
            try {
                Method method = Window.class.getMethod("addExtraFlags",
                    int.class);
                int flag = 0x00000100 | 0x00000200 | 0x00000400;
                method.invoke(getWindow(), flag);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        DisplayMetrics displayMetrics = new DisplayMetrics();
        display.getRealMetrics(displayMetrics);

        Resources resources = getResources();
        if (resources != null) {
            resources.getDisplayMetrics().scaledDensity = displayMetrics.scaledDensity;
            resources.getDisplayMetrics().density = displayMetrics.density;
            resources.getDisplayMetrics().densityDpi = displayMetrics.densityDpi;
            resources.getDisplayMetrics().heightPixels = displayMetrics.heightPixels;
            resources.getDisplayMetrics().widthPixels = displayMetrics.widthPixels;
            resources.getDisplayMetrics().xdpi = displayMetrics.xdpi;
            resources.getDisplayMetrics().ydpi = displayMetrics.ydpi;
            resources.getConfiguration().fontScale = 1;
        }
	}

    @Override
    public void onDisplayChanged() {
        super.onDisplayChanged();

        DisplayMetrics displayMetrics = new DisplayMetrics();
        display.getRealMetrics(displayMetrics);

        Resources resources = getResources();
        if (resources != null) {
            resources.getDisplayMetrics().scaledDensity = displayMetrics.scaledDensity;
            resources.getDisplayMetrics().density = displayMetrics.density;
            resources.getDisplayMetrics().densityDpi = displayMetrics.densityDpi;
            resources.getDisplayMetrics().heightPixels = displayMetrics.heightPixels;
            resources.getDisplayMetrics().widthPixels = displayMetrics.widthPixels;
            resources.getDisplayMetrics().xdpi = displayMetrics.xdpi;
            resources.getDisplayMetrics().ydpi = displayMetrics.ydpi;
            resources.getConfiguration().fontScale = 1;
        }
    }

    @Override
    public void show() {
        super.show();

        if (isHuawei() || isHonor()) {
            timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    if (MirrorDialog.this != null && MirrorDialog.this.getWindow() != null) {
                        new Handler(MirrorDialog.this.getContext().getMainLooper()).post(() -> {
                            if (MirrorDialog.this != null && MirrorDialog.this.getWindow() != null) {
                                MirrorDialog.this.getWindow().getDecorView().invalidate();
                            }
                        });
                    }
                }
            }, 0, 10);
        }
    }

    @Override
    public void hide() {
        super.hide();

        AnFileLog.e("wlconnector", "MirrorDialog - hide");

        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();

        AnFileLog.e("wlconnector", "MirrorDialog - dismiss");

        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }


    @Override
    public void cancel() {
        AnFileLog.e("wlconnector", "MirrorDialog - cancel");
    }

	public void init(int width, int height) {
//        try {
//			WindowManager.LayoutParams params = localWindow.getAttributes(); // 获取对话框当前的参数值
//			params.width = width;
//			params.height = height;
//
//			localWindow.setAttributes(params);
//			AnFileLog.e("v1","localWindow.setAttributes(params)=" + params.width + ",h=" + params.height);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
	}


    private Timer timer = null;
    private boolean isHuawei() {
        if (Build.BRAND == null) {
            return false;
        } else {
            return Build.BRAND.toLowerCase(Locale.getDefault()).equals("huawei");
        }
    }
    private boolean isHonor() {
        if (Build.BRAND == null) {
            return false;
        } else {
            return Build.BRAND.toLowerCase(Locale.getDefault()).equals("honor");
        }
    }
}
