package com.autoai.welink.auto.v1;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.os.Build;

public class WiFiUseCellular {
	private ConnectivityManager connectivityManager;
	private ConnectivityManager.NetworkCallback networkCallback;

	boolean useCellular(Context context) {
		connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
		NetworkRequest.Builder req = new NetworkRequest.Builder();
		req.addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR);
		req.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);

		networkCallback = new ConnectivityManager.NetworkCallback() {
			@Override
			public void onAvailable(Network network) {
				if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
					connectivityManager.bindProcessToNetwork(network);
				}
//				connectivityManager.unregisterNetworkCallback(this);
			}
		};

		try {
            connectivityManager.requestNetwork(req.build(), networkCallback);
            return true;
        } catch (SecurityException e) {
		    e.printStackTrace();
        }

        connectivityManager = null;
        networkCallback = null;
        return false;
	}

	void useWifi() {
		if (connectivityManager != null && networkCallback != null) {
			connectivityManager.unregisterNetworkCallback(networkCallback);
		}
		networkCallback = null;
		connectivityManager = null;
	}
}
