package com.autoai.welink.auto.protocol;

import android.os.Handler;
import android.os.Looper;

import com.autoai.welink.auto.WLConnectListener;
import com.autoai.welink.auto.WLMicrophone;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.MainHandler;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by gao<PERSON> on 2019/8/8.
 */

public class MicrophoneProtocol extends ProtocolBase implements WLMicrophone {
    public MicrophoneProtocol(Transmisson transmisson) {
        super(transmisson);
    }

   private WLConnectListener welinkListener;
    public void setWelinkListener(WLConnectListener listener){
        welinkListener = listener;
    }

    @Override
    public void onReciveData(byte[] data) {

        try {
            JSONObject jsonObject = new JSONObject(new String(data));

            if (null == jsonObject) {
                return;
            }

            //读文件
            byte[] bytes = null;
            String fileName = null;
            if (jsonObject.has("FileName")) {
                fileName = jsonObject.optString("FileName");
                bytes = transmisson.read(fileName);
            }

            if (callBack != null) {
                if (null == bytes) {
                    callBack.onCompleted();
                } else {
                    callBack.onSound(bytes,
                        jsonObject.optInt("Rate"),
                        jsonObject.optInt("Bit"),
                        jsonObject.optInt("Channel"));
                }
            }

            if (null != fileName) {
                readFileSuccess(fileName);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知新音乐App开始发送PCM
     */
    private void readFileSuccess(String fileName) {

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", "READ_FILE_SUCCESS");
            obj.put("FileName", fileName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MICROPHONE, json.getBytes());
        }
    }

    //byte[]转int
    private static int byteArrayToInt(byte[] b) {
        return   b[3] & 0xFF |
            (b[2] & 0xFF) << 8 |
            (b[1] & 0xFF) << 16 |
            (b[0] & 0xFF) << 24;
    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected(WLConfigurationImp wlConfiguration) {
        return false;
    }

    @Override
    public void onDisconnected() {
    }

    @Override
    public void onError(int errorCode) {

    }

    private WLMicrophone.Callback callBack;
    @Override
    public void request(Callback cb) {
        MainHandler.getInstance().post(() -> request_imp(cb));
    }
    public void request_imp(Callback cb) {
        callBack = cb;
        transmisson.getProtocolMgr().getActionProtocol().requestVoiceData();
    }

    @Override
    public void stop() {
        MainHandler.getInstance().post(this::stop_imp);
    }
    public void stop_imp() {
        transmisson.getProtocolMgr().getActionProtocol().stopVoiceData();
    }
}
