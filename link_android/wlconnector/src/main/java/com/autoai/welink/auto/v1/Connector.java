package com.autoai.welink.auto.v1;

import android.app.Application;
import android.content.Context;

import com.autoai.welink.auto.WLConfiguration;
import com.autoai.welink.auto.WLConnector;
import com.autoai.welink.auto.WLMicrophone;
import com.autoai.welink.auto.WLMusic;
import com.autoai.welink.auto.WLSound;
import com.autoai.welink.auto.WLTBTInfo;
import com.autoai.welink.auto.WLConnectListener;
import com.autoai.welink.auto.client.WLAutoConnectListener;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.protocol.Transmisson;

public class Connector extends WLConnector implements WLAutoConnectListener {
    private Boolean bRecordstart = false;//临时加判断是否开始录屏，后期红军修改接口

	private WLConnectListener WLConnectListener;
//	private CodecController codecController;
	private Context context;
	private Transmisson transmisson;
	private final WiFiUseCellular wiFiUseCellular = new WiFiUseCellular();
	private BackgroundNotify backgroundNotify;
	private final boolean useCellular=false;//是否在有wifi时仍然使用流量访问网络
    private WLConfigurationImp wlConfiguration;

	@Override
	public boolean onConnected(WLConfigurationImp wlConfiguration) {
        this.wlConfiguration=(WLConfigurationImp)wlConfiguration;
		AnFileLog.e("v1","onHeartbeat connectVersion="+wlConfiguration.getConnectVersion());

        Context appCtx = context.getApplicationContext();
        if (appCtx instanceof Application) {
            backgroundNotify = new BackgroundNotify((Application) appCtx);
            backgroundNotify.start(transmisson.getProtocolMgr().getBackgroundStatusProtocol());
        }else{
            throw new IllegalStateException("cannot obtain the Application object");
        }
//        backgroundNotify = new BackgroundNotify((Application)context);
        backgroundNotify.start(transmisson.getProtocolMgr().getBackgroundStatusProtocol());

       /* codecController = new CodecController2(this, context, null);

        codecController.setVideoFPS(this.wlConfiguration.getFps());
        codecController.setWH(wlConfiguration.getHUScreenWidth(), wlConfiguration.getHUScreenHeight(), wlConfiguration.getHUDensityDpi());
        codecController.start(transmisson.connectStr);*/

		bRecordstart=false;
		WLMusic wlMusic = transmisson.getProtocolMgr().getWLMusic();
		WLSound wlSound = transmisson.getProtocolMgr().getWLSound();
		WLTBTInfo wltbtInfo = transmisson.getProtocolMgr().getWLTBTInfo();
        WLMicrophone wlMicrophone = transmisson.getProtocolMgr().getMicrophoneProtocol();

       /* MirrorDialog wlMirrorDialog = codecController.getCaptureDialog();
        if (wlMirrorDialog==null)
        	return false;

        wlConfiguration.setDisplayCapability(wlMirrorDialog);*/
        wlConfiguration.setMusicCapability(wlMusic);
        wlConfiguration.setSoundCapability(wlSound);
        wlConfiguration.setTBTInfoCapability(wltbtInfo);
        wlConfiguration.setMicrophoneCapability(wlMicrophone);

		boolean startRecord = false;
        if (WLConnectListener != null) {
            startRecord = WLConnectListener.onConnected(this);
            transmisson.getProtocolMgr().getCommandProtocol().setWelinkListener(WLConnectListener);
            transmisson.getProtocolMgr().getMicrophoneProtocol().setWelinkListener(WLConnectListener);
        }

		/*if (startRecord){
            codecController.showPresentation();

//            if (!isServerCodec) {
//                codecController.recordStart();
//            }
//            codecController.recordController.recordStart();//回调返回后,就开始录屏

            transmisson.getProtocolMgr().setDialog(codecController.getCaptureDialog());
        }else{
            AnFileLog.e("v1","onHeartbeat return false!");

            codecController.stop();
            codecController.release();
            codecController = null;
        }*/
		return startRecord;
	}

	@Override
	public void onError(int errorCode) {
        if (WLConnectListener != null) {
            WLConnectListener.onError(errorCode);
        }
        WLConnectListener =null;
        if (useCellular)
            wiFiUseCellular.useWifi();

        //清理录屏的相关内容
      /*  if (codecController!=null){
            codecController.stop();
            codecController.release();
        }
        codecController=null;*/
    }
	@Override
	public void onDisconnected() {
		//断开连接
		if (WLConnectListener != null)
			WLConnectListener.onDisconnected();
        WLConnectListener =null;
		if (useCellular)
			wiFiUseCellular.useWifi();

		//清理录屏的相关内容
		/*if (codecController!=null){
			codecController.stop();
			codecController.release();
		}
        codecController=null;*/
	}

    /**
     * 返回Launcher
     */
    public void triggerLauncher() {
        MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                transmisson.getProtocolMgr().getActionProtocol().returnLauncherAction();
            }
        });
    }

    /**
     * 将自己调到前台
     */
    public void triggerForeground() {
        MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                transmisson.getProtocolMgr().getActionProtocol().foregroundSelfAction();
            }
        });
    }

    /**
     * 激活语音识别
     */
    public void triggerSpeechRecognition() {
        MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                transmisson.getProtocolMgr().getActionProtocol().openSRAction();
            }
        });
    }

//    /**
//     * 完成Home状态切换
//     *
//     * @param home true:进入Home状态，false：退出Home状态
//     */
//    @Override
//    public void reportHomeStatus(boolean home) {
//        if (home)
//            transmisson.getProtocolMgr().getActionProtocol().enterHomeAction();
//        else
//            transmisson.getProtocolMgr().getActionProtocol().quitHomeAction();
//    }

    public Connector() {
        super(null);
    }

	/***
	 * 创建连接器对象
	 * @param context
	 */

	private Connector(Context context) {
		super(context);
	}

    private static volatile boolean isHaveInstance = false;

	/**
	 * 释放资源
	 */
	@Override
	public void release() {
	    MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                isHaveInstance = false;

                if (transmisson != null) {
                    transmisson.disconnect();
                    transmisson = null;
                }

                if (backgroundNotify != null){
                    backgroundNotify.stop();
                    backgroundNotify = null;
                }

                MainHandler.release();
            }
        });
	}

	public static boolean connect(final Context context, final String connectStr, final WLConnectListener listener) {
	    if (isHaveInstance) {
	        return false;
        }

	    isHaveInstance = true;

	    MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                Connector connector = new Connector(context);
                connector.WLConnectListener = listener;
                connector.connect(context, connectStr);
            }
        });

        return true;
	}

	private void connect(Context context, String connectStr) {
		this.context=context;
		transmisson =new Transmisson(context);
		transmisson.connect(connectStr,this);
		AnFileLog.e("v1","connector connect "+connectStr);
	}
	/**
	 * 断开于welink app的连接
	 */
	@Override
	public void disconnect() {
	    MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                if (transmisson != null)
                    transmisson.disconnect();
            }
        });
	}

	public void pause() {
		/*AnFileLog.e("v1","pause "+codecController);
		MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                if (codecController != null)
                    codecController.pause();
            }
        });*/
	}

	public void resume() {
//		AnFileLog.e("v1","resume "+codecController);
	/*	MainHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                if (codecController != null) {
                    if (bRecordstart) {
                        codecController.resume();
                    } else {
                        bRecordstart = true;
                        codecController.recordStart();//回调返回后,就开始录屏
                    }
                }
            }
        });*/
	}

    public boolean isCodecAlive() {
       /* if (codecController != null) {
            return codecController.isCodecAlive();
        }*/

        return false;
    }

    @Override
    public WLConfiguration getConfiguration(){
        return wlConfiguration;
    }
}
