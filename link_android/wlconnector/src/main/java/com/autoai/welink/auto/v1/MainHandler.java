package com.autoai.welink.auto.v1;

import android.os.Handler;
import android.os.Looper;

public class <PERSON><PERSON><PERSON><PERSON> extends Handler {
    static class LooperThread extends Thread {
        public Looper looper = null;
        public void run() {
            Looper.prepare();

            synchronized (lock) {
                looper = Looper.myLooper();
                lock.notify();
            }

            Looper.loop();
            looper = null;
        }
    }

	private static MainHandler instance;
    private static LooperThread looperThread;
    private static final Object lock = new Object();

	public static MainHandler getInstance() {
	    synchronized (MainHandler.class) {
            if (null == instance) {
                looperThread = new LooperThread();

                synchronized (lock) {
                    looperThread.start();

                    try {
                        lock.wait();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                instance = new MainHandler();
            }
        }
		return instance;
	}

	public static void release() {
	    if (looperThread != null) {
	        if (looperThread.looper != null) {
                looperThread.looper.quit();
            }

            looperThread.interrupt();
        }

        looperThread = null;
        instance = null;
    }

	private MainHandler() {
        super(looperThread.looper);
	}
}
