package com.autoai.welink.auto.protocol;

import com.autoai.welink.auto.client.WLAutoConnectListener;
import com.autoai.welink.auto.client.WLConfigurationImp;

//协议基类,每个协议实现都需要自此类继承
public class ProtocolBase implements WLAutoConnectListener{
	//每个协议默认都会持有一个传输对象,用来发送数据
    final Transmisson transmisson;
	public ProtocolBase(Transmisson transmisson) {
		this.transmisson = transmisson;
	}

	//=========================================================
	//每个协议默认都需要接收自己协议的数据包,此处传入的数据包只是协议自身的数据,不会包含底层模块自行增加的各种协议头
	public void onReciveData(byte[] data) {

	}
	//=========================================================
	//每个协议默认都需要响应网络状态,只处理自己关心的状态变化
	@Override
	public boolean onConnected(WLConfigurationImp wlConfiguration) {
		return false;
	}

	@Override
	public void onDisconnected() {

	}

	@Override
	public void onError(int errorCode) {

	}

    public  String getRandomFileName() {

        //获取当前时间+随机6位字符串
        String randomStr = getRandomString(6);//"aeviiq";
        long nowTime = System.currentTimeMillis();
        return randomStr+nowTime;
    }

    private static final String string = "abcdefghijklmnopqrstuvwxyz";
    /**
     * 生成随机字符串
     * @param length 随机字符串长度
     */
    private static String getRandomString(int length){
        StringBuffer sb = new StringBuffer();
        int len = string.length();
        for (int i = 0; i < length; i++) {
            sb.append(string.charAt(getRandom(len)));
        }

        return sb.toString();
    }

    private static int getRandom(int length) {

        return (int)(Math.random() * length);
    }
}
