package com.autoai.welink.auto;

public interface WLConnectListener {
    /**
     * 第三方导航App连接成功,并返回各个构建好的功能模块对象
     * 该回调函数保证是在主线程回调，便于进行UI构建，其它回调函数不保证在主线程回调
     *
     * @param wlConnector       管理与WeLink的连接
     * @return 是否接受在该分辨率的dialog上创建投屏用的界面
     * true:接受,将自动开始录屏投屏动作, false:不接受,不会开启投屏动作,并断开与WeLink的连接
     */
    boolean onConnected(WLConnector wlConnector);

    /**
     * 需要在回调函数里调用WLConnector的release函数，以释放资源，所有其他的对象都依赖于WLConnector, 会自动被释放
     */
    void onDisconnected();

    /**
     * 连接发生错误
     * @param errorCode 错误码：
     *                  1. WeLink服务无效，有可能是WeLink App没有运行或者没有连接车机
     *                  2. 连接字符串不正确
     *                  3. WLConnector版本太老，无法连接当前的WeLink服务
     *                  4. 重复连接
     *                  5. 连接WeLink服务失败
     *                  6. App不支持该分辨率
     */
    void onError(int errorCode);

    /**
     * 车机命令
     * @param command 命令字符串 -- 参照文档《车机命令》
     */
    void onCommand(String command);

}
