package com.autoai.welink.auto;

import android.app.Dialog;

public interface WLConfiguration {
    /**
     * 获取车机显示能力
     * 加载View时要使用该Dialog的Context，否则车机DPI将不起作用
     */
    Dialog getDisplayCapability();

    /**
     * 获取车机语音提示能力
     */
    WLSound getSoundCapability();

    /**
     * 获取车机TBTInfo展示能力
     */
    WLTBTInfo getTBTInfoCapability();

    /**
     * 获取车机音乐播放能力
     */
    WLMusic getMusicCapability();

    /**
     * 获取车机声音数据能力
     */
    WLMicrophone getMicrophoneCapability();

    /**
     * 获取车机蓝牙电话能力
     */
    WLBluetoothPhone getBluetoothPhoneCapability();

    /**
     * 获取车机屏幕宽
     *
     * @return 车机屏幕宽 hu screen width
     */
    int getHUScreenWidth();

    /**
     * 获取车机屏幕高
     *
     * @return 车机屏幕高 hu screen height
     */
    int getHUScreenHeight();

    /**
     * 获取车机屏幕密度
     *
     * @return 车机屏幕密度 density dpi
     */
    int getHUDensityDpi();

    /**
     * 获取车辆类别
     *
     * @return 车辆类别
     */
    String getVehicleType();

    /**
     * 获取车辆标识
     *
     * @return 车辆标识
     */
    String getVehicleID();

    /**
     * 获取用户标识
     *
     * @return 用户标识
     */
    String getUserID();
}
