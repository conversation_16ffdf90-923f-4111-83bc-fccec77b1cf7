package com.autoai.welink.auto.protocol;

import android.app.Dialog;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.view.Display;
import android.view.InputDevice;
import android.view.MotionEvent;
import android.view.View;

import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.AnFileLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

//回控协议
public class TouchEventProtocol extends ProtocolBase {
    private final Handler mHandler;
    private final static int CANSHU = 1;

    public TouchEventProtocol(Transmisson transmisson) {
		super(transmisson);

        mHandler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(Message msg) {
                switch(msg.what){
                    case CANSHU :
                        byte[] data = msg.getData().getByteArray("touchData");
                        handleTouchData(data);
                        break;
                    default :
                        break;
                }
            }
        };
	}

	//回控协议
	//=========================================================
	//每个协议默认都需要接收自己协议的数据包,此处传入的数据包只是协议自身的数据,不会包含底层模块自行增加的各种协议头
	@Override
	public void onReciveData(byte[] data) {
        Message msg = mHandler.obtainMessage(CANSHU);
        Bundle bundle = new Bundle();
        bundle.putByteArray("touchData",data);
        msg.setData(bundle);//mes利用Bundle传递数据
        msg.sendToTarget();
	}

	private void handleTouchData(byte[] data) {
        AnFileLog.e("wlconnector-protocol","handleTouchData start");
        try {
            String json = new String(data);
            JSONObject jsonObject = new JSONObject(json);
            if (null == jsonObject)
                return;
            if (!jsonObject.has("pointerCount"))
                return;
            int pointerCount = jsonObject.getInt("pointerCount");
            int action = jsonObject.getInt("action");
            if (pointerCount == 1) {
                int raw_x = jsonObject.getInt("raw_x");
                int raw_y = jsonObject.getInt("raw_y");
                AnFileLog.e("wlconnector-protocol","handleTouchData 1");
                parseMotionEvent(action, raw_x, raw_y);
            } else if (pointerCount > 1) {
                JSONArray array_x = jsonObject.getJSONArray("array_x");
                JSONArray array_y = jsonObject.getJSONArray("array_y");
                int[] xArr = new int[pointerCount];
                int[] yArr = new int[pointerCount];
                if (array_x == null)
                    return;
                for (int i = 0; i < array_x.length(); i++) {
	                xArr[i] = yArr[i] = 0;
                	try {
		                xArr[i] = array_x.getInt(i);
		                yArr[i] = array_y.getInt(i);
	                }catch (Exception e){
                		e.printStackTrace();
	                }
                }
                AnFileLog.e("wlconnector-protocol","handleTouchData 2");
                parseMotionEvent(action, xArr, yArr);
            }
            AnFileLog.e("wlconnector-protocol","handleTouchData end");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

	//每个协议默认都需要响应网络状态,只处理自己关心的状态变化
	@Override
	public boolean onConnected(WLConfigurationImp wlConfiguration) {
		return false;
	}

	@Override
	public void onDisconnected() {

	}

	@Override
	public void onError(int errorCode) {

	}

	//=========================================================
	//功能实现相关
	private Dialog dialog;

	public void setDialog(Dialog dialog) {
	    this.dialog = dialog;
	}

	public void onTouchEvent(MotionEvent event) {
		int action = event.getAction();
		int pointerCount = event.getPointerCount();
		int raw_x = (int) event.getRawX();
		int raw_y = (int) event.getRawY();
		String json = "{}";
		JSONObject obj = new JSONObject();
		try {
			obj.put("pointerCount", pointerCount);
			if (pointerCount == 1) {
				obj.put("action", action);
				obj.put("raw_x", raw_x);
				obj.put("raw_y", raw_y);
			} else if (pointerCount > 1) {
				JSONArray array_x = new JSONArray();
				JSONArray array_y = new JSONArray();
				for (int i = 0; i < pointerCount; i++) {
					try {
						array_x.put((int) event.getX(i));
						array_y.put((int) event.getY(i));
					}catch (Exception e){
						e.printStackTrace();
					}
				}
//				action &= 0xFF;
//				if (action == MotionEvent.ACTION_POINTER_DOWN)
//					action = MotionEvent.ACTION_DOWN;
//				if (action == MotionEvent.ACTION_POINTER_UP)
//					action = MotionEvent.ACTION_UP;
				obj.put("array_x", array_x);
				obj.put("array_y", array_y);
				obj.put("action", action);
			}
			json = obj.toString();
			if (transmisson != null) {
				transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_TOUCH_EVENT, json.getBytes());
			}
		} catch (JSONException e) {
			e.printStackTrace();
		}
	}

	//=========================================================
	private final float DEFAULT_SIZE = 1.0f;
	private final int DEFAULT_META_STATE = 0;
	private final int DEFAULT_BUTTONSTATE = 0;
	private final float DEFAULT_PRECISION_X = 1.0f;
	private final float DEFAULT_PRECISION_Y = 1.0f;
	private final int DEFAULT_DEVICE_ID = 1;
	private final int DEFAULT_EDGE_FLAGS = 0;
	private final int DEFAULT_FLAGS = 0;
	private long g_down_time;

	private void parseMotionEvent(int type, int x, int y) {
		AnFileLog.e("wlconnector-protocol","parseMotionEvent 1 " + type + ",x=" + x + ",y=" + y);
		long now = SystemClock.uptimeMillis();
		switch (type&0xFF) {
			case MotionEvent.ACTION_DOWN:
			case MotionEvent.ACTION_POINTER_DOWN:
				g_down_time = now;
				justMotionEventInput(type, now, x, y, 1.0f);
				break;
			case MotionEvent.ACTION_MOVE:
				justMotionEventInput(type, now, x, y, 1.0f);
				break;
			case MotionEvent.ACTION_UP:
			case MotionEvent.ACTION_POINTER_UP:
				justMotionEventInput(type, now, x, y, 0.0f);
				break;
		}
	}

	private void parseMotionEvent(int type, int x[], int y[]) {
		AnFileLog.e("wlconnector-protocol","parseMotionEvent 2 " + type + ",x=" + x + ",y=" + y);
		long now = SystemClock.uptimeMillis();
		switch (type&0xFF) {
			case MotionEvent.ACTION_DOWN:
			case MotionEvent.ACTION_POINTER_DOWN:
				justMotionEventInput(type, now, x, y, 1.0f);
				break;
			case MotionEvent.ACTION_MOVE:
				justMotionEventInput(MotionEvent.ACTION_MOVE, now, x, y, 1.0f);
				break;
			case MotionEvent.ACTION_UP:
			case MotionEvent.ACTION_POINTER_UP:
				justMotionEventInput(type, now, x, y, 0.0f);
				break;
		}
	}

	private void justMotionEventInput(int action, long when, int x, int y, float pressure) {
        AnFileLog.e("wlconnector-protocol","justMotionEventInput 1");
		MotionEvent motionEvent = MotionEvent.obtain(g_down_time, when, action, x, y, pressure, DEFAULT_SIZE, DEFAULT_META_STATE, DEFAULT_PRECISION_X, DEFAULT_PRECISION_Y, DEFAULT_DEVICE_ID, DEFAULT_EDGE_FLAGS);
		motionEvent.setSource(InputDevice.SOURCE_TOUCHSCREEN);
		if (dialog != null) {
            AnFileLog.e("wlconnector-protocol","dispatchTouchEvent");
		    dialog.dispatchTouchEvent(motionEvent);
        }
	}

	private void justMotionEventInput(int action, long when, int x[], int y[], float pressure) {
        AnFileLog.e("wlconnector-protocol","justMotionEventInput 2");
		int pointerCount = Math.min(x.length, y.length);
		MotionEvent.PointerProperties pointerProperties[] = new MotionEvent.PointerProperties[pointerCount];
		MotionEvent.PointerCoords pointerCoords[] = new MotionEvent.PointerCoords[pointerCount];

		for (int i = 0; i < pointerCount; i++) {
			MotionEvent.PointerProperties pps = new MotionEvent.PointerProperties();
			pps.id = i;
			pps.toolType = MotionEvent.TOOL_TYPE_FINGER;
			pointerProperties[i] = pps;

			MotionEvent.PointerCoords pcs = new MotionEvent.PointerCoords();
			pcs.pressure = pressure;
			pcs.x = x[i];
			pcs.y = y[i];
			pointerCoords[i] = pcs;
		}

		MotionEvent motionEvent = MotionEvent.obtain(g_down_time, when, action,
				pointerCount, pointerProperties, pointerCoords,
				DEFAULT_META_STATE, DEFAULT_BUTTONSTATE, DEFAULT_PRECISION_X,
				DEFAULT_PRECISION_Y, DEFAULT_DEVICE_ID, DEFAULT_EDGE_FLAGS,
				InputDevice.SOURCE_TOUCHSCREEN, DEFAULT_FLAGS);
        if (dialog != null) {
            AnFileLog.e("wlconnector-protocol","dispatchTouchEvent");
            dialog.dispatchTouchEvent(motionEvent);
        }
	}
	//=========================================================

}
