package com.autoai.welink.auto.protocol;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.autoai.welink.auto.client.WLAutoClient;
import com.autoai.welink.auto.client.WLAutoConnectListener;
import com.autoai.welink.auto.client.WLAutoDataListener;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.AnFileLog;
import com.autoai.welink.auto.v1.MainHandler;

import java.util.concurrent.Semaphore;

public class Transmisson {
	private final ProtocolMgr protocolMgr;
	public String connectStr;

	public ProtocolMgr getProtocolMgr() {
		return protocolMgr;
	}
	//=========================================================
	private boolean connected = false;//底层连接模块会多次调用onDisconnected,通过状态遍历进行过滤
	private final Context context;
	private WLAutoClient wlAutoClient;
	private WLAutoConnectListener wlAutoConnectListenerOuter;
	private WLAutoConnectListener wlAutoConnectListener = new WLAutoConnectListener() {
		@Override
		public void onError(int errorCode) {
			if (wlAutoConnectListenerOuter != null)
				wlAutoConnectListenerOuter.onError(errorCode);
		}

		boolean retOnConnected=false;
        Semaphore semaphore = null;
		@Override
		public boolean onConnected(WLConfigurationImp wlConfiguration) {
			connected = true;
            semaphore = new Semaphore(0);
			if (wlAutoConnectListenerOuter != null) {
                final WLConfigurationImp wlConfiguration2=wlConfiguration;
				new Handler(Looper.getMainLooper()).post(new Runnable() {
					@Override
					public void run() {
                        try {
                            retOnConnected = wlAutoConnectListenerOuter.onConnected(wlConfiguration2);
                        }catch (Exception e){ e.printStackTrace(); }
                        semaphore.release();
					}
				});
			}else{
                semaphore.release();
            }
            try {
                semaphore.acquire();
                semaphore=null;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            wlAutoClient.connectResult(retOnConnected);

            if (retOnConnected) {
                protocolMgr.onConnected(wlConfiguration);
            }

            return retOnConnected;
		}

		@Override
		public void onDisconnected() {
			if (connected) {
				connected = false;
				protocolMgr.onDisconnected();
				if (wlAutoConnectListenerOuter != null)
					wlAutoConnectListenerOuter.onDisconnected();
                disconnect();
			}
		}
	};
	private final WLAutoDataListener wlAutoDataListener = new WLAutoDataListener() {
		@Override
		public void onReciveData(int type, byte[] data) {
			//AnFileLog.e("onReciveData data.length=" + data.length);
			protocolMgr.onReciveData(type, data);
//			try {
//				int packsize = data.length;
//				if (data.length < 4) return;//数据包异常,长度不够
//				//解析数据,分出type和数据包
//				ByteArrayInputStream bais = new ByteArrayInputStream(data);
//				DataInputStream bis = new DataInputStream(bais);
//				int type = bis.readInt();
//				byte[] pack = new byte[packsize - 4];//申请数据数据包的缓冲区
//				int readsize = bis.read(pack);
//				if (readsize != packsize - 4) return;//数据包异常,长度不对
//				protocolMgr.onReciveData(type, pack);
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
			return;
		}
	};

	public Transmisson(Context context) {
		this.context = context;
		protocolMgr = new ProtocolMgr(this);
	}

	public void connect(String connectStr, WLAutoConnectListener connectListener) {
		this.connectStr = connectStr;
		if (wlAutoClient == null) {
			wlAutoConnectListenerOuter = connectListener;
			wlAutoClient = new WLAutoClient(context, wlAutoConnectListener);

			wlAutoClient.setAutoDataListener(wlAutoDataListener);
            wlAutoClient.connect(connectStr, MAX_VER, MIN_VER);
		}
	}

	public void disconnect() {
		if (wlAutoClient != null)
			wlAutoClient.disconnect();
		wlAutoClient = null;
		if (wlAutoConnectListener != null)
			wlAutoConnectListener.onDisconnected();
		wlAutoConnectListener = null;
	}

    public void remove(String filename) {
	    if (wlAutoClient != null) {
	        wlAutoClient.remove(filename);
        }
    }

    public boolean create(String filename, byte[] buffer) {
	    if (wlAutoClient != null) {
	        return wlAutoClient.create(filename, buffer);
        }

	    return false;
    }

    public byte[] read(String filename) {
	    if (wlAutoClient != null) {
	        return  wlAutoClient.read(filename);
        }

	    return null;
    }

    public boolean isMirrorScreen() {
	    if (wlAutoClient != null) {
	        return wlAutoClient.isMirrorScreen();
        }

	    return false;
    }

	/**
	 * 发送数据
	 *
	 * @param type  数据类型,TRANSIMISSON_TYPE_HEART_BEAT等以TRANSIMISSON_TYPE_*类型
	 * @param bytes 数据
	 */
	public void sendData(final int type, final byte[] bytes) {
		AnFileLog.e("wlconnector-protocol","sendData type="+type+",length="+bytes.length);
		if (!connected)return;

        MainHandler.getInstance().post(() -> {
            if (wlAutoClient != null) {
                wlAutoClient.sendData(type, bytes);
            }
        });
	}

    private static final int MAX_VER = 1;
    private static final int MIN_VER = 1;
}
