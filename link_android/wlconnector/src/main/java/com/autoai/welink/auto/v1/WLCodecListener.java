package com.autoai.welink.auto.v1;

public interface WLCodecListener {
    int CODCE_TYPE_IMAGE = 0x0001;
    int CODEC_TYPE_VEDIO = 0x0002;

    int CODEC_STATUS_ERROR = 0x0001;
    int CODEC_STATUS_START = 0x0002;

    /**
     * 接收截屏数据
     *
     * @param type  截屏类型
     * @param bytes 截屏数据
     */
    void onCallBack(int type, byte[] bytes);


    /**
     * 截屏状态回调
     *
     * @param type   0：视频异常中断
     * @param object
     */
    void onCodecStatusCallBack(int type, Object object);

    /**
     * 屏幕无变化时回调
     */
    void onScreenNoUpdate();
}
