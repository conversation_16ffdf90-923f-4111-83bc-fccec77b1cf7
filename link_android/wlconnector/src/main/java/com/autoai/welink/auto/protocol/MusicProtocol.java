package com.autoai.welink.auto.protocol;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.SystemClock;

import com.autoai.welink.auto.WLMusic;
import com.autoai.welink.auto.client.WLConfigurationImp;
import com.autoai.welink.auto.v1.AnFileLog;
import com.autoai.welink.auto.v1.MainHandler;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;

/**
 * Created by gaole on 2019/2/21.
 */

public class MusicProtocol extends ProtocolBase implements WLMusic {

    private Callback callBack;

    private long lastPosition = 0;
    private long tickPosition = 0;
    private long lastTimeMillis = 0;
    private Thread tickThread = null;
    private boolean isFocus = false;
    private long sTotalLen = 0;
    private int sRate = 0;
    private int sBit = 0;
    private int sChannel = 0;
    private boolean bTickCallback = false;//标志onTick是否回调（解决调用不同步问题）

    private static final String PROTOCOL_MUSIC_ID3 = "ID3";
    private static final String PROTOCOL_MUSIC_PCM = "PCM";
    private static final String PROTOCOL_MUSIC_ORDER = "order";
    private static final String PROTOCOL_MUSIC_CANCEL = "cancel";

    private static final String PROTOCOL_MUSIC_START = "start";
    private static final String PROTOCOL_MUSIC_STOP = "stop";
    private static final String PROTOCOL_MUSIC_PAUSE = "pause";
    private static final String PROTOCOL_MUSIC_RESUME = "resume";

    private static final String PROTOCOL_MUSIC_REGISTER = "register";
    private static final String PROTOCOL_MUSIC_REQUEST_FOCUS = "request_focus";
    private static final String PROTOCOL_MUSIC_CANCEL_SUCCESS = "cancel_success";
    private static final String PROTOCOL_MUSIC_PLAY_SUCCESS = "play_success";
    private static final String PROTOCOL_MUSIC_READFILE_SUCCESS = "read_file_success";

    public MusicProtocol(Transmisson transmisson) {
        super(transmisson);
    }

    private void release() {
        stop();
    }

    //每个协议默认都需要接收自己协议的数据包,此处传入的数据包只是协议自身的数据,不会包含底层模块自行增加的各种协议头
    @Override
    public void onReciveData(byte[] data) {

        if (null == callBack) {
            return;
        }

        try {
            JSONObject jsonObject = new JSONObject(new String(data));
            if (null == jsonObject) {
                return;
            }

            String type = jsonObject.optString("Type");
            if (type.equals(PROTOCOL_MUSIC_CANCEL)) {
                AnFileLog.e("ASDFXXXCV connector","Music onReciveData PROTOCOL_MUSIC_CANCEL ");
                isFocus = false;
                stopTickThread();
                tickPosition = 0;
                lastPosition = 0;
                lastTimeMillis = 0;
                callBack.onDisableFocus();

                JSONObject obj = new JSONObject();
                try {
                    obj.put("Type", PROTOCOL_MUSIC_CANCEL_SUCCESS);

                } catch (JSONException e) {
                    e.printStackTrace();
                }

                String json = obj.toString();
                if (transmisson != null) {
                    transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
                }
            }
            else if (type.equals(PROTOCOL_MUSIC_PLAY_SUCCESS)) {
                AnFileLog.e("ASDFXXXCV connector","Music onReciveData PROTOCOL_MUSIC_PLAY_SUCCESS ");
                isFocus = true;//回调前先设置，避免使用者在回调中调用其它接口
                callBack.onEnableFocus();

            }
            else if (type.equals(PROTOCOL_MUSIC_READFILE_SUCCESS)) {
                String fileName = jsonObject.optString("FileName");
                transmisson.remove(fileName);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    //每个协议默认都需要响应网络状态,只处理自己关心的状态变化
    @Override
    public boolean onConnected(WLConfigurationImp wlConfiguration) {
        return false;
    }

    @Override
    public void onDisconnected() {
        release();
    }

    @Override
    public void onError(int errorCode) {
        release();
    }

    @Override
    public void register(Callback callback) {
        AnFileLog.e("ASDFXXXCV connector","Music register!!!");
        MainHandler.getInstance().post(() -> register_imp(callback));
    }
    private void register_imp(Callback callback) {
        AnFileLog.e("ASDFXXXCV connector","Music register imp!!!");
        callBack = callback;

        boolean isCancelRegister = false;
        if (null == callback) {
            isCancelRegister = true;
        }

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_REGISTER);
            obj.put("IsCancelRegister", isCancelRegister);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
        AnFileLog.e("ASDFXXXCV connector","Music register ok!!!");
    }

    /**
     * 请求音频焦点
     *
     * 以下2中情况请求音频焦点可以成功：
     * 1.手机前台的音乐App
     * 2.投屏状态的音乐App
     */
    @Override
    public void requestFocus() {
        AnFileLog.e("ASDFXXXCV connector","requestFocus!!!");
        MainHandler.getInstance().post(this::requestFocus_imp);
    }
    private void requestFocus_imp() {
        AnFileLog.e("ASDFXXXCV connector","Music requestFocus imp!!!");
        // 如果不是激活状态，则只有 app在前台 || 正在投屏 才允许发送play消息等待进入激活状态
        if (!transmisson.getProtocolMgr().getBackgroundStatusProtocol().isForeground &&
            !transmisson.isMirrorScreen()) {
            return;
        }

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_REQUEST_FOCUS);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
        AnFileLog.e("ASDFXXXCV connector","Music requestFocus ok!!!");
    }

    @Override
    public boolean start(long totalLen, int rate, int bit, int channel) {
        if (!isFocus) {
            AnFileLog.e("ASDFXXXCV connector","Music start noFocus!!!");
            return false;
        }

        MainHandler.getInstance().post(() -> start_imp(totalLen, rate, bit, channel));
        return true;
    }
    public void start_imp(long totalLen, int rate, int bit, int channel) {
        sTotalLen = totalLen;
        sRate = rate;
        sBit = bit;
        sChannel = channel;

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_START);
            obj.put("TotalLen", totalLen);
            obj.put("Rate", rate);
            obj.put("Bit", bit);
            obj.put("Channel", channel);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }

        AnFileLog.e("ASDFXXXCV connector","Music start!!!");
    }

    @Override
    public boolean stop() {
        if (!isFocus) {
            return false;
        }

        bTickCallback = false;
        MainHandler.getInstance().post(this::stop_imp);
        return true;
    }
    public void stop_imp() {
        stopTickThread();
        tickPosition = 0;
        lastPosition = 0;
        lastTimeMillis = 0;

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_STOP);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }

        AnFileLog.e("ASDFXXXCV connector","Music stop!!!");
    }

    @Override
    public boolean resume() {
        if (!isFocus) {
            AnFileLog.e("ASDFXXXCV connector","resume abort, isFocus: " + isFocus + ", lastPosition: " + lastPosition);
            return false;
        }
        MainHandler.getInstance().post(this::resume_imp);
        return true;
    }
    public void resume_imp() {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_RESUME);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }

        if (lastPosition > 0){
            // lastPosition 不合法时, 依旧透传对应的暂停和播放事件, 不过不再触发进度通知
            // 后续 pcm 数据更新时, 均有进度通知检查的
            startTickThread();
        }

        AnFileLog.e("ASDFXXXCV connector","Music resume!!!");
    }

    @Override
    public boolean pause() {
        if (!isFocus) {
            AnFileLog.e("ASDFXXXCV connector","pause abort, lastPosition: " + lastPosition);
            return false;
        }
        bTickCallback = false;
        MainHandler.getInstance().post(this::pause_imp);
        return true;
    }
    public void pause_imp() {
        stopTickThread();

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_PAUSE);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }

        AnFileLog.e("ASDFXXXCV connector","Music pause!!!");
    }

    @Override
    public 	boolean updateID3(String source, String artist, String title, String album, String lyric, int lyricType, int duration, Bitmap cover){
        if (!isFocus) {
            return false;
        }
        MainHandler.getInstance().post(() -> updateID3_imp(source, artist, title, album, lyric, lyricType, duration, cover));
        return true;
    }
    public void updateID3_imp(String source, String artist, String title, String album, String lyric, int lyricType, int duration, Bitmap cover){
        //写文件
        String fileName = null;
        int count = 0;
        if (null != cover) {
            do {
                if (fileName == null) {
                    fileName = getRandomFileName();
                } else {
                    fileName += count;
                }
            } while (!transmisson.create(fileName, getBytesByBitmap(cover)) && ++count < 3);
        }
        if (count >= 3) {
            return;
        }

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_ID3);
            obj.put("Source", source);
            obj.put("Artist", artist);
            obj.put("Title", title);
            obj.put("Album", album);
            obj.put("Lyric", lyric);
            obj.put("LyricType", lyricType);
            obj.put("Duration", duration);
            obj.put("FileName", fileName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
    }

    /**
     * 更新播放顺序
     *
     * @param order 播放顺序：参见《车机命令》，0: 循环播放、1: 顺序播放、2: 随机播放、3: 单曲循环播放
     */
    @Override
    public  boolean updateOrder(int order) {
        if (!isFocus) {
            return false;
        }
        MainHandler.getInstance().post(() -> updateOrder_imp(order));
        return true;
    }
    public  void updateOrder_imp(int order) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_ORDER);
            obj.put("Order", order);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String json = obj.toString();
        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, json.getBytes());
        }
    }

    private void stopTickThread(){
        if (tickThread != null) {
            AnFileLog.e("ASDFXXXCV connector","Music stopTickThread!!!");
            tickThread.interrupt();
            tickThread = null;
        }
    }

    private void startTickThread(){
        if (tickThread == null) {
            if(tickPosition < 0) {
                //lastPosition==-1的直播处理不更新进度
                return;
            }
            bTickCallback = true;
            tickThread = new Thread() {
                @Override
                public void run() {
                    AnFileLog.e("ASDFXXXCV connector","Music startTickThread!!!");
                    while (!isInterrupted() && bTickCallback) {
                        try{
                            long tickTimeMillis = SystemClock.elapsedRealtime() - lastTimeMillis;
                            long pos = tickPosition + sRate * sChannel * sBit * tickTimeMillis / 8 / 1000L;
                            AnFileLog.e("ASDFXXXCV connector", "Music startTickThread ## 111 onTick = " + pos + ", tick:" + tickPosition + ", last: " + lastPosition + ", total:" + sTotalLen);
                            if (pos <= lastPosition) {
                                AnFileLog.e("ASDFXXXCV connector", "Music startTickThread ## 111 onTick callback");
                                callBack.onTick(Math.min(pos, sTotalLen));
                            }

                            if (pos >= sTotalLen) {
                                break;
                            }

                            Thread.sleep(300);
                        }catch(InterruptedException e){
                            e.printStackTrace();
                            break;//捕获到异常之后，执行break跳出循环。
                        }
                    }
                }
            };
            tickThread.start();
        }
    }

    @Override
    public boolean updatePCM(long position, byte[] pcm) {
        if (!isFocus || (pcm != null && pcm.length <= 0)) {
            return false;
        }
        if (position + pcm.length > sTotalLen) {
            sTotalLen = position + pcm.length;
        }
        MainHandler.getInstance().post(() -> updatePCM_imp(position, pcm));
        return true;
    }
    public void updatePCM_imp(long position, byte[] pcm) {
        String fileName = null;
        int count = 0;
        if (null != pcm) {
            do {
                if (fileName == null) {
                    fileName = getRandomFileName();
                } else {
                    fileName += count;
                }
            } while (!transmisson.create(fileName, pcm) && ++count < 3);
        }
        if (count >= 3) {
            return;
        }

        JSONObject obj = new JSONObject();
        try {
            obj.put("Type", PROTOCOL_MUSIC_PCM);
            obj.put("Position", position);
            obj.put("FileName", fileName);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (null != pcm) {
            AnFileLog.e("ASDFXXXCV connector","Music sendPCMData ## 111 position = "+position +", pcm.len = "+pcm.length);
        }
        else {
            AnFileLog.e("ASDFXXXCV connector","Music sendPCMData ## 111 position = "+position +", pcm. == null ");
        }

        if (transmisson != null) {
            transmisson.sendData(ProtocolMgr.TRANSIMISSON_TYPE_MUSIC, obj.toString().getBytes());
        }

        if (pcm != null) {
            tickPosition = position;
            if (tickPosition >= 0) {
                lastPosition = position + pcm.length;
                lastTimeMillis = SystemClock.elapsedRealtime();
            } else {
                lastPosition = -1;
                lastTimeMillis = 0;
            }
            startTickThread();
        }
    }

    private byte[] getBytesByBitmap(Bitmap bitmap) {
        if (null == bitmap) {
            return null;
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(bitmap.getByteCount());
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
        return outputStream.toByteArray();
    }
}
