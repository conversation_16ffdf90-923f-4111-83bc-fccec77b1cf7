// IClientService.aidl
package com.autoai.welink.auto.client;

import android.os.IInterface;
import android.os.IBinder;
import android.os.Parcel;
import android.os.ParcelFileDescriptor;
import android.os.SharedMemory;

interface IClientService {
	ParcelFileDescriptor getFileDescriptor(String connectStr, String filename);
	SharedMemory getSharedMemory(String connectStr, String filename);
	int length(String connectStr, String filename);

	void pauseMirrorScreen(String connectStr);
	oneway void resumeMirrorScreen(String connectStr);

	oneway void sendData(String connectStr, int type,in byte[] bytes);
}