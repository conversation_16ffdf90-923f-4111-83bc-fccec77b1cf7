// IServerService.aidl
package com.autoai.welink.autoproxy.server;

import android.os.IInterface;
import android.os.IBinder;
import android.os.Parcel;
import android.os.ParcelFileDescriptor;
import android.os.SharedMemory;

interface IServerService {
	ParcelFileDescriptor getFileDescriptor(String connectStr, String filename);
	SharedMemory getSharedMemory(String connectStr, String filename);
	int length(String connectStr, String filename);

	Surface getSurface(String connectStr);

	oneway void sendData(String connectStr,int type,in byte[] bytes);
}
