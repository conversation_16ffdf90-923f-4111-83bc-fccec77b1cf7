apply plugin: 'com.android.library'

//apply from: "${project.rootDir}/library.gradle"
//apply from: "${project.rootDir}/maven_push.gradle"

apply from: "../library.gradle"
apply from: "../versions.gradle"
apply from: "../maven_push.gradle"


dependencies {
    implementation project(":wlconnector")
    implementation project(":wlhardwarehub")
    implementation project(":wlserver")
    implementation project(":wlchannel")
//    implementation 'com.github.wseemann:FFmpegMediaMetadataRetriever:1.0.15'

//    implementation files('libs\\FFmpegMediaMetadataRetriever-core-1.0.19.aar')
//    implementation files('libs\\FFmpegMediaMetadataRetriever-native-1.0.19.aar')
//    implementation files('libs\\FFmpegMediaMetadataRetriever-native-armeabi-v7a-1.0.19.aar')

    //---> she tips:因为这个库，没有aliyun代理备份，需要去github获取，容易get不到仓库，所以做成本地依赖
//    implementation project(':local_aarlib:ffmpeg_media_retriever_core')
//    implementation project(':local_aarlib:ffmpeg_media_retriever_core_native')
//    implementation project(':local_aarlib:ffmpeg_media_retriever_native_v7')

    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    // 视频识别
    implementation 'com.autoai.link.image:decodejpeg:*******'
    implementation 'org.tensorflow:tensorflow-lite:2.17.0'
    implementation 'org.tensorflow:tensorflow-lite-select-tf-ops:2.14.0'
}

