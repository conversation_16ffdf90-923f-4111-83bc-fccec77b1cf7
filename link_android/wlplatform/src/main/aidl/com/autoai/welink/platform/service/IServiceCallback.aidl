// IServiceCallback.aidl
package com.autoai.welink.platform.service;

// Declare any non-default types here with import statements

interface IServiceCallback {
    oneway void onLinkAOAReady();
    oneway void onLinkConnected(int HUScreenWidth, int HUScreenHeight, int densityDpi, String vehicleType, String vehicleVersion, String huBtMacAddress, boolean isAOA);
    oneway void onLinkError();
    oneway void onLinkUnconnected();
    oneway void onFrameData(in byte[] data);
    oneway void onLinkTouch(in MotionEvent event);
    oneway void onLinkHUMessageData(String data);
    oneway void onLinkHUCanData(in byte[] data);
    oneway void onLinkSuspend();
    oneway void onLinkResume();
    oneway void onAppConnected(String connectStr);
    oneway void onAppForeground(String connectStr);
    oneway void onAppBackground(String connectStr);
    oneway void onAppDisconnected(String connectStr);
    oneway void onAppError(String connectStr, int type);
    oneway void onAppAction(String connectStr, int ActionID);
    int onAppReceiveSound(String connectStr, String mark, int duration);
    oneway void onMusicRegister(String connectStr);
    oneway void onMusicUnregister(String connectStr);
    oneway void onMusicID3(String source, String artist, String title, String album, String lyric, int duration, in Bitmap cover);
    oneway void onMusicOrder(int order);
    oneway void onMusicPCM(long position, long totalLen, int rate, int bit, int channel);
    oneway void onHardwareGroupStatusChanged(int status, String content);
    oneway void onHardwareGroupError(int reason);
    oneway void onWiFiApConfig(String ssid, String password);
}
