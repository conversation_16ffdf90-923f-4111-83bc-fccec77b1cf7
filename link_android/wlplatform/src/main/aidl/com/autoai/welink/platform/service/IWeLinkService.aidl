// IWeLinkService.aidl
package com.autoai.welink.platform.service;

import com.autoai.welink.platform.service.IServiceCallback;
import com.autoai.welink.platform.service.IReqCurFrameCallback;


// Declare any non-default types here with import statements

interface IWeLinkService {
    String getVersion();
    void init(IServiceCallback cb, IBinder binder);
    void deinit();
    void createHardwareGroup(String networkName, String passphrase, int frequency, int rssi, int timeout, in List<ScanFilter> BLEDeviceFilterList);
    void refreshHardwareGroup();
    void destroyHardwareGroup();
    void hideGroupInfo();
    void showGroupInfo();
    void startAPConnect(String ssid,String password,int rssi,int timeout,in List<ScanFilter> BLEDeviceFilterList);
    void stopAPConnect();
    void initHU(in Rect connectorRect, String classNameForPCMPolicy, String classNameForCommandPolicy);
    void deinitHU();
    void linkAOA(in Intent intent);
    void unlink();
    String assign(String packageName, int cap);
    String find(String packageName, int cap);
    void revoke(String connectStr);
    void start();
    void startAndGetFrameData(int frame, int type, int offset, int size);
    void stop();
    void toggleLocalVideo(boolean start, String localPath);
    void command(String connectStr, String command);
    void mirror(String mirror, int x, int y);
    boolean touch(in MotionEvent motionEvent);
    void voice(String connectStr);
    String getActivedMusic();
    void activateMusic(String connectStr);
    int getWidth();
    int getHeight();
    int getDensity();
    Surface enableExternal(int left, int top, int right, int bottom, boolean forceFullScreen);
    void disableExternal();
    void reqKeyFrame(int frame ,int time);
    Surface showOverlay();
    void hideOverlay();
    void sendMessageDataToHU(String data);
    void sendCanDataToHU(in byte[] data);
    void startUDPBroadcast(String type);
    void stopUDPBroadcast();
    void enableLogFile(boolean enable,int logLevel);
    void enableLogCat(boolean enable,int logLevel);
    long getThroughput();
    void checkDisplay(int width, int height, int dpi, int fps);
    void changeFps(int fps);

    int reqCurFrame(int carDriverStatus, IReqCurFrameCallback cb);
    int checkSwitch(boolean driveSafetySaveVideo, boolean driveSafetyOffline, boolean driveSafetyVideoPlay);

    void updateStates(int width, int height, int angle);
}
