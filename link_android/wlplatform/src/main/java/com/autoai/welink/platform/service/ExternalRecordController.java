package com.autoai.welink.platform.service;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.view.Surface;

import com.autoai.welink.autoproxy.codec.WLCodecListener;
import com.autoai.welink.platform.utiliy.AnFileLog;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 * 用于全录屏
 */
public class ExternalRecordController {
    private final static String TAG = "ExternalRecordController";

    /**
     * h264编码
     */
    private String MIME_TYPE = MediaFormat.MIMETYPE_VIDEO_AVC;

    /**
     * 投屏编码器
     */
    private MediaCodec mMediaCodec;

    /**
     * 用于标记编码器正在编码run的一个状态
     */
    private boolean codecIsRunning = false;


    private static ExternalRecordController instance;

    /**
     * 第一次互联 每秒发送1次I帧 发送5次 。防止有的手机 第一帧丢失后，长时间无I帧导致的黑屏问题
     */
    private int mIFrameCount = 0;

    /**
     * 请求一次补帧 关键帧最多数量
     */
    private int maxFrameCount = 0;

    /**
     * 请求一次补针时间隔
     */
    private int intervalTime = 1000;

    WLCodecListener codecListener;//视频数据输出

    MediaCodec.Callback callback = new MediaCodec.Callback() {
        @Override
        public void onInputBufferAvailable(MediaCodec codec, int index) {
            AnFileLog.e(TAG, "onInputBufferAvailable");
        }

        @Override
        public void onOutputBufferAvailable(MediaCodec codec, int index, MediaCodec.BufferInfo info) {
            AnFileLog.e(TAG, "onOutputBufferAvailable info.size=" + info.size + ",presentationTimeUs=" + info.presentationTimeUs);
            //如果当前解码器处于非工作状态，那么即便有回调，也不去处理
            //这个onOutputBufferAvailable是在系统编码器线程，这个回调会在重启编码器（比如旋转屏幕），扔然又callback
            //那么再通过codec.getOutputBuffer(index)
            if(!isCodecIsRunning()){
                return;
            }

            getFps();
            try {
//                lock.lock();
                ByteBuffer encodedData = codec.getOutputBuffer(index);
                if ((info.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                    info.size = 0;
                }
                if (info.size == 0) {
                    AnFileLog.e(TAG, "info.size == 0, drop it.");
                    encodedData = null;
                }
                if (encodedData != null) {
                    encodedData.position(info.offset);
                    encodedData.limit(info.offset + info.size);
                    byte[] outData = new byte[info.size];
                    encodedData.get(outData);

                    //7代表SPS帧，8代表PPS帧(只有第一帧会出现此类帧（目前发现只有7），其余都是1 P帧或者5 I帧)
                    int typeH264 = encodedData.get(4) & 0x1F;
                    AnFileLog.e(TAG, "onOutputBufferAvailable typeH264=" + typeH264);
                    if (!mustIFrame || typeH264 == 5 || typeH264 == 7) {
                        // Pixel 手机硬编码编出的I帧均为7，非首I帧强制转化为5
                        if (typeH264 == 7) {
                            outData[4] = (byte) 5;
                        }

                        mustIFrame = false;
                        if (codecListener != null) {
                            codecListener.onVideoTime(info.presentationTimeUs);
                            codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, outData);
                            getFps1();
                        }
                       /* if(mIFrameCount < maxFrameCount && (typeH264 == 5 || typeH264 == 7)){
                            mLastTime2 = System.currentTimeMillis();
                            mIFrameCount++;
                        }*/
                        requestSysFrame();
                    } else {
                        if (mMediaCodec != null && System.currentTimeMillis() - mLastTime2 > 2000) {
                            mLastTime2 = System.currentTimeMillis();
                            Bundle params = new Bundle();
                            params.putInt(MediaCodec.PARAMETER_KEY_REQUEST_SYNC_FRAME, 0);
                            mMediaCodec.setParameters(params);
                        }
                    }
                }
                codec.releaseOutputBuffer(index, false);
            } catch (Exception e) {
                e.printStackTrace();
                AnFileLog.e(TAG, "onOutputBufferAvailable got Exception=" + e.toString());
            } finally {
//                lock.unlock();
            }
        }

        @Override
        public void onError(MediaCodec codec, MediaCodec.CodecException e) {
            AnFileLog.e(TAG, "onError:" + e.toString());
            e.printStackTrace();
        }

        @Override
        public void onOutputFormatChanged(MediaCodec codec, MediaFormat format) {
            AnFileLog.e(TAG, "onOutputFormatChanged in " + format.toString());
            if (codecListener != null) {
                codecListener.onVideoFormatChange(format);
            }
            try {
                byte[] sps = format.getByteBuffer("csd-0").array();
                byte[] pps = format.getByteBuffer("csd-1").array();
                codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, sps);
                codecListener.onCallBack(WLCodecListener.CODEC_TYPE_VEDIO, pps);
            } catch (Exception e) {
                e.printStackTrace();
            }
            AnFileLog.e(TAG, "onOutputFormatChanged out");
        }
    };

    public ExternalRecordController() {
        initMediaCodec();
        //第一次互联
        mIFrameCount = 0;
        maxFrameCount = 0;
        //初始化 延迟 第一个是关键帧不用请求
        mLastTime2 = System.currentTimeMillis() + 1000;
    }

    public static synchronized ExternalRecordController getInstance() {
        if (instance == null) {
            instance = new ExternalRecordController();
        }

        return instance;
    }

    /**
     * 主动请求I帧
     */
    private void requestSysFrame(){
        AnFileLog.e(TAG, "onOutputBufferAvailable mIFrameCount=" + mIFrameCount+",maxFrameCount:"+maxFrameCount+",intervalTime="+intervalTime);
        if (mIFrameCount < maxFrameCount && mMediaCodec != null && System.currentTimeMillis() - mLastTime2 > intervalTime) {
            AnFileLog.e(TAG, "onOutputBufferAvailable mIFrameCount=" + mIFrameCount);
            mLastTime2 = System.currentTimeMillis();
            Bundle params = new Bundle();
            params.putInt(MediaCodec.PARAMETER_KEY_REQUEST_SYNC_FRAME, 0);
            mMediaCodec.setParameters(params);
            mIFrameCount++;
        }
    }
    /**
     * 实例化用于全投屏的编码器
     */
    public void initMediaCodec() {
        try {
            mMediaCodec = MediaCodec.createEncoderByType(MIME_TYPE);
//            mMediaCodec = MediaCodec.createByCodecName("she");
        } catch (IOException e) {
            mMediaCodec = null;
        }
    }

    Surface inputSurface = null;

    public Surface createInputSurface() {
        if (mMediaCodec != null) {
            inputSurface = mMediaCodec.createInputSurface();
        }
        return inputSurface;
    }

    public void startMediaCodec() {
        if (!codecIsRunning && mMediaCodec != null) {
            codecIsRunning = true;
            workhandler.post(() -> mMediaCodec.start());
        }
    }

    /**
     * stop 编码器，释放相关资源，并且将mMediaCodec重新调整为reset出事态
     */
    public void stopMediaCodec() {
        if (codecIsRunning && mMediaCodec != null) {
            codecIsRunning = false;
            try {
                mMediaCodec.stop();
            }catch (MediaCodec.CodecException codecException){
                AnFileLog.e(TAG, "stopMediaCodec codecException: " + codecException.toString());
            }finally {
                mMediaCodec.reset();
                if (inputSurface != null) {
                    inputSurface.release();
                    inputSurface = null;
                }
            }
        }
    }
    public void reqKeyFrame(int frame ,int time) {
        this.maxFrameCount = frame;
        this.intervalTime = time/maxFrameCount;
        mIFrameCount = 0;
        AnFileLog.e(TAG,"reqKeyFrame --maxFrameCount:"+maxFrameCount+",intervalTime:"+intervalTime);
    }
    MediaFormat mediaFormat;

    public void configMediaFormat(int screenWidth, int screenHeight, int fps) {
        //fixme:@menglg fps调查一下，支持动态参数，而不是写死 保守调试为30 后面调整为60
        AnFileLog.e(TAG, "fps==="+fps);
        fps = fps==0?30:fps;
        //---> 实例对象
        mediaFormat = MediaFormat.createVideoFormat(MIME_TYPE, screenWidth, screenHeight);
        mediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, Math.min(screenWidth * screenHeight * 8, 6 * 1024 * 1024)); // 4m易连/8mScrcpy
        mediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, fps);
        mediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mediaFormat.setInteger(MediaFormat.KEY_INTRA_REFRESH_PERIOD, fps * 3);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mediaFormat.setFloat(MediaFormat.KEY_MAX_FPS_TO_ENCODER, fps);
        }
        mediaFormat.setLong(MediaFormat.KEY_REPEAT_PREVIOUS_FRAME_AFTER, 100_000); // µs

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            mediaFormat.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileBaseline);
            mediaFormat.setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel31);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            mediaFormat.setInteger(MediaFormat.KEY_BITRATE_MODE, MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_CBR_FD);
        }

        try {
            mMediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            AnFileLog.e(TAG, "configureMedia MediaCodec.getName: " + mMediaCodec.getName());
            AnFileLog.e(TAG, "configureMedia mScreenWidth: " + screenWidth + ", mScreenHeight: " + screenHeight);
            if (mDecoderThread == null) {
                mDecoderThread = new HandlerThread("AsyncMediaDecoderThread");
                mDecoderThread.start();
                workhandler = new Handler(mDecoderThread.getLooper());
            }
            //保证发出第一帧为I帧
            mustIFrame = true;
            mMediaCodec.setCallback(callback, workhandler);
        } catch (Exception e) {
            e.printStackTrace();
            AnFileLog.e(TAG, "configureMedia got Exception=" + e);
        }

    }

    public boolean isCodecIsRunning() {
        return codecIsRunning;
    }

    public void setCodecListener(WLCodecListener codecListener) {
        this.codecListener = codecListener;
    }

    //========================================== test start ===============================================================================
    private Handler workhandler;

    private HandlerThread mDecoderThread;
    private boolean mustIFrame = false;

    private int mFps = 0;
    private long mLastTime = 0;
    private long mLastTime2 = 0;



    private void getFps() {
        mFps++;
        long timeStamp = System.currentTimeMillis();
        if (timeStamp - mLastTime >= 1000) {
            AnFileLog.e(TAG, "onOutputBufferAvailable fps ==>" + mFps);
            mFps = 0;
            mLastTime = timeStamp;
        }
    }

    private int mFps1 = 0;
    private long mLastTime1 = 0;

    private void getFps1() {
        mFps1++;
        long timeStamp = System.currentTimeMillis();
        if (timeStamp - mLastTime1 >= 1000) {
            AnFileLog.e(TAG, "IFrame fps ==>" + mFps1);
            mFps1 = 0;
            mLastTime1 = timeStamp;
        }
    }

    //========================================== test end ===============================================================================

}
