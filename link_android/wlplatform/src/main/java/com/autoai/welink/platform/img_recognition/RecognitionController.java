package com.autoai.welink.platform.img_recognition;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;

import com.autoai.link.image.decodeandgetjpeg.H264Decoder;
import com.autoai.welink.platform.utiliy.TimerUtil;
import com.autoai.welink.platform.utiliy.ToolUtil;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 用于收集截取录屏画面，进而间隔来抽取帧，识别关心信息
 * 目前主要是行车安全，识别画面是否正在播放视频
 */
public class RecognitionController {

    private static final String TAG = "Recognition.Controller";

    private static final int COLLECT_FRAME_COUNT = 8;

    /**
     * 初始化的标记位置
     */
    private boolean hasInitedFlag = false;

    /**
     * 上下文
     */
    private Context context;

    /**
     * 工作调度器线程
     */
    HandlerThread taskDispatcherThread;

    /**
     * 任务调度者的分发器
     */
    Handler taskDispatcher;

    /**
     * 播放状态检测流程控制
     */
    private IRecognizeCallBack iRecognizeCallBack;

    private boolean driveSafetyOfflineSwitch = false;
    private boolean driveSafetyVideoPlay = false;
    private final LinkedBlockingQueue<Bitmap> mediaQueue = new LinkedBlockingQueue<>();
    private long lastTime = 0;
    private  ScheduledExecutorService scheduler;
    private int currentDriverStatus = 0;
    private H264Decoder h264Decoder;
    private int dataNum = 0;
    private static final int MAX_FRAME_COUNT = 4;
    private boolean fullPowerMode = true;
    private int devicePowerLevel = 3;
    private VideoPlaybackChecker videoPlaybackChecker;
    private boolean mediaPlaying = true;
    private Runnable task;
    private int width = 0;
    private int height = 0;
    private int angle = 0;
    boolean isVertical  = true;

    private TimerUtil timerUtil = new TimerUtil(()->{
        this.fullPowerMode = false;
        Log.i(TAG, "onTimerCompleted ---> fullPowerMode = " + fullPowerMode);
    });

    /**
     * 初始化方法
     */
    public void init(Context context, IRecognizeCallBack recognizeCallBack) {
        Log.i(TAG, "init ---> in");
        if (hasInitedFlag) {
            return;
        }
        iRecognizeCallBack = recognizeCallBack;
        hasInitedFlag = true;
        this.context = context;
        devicePowerLevel = ToolUtil.getDevicePowerLevel(context);
        taskDispatcherThread = new HandlerThread("识别取帧线程");
        taskDispatcherThread.start();
        taskDispatcher = new Handler(taskDispatcherThread.getLooper());
        videoPlaybackChecker = VideoPlaybackChecker.getInstance(context);
        videoPlaybackChecker.setPlaybackStatusListener(playbackStatus -> {
            mediaPlaying = playbackStatus;
            fullPowerMode = true;
            timerUtil.startTimer(10000);
            Log.d(TAG, "mediaPlaying = " + mediaPlaying);
        });
        VideoDetector.init(context);
        startTaskFfmpeg();
        Log.i(TAG, "init ---> out " + devicePowerLevel);
        h264Decoder = new H264Decoder((frame, width, height, size)->{
            Log.i(TAG, "onFrame ---> width: " + width + ", height: " + height + ", size: " + size + ",frame len = " + frame.length);
            try {
                if(dataNum % COLLECT_FRAME_COUNT == 0) {
                    Bitmap bitmap = BitmapFactory.decodeByteArray(frame, 0, size);
                    if (mediaQueue.size() == MAX_FRAME_COUNT) {
                        mediaQueue.take();
                    }
                    mediaQueue.put(bitmap);
                    Log.i(TAG, "onFrame ---> input dataNum = " + dataNum);
                }
                dataNum++;
            } catch (InterruptedException e) {
                Log.e(TAG, "onFrame ---> ", e);
            }
        });

        flowCheckInit();
        startOrStopVideoPlaybackChecker(driveSafetyVideoPlay);
    }


    /**
     * 流量检测
     */
    private void flowCheckInit(){
        scheduler = Executors.newScheduledThreadPool(1);
        task = () -> {
            long nowTime = System.currentTimeMillis();
            Log.i(TAG, "flowCheck ---> lastTime = " + lastTime + ", nowTime = " + nowTime);
            if(currentDriverStatus == 1 && nowTime - lastTime > 1000) {
                iRecognizeCallBack.onRecognizeResult(false, true);
                lastTime = nowTime;
                Log.i(TAG, "flowCheck ---> path: " + null + " offline isPlay: " + false + ", time out");
            }
        };
        scheduler.scheduleAtFixedRate(task, 0, 1, TimeUnit.SECONDS);
    }


    /**
     * 图片剪切
     * @return 剪切后图片列表
     */
    private List<Bitmap> getCutOutBitmaps() {
        List<Bitmap> bitmaps = new ArrayList<>();
        try {
            int num = 0;
            for (int i = 0; i < MAX_FRAME_COUNT; i++) {
                Bitmap bitmap = mediaQueue.take();
                int originalWidth = bitmap.getWidth();
                int originalHeight = bitmap.getHeight();
                Log.i(TAG, "startTaskFfmpeg ---> originalWidth: " + originalWidth + ", originalHeight: " + originalHeight + ", width: " + width + ", height: " + height);
                if (isVertical) {
                    bitmap = Bitmap.createBitmap(bitmap, (originalWidth - width) / 2, 0, width, height);
                    num++;
                } else {
                    bitmap = Bitmap.createBitmap(bitmap, 0, (originalHeight - height) / 2, width, height);
                }
                Log.i(TAG, "startTaskFfmpeg ---> bitmap: " + bitmap.getWidth() + ", " + bitmap.getHeight());
                bitmaps.add(bitmap);
            }

            if(num != 0 &&  num != MAX_FRAME_COUNT){
                bitmaps.clear();
            }
        }catch (InterruptedException e) {
            Log.e(TAG, "startTask ---> e = " + e.getMessage());
        }
        return bitmaps;
    }

    /**
     * 开始检测
     */
    private void startTaskFfmpeg() {
        taskDispatcher.post(()->{
            int num = 0;
            while (true) {
                List<Bitmap> bitmaps = getCutOutBitmaps();
                num++;
                if(mediaPlaying) {
                    if (fullPowerMode || num > devicePowerLevel) {
                        if(bitmaps.size() == MAX_FRAME_COUNT) {
                            Log.i(TAG, "finishRecordAndRecognize ---> 使用图片数量:" + bitmaps.size() + ", 队列图片数量:" + mediaQueue.size() + ",fullPowerMode = " + fullPowerMode + ", num = " + num + ", devicePowerLevel = " + devicePowerLevel);
                            recogniseFromServerByByte(bitmaps);
                        }
                        num = 0;
                    }
                }else{
                    iRecognizeCallBack.onRecognizeResult(false, true);
                }
                bitMapsRecycle(bitmaps);
            }
        });
    }

    public void setCurrentDriverStatus(int currentDriverStatus){
        this.currentDriverStatus = currentDriverStatus;
    }

    public void startOrStopVideoPlaybackChecker(boolean start) {
        if(videoPlaybackChecker == null){
            return;
        }
        if (start) {
            videoPlaybackChecker.startChecking();
        } else {
            videoPlaybackChecker.stopChecking();
        }
    }

    /**
     * 改方法为非阻塞异步处理方法
     *
     * @param frameData
     */
    public void writeFrame(byte[] frameData) {
        lastTime = System.currentTimeMillis();
        h264Decoder.inputFrame(frameData);
    }

    public static RecognitionController getInstance() {
        return SingleInstance.INSTANCE;
    }

    public void setDriveSafetyOfflineSwitch(boolean driveSafetyOffline) {
        driveSafetyOfflineSwitch = driveSafetyOffline;
    }
    public void setDriveSafetyVideoPlay(boolean driveSafetyOnlineGpt) {
        driveSafetyVideoPlay = driveSafetyOnlineGpt;
    }

    private static class SingleInstance {
        private static final RecognitionController INSTANCE = new RecognitionController();

    }

    private  List<Bitmap> convertToBitmaps(List<byte[]> bitmapsList) {
        List<Bitmap> bitmaps = new ArrayList<>();

        for (byte[] bitmapBytes : bitmapsList) {
            ByteArrayInputStream bais = new ByteArrayInputStream(bitmapBytes);
            Bitmap bitmap = BitmapFactory.decodeStream(bais);
            if (bitmap != null) {
                bitmaps.add(bitmap);
            }
        }
        return bitmaps;
    }


    public void updateStates(int width, int height, int angle){
        this.width = width;
        this.height = height;
        this.angle = angle;
        if(angle == 90 || angle == 270){
            isVertical = false;
        }else{
            isVertical = true;
        }
        Log.i(TAG, "updateStates ---> width: " + width + ", height: " + height + ", angle: " + angle);
    }

    /**
     * 调用c库开始识别
     * @param bitmapsList
     */
    private void recogniseFromServerByByte(List<Bitmap>bitmapsList) {
        Log.i(TAG, "recogniseFromServer ---> in:");
        if(bitmapsList == null || bitmapsList.size() == 0){
            return;
        }

        //FileUtil.saveBitmapToFile(bitmapsList.get(0),  context);
        if (driveSafetyOfflineSwitch){
            // 离线本地sdk识别
            try {
                Long beginTime = System.currentTimeMillis();
                int result = VideoDetector.getInstance().detect(bitmapsList,isVertical);
                Long endTime = System.currentTimeMillis();
                Log.i(TAG, "finishRecordAndRecognize --->  offline isPlay: " + result +  ", times = " + (endTime - beginTime));
                if(result == 0){
                    iRecognizeCallBack.onRecognizeResult(false,true);
                }else if(result == 1){
                    iRecognizeCallBack.onRecognizeResult(true,true);
                }
            } catch (Exception e) {
                Log.e(TAG, "recogniseFromServerByByte e = " + e);
            }
        }
    }

    private void bitMapsRecycle(List<Bitmap>bitmapsList){
        if (bitmapsList == null) {
            return;
        }
        for (Bitmap bitmap : bitmapsList) {
            if (bitmap != null && !bitmap.isRecycled()) {
                bitmap.recycle();
            }
        }
        bitmapsList.clear();
    }

    public interface IRecognizeCallBack {
        /**
         * 获取识别结果
         *
         * @param state
         */
        void onRecognizeResult(boolean state, boolean timeout);

    }

}
