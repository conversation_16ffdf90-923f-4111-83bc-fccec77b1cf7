package com.autoai.welink.platform.output;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaMuxer;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 */
public class MediaMuxerOutput extends FileOutput{

    private MediaMuxer mediaMuxer;
    private int index;
    private MediaCodec.BufferInfo bufferInfo;

    private long startTime;

    public MediaMuxerOutput(String filePath) {
        super(filePath);
        try {
            mediaMuxer = new MediaMuxer(filePath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
        } catch (IOException e) {
            e.printStackTrace();
        }
        bufferInfo = new MediaCodec.BufferInfo();
        bufferInfo.flags = MediaCodec.BUFFER_FLAG_KEY_FRAME;
    }

    public void addTrack(MediaFormat mediaFormat) {
        if (mediaMuxer != null) {
            index = mediaMuxer.addTrack(mediaFormat);
            mediaMuxer.start();
            startTime = -1;
        }
    }

    public void updateTime(long presentationTimeUs) {
        if (startTime == -1) {
            startTime = presentationTimeUs;
        }
        bufferInfo.presentationTimeUs = presentationTimeUs - startTime;
    }

    @Override
    public void write(byte[] data) {
        if (mediaMuxer != null) {
            bufferInfo.size = data.length;
            mediaMuxer.writeSampleData(index,ByteBuffer.wrap(data),bufferInfo);
        }
    }

    @Override
    public void close() {
        if (mediaMuxer != null) {
            mediaMuxer.stop();
            mediaMuxer.release();
        }
    }
}
