package com.autoai.welink.platform.utiliy;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FileUtil {
    private static String positiveDir = "positive";
    private static String reverseDir = "reverse";
    private static String horizontal = "horizontal";

    private static String featuresDir = "featuresDir";

    private static final String TAG = "FileUtil";
    private static Context mContext;

    public static String getPath(){
        if(mContext != null){
            return mContext.getExternalFilesDir("") + File.separator + featuresDir;
        }
        return null;
    }

    public static void deleteSubdirectoriesAndFiles(File directory) {
        // 检查是否是目录
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        // 递归删除子目录
                        deleteSubdirectoriesAndFiles(file);
                    }
                    // 删除文件和空目录
                    file.delete();
                }
            }
        }
    }


    public static void exportAssetsToExternalStorage(Context context) {
        mContext = context;
        AssetManager assetManager = context.getAssets();

        deleteSubdirectoriesAndFiles(new File(getPath()));
        //deleteSubdirectoriesAndFiles(new File(getOutPath()));

        // 目标外部存储目录
        File externalPositiveDir = new File(getPath(), positiveDir);
        File externalReverseDir = new File(getPath(), reverseDir);
        File externalHorizontalDir = new File(getPath(), horizontal);
        Log.i(TAG, "externalPositiveDir: " + externalPositiveDir.getAbsolutePath() + ", externalReverseDir = " + externalReverseDir.getAbsolutePath());

        if (!externalPositiveDir.exists()) {
            externalPositiveDir.mkdirs();
        }

        if (!externalReverseDir.exists()) {
            externalReverseDir.mkdirs();
        }

        if(!externalHorizontalDir.exists()){
            externalHorizontalDir.mkdirs();
        }

        try {
            // 拷贝 a 目录下的文件
            String[] filesA = assetManager.list(positiveDir);
            copyFiles(assetManager, filesA, positiveDir, externalPositiveDir);

            // 拷贝 b 目录下的文件
            String[] filesB = assetManager.list(reverseDir);
            copyFiles(assetManager, filesB, reverseDir, externalReverseDir);

            // 拷贝 C 目录下的文件
            String[] filesC = assetManager.list(horizontal);
            copyFiles(assetManager, filesC, horizontal, externalHorizontalDir);

        } catch (Exception e) {
            Log.e(TAG, "exportAssetsToExternalStorage: " + e.getMessage());
        }
    }


    private static void copyFiles(AssetManager assetManager, String[] files, String sourceDir, File targetDir) {
        try {
            for (String fileName : files) {
                InputStream inputStream = assetManager.open(sourceDir + "/" + fileName);
                File outFile = new File(targetDir, fileName);
                FileOutputStream outputStream = new FileOutputStream(outFile);

                byte[] buffer = new byte[1024];
                int read;
                while ((read = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, read);
                }

                inputStream.close();
                outputStream.close();
            }
        }catch (Exception e){
            Log.e(TAG, "copyFiles: " + e.getMessage());
        }
    }

    public static void renameFileResult(String path, int result){
        boolean isSuccess = false;
        if(result == 0 || result == 1){
            isSuccess = true;
        }
        for(int index = 0; index < 15;){
            File oldFile = new File(path + "-" + index + ".jpg");
            File newFile = new File(path + "-" + index + "-" + isSuccess + ".jpg");
            // 检查旧文件是否存在
            if (oldFile.exists()) {
                // 重命名文件
                oldFile.renameTo(newFile);
            }
            index+=3;
        }
    }

    public static void renameJpgFileResult(List<String> paths, int result){
        boolean isSuccess = false;
        if(result == 0 || result == 1){
            isSuccess = true;
        }

        int index = 1;
        for(String path : paths){
            File oldFile = new File(path);
//            if(index == 1) {
//                String fileName = oldFile.getName();
//                String outFileName = getResultPath(fileName);
//                File newFile = new File(outFileName + "-" + index + "-" + isSuccess + ".jpg");
//                if (oldFile.exists()) {
//                    // 重命名文件
//                    oldFile.renameTo(newFile);
//                }
//            }else{
                File newFile = new File(oldFile + "-" + index + "-" + isSuccess + ".jpg");
                if (oldFile.exists()) {
                    // 重命名文件
                    oldFile.renameTo(newFile);
                }
//            }
            index++;

        }
    }

    private static String getResultPath(String fileName){
        if(mContext == null){
            return null;
        }
        String path = mContext.getExternalFilesDir("") + File.separator + "videoOut";
        try {
            File file = new File(path);
            if (!file.exists()) {
                file.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return path + File.separator + fileName;
    }
    private static String getOutPath(){
        if(mContext == null){
            return null;
        }
        return mContext.getExternalFilesDir("") + File.separator + "video";
    }

    public static String genTempRecordFilePath() {
        if(mContext == null){
            return null;
        }
        String videoFileOutPath = getOutPath();
        try {
            File file = new File(videoFileOutPath);
            if (!file.exists()) {
                file.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return videoFileOutPath + File.separator + "video-" + getNowTime() + "-frame.mp4";
    }

    public static String genTempRecordFilePath2() {
        if(mContext == null){
            return null;
        }
        String videoFileOutPath = getOutPath();
        try {
            File file = new File(videoFileOutPath);
            if (!file.exists()) {
                file.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return videoFileOutPath + File.separator + "video-" + getNowTime() + "-frame";
    }

    public static String getNowTime(){
        long currentTimeMillis = System.currentTimeMillis();
        Date date = new Date(currentTimeMillis);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd-HHmmss-SSS");
        return sdf.format(date);
    }


    public static List<String> saveToImage(List<byte[]> bitmaps){
        List<String> paths = new ArrayList<>();
        for(byte[] frame : bitmaps) {
            String fileName = genTempRecordFilePath2();
            File outputFile = new File(fileName);
            // 直接写入BMP数据
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                fos.write(frame, 0, frame.length);
                fos.flush();
                Log.d("TAG", "保存BMP图片成功: " + fileName);
                paths.add(fileName);
            }catch (Exception e){
                Log.e(TAG, "saveToImage ---> ", e);
            }
        }
        return paths;
    }

    public static void saveToImage(byte[] bitmaps, int size){
        FileOutputStream out = null;
        try {
            // 创建文件对象
            String fileName = genTempRecordFilePath2() + ".jpg";
            File file = new File(fileName);
            out = new FileOutputStream(file);
            // 写入字节数组到文件
            out.write(bitmaps, 0, size);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭输出流
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void saveBitmapToFile(Bitmap bitmap, Context context) {
        FileOutputStream out = null;
        if(mContext == null) {
            mContext = context;
        }
        try {
            // 创建文件输出流
            String fileName = genTempRecordFilePath2() + ".jpg";
            out = new FileOutputStream(new File(fileName));
            // 将位图写入文件，格式为PNG
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 确保流被关闭
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
