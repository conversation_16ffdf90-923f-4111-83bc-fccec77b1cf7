package com.autoai.welink.platform.utiliy;

import android.app.ActivityManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioManager;
import android.os.Build;
import android.opengl.GLES20;

/**
 * <AUTHOR>
 */
public class ToolUtil {

    private static final int LOW = 10;
    private static final int MEDIUM = 5;
    private static final int HIGH = 3;


    private static boolean isGpuSupported(Context context) {
        // 检查 OpenGL ES 版本
        PackageManager pm = context.getPackageManager();
        boolean hasOpenGLES = pm.hasSystemFeature(PackageManager.FEATURE_OPENGLES_EXTENSION_PACK);

        // 获取 OpenGL ES 版本
        String version = GLES20.glGetString(GLES20.GL_VERSION);

        // 只需支持 OpenGL ES
        return hasOpenGLES && version != null;
    }

    public static int getDevicePowerLevel(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);
        long availableMemory = memoryInfo.availMem;
        int coreCount = Runtime.getRuntime().availableProcessors();
        boolean supportNnapi = Build.VERSION.SDK_INT >= Build.VERSION_CODES.P;
        boolean supportGpu = isGpuSupported(context);

        if (coreCount <= 4 && availableMemory <= 2 * 1024 * 1024 * 1024L &&!supportNnapi &&!supportGpu) {
            return LOW;
        } else if (coreCount >= 4 && coreCount <= 8 && availableMemory > 2 * 1024 * 1024 * 1024L && availableMemory <= 4 * 1024 * 1024 * 1024L && supportNnapi) {
            return MEDIUM;
        } else {
            return HIGH;
        }
    }



}
