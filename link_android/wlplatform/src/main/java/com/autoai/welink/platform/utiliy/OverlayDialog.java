package com.autoai.welink.platform.utiliy;

import android.app.Dialog;
import android.app.Presentation;
import android.content.Context;
import android.content.res.Resources;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.os.Build;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.Surface;
import android.view.Window;
import android.view.WindowManager;

import com.autoai.welink.platform.R;
import com.autoai.welink.platform.WLPlatform;

import java.lang.reflect.Method;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;


public final class OverlayDialog extends Presentation {
    private OverlayDialog(Context context, Display display) {
        super(context.createDisplayContext(display), display, R.style.presentationOverlayDialog);
        this.display = display;

        //针对小米特定手机适配
        Window window = getWindow();
        if (window != null) {
            window.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION);
            window.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        }

        //针对小米刘海适配
        try {
            Method method = Window.class.getMethod("addExtraFlags",
                    int.class);
            int flag = 0x00000100 | 0x00000200 | 0x00000400;
            method.invoke(getWindow(), flag);
        } catch (Exception e) {
            e.printStackTrace();
        }

        DisplayMetrics displayMetrics = new DisplayMetrics();
        display.getRealMetrics(displayMetrics);

        Resources resources = getResources();
        if (resources != null) {
            resources.getDisplayMetrics().scaledDensity = displayMetrics.scaledDensity;
            resources.getDisplayMetrics().density = displayMetrics.density;
            resources.getDisplayMetrics().densityDpi = displayMetrics.densityDpi;
            resources.getDisplayMetrics().heightPixels = displayMetrics.heightPixels;
            resources.getDisplayMetrics().widthPixels = displayMetrics.widthPixels;
            resources.getDisplayMetrics().xdpi = displayMetrics.xdpi;
            resources.getDisplayMetrics().ydpi = displayMetrics.ydpi;
            resources.getConfiguration().fontScale = 1;
        }
    }

    @Override
    public void onDisplayChanged() {
        super.onDisplayChanged();

        DisplayMetrics displayMetrics = new DisplayMetrics();
        display.getRealMetrics(displayMetrics);

        Resources resources = getResources();
        if (resources != null) {
            resources.getDisplayMetrics().scaledDensity = displayMetrics.scaledDensity;
            resources.getDisplayMetrics().density = displayMetrics.density;
            resources.getDisplayMetrics().densityDpi = displayMetrics.densityDpi;
            resources.getDisplayMetrics().heightPixels = displayMetrics.heightPixels;
            resources.getDisplayMetrics().widthPixels = displayMetrics.widthPixels;
            resources.getDisplayMetrics().xdpi = displayMetrics.xdpi;
            resources.getDisplayMetrics().ydpi = displayMetrics.ydpi;
            resources.getConfiguration().fontScale = 1;
        }
    }

    @Override
    public void cancel() {
        AnFileLog.e("wlplatform", "OverlayDialog - cancel");
    }

    /**
     * 获取车机悬浮对话框实例
     * @param WLPlatform WLPlatform
     * @return 悬浮对话框对象
     */
    public static Dialog getInstance(Context context, final com.autoai.welink.platform.WLPlatform WLPlatform) {
        AnFileLog.e("wlplatform", "OverlayDialog - getInstance");
      /*  if (dialog != null) {
            AnFileLog.e("wlplatform", "OverlayDialog - getInstance - old");
            return dialog;
        }

        int densityDpi = WLPlatform.getOverlayDensity();
        int width = WLPlatform.getOverlayWidth();
        int height = WLPlatform.getOverlayHeight();

        if(densityDpi <= 0 || width <= 0 || height <= 0){
            return null;
        }

        DisplayManager displayManager = (DisplayManager)context.getSystemService(Context.DISPLAY_SERVICE);
        VirtualDisplay virtualDisplay = displayManager.createVirtualDisplay(context.getPackageName() + ".WeLinkVD", width, height, densityDpi, null, DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_OWN_CONTENT_ONLY);

        dialog = new OverlayDialog(context, virtualDisplay.getDisplay());
        dialog.virtualDisplay = virtualDisplay;
        dialog.WLPlatform = WLPlatform;*/

        AnFileLog.e("wlplatform", "OverlayDialog - getInstance - new");
        return dialog;
    }

    public static void release() {
        AnFileLog.e("wlplatform", "OverlayDialog - release");
        if (dialog != null) {
            AnFileLog.e("wlplatform", "OverlayDialog - release - real");
            dialog.dismiss();
            dialog = null;
        }
    }

    @Override
    public void show() {
        AnFileLog.e("wlplatform", "OverlayDialog - show");
      /*  if (dialog != null && !isShowing()) {
            AnFileLog.e("wlplatform", "OverlayDialog - show - real");
            virtualDisplay.setSurface(WLPlatform.showOverlaySurface());

            if (isHuawei() || isHonor()) {
                timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        if (dialog != null && dialog.getWindow() != null) {
                            new Handler(dialog.getContext().getMainLooper()).post(() -> {
                                if (dialog != null && dialog.getWindow() != null) {
                                    dialog.getWindow().getDecorView().invalidate();
                                }
                            });
                        }
                    }
                }, 0, 10);
            }
        }

        super.show();*/
    }

    @Override
    public boolean isShowing(){
        Surface surface = virtualDisplay.getSurface();
        return surface != null;
    }

    @Override
    public void hide() {
        AnFileLog.e("wlplatform", "OverlayDialog - hide");
        if (dialog != null) {
            AnFileLog.e("wlplatform", "OverlayDialog - hide - real");

            if (timer != null) {
                timer.cancel();
                timer = null;
            }

            Surface surface = virtualDisplay.getSurface();
            virtualDisplay.setSurface(null);
            if (surface != null) {
                surface.release();
            }

            WLPlatform.hideOverlay();
        }

//        try {
//            super.hide();
//        } catch (Exception e) {
//        }
    }

    @Override
    public void dismiss() {
        AnFileLog.e("wlplatform", "OverlayDialog - dismiss");
        if (dialog != null) {
            if (timer != null) {
                timer.cancel();
                timer = null;
            }

            Surface surface = virtualDisplay.getSurface();
            virtualDisplay.setSurface(null);
            if(surface != null) {
                surface.release();
            }
            if(WLPlatform != null) {
                WLPlatform.hideOverlay();
            }
        }

        if (virtualDisplay != null) {
            AnFileLog.e("wlplatform", "OverlayDialog - dismiss - real");
            virtualDisplay.release();
        }

        virtualDisplay = null;
        WLPlatform = null;
        dialog = null;

        try {
            super.dismiss();
        } catch (Exception e) {
        }
    }

    private static OverlayDialog dialog = null;
    private WLPlatform WLPlatform;
    private VirtualDisplay virtualDisplay;
    private final Display display;


    private Timer timer = null;
    private boolean isHuawei() {
        if (Build.BRAND == null) {
            return false;
        } else {
            return "huawei".equals(Build.BRAND.toLowerCase(Locale.getDefault()));
        }
    }
    private boolean isHonor() {
        if (Build.BRAND == null) {
            return false;
        } else {
            return "honor".equals(Build.BRAND.toLowerCase(Locale.getDefault()));
        }
    }
}
