package com.autoai.welink.platform.img_recognition;

import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.util.Log;

import org.tensorflow.lite.Interpreter;

import java.io.FileInputStream;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.List;

/**
 * <AUTHOR>
 */
public class VideoDetector {
    private static final String TAG = "VideoDetector";
    /**
     * 竖屏模型
     */
    private static Interpreter  verticalTflite;
    /**
     * 横屏模型
     */
    private static Interpreter  horizontalTflite;
    /**
     * 竖屏模型路径
     */
    private static String verticalModelPath = "mobilenet_v3_android.tflite";

    /**
     * 横屏模型路径
     */
    private static String horizontalModelPath = "mobilenet_v3_android_horizontal.tflite";

    private static Context appContext;
    /**
     *  模型输入是256*256
     */
    private static final int inputSize = 256;
    /**
     *  4帧一组
     */
    private static final int clipLength = 4;

    private static class Holder {
        private static final VideoDetector INSTANCE = new VideoDetector(appContext);
    }

    private VideoDetector(Context context) {
        if (context == null) {
            return;
        }

        try {
            appContext = context.getApplicationContext();
            verticalTflite = new Interpreter(loadModelFile(context.getAssets(), verticalModelPath));
            horizontalTflite = new Interpreter(loadModelFile(context.getAssets(), horizontalModelPath));
            Log.d(TAG,"VideoDetector::init --> supportsGLES  not ok");

        } catch (Exception e){
            e.printStackTrace();
            Log.e(TAG,"VideoDetector::init --> loadModelFile error e = " +  e);
        }
    }

    public static VideoDetector getInstance() {
        return Holder.INSTANCE;
    }
    public static void init(Context context) {
        appContext = context.getApplicationContext();
    }

    private MappedByteBuffer loadModelFile(AssetManager assetManager, String modelPath) throws Exception {
        AssetFileDescriptor fileDescriptor = assetManager.openFd(modelPath);
        FileInputStream inputStream = new FileInputStream(fileDescriptor.getFileDescriptor());
        FileChannel fileChannel = inputStream.getChannel();
        long startOffset = fileDescriptor.getStartOffset();
        long declaredLength = fileDescriptor.getDeclaredLength();
        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength);
    }

    private float[][][][][] preprocessClip(List<Bitmap> frames) {
        float[][][][][] input = new float[1][clipLength][inputSize][inputSize][3];
        for (int i = 0; i < clipLength; i++) {
            Bitmap bitmap = Bitmap.createScaledBitmap(frames.get(i), inputSize, inputSize, true);
            for (int y = 0; y < inputSize; y++) {
                for (int x = 0; x < inputSize; x++) {
                    int pixel = bitmap.getPixel(x, y);
                    input[0][i][y][x][0] = ((pixel >> 16) & 0xFF) / 255.0f;
                    input[0][i][y][x][1] = ((pixel >> 8) & 0xFF) / 255.0f;
                    input[0][i][y][x][2] = (pixel & 0xFF) / 255.0f;
                }
            }
            bitmap.recycle();
        }
        return input;
    }

    /**
     *
     * @param frames
     * @return 进行推理，返回结果 0=非播放，1=播放
     */
    public int detect(List<Bitmap> frames, boolean isVertical) {
        float[][][][][] input = preprocessClip(frames);
        float[][] output = new float[1][2];
        if(isVertical) {
            verticalTflite.run(input, output);
        }else{
            horizontalTflite.run(input, output);
        }
        return output[0][0] > output[0][1] ? 0 : 1;
    }
}
