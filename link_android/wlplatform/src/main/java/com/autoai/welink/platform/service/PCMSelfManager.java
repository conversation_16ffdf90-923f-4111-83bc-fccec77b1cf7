package com.autoai.welink.platform.service;

import android.content.Context;

import com.autoai.welink.channel.WLChannel;
import com.autoai.welink.platform.utiliy.AnFileLog;

import org.json.JSONException;
import org.json.JSONObject;

class PCMSelfManager {
    public static final int MEDIA_STATE_START = 1;//通知车机端开始播放
    public static final int MEDIA_STATE_STOP = 2;//通知车机端停止播放

    private boolean isSendMediaPlay = false;//是否已经通知车机开始播放音乐

    private static PCMSelfManager mPCMSelfManager = null;

    public static PCMSelfManager getInstance() {
        if (mPCMSelfManager == null) {
            synchronized (PCMSelfManager.class) {
                if (mPCMSelfManager == null) {
                    mPCMSelfManager = new PCMSelfManager();
                }
            }
        }
        return mPCMSelfManager;
    }

    /**
     * 发送PCM数据到车机端
     * @param data         PCM数据包
     * @param rate         采样率
     * @param bit          采样位数
     * @param channel      声道数
     * @param mark         音源标识 0：导航；1：音乐；2：微信（包括 tts 转换来的）；3：tts 天气预报；4：tts 语音助手的提示语；5：tts 新闻
     * @param audioNumMark 音频位置
     */
    public void sendPackageToCar(Context context, byte[] data, int rate, int bit, int channel, int mark, int audioNumMark) {
        AnFileLog.e("pcm","sendPackageToCar  -------------->");

        byte[] pcm = ID3Manager.getPackageDataByParam(data, rate, bit, channel, mark, audioNumMark);
        if (audioNumMark == ID3Manager.SOUND_MARK_START) {
            AnFileLog.e("pcm","PcmManager  声音开始标志----"+mark);
        }else if(audioNumMark == ID3Manager.SOUND_MARK_END){
            AnFileLog.e("pcm","PcmManager  声音结束标志----"+mark);
        }
        AnFileLog.e("pcm", "mark:: "+mark+"---"+pcm.length+"-----"+"audioMark::"+ audioNumMark + " rate::  "+ rate);
        WLChannel.getInstance(context).sendPCM(pcm);
    }

    /**
     * 发送音乐PCM数据到车机端
     *
     * @param data         PCM数据包
     * @param rate         采样率
     * @param bit          采样位数
     * @param channel      声道数
     * @param audioNumMark 音频位置
     */
    public void sendMusicPackageToCar(Context context, byte[] data, int rate, int bit, int channel, int audioNumMark) {
        AnFileLog.e("pcm","sendMusicPackageToCar  -------------->");
        if (audioNumMark == ID3Manager.SOUND_MARK_START) {
            notifyCarMediaState(context, MEDIA_STATE_START);
        }
        if (audioNumMark == ID3Manager.SOUND_MARK_END) {
            notifyCarMediaState(context, MEDIA_STATE_STOP);
        }
//        sendPackageToCar(context, data, rate, 16, channel, ID3Manager.SOUND_MUSIC, audioNumMark);
        sendPackageToCar(context, data, rate, bit, channel, ID3Manager.SOUND_MUSIC, audioNumMark);
    }



    /**
     * 通知车机停止Pcm播报
     */
    public void notifyCarStopPlayPCM(Context context) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android|ios|ce");
            JSONObject command = new JSONObject();
            command.put("method", "onStopPcmPlay");
            JSONObject extData = new JSONObject();
            extData.put("pcmInfo",1);
            command.put("extData", extData);
            jsonObject.put("command", command);
            WLChannel.getInstance(context).sendMessage(jsonObject.toString().getBytes());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知车机端手机端音乐播放状态
     *
     * @param state 播放状态 1(MEDIA_STATE_START)：开始播放；2(MEDIA_STATE_START)：停止播放
     */
    public void notifyCarMediaState(Context context, int state) {
        if (isSendMediaPlay) {
            if (state == 1) {
                return;
            } else {
                isSendMediaPlay = false;
            }
        } else if (state == 1) {
            isSendMediaPlay = true;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android|ios|ce");
            JSONObject command = new JSONObject();
            command.put("method", "onMediaState");
            JSONObject extData = new JSONObject();
            extData.put("mediaState", state);
            command.put("extData", extData);
            jsonObject.put("command", command);
            WLChannel.getInstance(context).sendMessage(jsonObject.toString().getBytes());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

}
