package com.autoai.welink.platform.service;


import android.content.Context;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.view.MotionEvent;
import android.view.Surface;

import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.WLConfiguration;
import com.autoai.welink.autoproxy.WLServer;
import com.autoai.welink.platform.utiliy.AnFileLog;

import java.util.HashMap;
import java.util.Map;

public class ServerManager {

    Context mContext;
    WLServer mAutoServer;
    Map<String, TwoTuple<String, Integer>> mPackageStr = new HashMap<>();
    int mWidth = 0;
    int mHeight = 0;
    int mDensity = 0;
    int mFps = 0;
    String curConnectStr = null;
    private String mVoiceCStr;


    public ServerManager(Context context, WLConfiguration configuration, AppConnectListener appListener) {
        mContext = context;
        mWidth = configuration.getHUScreenWidth();
        mHeight = configuration.getHUScreenHeight();
        mDensity = configuration.getDensityDpi();
        mFps = configuration.getFps();
        mAutoServer = new WLServer(context, configuration, appListener);
    }

    public String assign(String packageName, int cap) {
        TwoTuple<String, Integer> connector = findConnectStr(packageName);

        if (connector != null) {
            return connector.first;
        }

        String connectStr = mAutoServer.assign(packageName, cap);
        curConnectStr = connectStr;

        mPackageStr.put(packageName, new TwoTuple<>(connectStr, cap));

        return connectStr;
    }

    public String find(String packageName, int cap) {
        TwoTuple<String, Integer> connector = findConnectStr(packageName);

        if (connector != null &&
                (connector.second & cap) == cap) {
            return connector.first;
        }

        return null;
    }

    private TwoTuple<String, Integer> findConnectStr(String packageName) {
        for (Map.Entry<String, TwoTuple<String, Integer>> entry : mPackageStr.entrySet()) {
            if (entry.getKey().equals(packageName)) {
                return entry.getValue();
            }
        }
        return null;
    }

    public Boolean touch(MotionEvent event) {
        AnFileLog.e("wlplatform", "touch_go  x:" + event.getX() + "     y:" + event.getY());
        return mAutoServer.touch(event);
    }

    public void deinit() {
        if (mAutoServer != null) {
            mAutoServer.stop();
            mAutoServer.release();
        }
    }

    public void revoke(String connectStr) {
        if (mAutoServer != null) {
            AnFileLog.e("wlplatform", "revoke_go"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            mAutoServer.revoke(connectStr);
        }
    }

    public void start() {
        if (mAutoServer != null) {
            AnFileLog.e("wlplatform", "start_go"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            mAutoServer.start();
        }
    }

    public void mirror(String curConnectStr, int x, int y) {
        if (mAutoServer != null) {
            AnFileLog.e("wlplatform", "mirror_go"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            mAutoServer.mirror(curConnectStr, x, y);
        }
    }

    public void stop() {
        if (mAutoServer != null) {
            AnFileLog.e("wlplatform", "stop_go"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            mAutoServer.stop();
//            mAutoServer.mirror(null, Integer.MAX_VALUE, Integer.MAX_VALUE);
        }
    }

    public void command(String connectStr, String command) {
        if (mAutoServer != null) {
            mAutoServer.command(connectStr, command);
        }
    }

    public void setVoiceCStr(String onnectStr) {
        if (mAutoServer != null) {
            mVoiceCStr = onnectStr;
        }
    }

    public void voice(byte[] pcm, int rate, int bit, int channel){
//        AnFileLog.e("wlplatform", "voice_go"
//                + " pid:" + android.os.Process.myPid()
//                + " tid:" + android.os.Process.myTid());
        if (mAutoServer != null && mVoiceCStr != null) {
            AnFileLog.e("wlplatform", "voice_go_go");
            mAutoServer.voice(mVoiceCStr, pcm, rate, bit, channel);
        }
    }

    public Surface showOverlay(){
        AnFileLog.e("wlplatform", "getOverlay_go"
                + " pid:" + android.os.Process.myPid()
                + " tid:" + android.os.Process.myTid());
        if (mAutoServer != null) {
            return new Surface(mAutoServer.showOverlaySurfaceTexture());
        }
        return null;
    }

    public void hideOverlay(){
        AnFileLog.e("wlplatform", "clearOverlay_go"
                + " pid:" + android.os.Process.myPid()
                + " tid:" + android.os.Process.myTid());
        if (mAutoServer != null) {
            mAutoServer.hideOverlaySurfaceTexture();
        }
    }

    public int getDensity() {
        AnFileLog.e("wlplatform", "getDensity_go"
                + " pid:" + android.os.Process.myPid()
                + " tid:" + android.os.Process.myTid());

        return mDensity;
    }

    public int getWidth() {
        AnFileLog.e("wlplatform", "getWidth_go"
                + " pid:" + android.os.Process.myPid()
                + " tid:" + android.os.Process.myTid());

        return mWidth;
    }

    public int getHeight() {
        AnFileLog.e("wlplatform", "getHeight_go"
                + " pid:" + android.os.Process.myPid()
                + " tid:" + android.os.Process.myTid());

        return mHeight;
    }

    public String getActivedMusic() {
        AnFileLog.e("wlplatform", "getActivedMusic"
                + " pid:" + android.os.Process.myPid()
                + " tid:" + android.os.Process.myTid());
        if (mAutoServer != null) {
            return mAutoServer.getActivedMusic();
        }
        return null;
    }

    public void activateMusic(String connectStr){
        AnFileLog.e("wlplatform", "activateMusic"
                + " pid:" + android.os.Process.myPid()
                + " tid:" + android.os.Process.myTid());
        if (mAutoServer != null) {
            mAutoServer.activateMusic(connectStr);
        }
    }

    /**
     * 发送声音开始播放状态
     *
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     */
    public void sendSoundBegin(String connectStr, int soundID) {
        if (mAutoServer != null) {
            mAutoServer.sendSoundBegin(connectStr, soundID);
        }
    }

    /**
     * 发送声音播放完毕状态
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     */
    public void sendSoundComplete(String connectStr, int soundID) {
        if (mAutoServer != null) {
            mAutoServer.sendSoundComplete(connectStr, soundID);
        }
    }

    /**
     * 声音发送完成，发送下一条数据
     *
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     */
    public void sendSoundPrepare(String connectStr, int soundID) {
        if (mAutoServer != null) {
            mAutoServer.sendSoundPrepare(connectStr, soundID);
        }
    }

    /**
     * 发送声音打断状态
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     * @param playTime 打断前已播放的时长，单位: 毫秒
     * @param totalTime 声音的总时长，单位: 毫秒
     */
    public void sendSoundInterrupt(String connectStr, int soundID, long playTime, long totalTime) {
        if (mAutoServer != null) {
            mAutoServer.sendSoundInterrupt(connectStr, soundID, playTime, totalTime);
        }
    }

    /**
     * 发送声音拒绝状态
     * @param connectStr 连接字符串
     * @param soundID Sound ID
     * @param waitingTime 重复发送前需要等待的时长，单位: 毫秒
     */
    public void sendSoundReject(String connectStr, int soundID, int waitingTime) {
        if (mAutoServer != null) {
            mAutoServer.sendSoundReject(connectStr, soundID, waitingTime);
        }
    }

    public SurfaceTexture enableExternal(int left, int top, int right, int bottom,boolean forceFullScreen) {
        if (mAutoServer != null) {
            return mAutoServer.enableExternal(new Rect(left, top, right, bottom),forceFullScreen);
        }

        return  null;
    }

    public void disableExternal() {
        if (mAutoServer != null) {
            mAutoServer.disableExternal();
        }
    }

    public class TwoTuple<A, B> {

        public final A first;

        public final B second;

        public TwoTuple(A a, B b){
            first = a;
            second = b;
        }

        @Override
        public String toString(){
            return "(" + first + ", " + second + ")";
        }
    }
}
