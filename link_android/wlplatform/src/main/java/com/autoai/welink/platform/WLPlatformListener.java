package com.autoai.welink.platform;

import android.graphics.Bitmap;
import android.view.MotionEvent;

public interface WLPlatformListener {
    /**
     * AOA就绪
     */
    void onLinkAOAReady();

    /**
     * 车机连接
     * @param HUScreenWidth 车机屏幕宽度
     * @param HUScreenHeight 车机屏幕高度
     * @param densityDpi 车机屏幕密度
     * @param vehicleType 车机类型
     * @param vehicleVersion 车机版本
     * @param huBtMacAddress 蓝牙地址
     * @param isAOA 是否是AOA互联
     */
    void onLinkConnected(int HUScreenWidth, int HUScreenHeight, int densityDpi, String vehicleType, String vehicleVersion, String huBtMacAddress, boolean isAOA);

    /**
     * 车机断开
     */
    void onLinkUnconnected(boolean isCrash);

    /**
     * 指定获取的视频帧数据
     * @param data
     */
    void onFrameData(byte[] data);

    /**
     * 车机屏幕触控事件
     * @param event 回控事件
     */
    void onLinkTouch(MotionEvent event);

    /**
     * 接收到车机message数据
     * @param data 收到的车机端message数据
     */
    void onLinkHUMessageData(String data);

    /**
     * 接收到车机candata数据
     * @param data 收到的车机端candata数据
     */
    void onLinkHUCanData(byte[] data);

    /**
     * 车机挂起
     */
    void onLinkSuspend();

    /**
     * 车机恢复
     */
    void onLinkResume();

    /**
     * 收到App发送的声音
     * 优先级如果返回<0的话，会每次都询问优先级，如果是返回>=0的话，连续相同的mark就不会再询问优先级
     * @param connectStr 连接字符串
     * @param mark 声音标识
     * @param duration 声音播放时长，单位: 毫秒
     * @return 声音优先级
     */
    int onAppSound(String connectStr, String mark, int duration);

    /**
     * App连接成功
     * @param connectStr 连接字符串
     */
    void onAppConnected(String connectStr);

    /**
     * App切换至前台
     * @param connectStr 连接字符串
     */
    void onAppForeground(String connectStr);

    /**
     * App切换至后台
     * @param connectStr 连接字符串
     */
    void onAppBackground(String connectStr);

    /**
     * App断开连接
     * @param connectStr 连接字符串
     */
    void onAppDisconnected(String connectStr);

    /**
     * App返回连接失败
     * @param connectStr 连接字符串
     * @param errorCode 错误码
     *                  1. WeLink服务无效，有可能是WeLink App没有运行或者没有连接车机
     *                  2. 连接字符串不正确
     *                  3. WLConnector版本太老，无法连接当前的WeLink服务
     *                  4. 重复连接
     *                  5. 连接WeLink服务失败
     *                  6. App不支持该分辨率
     */
    void onAppError(String connectStr, int errorCode);

    /**
     * 请求使用Microphone
     */
    static final int ACTION_REQUEST_MICROPHONE = 3;
    /**
     * 放弃使用Microphone
     */
    static final int ACTION_ABANDON_MICROPHONE = 4;
    /**
     * 活动触发通知
     * @param connectStr 发送者的连接字符串
     * @param action action id -- 参照文档《ActionID》
     * ACTION_REQUEST_MICROPHONE: 请求Microphone
     * ACTION_ABANDON_MICROPHONE: 放弃Microphone
     */
    void onAppAction(String connectStr, int action);


    /**
     * 播放器注册，播放器注册才可能成为激活音乐App
     * @param connectStr 连接字符串
     */
    void onMusicRegister(String connectStr);

    /**
     * 播放器取消注册，播放器取消注册将变为非激活音乐App
     * @param connectStr 连接字符串
     */
    void onMusicUnregister(String connectStr);

    /**
     * 音乐ID3信息更新
     *
     * @param source 音源
     * @param artist 演唱者(创建者)
     * @param title 歌曲名(电台名)
     * @param album 专辑名
     * @param lyric 歌词json字符串
     * @param duration 时长 单位:秒
     * @param cover 歌曲封面，可以为null
     */
    void onMusicID3(String source, String artist, String title, String album, String lyric, int duration, Bitmap cover);

    /**
     * 音乐播放顺序更新
     *
     * @param order 播放顺序：参见《车机命令》，0: 循环播放、1: 顺序播放、2: 随机播放、3: 单曲循环播放
     */
    void onMusicOrder(int order);

    /**
     * 接收到音乐PCM数据
     * @param position 数据的位置, -1代表直播
     * @param totalLen 声音数据总长度
     * @param rate PCM数据采样率
     * @param bit PCM数据采样位数
     * @param channel PCM数据声道数
     */
    void onMusicPCM(long position, long totalLen, int rate, int bit, int channel);

    /**
     * 错误
     * @param reason 错误码
     *               0: 创建Wi-Fi Direct GO成功
     *               1: 设备不支持蓝牙BLE
     *               2: 没有打开蓝牙
     *               3: 没有打开Wi-Fi
     *               4: 需要请求定位权限(ACCESS_FINE_LOCATION)
     *               5: 服务发生异常
     *               6: 创建Wi-Fi Direct GO失败
     *               7: 重复调用createHardwareGroup
     */
    void onHardwareGroupError(int reason);

    /**
     * HardwareGroup状态
     * @param status 状态码
     *               0: Wi-Fi Direct GO广播，content: GO签名
     *               1: BLE设备连接中...
     *               2: BLE设备连接成功
     *               3: BLE服务连接中...
     *               4: BLE服务连接成功
     *               5: 读取版本中...
     *               6: 版本读取成功，content：版本号
     *               7: 写Wi-Fi Direct GO签名
     *               8: 签名写成功，content：签名
     *               9: Wi-Fi Direct GC连接，content: 网络密码
     *               10: 未找到对应包名的硬件心跳
     *               -1: 设备连接失败
     *               -2: 服务连接失败
     *               -3: 版本读取失败
     *               -4: 签名写失败
     *               -5: 操作超时
     */
    void onHardwareGroupStatusChanged(int status, String content);

    /**
     * 互联的车机热点名称和密码
     * @param ssid
     * @param password
     */
    void onWiFiApConfig(String ssid, String password);
}
