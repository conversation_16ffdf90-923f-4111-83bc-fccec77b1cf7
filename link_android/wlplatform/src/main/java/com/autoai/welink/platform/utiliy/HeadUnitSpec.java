package com.autoai.welink.platform.utiliy;

import android.content.Context;
import android.content.res.XmlResourceParser;

import com.autoai.welink.platform.R;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;

public class HeadUnitSpec {
    private static final String TYPE = "0";
    private static final int DPI = 320;
    private static final int FPS = 30;
    private static final int RATE = 16000;
    private static final int BIT = 16;
    private static final int CHANNEL = 1;

    public HeadUnitSpec(Context context, String type, int huFPS, int width, int height) {
        this.width = width;
        this.height = height;

        if (!parser(context, type, width, height)) {
            parser(context, TYPE, width, height);
            if (huFPS > 0) {
                fps = huFPS;
            }
        }
    }

    private boolean parser(Context context, String type, int width, int height) {
        AnFileLog.e("auto-fps", "type: " + type);
        XmlResourceParser xmlParser = context.getResources().getXml(R.xml.headunit_specs);
        boolean returnTag = false;
        boolean findTag = false;

        try {
            int event = xmlParser.getEventType();   //先获取当前解析器光标在哪
            while (event != XmlPullParser.END_DOCUMENT) {    //如果还没到文档的结束标志，那么就继续往下处理
                switch (event) {
                    case XmlPullParser.START_TAG:
                        //一般都是获取标签的属性值，所以在这里数据你需要的数据
                        if ("headunit".equals(xmlParser.getName())) {
                            if (xmlParser.getAttributeValue(null, "type").equals(type)) {
                                returnTag = true;
                                findTag = true;
                            } else {
                                findTag = false;
                            }
                        } else if (findTag && "microphone".equals(xmlParser.getName())) {
                            rate = xmlParser.getAttributeIntValue(null, "rate", RATE);
                            bit = xmlParser.getAttributeIntValue(null, "bit", BIT);
                            channel = xmlParser.getAttributeIntValue(null, "channel", CHANNEL);
                        } else if (findTag && "display".equals(xmlParser.getName())) {
                            width = xmlParser.getAttributeIntValue(null, "width", width);
                            height = xmlParser.getAttributeIntValue(null, "height", height);
                            dpi = xmlParser.getAttributeIntValue(null, "dpi", DPI);
                            fps = xmlParser.getAttributeIntValue(null, "fps", FPS);
                            AnFileLog.e("auto-fps", "spec-fps: " + fps);
                        }
                        break;
                    default:
                        break;
                }
                event = xmlParser.next();   //将当前解析器光标往下一步移
            }
        } catch (XmlPullParserException | IOException e) {
            e.printStackTrace();
            return false;
        }

        return returnTag;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public int getDPI() {
        return dpi;
    }

    public int getFPS() {
        return fps;
    }

    public int getMicPCMRate() {
        return rate;
    }

    public int getMicPCMBit() {
        return bit;
    }

    public int getMicPCMChannel() {
        return channel;
    }

    private int width;
    private int height;
    private int dpi = DPI;
    private int fps = FPS;
    private int rate = RATE;
    private int bit = BIT;
    private int channel = CHANNEL;
}
