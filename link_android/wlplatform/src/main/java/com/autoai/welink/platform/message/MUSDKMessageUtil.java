package com.autoai.welink.platform.message;

import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * she tips: 有些车机-->手机的消息，不需要经过上层，在platform层触发
 * 但是遵循协议，临时封装改Util，便于消息发送
 * fixme：后续考虑如何和应用层保持一致
 */
public class MUSDKMessageUtil {

    private static final String Tag = "MUSDKMessageUtil";

    public static final int WL_PROTOCOL_VALUE_VERSION = 0;
    public static final String WL_PROTOCOL_VALUE_MODULENAME = "WeLink";
    public static final String WL_PROTOCOL_VALUE_PLATFORM = "android|ios|ce";

    public static final String WL_PROTOCOL_FIELD_MODULENAME = "moduleName";
    public static final String WL_PROTOCOL_FIELD_VERSION = "version";
    public static final String WL_PROTOCOL_FIELD_PLATFORM = "platform";
    public static final String WL_PROTOCOL_FIELD_COMMAND = "command";
    public static final String WL_PROTOCOL_FIELD_METHOD = "method";
    public static final String WL_PROTOCOL_FIELD_EXTDATA = "extData";

    /**
     *
     */
    public static final String MU_PROTOCOL_METHOD_PHONE_PLAY_VEADIO_STATE = "notify_phone_play_video_state";

    public static String getMethodProtocol(String methodName, JSONObject extData) {
        Log.d(Tag,"sendMessageDataCommand methodName = " + methodName);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(WL_PROTOCOL_FIELD_MODULENAME, WL_PROTOCOL_VALUE_MODULENAME);
            jsonObject.put(WL_PROTOCOL_FIELD_VERSION, WL_PROTOCOL_VALUE_VERSION);
            jsonObject.put(WL_PROTOCOL_FIELD_PLATFORM, WL_PROTOCOL_VALUE_PLATFORM);

            JSONObject commandObject = new JSONObject();
            commandObject.put(WL_PROTOCOL_FIELD_METHOD, methodName);
            commandObject.put(WL_PROTOCOL_FIELD_EXTDATA, extData);

            jsonObject.put(WL_PROTOCOL_FIELD_COMMAND, commandObject);
        } catch (JSONException e) {
            Log.e(Tag,"getMethodProtocol exception:" + e.getMessage());
        }
        return jsonObject.toString();
    }
}
