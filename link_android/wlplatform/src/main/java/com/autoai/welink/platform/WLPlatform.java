package com.autoai.welink.platform;

import android.app.Dialog;
import android.app.Notification;
import android.bluetooth.le.ScanFilter;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.media.MediaMetadataRetriever;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.view.MotionEvent;
import android.view.Surface;

import com.autoai.welink.auto.WLConnector;
import com.autoai.welink.auto.v1.MainHandler;
import com.autoai.welink.autoproxy.WLServer;
import com.autoai.welink.platform.message.MUSDKMessageUtil;
import com.autoai.welink.platform.service.IReqCurFrameCallback;
import com.autoai.welink.platform.service.IServiceCallback;
import com.autoai.welink.platform.service.IWeLinkService;
import com.autoai.welink.platform.utiliy.AnFileLog;
import com.autoai.welink.platform.utiliy.AwakeLock;
import com.autoai.welink.platform.utiliy.OverlayDialog;

import java.util.List;
import java.util.Locale;

import static android.content.Context.BIND_AUTO_CREATE;


import org.json.JSONException;
import org.json.JSONObject;

public final class WLPlatform {
    public static final int WL_CAP_HARDWARE = WLServer.WL_CAP_HARDWARE;
    public static final int WL_CAP_DISPLAY = WLServer.WL_CAP_DISPLAY;
    public static final int WL_CAP_SOUND = WLServer.WL_CAP_SOUND;
    public static final int WL_CAP_TBTINFO = WLServer.WL_CAP_TBTINFO;
    public static final int WL_CAP_MUSIC = WLServer.WL_CAP_MUSIC;
    public static final int WL_CAP_MICROPHONE = WLServer.WL_CAP_MICROPHONE;
    public static final int WL_CAP_BLUETOOTHPHONE = WLServer.WL_CAP_BLUETOOTHPHONE;

    public static final int HARDWARE_CONNECT_TIMEOUT = 50;
    public static final int HARDWARE_BLE_RSSI_THRESHOLD = -80;

    private Context mContext;
    private IWeLinkService mWeLinkService = null;
    private WLPlatformListener mListener;
    private static volatile WLPlatform mPlatform = null;
    private boolean mIsLinkConnected = false;
    private Intent mNeedLinkIntent = null;
    private boolean mNeedHardwareGroup = false;
    private String mNeedHardwareGroupName;
    private String mNeedHardwareGroupPassphrase;
    private int mNeedHardwareGroupFrequency;
    private int mNeedHardwareGroupRSSI;
    private int mNeedHardwareGroupTimeout;
    private boolean mNeedUDPBroadcast = false;
    private String mNeedUDPBroadcastType = null;
    private List<ScanFilter> mNeedHardwareGroupFilterList;

    private boolean mNeedWIFIAPConnect = false;
    /**
     * ap 名称和密码
     */
    private String mNeedWIFIAPSSID;
    private String mNeedWIFIAPPassword;
    private Notification mNotification;
    private AwakeLock mAwakeLock;
    private final IBinder mDeathBinder = new Binder();
    private IBinder mServiceBinder = null;
    private boolean mCrash = false;
    private IBinder.DeathRecipient mDeathRecipient = null;
    private boolean driveSafetySaveVideoSwitch = true;
    private boolean driveSafetyVideoPlay = true;
    private boolean driveSafetyOfflineSwitch = true;
    private int fps;
    private int logLevel;


    public void setDriveSafetyVideoPlay(boolean driveSafetyOnline) {
        driveSafetyVideoPlay = driveSafetyOnline;
        try {
            if (mWeLinkService != null && mIsLinkConnected) {
                mWeLinkService.checkSwitch(driveSafetySaveVideoSwitch,driveSafetyOfflineSwitch, driveSafetyVideoPlay);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setDriveSafetySaveVideo(boolean saveVideo) {
        driveSafetySaveVideoSwitch = saveVideo;
        try {
            if (mWeLinkService != null && mIsLinkConnected) {
                mWeLinkService.checkSwitch(driveSafetySaveVideoSwitch,driveSafetyOfflineSwitch, driveSafetyVideoPlay);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setDriveSafetyOfflineSwitch(boolean driveSafetyOnline) {
        driveSafetyOfflineSwitch = driveSafetyOnline;
        try {
            if (mWeLinkService != null && mIsLinkConnected) {
                mWeLinkService.checkSwitch(driveSafetySaveVideoSwitch,driveSafetyOfflineSwitch, driveSafetyVideoPlay);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    static class ResultHandler<T> extends Handler {
        T result;

        public ResultHandler(Looper looper) {
            super(looper);
        }
    }

    private final Runnable runnable = new Runnable() {
        @Override
        public void run() {
            Intent intent = new Intent();
            intent.setAction("com.autoai.welink.platform.PrimaryScreen");
            intent.setPackage(mContext.getPackageName());
            mContext.bindService(intent, mConnection, BIND_AUTO_CREATE);
            boolean isBound = mContext.bindService(intent, mConnection, BIND_AUTO_CREATE);
            if (isBound) {
                AnFileLog.e("WelinkPlatform", "runnable::Service bound successfully");
            } else {
                AnFileLog.e("WelinkPlatform", "runnable::Failed to bind service");
            }
        }
    };

    private final IServiceCallback mCallback = new IServiceCallback.Stub() {
        @Override
        public void onLinkAOAReady() {
            //AOA就绪
            AnFileLog.e("WelinkPlatform", "onLinkAOAReady");

            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onLinkAOAReady();
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onLinkConnected(final int HUScreenWidth, final int HUScreenHeight, final int densityDpi, final String vehicleType, String vehicleVersion, String huBtMacAddress, final boolean isAOA) {
            //车机互联成功
            AnFileLog.e("WelinkPlatform", "onLinkConnected");
            mIsLinkConnected = true;
            if(mAwakeLock != null) {
                mAwakeLock.lock(mNotification);
            }

            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onLinkConnected(HUScreenWidth, HUScreenHeight, densityDpi, vehicleType, vehicleVersion, huBtMacAddress, isAOA);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onLinkError() {
            onLinkUnconnected();
        }

        @Override
        public void onLinkUnconnected() {
            //车机互联断开
            AnFileLog.e("WelinkPlatform", "onLinkUnconnected  mIsLinkConnected:"+mIsLinkConnected);

            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mCrash = false;
                                if (mWeLinkService != null) {
                                    try {
                                        mContext.unbindService(mConnection);
                                    } catch (IllegalArgumentException e) {
                                        e.printStackTrace();
                                    }

                                    mWeLinkService = null;
                                    //解决LINKINTERNAL-395
                                    if(!mIsLinkConnected){
                                        mListener.onLinkUnconnected(mCrash);
                                    }
                                } else {
                                    mListener.onLinkUnconnected(mCrash);
                                }
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
            if(mAwakeLock != null) {
                mAwakeLock.unlock();
            }
        }

        @Override
        public void onFrameData(final byte[] data) {
            AnFileLog.e("WelinkPlatform", "onFrameData");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onFrameData(data);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onLinkTouch(final MotionEvent motionEvent) {
            //车机回控事件：若返回true则表示server不需要发送给connector处理；false则表示server使用者处理。
            AnFileLog.e("WelinkPlatform", "onLinkTouch");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                AnFileLog.e("WelinkPlatform", "onLinkTouch - Handler");
                                mListener.onLinkTouch(motionEvent);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onLinkHUMessageData(final String data) {
            AnFileLog.e("WelinkPlatform", "onLinkHUMessageData ");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onLinkHUMessageData(data);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onLinkHUCanData(final byte[] data) {
            AnFileLog.e("WelinkPlatform", "onLinkHUCanData ");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onLinkHUCanData(data);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onLinkSuspend() {
            AnFileLog.e("WelinkPlatform", "onLinkSuspend ");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onLinkSuspend();
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onLinkResume() {
            AnFileLog.e("WelinkPlatform", "onLinkResume ");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onLinkResume();
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onAppConnected(final String connectStr) {
            //第三方应用连接server。
            AnFileLog.e("WelinkPlatform", "onAppConnected :" + connectStr);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onAppConnected(connectStr);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onAppForeground(final String connectStr) {
            //第三方应用切换到前台。
            AnFileLog.e("WelinkPlatform", "onAppForground connectStr: " + connectStr);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onAppForeground(connectStr);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onAppBackground(final String connectStr) {
            //第三方应用切换到后台。
            AnFileLog.e("WelinkPlatform", "onAppBackground connectStr: " + connectStr);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onAppBackground(connectStr);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onAppDisconnected(final String connectStr) {
            //第三方应用与server连接断开。
            AnFileLog.e("WelinkPlatform", "onAppDisconnected :" + connectStr);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onAppDisconnected(connectStr);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onAppError(final String connectStr, final int type) {
            //连接错误
            AnFileLog.e("WelinkPlatform", "onAppError connectStr:" + connectStr + " Error:" + type);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onAppError(connectStr, type);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onAppAction(final String connectStr, final int ActionID) {
            //发送action
            AnFileLog.e("WelinkPlatform", "onAppAction connectStr:" + connectStr + " action:" + ActionID);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onAppAction(connectStr, ActionID);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public int onAppReceiveSound(final String connectStr, final String mark, final int duration) {
            //返回true则将sound数据发送给车机、false则忽略发送。
            AnFileLog.e("WelinkPlatform", "onAppReceiveSound connectStr:" + connectStr);

            if (mListener != null) {
                final Object lock = new Object();
                ResultHandler<Integer> handler = new ResultHandler<Integer>(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                synchronized (lock) {
                                    result = mListener.onAppSound(connectStr, mark, duration);
                                    lock.notify();
                                }
                        }
                    }
                };

                try {
                    synchronized (lock) {
                        Message msg = handler.obtainMessage(0);
                        msg.sendToTarget();
                        lock.wait();
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                if (handler != null && handler.result != null) {
                    return handler.result.intValue();
                }
            }

            return 0;
        }

        @Override
        public void onMusicRegister(final String connectStr) {
            AnFileLog.e("WelinkPlatform", "onMusicRegister:" + connectStr);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onMusicRegister(connectStr);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onMusicUnregister(final String connectStr) {
            AnFileLog.e("WelinkPlatform", "onMusicUnregister:" + connectStr);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onMusicUnregister(connectStr);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onMusicID3(final String source, final String artist, final String title, final String album, final String lyric, final int duration, final Bitmap cover) {
            AnFileLog.e("WelinkPlatform", "onMusicID3 artist:" + artist
                    + " title:" + title
                    + " album:" + album
                    + " lyric:" + lyric);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onMusicID3(source, artist, title, album, lyric, duration, cover);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onMusicOrder(final int order) {
            AnFileLog.e("WelinkPlatform", "onMusicOrder:" + order);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onMusicOrder(order);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onMusicPCM(final long position, final long totalLen, final int rate, final int bit, final int channel) {
            AnFileLog.e("WelinkPlatform", "onMusicPCM:" + position + "/" + totalLen);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onMusicPCM(position, totalLen, rate, bit, channel);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onHardwareGroupStatusChanged(final int status, final String content) {
            AnFileLog.e("WelinkPlatform", "onHardwareGroupStatusChanged");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onHardwareGroupStatusChanged(status, content);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }

        @Override
        public void onHardwareGroupError(final int reason) {
            AnFileLog.e("WelinkPlatform", "onHardwareGroupError");
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onHardwareGroupError(reason);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }
        @Override
        public void onWiFiApConfig(String ssid, String password){
            AnFileLog.e("WelinkPlatform", "onWiFiApConfig ssid:" + ssid
                    + " password:" + password);
            if (mListener != null) {
                Handler handler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        switch (msg.what) {
                            case 0:
                                mListener.onWiFiApConfig(ssid, password);
                                break;
                        }
                    }
                };

                Message msg = handler.obtainMessage(0);
                msg.sendToTarget();
            }
        }
    };

    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, final IBinder service) {
            if (mWeLinkService != null) {
                return;
            }
            MainHandler.getInstance().removeCallbacks(runnable);

            AnFileLog.e("WelinkPlatform", "onServiceConnected ComponentName:" + name);

            final boolean crash = mCrash;
            mCrash = true;
            //连接后拿到 Binder，转换成 AIDL，在不同进程会返回个代理
            mWeLinkService = IWeLinkService.Stub.asInterface(service);
            if (mWeLinkService != null) {
                try {

                    mWeLinkService.init(mCallback, mDeathBinder);
                    mWeLinkService.enableLogCat(AnFileLog.isEnableLogCat(),logLevel);
                    mWeLinkService.enableLogFile(AnFileLog.isEnableLogFile(),logLevel);

                    mWeLinkService.checkSwitch(driveSafetySaveVideoSwitch, driveSafetyOfflineSwitch, driveSafetyVideoPlay);
                    mWeLinkService.changeFps(fps);
                    mServiceBinder = service;
                    mDeathRecipient = new IBinder.DeathRecipient() {
                        @Override
                        public void binderDied() {
                            AnFileLog.e("WelinkPlatform", "WeLinkService is died!");
                            mServiceBinder.unlinkToDeath(this, 0);

                            if (mWeLinkService != null) {
                                try {
                                    mContext.unbindService(mConnection);
                                } catch (IllegalArgumentException e) {
                                    e.printStackTrace();
                                }

                                mWeLinkService = null;
                            }

                            if (mAwakeLock != null) {
                                mAwakeLock.unlock();
                            }

                            MainHandler.getInstance().post(runnable);
                            MainHandler.getInstance().postDelayed(runnable, 1000);
                        }
                    };
                    mServiceBinder.linkToDeath(mDeathRecipient, 0);

                    if (mIsLinkConnected && mListener != null) {
                        Handler handler = new Handler(Looper.getMainLooper()) {
                            @Override
                            public void handleMessage(Message msg) {
                                switch (msg.what) {
                                    case 0:
                                        mListener.onLinkUnconnected(crash);
                                        break;
                                }
                            }
                        };

                        Message msg = handler.obtainMessage(0);
                        msg.sendToTarget();
                        mIsLinkConnected = false;
                    }

                    if (mNeedLinkIntent != null) {
                        AnFileLog.e("WelinkPlatform", "mNeedLinkIntent not null, continue mWeLinkService.linkAOA()");
                        mWeLinkService.linkAOA(mNeedLinkIntent);
                        mNeedLinkIntent = null;
                    }

                    if (mNeedHardwareGroup) {
                        AnFileLog.e("WelinkPlatform", "mNeedHardwareGroup , continue mWeLinkService.linkBleWifi()");
                        mNeedHardwareGroup = false;
                        mWeLinkService.createHardwareGroup(mNeedHardwareGroupName, mNeedHardwareGroupPassphrase, mNeedHardwareGroupFrequency, mNeedHardwareGroupRSSI, mNeedHardwareGroupTimeout, mNeedHardwareGroupFilterList);
                        mNeedHardwareGroupName = null;
                        mNeedHardwareGroupPassphrase = null;
                        mNeedHardwareGroupFrequency = 0;
                        mNeedHardwareGroupFilterList = null;
                    }

                    if (mNeedUDPBroadcast) {
                        mNeedUDPBroadcast = false;
                        mWeLinkService.startUDPBroadcast(mNeedUDPBroadcastType);
                        mNeedUDPBroadcastType = null;
                    }
                    if(mNeedWIFIAPConnect){
                        mNeedWIFIAPConnect = false;
                        mWeLinkService.startAPConnect(mNeedWIFIAPSSID, mNeedWIFIAPPassword,mNeedHardwareGroupRSSI, mNeedHardwareGroupTimeout, mNeedHardwareGroupFilterList);
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            AnFileLog.e("WelinkPlatform", "onServiceDisconnected ComponentName:" + name);
            mWeLinkService = null;
        }
    };

    /**
     * 连接WeLinkPlatform服务
     *
     * @param context  Application Context
     * @param listener WeLinkPlatform服务的回调
     */
    private WLPlatform(final Context context, final Notification notification, WLPlatformListener listener) {
        boolean isHuaWei = "huawei".equals(Build.BRAND.toLowerCase(Locale.getDefault()));
        AnFileLog.e("WelinkPlatform", "WLPlatform isHuaWei:"+isHuaWei);
        mContext = context.getApplicationContext();
        //todo 暂时判断华为手机时启动 前台服务保活，解决华为手机互联成功 播放视频断开问题，此服务关闭在11 和12 版本会导致持有的录屏对象失效，待优化
        if(isHuaWei || Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU || Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            mAwakeLock = AwakeLock.create(mContext);
        }

        Intent intent = new Intent();
        intent.setAction("com.autoai.welink.platform.PrimaryScreen");
        intent.setPackage(mContext.getPackageName());
        boolean isBound = mContext.bindService(intent, mConnection, BIND_AUTO_CREATE);
        if (isBound) {
            AnFileLog.e("WelinkPlatform", "Service bound successfully");
        } else {
            AnFileLog.e("WelinkPlatform", "Failed to bind service");
            MainHandler.getInstance().postDelayed(runnable, 2000);
        }
    }
    /**
     * 建立WeLinkPlatform服务
     *
     * @param context      Application Context
     * @param notification 前台服务Notification, 用于保活、防休眠
     * @param listener     WeLinkPlatform服务的回调
     */
    public static WLPlatform create(Context context, Notification notification, WLPlatformListener listener) {
        AnFileLog.init(context.getApplicationContext(), "/Test/welinklog/wlplatform-log.txt");

        AnFileLog.e("WelinkPlatform", "create");

        if (mPlatform == null) {
            mPlatform = new WLPlatform(context, notification, listener);
        }

        mPlatform.mNotification = notification;
        mPlatform.mListener = listener;

        return mPlatform;
    }

    /**
     * 释放WeLinkPlatform
     */
    public void release() {
        AnFileLog.e("WelinkPlatform", "release");

        if (mServiceBinder != null) {
            mServiceBinder.unlinkToDeath(mDeathRecipient, 0);
            mServiceBinder = null;
            mDeathRecipient = null;
        }

        if (mWeLinkService != null) {
            try {
                mContext.unbindService(mConnection);
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            }

            mWeLinkService = null;
        }

        Dialog dialog = getOverlay();
        if (dialog != null) {
            dialog.dismiss();
        }
        if(mAwakeLock != null) {
            mAwakeLock.unlock();
            mAwakeLock = null;
        }

        mNeedLinkIntent = null;
        mContext = null;
        mPlatform = null;
    }

    /**
     * 获取WeLinkPlatform版本号
     *
     * @return WeLinkPlatform版本号
     */
    public static String getVersion() {
        return BuildConfig.VERSION_NAME + "/" + BuildConfig.BUILD_TYPE;
    }

    /**
     * 建立无线硬件组
     * 调用该函数前需要申请Manifest.permission.ACCESS_FINE_LOCATION权限，否则调用失败
     *
     * @param BLEDeviceFilterList BLE设备扫描列表
     */
    public void createHardwareGroup(final List<ScanFilter> BLEDeviceFilterList) {
        createHardwareGroup("WeLink", "hardware", 0, HARDWARE_BLE_RSSI_THRESHOLD, HARDWARE_CONNECT_TIMEOUT, BLEDeviceFilterList);
    }

    /**
     * 建立无线硬件组
     * 调用该函数前需要申请Manifest.permission.ACCESS_FINE_LOCATION权限，否则调用失败
     *
     * @param frequency           信道频率
     *                            以下是中国国内Wi-Fi 5G推荐信道，其他信道是禁止或有DFS要求，DFS有要求的信道不建议单独指定
     *                            信道：频率
     *                            36: 5180
     *                            38: 5190
     *                            40: 5200
     *                            44: 5220
     *                            46: 5230
     *                            48: 5240
     *                            149: 5745
     *                            151: 5755
     *                            153: 5765
     *                            157: 5785
     *                            159: 5795
     *                            161: 5805
     *                            165: 5825
     * @param BLEDeviceFilterList BLE设备扫描列表
     */
    public void createHardwareGroup(String networkName,final Integer frequency, final List<ScanFilter> BLEDeviceFilterList) {
        createHardwareGroup(networkName, "hardware", frequency, HARDWARE_BLE_RSSI_THRESHOLD, HARDWARE_CONNECT_TIMEOUT, BLEDeviceFilterList);
    }

    public void createHardwareGroup(Integer frequency, int rssi, int timeout, List<ScanFilter> BLEDeviceFilterList) {
        createHardwareGroup("WeLink", "hardware", frequency, rssi, timeout, BLEDeviceFilterList);
    }

    /**
     * 建立无线硬件组
     * 调用该函数前需要申请Manifest.permission.ACCESS_FINE_LOCATION权限，否则调用失败
     *
     * @param networkName         Wi-Fi Direct 热点名称
     * @param passphrase          Wi-Fi Direct 热点密码
     * @param BLEDeviceFilterList BLE设备扫描列表
     */
    public void createHardwareGroup(final String networkName, final String passphrase, final List<ScanFilter> BLEDeviceFilterList) {
        createHardwareGroup(networkName, passphrase, 0, HARDWARE_BLE_RSSI_THRESHOLD, HARDWARE_CONNECT_TIMEOUT, BLEDeviceFilterList);
    }

    /**
     * 建立无线硬件组
     * 调用该函数前需要申请Manifest.permission.ACCESS_FINE_LOCATION权限，否则调用失败
     *
     * @param networkName         Wi-Fi Direct 热点名称
     * @param passphrase          Wi-Fi Direct 热点密码
     * @param frequency           信道频率
     *                            以下是中国国内Wi-Fi 5G推荐信道，其他信道是禁止或有DFS要求，DFS有要求的信道不建议单独指定
     *                            信道：频率
     *                            36: 5180
     *                            38: 5190
     *                            40: 5200
     *                            44: 5220
     *                            46: 5230
     *                            48: 5240
     *                            149: 5745
     *                            151: 5755
     *                            153: 5765
     *                            157: 5785
     *                            159: 5795
     *                            161: 5805
     *                            165: 5825
     * @param rssi                最小信号强度, 0为最大信号强度
     * @param timeout             连接超时，单位：秒，小于等于0采用默认值5秒
     * @param BLEDeviceFilterList BLE设备扫描列表
     */
    public void createHardwareGroup(final String networkName, final String passphrase, final Integer frequency, final int rssi, final int timeout, final List<ScanFilter> BLEDeviceFilterList) {
        AnFileLog.e("WelinkPlatform", "createHardwareGroup");

        if (mWeLinkService != null) {
            try {
                mWeLinkService.createHardwareGroup(networkName, passphrase, frequency, rssi, timeout, BLEDeviceFilterList);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        } else {
            mNeedHardwareGroup = true;
            mNeedHardwareGroupName = networkName;
            mNeedHardwareGroupPassphrase = passphrase;
            mNeedHardwareGroupFrequency = frequency;
            mNeedHardwareGroupFilterList = BLEDeviceFilterList;
            mNeedHardwareGroupRSSI = rssi;
            mNeedHardwareGroupTimeout = timeout;
        }
    }

    /**
     * 停止扫描 ，以及wlan直连监听
     */
    public void hideGroupInfo() {
        if (mWeLinkService != null) {
            try {
                mWeLinkService.hideGroupInfo();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 继续扫描 ，以及wlan直连监听
     */
    public void showGroupInfo() {
        if (mWeLinkService != null) {
            try {
                mWeLinkService.showGroupInfo();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 刷新无线硬件组，互联长时间没有响应时，可以加速重新互联
     */
    public void refreshHardwareGroup() {
        AnFileLog.e("WelinkPlatform", "destroyHardwareGroup");

        if (mWeLinkService != null) {
            try {
                mWeLinkService.refreshHardwareGroup();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 销毁无线硬件组
     */
    public void destroyHardwareGroup() {
        AnFileLog.e("WelinkPlatform", "destroyHardwareGroup");

        if (mWeLinkService != null) {
            try {
                mWeLinkService.destroyHardwareGroup();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        } else {
            mNeedHardwareGroup = false;
            mNeedHardwareGroupFilterList = null;
        }
    }

    /**
     * 开启扫描车机ap热点 并连接
     */
    public void startAPConnect(String networkName,String passphrase,final List<ScanFilter> BLEDeviceFilterList) {
        AnFileLog.e("WelinkPlatform", "startAPConnect----");
        if (mWeLinkService != null) {
            try {
                mWeLinkService.startAPConnect(networkName, passphrase,HARDWARE_BLE_RSSI_THRESHOLD, HARDWARE_CONNECT_TIMEOUT, BLEDeviceFilterList);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }else{
            mNeedWIFIAPConnect = true;
            //由于和 无感ble 共用蓝牙扫描功能，蓝牙相关配置共用
            mNeedHardwareGroupFilterList = BLEDeviceFilterList;
            mNeedWIFIAPSSID = networkName;
            mNeedWIFIAPPassword = passphrase;
            mNeedHardwareGroupRSSI = HARDWARE_BLE_RSSI_THRESHOLD;
            mNeedHardwareGroupTimeout = HARDWARE_CONNECT_TIMEOUT;
        }
    }
    public void stopAPConnect() {
        if (mWeLinkService != null) {
            try {
                mWeLinkService.stopAPConnect();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }else{
            mNeedWIFIAPConnect = false;
        }
    }
    /**
     * 连接车机
     *
     * @param intent AOA激活的Intent
     */
    public void link(Intent intent) {
        AnFileLog.e("WelinkPlatform", "link");

        if (mWeLinkService != null) {
            mNeedLinkIntent = null;
            try {
                mWeLinkService.linkAOA(intent);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        } else {
            mNeedLinkIntent = intent;
        }
    }

    /**
     * 断开车机
     */
    public void unlink() {
        AnFileLog.e("WelinkPlatform", "unlink");

        mNeedLinkIntent = null;

        if (mWeLinkService != null) {
            try {
                mWeLinkService.unlink();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 初始化WeLink服务
     *
     * @param classNameForPCMPolicy     声音策略类名，该类需要实现WLPCMPolicy接口，null表示没有声音策略
     * @param classNameForCommandParser 车机命令解析器类名，该类需要实现WLCommandParser接口，null表示没有车机命令解析器
     */
    public void init(String classNameForPCMPolicy, String classNameForCommandParser) {
        init(null, classNameForPCMPolicy, classNameForCommandParser);
    }

    /**
     * 初始化WeLink服务
     *
     * @param connectorRect             Connector显示范围
     * @param classNameForPCMPolicy     声音策略类名，该类需要实现WLPCMPolicy接口，null表示没有声音策略
     * @param classNameForCommandParser 车机命令解析器类名，该类需要实现WLCommandParser接口，null表示没有车机命令解析器
     */
    public void init(Rect connectorRect, String classNameForPCMPolicy, String classNameForCommandParser) {
        AnFileLog.e("WelinkPlatform", "init");
        if (mWeLinkService != null) {
            try {
                mWeLinkService.initHU(connectorRect, classNameForPCMPolicy, classNameForCommandParser);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 释放WeLink服务
     */
    public void deinit() {
        AnFileLog.e("WelinkPlatform", "deinit");

        OverlayDialog.release();

        if (mWeLinkService != null) {
            try {
                mWeLinkService.deinitHU();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        if(mAwakeLock != null) {
            mAwakeLock.unlock();
        }
        mNeedLinkIntent = null;
    }

    /**
     * 发送数据到车机
     *
     * @param data 发送给车机message数据
     */
    public void sendMessageData(String data) {
        AnFileLog.e("WelinkPlatform", "sendMessageData");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.sendMessageDataToHU(data);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 发送数据到车机
     *
     * @param data 发送给车机candata数据
     */
    public void sendCanData(byte[] data) {
        AnFileLog.e("WelinkPlatform", "sendCanData");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.sendCanDataToHU(data);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 分配连接字符串
     *
     * @param packageName 连接字符串对应的App包名
     * @param cap         能力标识
     * @return 连接字符串，连接字符串是App连接WeLink的凭证，也是WeLinkPlatform标识App的唯一ID
     */
    public String assign(String packageName, int cap) {
        AnFileLog.e("WelinkPlatform", "assign");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.assign(packageName, cap);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 查找已分配连接字符串
     *
     * @param packageName 连接字符串对应的App包名
     * @param cap         能力标识
     * @return 连接字符串，连接字符串是App连接WeLink的凭证，也是WeLinkPlatform标识App的唯一ID
     */
    public String find(String packageName, int cap) {
        AnFileLog.e("WelinkPlatform", "find");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.find(packageName, cap);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 撤销连接字符串，并断开对应的App连接
     *
     * @param connectStr 连接字符串
     */
    public void revoke(String connectStr) {
        AnFileLog.e("WelinkPlatform", "revoke");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.revoke(connectStr);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 开始投屏
     */
    public void start() {
        AnFileLog.e("WelinkPlatform", "start");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.start();
            } catch (RemoteException e) {
                AnFileLog.e("WelinkPlatform", e.toString());
            }
        }
    }

    /**
     * 开始投屏，并返回指定帧的数据
     *
     * @param frame  指定帧ID，ID从0开始
     * @param type   指定帧类型，SPS: 7, PPS: 8, IDR: 5, P: 1, all: 0
     * @param offset 帧数据偏移
     * @param size   帧数据大小
     */
    public void start(int frame, int type, int offset, int size) {
        AnFileLog.e("WelinkPlatform", "start - frame:" + frame + ", type:" + type + ", offset:" + offset + ", size:" + size);
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.startAndGetFrameData(frame, type, offset, size);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 停止投屏
     */
    public void stop() {
        AnFileLog.e("WelinkPlatform", "stop");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.stop();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 保存手机录制原始视频; 需要在完成连接之后调用.
     *
     * @param startOrStop 开始还是暂停
     * @param localPath   保存路径
     */
    public void toggleLocalVideo(boolean startOrStop, String localPath) {
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.toggleLocalVideo(startOrStop, localPath);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 设置已连接的App投屏
     *
     * @param connectStr 连接字符串，null代表没有App投屏
     */
    public void mirror(String connectStr) {
        AnFileLog.e("WelinkPlatform", "mirror");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.mirror(connectStr, Integer.MAX_VALUE, Integer.MAX_VALUE);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 设置已连接的App投屏
     *
     * @param connectStr 连接字符串，null代表没有App投屏
     * @param x,y        Connector的显示左上角位置
     */
    public void mirror(String connectStr, int x, int y) {
        AnFileLog.e("WelinkPlatform", "mirror");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.mirror(connectStr, x, y);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 向正在投屏的App发送触控事件
     *
     * @param motionEvent 触控事件，触控事件的坐标以车机屏幕为准，内部会转换为Connector的坐标
     * @return 触控事件是否被响应
     */
    public boolean touch(MotionEvent motionEvent) {
        AnFileLog.e("WelinkPlatform", "touch");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.touch(motionEvent);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    /**
     * 向指定App发送命令
     *
     * @param connectStr 连接字符串
     * @param command    命令字符串，具体格式见文档《车机命令》
     */
    public void command(String connectStr, String command) {
        AnFileLog.e("WelinkPlatform", "command");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.command(connectStr, command);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 打开车机MIC被指定的App使用
     *
     * @param connectStr 连接字符串
     */
    public void openMicrophone(String connectStr) {
        AnFileLog.e("WelinkPlatform", "openMicrophone");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.voice(connectStr);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取被激活的音乐App对应的连接字符串
     * WeLink支持的音乐操作：play、stop、pause、resume、order
     * 能够有效执行音乐操作的音乐App有三类:
     * 1.手机前台的音乐App；
     * 2.投屏状态的音乐App；
     * 3.激活状态的音乐App -- 激活状态App: 最后一个成功执行play音乐操作，并且没有断开的App;
     *
     * @return 连接字符串，如果返回null，说明目前没有音乐App被激活
     */
    public String getActivedMusic() {
        AnFileLog.e("WelinkPlatform", "getActivedMusic");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.getActivedMusic();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 设定激活音乐App
     *
     * @param connectStr 连接字符串
     */
    public void activateMusic(String connectStr) {
        AnFileLog.e("WelinkPlatform", "activateMusic");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.activateMusic(connectStr);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 当前全投屏使用的矩阵
     */
    private Rect curExternalRect = null;

    /**
     * 允许External显示
     *
     * @param rect External显示范围
     * @return 代表external的Surface
     */
    public Surface enableExternal(Rect rect) {
        AnFileLog.e("WelinkPlatform", "enableExternal");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                this.curExternalRect = rect;
                return mWeLinkService.enableExternal(rect.left, rect.top, rect.right, rect.bottom, false);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 允许External显示
     *
     * @param rect            External显示范围
     * @param forceFullScreen 区域内的内容强制全屏显示(强制缩放到全屏显示会使得rect中的偏移位置不再起作用,只有宽高起作用,非等比例缩放)
     * @return 代表external的Surface
     */
    public Surface enableExternal(Rect rect, boolean forceFullScreen) {
        AnFileLog.e("WelinkPlatform", "enableExternal");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.enableExternal(rect.left, rect.top, rect.right, rect.bottom, forceFullScreen);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public void updateStates(int width, int height, int angle){
        AnFileLog.e("WelinkPlatform", "updateStates");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.updateStates(width,height,angle);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 禁止External显示
     */
    public void disableExternal() {
        AnFileLog.e("WelinkPlatform", "disableExternal");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.disableExternal();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取车机悬浮对话框
     *
     * @return 悬浮对话框
     */
    public Dialog getOverlay() {
        AnFileLog.e("WelinkPlatform", "getOverlay");
        return OverlayDialog.getInstance(this.mContext, WLPlatform.this);
    }

    public Surface showOverlaySurface() {
        AnFileLog.e("WelinkPlatform", "showOverlaySurface");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.showOverlay();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public void hideOverlay() {
        AnFileLog.e("WelinkPlatform", "hideOverlay");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.hideOverlay();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public int getOverlayWidth() {
        AnFileLog.e("WelinkPlatform", "getOverlayWidth");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.getWidth();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public int getOverlayHeight() {
        AnFileLog.e("WelinkPlatform", "getOverlayHeight");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.getHeight();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public int getOverlayDensity() {
        AnFileLog.e("WelinkPlatform", "getOverlayDensity");
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                return mWeLinkService.getDensity();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public void checkDisplay(int width, int height, int dpi, int fps) {
        AnFileLog.e("WelinkPlatform", "checkDisplay");
        if (mWeLinkService != null) {
            try {
                mWeLinkService.checkDisplay(width, height, dpi, fps);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }
    public void changeFps(int fps){
        AnFileLog.e("WelinkPlatform", "changeFps fps="+fps);
        this.fps = fps;
        if (mWeLinkService != null) {
            try {
                mWeLinkService.changeFps(fps);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }
    /**
     * 开始发送udp广播
     *
     * @param UDP_TYPE 广播类型，为null则为上汽项目
     */
    public void startUDPBroadcast(final String UDP_TYPE) {
        AnFileLog.e("WelinkPlatform", "startUDPBroadcast");
        if (mWeLinkService != null) {
            try {
                mWeLinkService.startUDPBroadcast(UDP_TYPE);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        } else {
            mNeedUDPBroadcast = true;
            mNeedUDPBroadcastType = UDP_TYPE;
        }
    }

    /**
     * 停止发送udp广播
     */
    public void stopUDPBroadcast() {
        AnFileLog.e("WelinkPlatform", "stopUDPBroadcast");
        if (mWeLinkService != null) {
            try {
                mWeLinkService.stopUDPBroadcast();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        } else {
            mNeedUDPBroadcast = false;
            mNeedUDPBroadcastType = null;
        }
    }

    public void enableLogCat(boolean enable,int logLevel) {
        this.logLevel = logLevel;
        AnFileLog.e("WelinkPlatform", "enableLogCat: " + enable);
        AnFileLog.enableLogCat(enable);

        try {
            Class.forName("com.autoai.welink.auto.WLConnector");
            WLConnector.enableLogCat(enable);
        } catch (ClassNotFoundException e) {
        }

        if (mWeLinkService != null) {
            try {
                mWeLinkService.enableLogCat(enable,logLevel);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public void enableLogFile(boolean enable,int logLevel) {
        AnFileLog.e("WelinkPlatform", "enableLogFile: " + enable);
        AnFileLog.enableLogFile(enable);

        try {
            Class.forName("com.autoai.welink.auto.WLConnector");
            WLConnector.enableLogFile(enable);
        } catch (ClassNotFoundException e) {
        }

        if (mWeLinkService != null) {
            try {
                mWeLinkService.enableLogFile(enable,logLevel);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public long getThroughput() {
        AnFileLog.e("WelinkPlatform", "getThroughput");
        if (mWeLinkService != null) {
            try {
                return mWeLinkService.getThroughput();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        return -1;
    }
    /**
     * 主动请求关键帧
     * @param frame 请求关键帧次数
     * @param time  总时间内请求的framse次关键帧
     */
    public void reqKeyFrame(int frame ,int time){
        AnFileLog.e("WelinkPlatform", "reqKeyFrame");
        if (mWeLinkService != null) {
            try {
                 mWeLinkService.reqKeyFrame(frame,time);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }


    private MediaMetadataRetriever mediaMetadataRetriever = new MediaMetadataRetriever();

    int i = 0;

    /**
     * 判断播放状态，将结果传递给车机
     */
    public void checkCurrentPlayStatus(int action) {
        if (mWeLinkService != null && mIsLinkConnected) {
            try {
                mWeLinkService.reqCurFrame(action,new IReqCurFrameCallback.Stub() {
                    @Override
                    public void onFame(byte[] curCaptureFrame) {
                        // ==1 为true,在在播视频
                        int result = curCaptureFrame[0];
                        // 发送到车机
                        notifyPhonePlayState(result);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private void notifyPhonePlayState(int playStatus) {
        try {
            JSONObject extData = new JSONObject();
            extData.put("key", MUSDKMessageUtil.MU_PROTOCOL_METHOD_PHONE_PLAY_VEADIO_STATE);
            extData.put("value", playStatus);
            String data = MUSDKMessageUtil.getMethodProtocol(MUSDKMessageUtil.MU_PROTOCOL_METHOD_PHONE_PLAY_VEADIO_STATE, extData);
            sendMessageData(data);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }


}
