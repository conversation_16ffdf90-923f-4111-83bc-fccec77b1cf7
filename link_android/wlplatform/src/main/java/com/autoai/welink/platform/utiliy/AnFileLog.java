package com.autoai.welink.platform.utiliy;

import android.content.Context;
import android.util.Log;

import com.autoai.welink.platform.BuildConfig;

import java.io.File;
import java.io.RandomAccessFile;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class AnFileLog {
    static private String externalDir = "";
    static private final String FLAG_DIR = "/Test/welinklog";
    static private String LOG_FILE;
    static private boolean checkFlagFile = false;
    static private boolean writeFileLog = false;
    static private final Object writeFileLock = new Object();
    static private boolean outputLogcat = BuildConfig.DEBUG;
    static private final SimpleDateFormat fmt = new SimpleDateFormat("MMdd HH:mm:ss.SSS", Locale.ENGLISH);

    static public void init(Context context, final String logFile) {
        LOG_FILE = logFile;
        File file = context.getExternalFilesDir(null);

        if (file != null) {
            externalDir = file.getAbsolutePath();
        }
    }

    static public boolean isEnableLogFile() {
        return writeFileLog;
    }

    static public boolean isEnableLogCat() {
        return outputLogcat;
    }

    static public void enableLogCat(boolean enable) {
        outputLogcat = enable;
    }

    static public void enableLogFile(boolean enable) {
        writeFileLog = enable;

        if (writeFileLog) {
            String filename = externalDir + FLAG_DIR;
            File file = new File(filename);
            file.mkdirs();
        }
    }

    static public void e(String tag, String str) {
        if (outputLogcat) {//既按指定tag输出,也会按统一的tag输出一次,便于同时查看所有log
            Log.w(tag, str);
        }

        if (writeFileLog) {
            synchronized (writeFileLock) {
                logFile(tag + ">" + str);
            }
        }
    }

    static public void del() {
        String filename = externalDir + LOG_FILE;
        try {
            File file = new File(filename);
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception var9) {
            var9.printStackTrace();
        }
    }

    static private void logFile(String str) {
        if (writeFileLog) {
            String filename = externalDir + LOG_FILE;
            String ss = fmt.format(new Date()) + " " + str + "\n";
            try {
                byte[] buffer = ss.getBytes();
                File file = new File(filename);
                if (!file.exists()) {
                    File dir = file.getParentFile();
                    if (dir != null) {
                        dir.mkdirs();
                    }

                    file.createNewFile();
                }

                RandomAccessFile raf = new RandomAccessFile(file, "rwd");
                raf.seek(file.length());
                raf.write(buffer);
                raf.close();
            } catch (Exception var9) {
                var9.printStackTrace();
            }
        }
    }
}

