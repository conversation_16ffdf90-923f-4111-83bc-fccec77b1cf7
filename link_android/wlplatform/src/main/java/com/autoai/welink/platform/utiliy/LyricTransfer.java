package com.autoai.welink.platform.utiliy;

//import com.wedrive.welink.music.qq.bean.LyricBean;
//import com.wedrive.welink.music.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

class LyricBean {
    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    private String time; // 时间

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    private String content; // 歌词

    @Override
    public String toString() {
        return "{" +
                "time:\"" + time + '\"' +
                ", content:\"" + content + '\"' +
                "}";
    }
}

public class LyricTransfer {
    private final Pattern pattern = Pattern.compile("[0-9]*");
    private List<LyricBean> id3lyricList = new ArrayList<>();
    /**
     * 判断字符串是不是纯数字
     *
     * @param str 要判断的字符串
     * @return boolean 判断结果
     */
    private boolean isNumeric(String str) {
        return pattern.matcher(str).matches();
    }

    public LyricTransfer() {
        super();
    }

    public JSONArray parserLyric(String lyric, int type){
        JSONArray jsonArray = new JSONArray();
        if(lyric != null){
            id3lyricList.clear();
            byte[] by = lyric.getBytes();
            InputStream is = new ByteArrayInputStream(by);
            InputStreamReader isr = new InputStreamReader(is);
            BufferedReader reader = new BufferedReader(isr);
            String line;
            try {
                while ((line = reader.readLine()) != null) {
                    switch (type){
                        case 1:{
                            //LRC
                            parserLineLrc(line, true);
                        }
                        break;
                        case 2:{
                            //QRC
                            parserLineQrc(line, true);
                        }
                        break;
                        default:
                            break;
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (id3lyricList == null ||id3lyricList.isEmpty()) {
                return jsonArray;
            }

            for (Object object : id3lyricList) {
                try {
                    JSONObject obj = new JSONObject(object.toString());
                    jsonArray.put(obj);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        return jsonArray;
    }

    /**
     *
     * @param line
     * @param isOriLyric true表示带时间存入map，false表示不带时间
     */
    private void parserLineQrc(String line, boolean isOriLyric) {
        if (line.contains(",")) {
            int indexOf = line.indexOf(",");
            if (line.startsWith("[")) {
                String time = line.substring(1, indexOf);
                String name = parseContent(line);
                LyricBean lyricBean = new LyricBean();
                if (isNumeric(time)) {
//                    int parseInt1 = Integer.parseInt(time);
//                    int parseInt = parseInt1 / 1000;
//                    if(isOriLyric) {
//                        lyricMap.put(parseInt + "", line);
//                    }else{
//                        lyricMap.put(parseInt + "", name);
//                    }
                    lyricBean.setTime(time);
                    lyricBean.setContent(name);
                    id3lyricList.add(lyricBean);
                }
            }
        }
    }

    /**
     *
     * @param line
     * @param isOriLyric true表示带时间存入map，false表示不带时间
     */
    private void parserLineLrc(String line, boolean isOriLyric) {
        if (line.startsWith("[") && line.contains("]")) {
            int indexOf = line.indexOf("]");
            String time = line.substring(1, indexOf);
            String lyric = line.substring(indexOf + 1);
            LyricBean lyricBean = new LyricBean();

            int ts = parseTime(time);
            if (ts > -1) {
//                int parseInt = ts / 1000;
//                if (isOriLyric) {
//                    lyricMap.put(parseInt + "", line);
//                } else {
//                    lyricMap.put(parseInt + "", lyric);
//                }
                lyricBean.setTime(String.valueOf(ts));
                lyricBean.setContent(lyric);
                id3lyricList.add(lyricBean);
            }
        }
    }

    private String parseContent(String str) {
        String s = str.replaceAll("\\([0-9]*,[0-9]*\\)", "");
        return s.replaceAll("\\[[0-9]*,[0-9]*\\]", "");
    }

    private int parseTime(String time){
        int t = -1;
        try {
            if (time.contains(".")){
                int index = time.indexOf(".");
                String t1 = time.substring(0,index);
                String t2 = time.substring(index + 1);
                String[] temp = t1.split(":");
                if (temp.length == 2){
                    String time0 = temp[0];
                    String time1 = temp[1];
                    int tm = Integer.parseInt(time0);
                    int ts = Integer.parseInt(time1);
                    int tms = Integer.parseInt(t2);
                    t = tm * 60 * 1000 + ts * 1000 + tms;
                }
            }
        }catch (Exception e){
        }
        return t;
    }

}
