package com.autoai.welink.platform.service;

import static com.autoai.welink.platform.WLPlatform.WL_CAP_HARDWARE;

import android.app.Service;
import android.bluetooth.le.ScanFilter;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Rect;
import android.media.MediaFormat;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;
import android.view.MotionEvent;
import android.view.Surface;

import com.autoai.welink.autoproxy.AppConnectListener;
import com.autoai.welink.autoproxy.WLConfiguration;
import com.autoai.welink.autoproxy.WLServer;
import com.autoai.welink.autoproxy.codec.WLCodecListener;
import com.autoai.welink.channel.WLChannel;
import com.autoai.welink.channel.WLChannelListener;
import com.autoai.welink.platform.BuildConfig;
import com.autoai.welink.platform.WLCommandParser;
import com.autoai.welink.platform.WLPCMPolicy;
import com.autoai.welink.platform.img_recognition.RecognitionController;
import com.autoai.welink.platform.img_recognition.VideoPlaybackChecker;
import com.autoai.welink.platform.output.MediaMuxerOutput;
import com.autoai.welink.platform.output.VideoOutput;
import com.autoai.welink.platform.utiliy.AnFileLog;
import com.autoai.welink.platform.utiliy.BooleanQueue;
import com.autoai.welink.platform.utiliy.HeadUnitSpec;
import com.autoai.welink.platform.utiliy.LyricTransfer;
import com.autoai.welink.platform.utiliy.TouchTranslator;
import com.autoai.welink.platform.utiliy.WifiUtil;
import com.autoai.welink.wireless.WLAPConnectListener;
import com.autoai.welink.wireless.WLHardwareHub;
import com.autoai.welink.wireless.WLHardwareHubListener;
import com.autoai.welink.wireless.wifiap.WifiConnectHU;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class WeLinkService extends Service {

    ///////////////// 互联 /////////////////

    private HeadUnitSpec headUnitSpec = null;
    private WLPCMPolicy wlPCMPolicy = null;
    private WLCommandParser wlCommandParser = null;
    private WLHardwareHub wlHardwareHub = null;
    private WifiConnectHU wifiConnectHU = null;
    private WLChannel wlChannel = null;
    private long wlChannelSize = 0;
    private final Object wlChannelSizeLock = new Object();
    private final WLChannelListener wlChannelListener = new WLChannelListener() {
        private TouchTranslator touchTranslator = null;

        @Override
        public Config onConnecting(int huWidth, int huHeight) {
            AnFileLog.e("wlplatform", "onConnecting tid:" + android.os.Process.myTid());
            HUScreenWidth = huWidth;
            HUScreenHeight = huHeight;

            touchTranslator = new TouchTranslator(huWidth, huHeight, huWidth, huHeight, motionEvent -> {
                AnFileLog.e("wlplatform", "onTouch tid:" + android.os.Process.myTid());
                mTouchExecutorService.execute(() -> {
                    AnFileLog.e("wlplatform", "ExecutorService onTouch start");
                    if (mAidlCallback != null) {
                        try {
                            mAidlCallback.onLinkTouch(motionEvent);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                    AnFileLog.e("wlplatform", "ExecutorService onTouch end");
                });
            });

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onConnecting start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onLinkAOAReady();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onConnecting end");
            });

            AnFileLog.e("wlplatform", "MuReady -> huWidth: " + huWidth + ", huHeight: " + huHeight);
            return new Config(huWidth, huHeight, huWidth, huHeight);
        }

        @Override
        public void onConnected(final boolean isAOA, String huType, int huFPS, boolean huSupportRecord, boolean huSupportBtPhone, String huBtMacAddress, String huPlatform, String vehicleVersion) {
            AnFileLog.e("wlplatform", "onConnected tid:" + android.os.Process.myTid());

            vehicleType = huType;
            headUnitSpec = new HeadUnitSpec(getApplicationContext(), vehicleType, huFPS, HUScreenWidth, HUScreenHeight);
            HUScreenWidth = headUnitSpec.getWidth();
            HUScreenHeight = headUnitSpec.getHeight();
            HUScreenDPI = headUnitSpec.getDPI();
            //本地赋值fps
//            HUScreenFPS = headUnitSpec.getFPS();

           /* if (checkScreen) {
                HUScreenWidth = checkScreenWidth;
                HUScreenHeight = checkScreenHeight;
                HUScreenDPI = checkScreenDPI;
                HUScreenFPS = checkScreenFPS;
            }*/
            wlChannel.startRecord();

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onConnected start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onLinkConnected(HUScreenWidth, HUScreenHeight, HUScreenDPI, vehicleType, vehicleVersion, huBtMacAddress, isAOA);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onConnected end");
            });
        }

        @Override
        public void onDisconnected() {
            AnFileLog.e("wlplatform", "onDisconnected tid:" + android.os.Process.myTid());

            wlChannel.stopRecord();

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onDisconnected start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onLinkUnconnected();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onDisconnected end");
            });
        }

        @Override
        public void onStartSendH264() {
            AnFileLog.e("wlplatform", "onStartSendH264 tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onStartSendH264 start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onLinkResume();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onStartSendH264 end");
            });
        }

        @Override
        public void onStopSendH264() {
            AnFileLog.e("wlplatform", "onStopSendH264 tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onStopSendH264 start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onLinkSuspend();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onStopSendH264 end");
            });
        }

        @Override
        public void onMotionEvent(byte[] bytes) {
            touchTranslator.doTouchEvent(new String(bytes));
        }

        @Override
        public void onHardKey(byte[] bytes) {
            touchTranslator.doTouchEvent(new String(bytes));
        }

        @Override
        public void onMessage(final byte[] data) {
            AnFileLog.e("wlplatform", "onMessage tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onMessage start");
                if (mAidlCallback != null) {
                    try {
                        AnFileLog.e("wlplatform", "ExecutorService onMessage :" + data);
                        String command = new String(data);
                        if (wlCommandParser != null && wlCommandParser.receiveCommand(command)) {
                            return;
                        }
                        mAidlCallback.onLinkHUMessageData(command);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onMessage end");
            });
        }

        @Override
        public void onCarData(final byte[] data) {
            AnFileLog.e("wlplatform", "onCarData tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onCarData start");
                if (mAidlCallback != null) {
                    try {
                        AnFileLog.e("wlplatform", "ExecutorService onCarData :" + data);
                        mAidlCallback.onLinkHUCanData(data);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onCarData end");
            });
        }

        @Override
        public void onMicData(final byte[] bytes) {
            mExecutorService.execute(() -> {
                if (serverManager != null) {
                    serverManager.voice(bytes, headUnitSpec.getMicPCMRate(), headUnitSpec.getMicPCMBit(), headUnitSpec.getMicPCMChannel());
                }
            });
        }

        @Override
        public void onAudioPlayStart() {
            AnFileLog.e("wlplatform", "app onSendPCM HU send app Begin:" + mPCMPlaySoundID);
            if (serverManager != null) {
                serverManager.sendSoundBegin(mPCMPlayConnectStr, mPCMPlaySoundID);
            }
            mPCMPlayConnectStr_Last = mPCMPlayConnectStr;
            mPCMPlaySoundID_Last = mPCMPlaySoundID;
        }

        @Override
        public void onAudioPlayEnds() {
            if (serverManager != null && mPcmTTSEnd) {
                AnFileLog.e("wlplatform", "app onSendPCM Hu send app End:" + mPCMPlaySoundID_Last);
                serverManager.sendSoundComplete(mPCMPlayConnectStr_Last, mPCMPlaySoundID_Last);
            }
        }

        @Override
        public void onReadSize(int size) {
            synchronized (wlChannelSizeLock) {
                wlChannelSize += size;
            }
        }

        @Override
        public void onWriteSize(int size) {
            synchronized (wlChannelSizeLock) {
                wlChannelSize += size;
            }
        }
    };

    ///////////////// 三投服务 /////////////////

    private ServerManager serverManager;
    private boolean mInited;
    private IServiceCallback mAidlCallback;
    private ExecutorService mExecutorService;
    private ExecutorService mSoundService;
    private ExecutorService mTouchExecutorService;
    private AidlServerListener mAppListener;
    private String mPCMPlayConnectStr;
    private int mPCMPlaySoundID;
    private String mPCMPlayConnectStr_Last;
    private int mPCMPlaySoundID_Last;
    private boolean mPcmTTSEnd;

    private int HUScreenWidth;
    private int HUScreenHeight;
    private int HUScreenDPI;
    private int HUScreenFPS;
    private boolean checkScreen = false;
    private int checkScreenWidth;
    private int checkScreenHeight;
    private int checkScreenDPI;
    private int checkScreenFPS = 30;
    private int pcm = 200;
    private int pcmCache = 100;
    private String vehicleType;
    private String vehicleID = null;
    private String userID = null;

    private int mFrameCount;
    private int mGetFrameID;
    private int mGetFrameType;
    private int mGetFrameOffset;
    private int mGetFrameSize;
    private MediaFormat mediaFormat;
    private VideoOutput videoSaveLocalFs;

    private String m_TBTConnecter = null;
    private static volatile int currentDriverStatus = 0;

    private byte[] lastSPSFrame = null;

    private byte[] lastPPSFrame = null;
    /**
     * 检测结果队列,结果有变化时才通知更改状态
     */
    private BooleanQueue booleanQueue = new BooleanQueue(2);

    private byte[] curCaptureFrame = null;
    private final Handler handler = new Handler();

    private IReqCurFrameCallback iReqCurFrameCallback;

    private boolean lastResult = false;
    private boolean secondToLastResult = false;

    /**
     * 手机是否录制视屏并保存开关
     */
    private boolean IS_VIDEO_TEST_MODEL = true;


    private RecognitionController.IRecognizeCallBack iRecognizeCallBack = new RecognitionController.IRecognizeCallBack() {
        @Override
        public void onRecognizeResult(boolean state, boolean forceUpdate) {
            booleanQueue.add(state);
            if (iReqCurFrameCallback != null) {
                Log.d("wlplatform", "finishRecordAndRecognize ---> state:" + state + ", lastResult = " + lastResult + ", secondToLastResult = " + secondToLastResult);
                if (!booleanQueue.allValuesSame() || forceUpdate) {
                    new Thread(() -> {
                        try {
                            byte result = (byte) (state ? 1 : 0);
                            iReqCurFrameCallback.onFame(new byte[]{result});
                        } catch (RemoteException e) {
                            throw new RuntimeException(e);
                        }
                    }).start();
                }
            }
        }
    };

    private static class ResultHandler<T> extends Handler {
        T result;

        public ResultHandler(Looper looper) {
            super(looper);
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        AnFileLog.e("wlplatform", "onBind");
        return stub;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        AnFileLog.e("wlplatform", "onUnbind start");
        if (wlHardwareHub != null) {
            wlHardwareHub.destroyHardwareGroup();
            wlHardwareHub = null;
        }
        if(wifiConnectHU != null){
            wifiConnectHU.release();
            wifiConnectHU = null;
        }
        if (serverManager != null) {
            serverManager.stop();
            serverManager.deinit();
            serverManager = null;
        }

        if (wlChannel != null) {
            wlChannel.deinit();
        }

        mInited = false;

        AnFileLog.e("wlplatform", "onUnbind stop");

        stopSelf();
        return super.onUnbind(intent);
    }

    @Override
    public void onCreate() {
        super.onCreate();

        AnFileLog.e("wlplatform", "onCreate");
        AnFileLog.init(getApplicationContext(), "/Test/welinklog/wlservice-log.txt");

        new CrashHandler(e -> {
            if (wlHardwareHub != null) {
                wlHardwareHub.destroyHardwareGroup();
                wlHardwareHub = null;
            }
            if(wifiConnectHU != null){
                wifiConnectHU.release();
                wifiConnectHU = null;
            }
            if (serverManager != null) {
                serverManager.stop();
                serverManager.deinit();
                serverManager = null;
            }

            if (wlChannel != null) {
                wlChannel.deinit();
            }

            mInited = false;

            StringBuilder ex = new StringBuilder("crash \n");
            ex.append("Crash -_- -_- -_- -_- -_- --> \n\n");
            String message = e.getMessage();
            if (message != null) {
                ex.append(message);
                for (StackTraceElement t : e.getStackTrace()) {
                    ex.append(t);
                    ex.append("\n");
                }
            }
            ex.append("\n <-- -_- -_- -_- -_- -_- Crash");
            AnFileLog.e("wlplatform", ex.toString());
            e.printStackTrace();
        });

        AnFileLog.e("wlplatform", "onCreate :"
                + "  pid:" + android.os.Process.myPid()
                + "  Tid:" + android.os.Process.myTid());
        mExecutorService = Executors.newSingleThreadExecutor();
        mSoundService = Executors.newSingleThreadExecutor();
        mTouchExecutorService = Executors.newSingleThreadExecutor();
    }

    private final IWeLinkService.Stub stub = new IWeLinkService.Stub() {

        @Override
        public String getVersion() {
            AnFileLog.e("wlplatform", "getVersion"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            return BuildConfig.VERSION_NAME + ":" + BuildConfig.VERSION_CODE;
        }

        @Override
        public void init(final IServiceCallback cb, final IBinder binder) {
            AnFileLog.e("wlplatform", "init"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "initHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (!mInited) {
                                    AnFileLog.e("wlplatform", "wlChannel.init");
                                    mAidlCallback = cb;
                                    wlChannel = WLChannel.getInstance(getApplicationContext());
                                    wlChannel.init(wlChannelListener);
                                    mInited = true;

                                    try {
                                        binder.linkToDeath(new DeathRecipient() {
                                            @Override
                                            public void binderDied() {
                                                AnFileLog.e("wlplatform", "WeLinkPlatform is died!");
                                                if (wlHardwareHub != null) {
                                                    wlHardwareHub.destroyHardwareGroup();
                                                    wlHardwareHub = null;
                                                }
                                                if(wifiConnectHU != null){
                                                    wifiConnectHU.release();
                                                    wifiConnectHU = null;
                                                }
                                                stopUDPBroadcast();
                                                deinit();

                                                binder.unlinkToDeath(this, 0);
                                            }
                                        }, 0);
                                    } catch (RemoteException e) {
                                        AnFileLog.e("wlplatform", "linkToDeath failed");
                                    }
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void deinit() {
            AnFileLog.e("wlplatform", "deinit"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "deinitHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.stop();
                                    serverManager.deinit();
                                    serverManager = null;
                                }

                                if (wlChannel != null) {
                                    AnFileLog.e("wlplatform", "wlChannel.deinit");
                                    wlChannel.deinit();
                                }

                                mInited = false;

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void initHU(final Rect connectorRect, final String classNameForPCMPolicy, final String classNameForCommandPolicy) {
            AnFileLog.e("wlplatform", "initSM :"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "initSMHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.stop();
                                    serverManager.deinit();
                                }

                                AnFileLog.e("wlplatform", "Configuration" +
                                        " width:" + HUScreenWidth +
                                        " height:" + HUScreenHeight +
                                        " fps:" + HUScreenFPS +
                                        " dpi:" + HUScreenDPI +
                                        " pcm:" + pcm +
                                        " pcmCache:" + pcmCache);

                                wlPCMPolicy = null;
                                if (classNameForPCMPolicy != null) {
                                    try {
                                        Class clasz = Class.forName(classNameForPCMPolicy);
                                        wlPCMPolicy = (WLPCMPolicy) clasz.newInstance();

                                        // 重新启动下对应的日志开关
                                        wlPCMPolicy.enableLogCat(AnFileLog.isEnableLogCat());
                                        wlPCMPolicy.enableLogFile(AnFileLog.isEnableLogFile());

                                    } catch (Exception e) {
                                        wlPCMPolicy = null;
                                    }
                                }

                                wlCommandParser = null;
                                if (classNameForCommandPolicy != null) {
                                    try {
                                        Class clasz = Class.forName(classNameForCommandPolicy);
                                        wlCommandParser = (WLCommandParser) clasz.newInstance();
                                    } catch (Exception e) {
                                        wlCommandParser = null;
                                    }
                                }

                                WLConfiguration wlConfiguration;
                                if (connectorRect != null) {
                                    wlConfiguration = new WLConfiguration(connectorRect.width(), connectorRect.height(), connectorRect, HUScreenDPI, HUScreenFPS, pcm, pcmCache, vehicleType, vehicleID, userID);
                                } else {
                                    wlConfiguration = new WLConfiguration(HUScreenWidth, HUScreenHeight, connectorRect, HUScreenDPI, HUScreenFPS, pcm, pcmCache, vehicleType, vehicleID, userID);
                                }
                                mAppListener = new AidlServerListener();
                                serverManager = new ServerManager(getApplicationContext(), wlConfiguration, mAppListener);

                                //初始化
                                RecognitionController.getInstance().init(WeLinkService.this, iRecognizeCallBack);
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void deinitHU() {
            AnFileLog.e("wlplatform", "deinitSM"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "deinitSMHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlPCMPolicy != null) {
                                    wlPCMPolicy.release();
                                    wlPCMPolicy = null;
                                }

                                if (wlCommandParser != null) {
                                    wlCommandParser = null;
                                }

                                if (serverManager != null) {
                                    serverManager.stop();
                                    serverManager.deinit();
                                    serverManager = null;
                                }

                                if(IS_VIDEO_TEST_MODEL) {
                                    try {
                                        AnFileLog.e("wlplatform", "deinitSMHandle toggleLocalVideo");
                                        toggleLocalVideo(false, null);
                                    } catch (Exception e) {
                                        AnFileLog.e("wlplatform", "deinitSMHandle e =" + e);
                                    }
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void linkAOA(final Intent intent) {
            AnFileLog.e("wlplatform", "linkAOA"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "linkAOAHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());
                                if (wlChannel != null) {
                                    wlChannel.connect(intent);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void unlink() {
            AnFileLog.e("wlplatform", "unlink"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "unlinkHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlChannel != null) {
                                    wlChannel.reset();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public String assign(final String packageName, final int cap) {
            AnFileLog.e("wlplatform", "assign"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<String> handler = new ResultHandler<String>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "assignHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.assign(packageName, cap);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result;
            }

            return null;
        }

        @Override
        public String find(final String packageName, final int cap) {
            AnFileLog.e("wlplatform", "find"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<String> handler = new ResultHandler<String>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "findHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.find(packageName, cap);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result;
            }

            return null;
        }

        @Override
        public void revoke(final String connectStr) {
            AnFileLog.e("wlplatform", "revoke"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "revokeHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.revoke(connectStr);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public Surface showOverlay() {
            AnFileLog.e("wlplatform", "getOverlay"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<Surface> handler = new ResultHandler<Surface>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "getOverlayHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.showOverlay();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result;
            }

            return null;
        }

        @Override
        public int getDensity() {
            AnFileLog.e("wlplatform", "getDensity"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<Integer> handler = new ResultHandler<Integer>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "getOverlayDensityDpiHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.getDensity();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result.intValue();
            }

            return 0;
        }

        @Override
        public int getWidth() {
            AnFileLog.e("wlplatform", "getWidth"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<Integer> handler = new ResultHandler<Integer>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "getOverlayWidthHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.getWidth();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result.intValue();
            }

            return 0;
        }

        @Override
        public int getHeight() {
            AnFileLog.e("wlplatform", "getHeight"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<Integer> handler = new ResultHandler<Integer>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "getOverlayHeightHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.getHeight();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result.intValue();
            }

            return 0;
        }

        @Override
        public void hideOverlay() {
            AnFileLog.e("wlplatform", "clearOverlay"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "clearOverlayHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.hideOverlay();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void start() {
            AnFileLog.e("wlplatform", "start"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            mGetFrameID = -1;

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "startHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.start();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void startAndGetFrameData(int frame, int type, int offset, int size) {
            AnFileLog.e("wlplatform", "startAndGetFrameData"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            mFrameCount = 0;
            mGetFrameID = frame;
            mGetFrameType = type;
            mGetFrameOffset = offset;
            mGetFrameSize = size;

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "startHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.start();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void stop() {
            AnFileLog.e("wlplatform", "stop"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "stopHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.stop();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void toggleLocalVideo(boolean start, String localPath) throws RemoteException {
            try {
                if (start) {
                    if (videoSaveLocalFs != null) {
                        videoSaveLocalFs.close();
                        videoSaveLocalFs = null;
                    }
                    File parentFile = new File(localPath).getParentFile();
                    if (!parentFile.exists()) {
                        parentFile.mkdirs();
                    }
//                    videoSaveLocalFs = new FileOutputStreamOutput(localPath);
                    MediaMuxerOutput mediaMuxerOutput = new MediaMuxerOutput(localPath);
                    if (mediaFormat != null) {
                        mediaMuxerOutput.addTrack(mediaFormat);
                    }
                    videoSaveLocalFs = mediaMuxerOutput;
                } else {
                    if (videoSaveLocalFs != null) {
                        videoSaveLocalFs.close();
                        videoSaveLocalFs = null;
                    }
                }
            } catch (Exception e) {
                AnFileLog.e("wlplatform", "toggleLocalVideo error e = " + e);
            }
        }

        @Override
        public void command(final String connectStr, final String command) {
            AnFileLog.e("wlplatform", "command"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "commandHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.command(connectStr, command);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void mirror(final String connectStr, final int x, final int y) {
            AnFileLog.e("wlplatform", "app mirror:" + connectStr + "//////"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "mirrorHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.mirror(connectStr, x, y);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public boolean touch(final MotionEvent motionEvent) {
            AnFileLog.e("wlplatform", "touch"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<Boolean> handler = new ResultHandler<Boolean>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "touchHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.touch(motionEvent);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result.booleanValue();
            }

            return false;
        }


        @Override
        public Surface enableExternal(final int left, final int top, final int right, final int bottom, final boolean forceFullScreen) {

            AnFileLog.e("wlplatform", "enableExternal"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<Surface> handler = new ResultHandler<Surface>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "enableExternalHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                //--->计算出宽高
                                int width = alignToEven(right - left);
                                int height = alignToEven(bottom - top);
                                AnFileLog.e("WeLinkService", "enableExternalHandle width:" + width + " height:" + height);
                                Log.i("shecw1","WeLinkService::enableExternal code mark  = 11111");
                                //---> 判断一下当前编码器是否在工作，如果在工作，需要reset
                                if (ExternalRecordController.getInstance().isCodecIsRunning()) {
                                    ExternalRecordController.getInstance().stopMediaCodec();
                                }
                                Log.i("shecw1","WeLinkService::enableExternal code mark  = 22222");

                                //---> 用新的分辨率更新参数，重置编码器
                                ExternalRecordController.getInstance().configMediaFormat(width, height, HUScreenFPS);
                                result = ExternalRecordController.getInstance().createInputSurface();
                                Log.i("shecw1","WeLinkService::enableExternal code mark  = 33333");

                                //---> 更新编码回调
                                ExternalRecordController.getInstance().setCodecListener(new WLCodecListener() {
                                    @Override
                                    public void onCallBack(int type, byte[] bytes) {
                                        if (mAppListener != null) {
                                            mAppListener.onVideoFrame(bytes);
                                        }
                                    }

                                    @Override
                                    public void onVideoFormatChange(MediaFormat mediaFormat) {
                                        Log.e("WeLinkService", "setScreenSurface codecListener onVideoFormatChang");
                                        if (mAppListener != null) {
                                            mAppListener.onVideoFormatChange(mediaFormat);
                                        }
                                    }

                                    @Override
                                    public void onVideoTime(long time) {
//                                            Log.e("ScreenControl", "setScreenSurface codecListener onVideoTime time=" + time);
                                        if (mAppListener != null) {
                                            mAppListener.onVideoTime(time);
                                        }
                                    }

                                    @Override
                                    public void onCodecStatusCallBack(int type, Object object) {
                                        Log.e("WeLinkService", "setScreenSurface codecListener onCodecStatusCallBack type=" + type);
                                    }

                                    @Override
                                    public void onScreenNoUpdate() {
                                        Log.e("WeLinkService", "setScreenSurface codecListener onScreenNoUpdate");
                                    }
                                });
                                ExternalRecordController.getInstance().startMediaCodec();
                                Log.i("shecw1","WeLinkService::enableExternal code mark  = 44444");

                                if(IS_VIDEO_TEST_MODEL) {
                                    try {
                                        Date currentDate = new Date();
                                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss", Locale.getDefault());
                                        String formattedDate = sdf.format(currentDate);
                                        toggleLocalVideo(true, getExternalFilesDir(null) + "/" + formattedDate + ".mp4");
                                        AnFileLog.e("WeLinkService", "toggleLocalVideo path = " + getExternalFilesDir(null) + "/" + formattedDate + ".mp4");
                                    } catch (RemoteException e) {
                                        AnFileLog.e("WeLinkService", "toggleLocalVideo e = " + e);
                                    }
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            Log.i("shecw1","WeLinkService::enableExternal code mark  = 55555");


            if (handler != null && handler.result != null) {
                //将编码器的input surface 给到应用层
                return handler.result;
            }

            return null;
        }


        @Override
        public void updateStates(int width, int height, int angle){
            RecognitionController.getInstance().updateStates(width, height, angle);
        }

        /**
         * 编码器要求偶对齐
         *
         * @param number
         * @return
         */
        private int alignToEven(int number) {
            // 如果number是偶数，直接返回；如果number是奇数，则对number执行加1操作
            return (number & 1) == 1 ? number + 1 : number;
        }

        @Override
        public void disableExternal() {
            AnFileLog.e("wlplatform", "disableExternal"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "disableExternalHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                               /* if (serverManager != null) {
                                    serverManager.disableExternal();
                                }*/
                                //---> 判断一下当前编码器是否在工作，如果在工作，需要reset
                                if (ExternalRecordController.getInstance().isCodecIsRunning()) {
                                    ExternalRecordController.getInstance().stopMediaCodec();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        @Override
        public void reqKeyFrame(int frame ,int time){
            AnFileLog.e("wlplatform", "reqKeyFrame"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "reqKeyFrame"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());


                                //---> 判断一下当前编码器是否在工作，如果在工作，需要 请求关键帧
                                if (ExternalRecordController.getInstance().isCodecIsRunning()) {
                                    ExternalRecordController.getInstance().reqKeyFrame(frame,time);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        @Override
        public void voice(final String connectStr) {
            AnFileLog.e("wlplatform", "voice"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "voiceHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.setVoiceCStr(connectStr);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public String getActivedMusic() {
            AnFileLog.e("wlplatform", "getActivedMusic"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            ResultHandler<String> handler = new ResultHandler<String>(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "getActivedMusicHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    result = serverManager.getActivedMusic();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (handler != null && handler.result != null) {
                return handler.result;
            }

            return null;
        }

        @Override
        public void activateMusic(final String connectStr) {
            AnFileLog.e("wlplatform", "activateMusic"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "activateMusicHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (serverManager != null) {
                                    serverManager.activateMusic(connectStr);
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void sendCanDataToHU(final byte[] data) {
            AnFileLog.e("wlplatform", "sendCanDataToHU"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "sendCanDataToHUHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlChannel != null) {
                                    wlChannel.sendCarData(data);
                                }
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void sendMessageDataToHU(final String data) {
            AnFileLog.e("wlplatform", "sendMessageDataToHU"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "sendMessageDataToHUHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlChannel != null) {
                                    wlChannel.sendMessage(data.getBytes());
                                }
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }
                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        private Thread mUDPBroadcastThread = null;

        @Override
        public void startUDPBroadcast(final String type) {
            AnFileLog.e("wlplatform", "startUDPBroadcast"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "startUDPBroadcastHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (mUDPBroadcastThread != null) {
                                    lock.notify();
                                    return;
                                }

                                final int wifiUdpPort = 6840;
                                final long wifiUdpFrequency = 1000;
                                mUDPBroadcastThread = new Thread() {
                                    @Override
                                    public void run() {
                                        byte[] buffer;
                                        String macFromHardware = WifiUtil.getMacFromHardware();
                                        if (type == null) {
                                            buffer = ("WLServer_Android_1_" + macFromHardware + "_" + WifiUtil.getPhoneModel()).getBytes();
                                        } else {
                                            buffer = ("WLServer_" + type + "_Android_1_" + macFromHardware + "_" + WifiUtil.getLocalInetAddress().toString() + "_" + WifiUtil.getPhoneModel()).getBytes();
                                        }
//                                        if (type == null) {
//                                            buffer = (new String("WLServer_iOS_1_" + "00:00:00:00:00:00" + "_" + WifiUtil.getPhoneModel())).getBytes();
//                                        } else {
//                                            buffer = (new String("WLServer_" + type + "_iOS_1_" + WifiUtil.getMacFromHardware() + "_" + WifiUtil.getLocalInetAddress().toString() + "_" + WifiUtil.getPhoneModel())).getBytes();
//                                        }

//                                        String hostName = null;
//                                        InetAddress address = WifiUtil.getLocalInetAddress();
                                        List<InetAddress> addressList = WifiUtil.getLocalInetAddressList();
                                        if (addressList.isEmpty()) {
                                            AnFileLog.e("WelinkPlatform", "address is null send udp-broadcast failed");
                                            return;
                                        }
                                        List<DatagramPacket> datagramPacketList = new ArrayList<>();
                                        for (InetAddress address : addressList) {
                                            if (address != null) {
                                                String ip = address.toString();
                                                String[] str = ip.split("\\.");
                                                String tempHostName = str[0] + "." + str[1] + "." + str[2] + ".255";
                                                if (tempHostName.contains("/")) {
                                                    String hostName = tempHostName.replace("/", "");
                                                    datagramPacketList.add(new DatagramPacket(buffer, buffer.length, new InetSocketAddress(hostName, wifiUdpPort)));
                                                }
                                            }
                                        }
                                        if (datagramPacketList.isEmpty()) {
                                            AnFileLog.e("WelinkPlatform", "send udp-broadcast failed");
                                            return;
                                        }

//                                        DatagramPacket dp = new DatagramPacket(buffer, buffer.length, new InetSocketAddress(hostName, wifiUdpPort));
                                        DatagramSocket ds;
                                        try {
                                            ds = new DatagramSocket();
//                                            if ("FF:FF:FF:FF:FF:FF".equals(WifiUtil.getMacFromHardware())) {
//                                                ds.close();
//                                                AnFileLog.e("WelinkPlatform", "send udp-broadcast failed");
//                                                return;
//                                            }
                                        } catch (Exception e) {
                                            AnFileLog.e("WelinkPlatform", "send udp-broadcast failed");
                                            return;
                                        }

                                        while (!isInterrupted()) {

                                            try {
                                                for (DatagramPacket dp : datagramPacketList) {
                                                    ds.send(dp);
                                                    AnFileLog.e("WelinkPlatform", "send udp-broadcast: " + new String(dp.getData()) + "--" + macFromHardware + "--SocketAddress--" + dp.getSocketAddress() + "--Port--" + dp.getPort() + "----" + dp.getAddress());
                                                    Thread.sleep(100);
                                                }
                                                Thread.sleep(wifiUdpFrequency);
                                            } catch (Exception e) {
                                                ds.close();
                                                return;
                                            }
                                        }
                                        ds.close();
                                    }
                                };
                                mUDPBroadcastThread.start();

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void stopUDPBroadcast() {
            AnFileLog.e("wlplatform", "stopUDPBroadcast"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "stopUDPBroadcastHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (mUDPBroadcastThread != null) {
                                    mUDPBroadcastThread.interrupt();
                                    mUDPBroadcastThread = null;
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void enableLogCat(final boolean enable,int logLevel) {
            AnFileLog.e("wlplatform", "enableLogCat"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "enableLogCatHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());
                                if (wlChannel != null) {
                                    wlChannel.enableLogCat(enable,logLevel);
                                }

                                try {
                                    Class.forName("com.autoai.welink.wireless.WLHardwareHub");
                                    WLHardwareHub.enableLogCat(enable);
                                } catch (ClassNotFoundException e) {
                                }

                                try {
                                    Class.forName("com.autoai.welink.autoproxy.WLServer");
                                    WLServer.enableLogCat(enable);
                                } catch (ClassNotFoundException e) {
                                }

                                if (wlPCMPolicy != null) {
                                    wlPCMPolicy.enableLogCat(enable);
                                }

                                AnFileLog.enableLogCat(enable);
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void enableLogFile(final boolean enable,int logLevel) {
            AnFileLog.e("wlplatform", "enableLogFile" + "logLevel = " + logLevel
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "enableLogFileHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());
                                if (wlChannel != null) {
                                    wlChannel.enableLogFile(enable,logLevel);
                                }

                                try {
                                    WLHardwareHub.enableLogInit(getApplicationContext());
                                    Class.forName("com.autoai.welink.wireless.WLHardwareHub");
                                    WLHardwareHub.enableLogFile(enable);
                                } catch (ClassNotFoundException e) {
                                }

                                try {
                                    Class.forName("com.autoai.welink.autoproxy.WLServer");
                                    WLServer.enableLogFile(enable);
                                } catch (ClassNotFoundException e) {
                                }

                                if (wlPCMPolicy != null) {
                                    wlPCMPolicy.enableLogFile(enable);
                                }

                                AnFileLog.enableLogFile(enable);
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void createHardwareGroup(final String networkName, final String passphrase, final int frequency, final int rssi, final int timeout, final List<ScanFilter> BLEDeviceFilterList) {
            AnFileLog.e("wlplatform", "createHardwareGroup"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "createHardwareGroupHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());
                                if (wlHardwareHub != null) {
                                    if (mAidlCallback != null) {
                                        try {
                                            mAidlCallback.onHardwareGroupError(7);
                                        } catch (RemoteException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    return;
                                }

                                wlHardwareHub = WLHardwareHub.createHardwareGroup(getApplicationContext(), networkName, passphrase, frequency, rssi, timeout, BLEDeviceFilterList, new WLHardwareHubListener() {
                                    @Override
                                    public void onStatusChanged(final int status, final String content) {
                                        AnFileLog.e("wlplatform", "createHardwareGroup onStatusChanged status: " + status + ", content: " + content);
                                        mExecutorService.execute(() -> {
                                            AnFileLog.e("wlplatform", "ExecutorService onStatusChanged start");
                                            if (mAidlCallback != null) {
                                                try {
                                                    mAidlCallback.onHardwareGroupStatusChanged(status, content);
                                                } catch (RemoteException e) {
                                                    e.printStackTrace();
                                                }
                                            }
                                            AnFileLog.e("wlplatform", "ExecutorService onStatusChanged end");
                                        });
                                    }

                                    @Override
                                    public void onHeartbeat(String packageName, String ip, int port, String device) {
                                        AnFileLog.e("wlplatform", "createHardwareGroup onHeartbeat packageName=" + packageName + ",ip=" + ip + ",port=" + port + ",device=" + device);
                                        String connectStr = find(packageName, WL_CAP_HARDWARE);
                                        final String heartbeat = "{\"Ver\": \"1.0\", \"Type\": \"All\", \"Cmd\": \"Hardware\", \"Param\": {\"IP\": \"" + ip + "\", \"Port\": \"" + port + "\", \"Device\": \"" + device + "\"}}";
                                        if (connectStr != null) {
                                            command(connectStr, heartbeat);
                                        } else {
                                            mExecutorService.execute(() -> {
                                                AnFileLog.e("wlplatform", "ExecutorService onStatusChanged start");
                                                if (mAidlCallback != null) {
                                                    try {
                                                        mAidlCallback.onHardwareGroupStatusChanged(10, heartbeat);
                                                    } catch (RemoteException e) {
                                                        e.printStackTrace();
                                                    }
                                                }
                                                AnFileLog.e("wlplatform", "ExecutorService onStatusChanged end");
                                            });
                                        }
                                    }

                                    @Override
                                    public void onError(final int reason) {
                                        AnFileLog.e("wlplatform", "createHardwareGroup onError " + reason);
                                        mExecutorService.execute(() -> {
                                            AnFileLog.e("wlplatform", "ExecutorService onError start");
                                            if (mAidlCallback != null) {
                                                try {
                                                    mAidlCallback.onHardwareGroupError(reason);
                                                } catch (RemoteException e) {
                                                    e.printStackTrace();
                                                }
                                            }
                                            AnFileLog.e("wlplatform", "ExecutorService onError end");
                                        });
                                    }
                                });
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void refreshHardwareGroup() {
            AnFileLog.e("wlplatform", "refreshHardwareGroup"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "refreshHardwareGroupHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlHardwareHub != null) {
                                    wlHardwareHub.refreshGroupInfo();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void destroyHardwareGroup() {
            AnFileLog.e("wlplatform", "destroyHardwareGroup"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "destroyHardwareGroupHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlHardwareHub != null) {
                                    wlHardwareHub.destroyHardwareGroup();
                                    wlHardwareHub = null;
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void hideGroupInfo() {
            AnFileLog.e("wlplatform", "refreshHardwareGroup"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "hideGroupInfoHandler"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlHardwareHub != null) {
                                    //停止蓝牙扫描车机设备。停止wifi-direct 广播
                                    wlHardwareHub.hideGroupInfo();
                                }
                                if(wifiConnectHU != null){
                                    //停止蓝牙扫描车机设备，获取热点信息
                                    wifiConnectHU.stopBle();
                                }
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void showGroupInfo() {
            AnFileLog.e("wlplatform", "refreshHardwareGroup"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "showGroupInfoHandle"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wlHardwareHub != null) {
                                    wlHardwareHub.showGroupInfo();
                                }

                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        @Override
        public void startAPConnect(final String ssid,final String password,final int rssi, final int timeout, final List<ScanFilter> BLEDeviceFilterList) {
            AnFileLog.e("wlplatform", "startAPConnect"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "startAPConnect"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                                if (wifiConnectHU == null) {

                                    wifiConnectHU = WifiConnectHU.init(getApplicationContext(),ssid,password, new WLAPConnectListener() {
                                        @Override
                                        public void onConnectionSuccess(String networkName, String password) {
                                            //Ap热点连接成功  启动udp发送  ,如果已开始发送，先关闭再发送
                                            stopUDPBroadcast();
                                            startUDPBroadcast(null);
                                            if(mAidlCallback != null){
                                                try {
                                                    mAidlCallback.onWiFiApConfig(networkName,password);
                                                } catch (RemoteException e) {
                                                    e.printStackTrace();
                                                }
                                            }
                                        }

                                    }, rssi, timeout, BLEDeviceFilterList);
                                }else{
                                    wifiConnectHU.startBle();
                                }
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void stopAPConnect() {
            AnFileLog.e("wlplatform", "stopAPConnect"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            final Object lock = new Object();
            Handler handler = new Handler(Looper.getMainLooper()) {
                @Override
                public void handleMessage(Message msg) {
                    switch (msg.what) {
                        case 0:
                            synchronized (lock) {
                                AnFileLog.e("wlplatform", "stopAPConnect"
                                        + " pid:" + android.os.Process.myPid()
                                        + " tid:" + android.os.Process.myTid());

                              if(wifiConnectHU != null){
                                  wifiConnectHU.release();
                                  wifiConnectHU = null;
                              }
                                lock.notify();
                            }
                            break;
                        default:
                            break;
                    }

                }
            };

            try {
                synchronized (lock) {
                    Message msg = handler.obtainMessage(0);
                    msg.sendToTarget();
                    lock.wait();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @Override
        public long getThroughput() {
            long temp;
            synchronized (wlChannelSizeLock) {
                temp = wlChannelSize;
                wlChannelSize = 0;
            }
            return temp;
        }

        @Override
        public void checkDisplay(int width, int height, int dpi, int fps) {
            checkScreen = true;
            checkScreenWidth = width;
            checkScreenHeight = height;
            checkScreenDPI = dpi;
            checkScreenFPS = fps;
        }
        @Override
        public void changeFps(int fps){
            HUScreenFPS = fps;
        }
        @Override
        public int reqCurFrame(int carDriverStatus, IReqCurFrameCallback reqCurFrameCallback) {
            iReqCurFrameCallback = reqCurFrameCallback;
            // 1：正在行车，2：P档
            currentDriverStatus = carDriverStatus;
            Log.d("TAG","finishRecordAndRecognize ---> path: currentDriverStatus = " + currentDriverStatus);
            RecognitionController.getInstance().setCurrentDriverStatus(carDriverStatus);
            return 0;
        }

        @Override
        public int checkSwitch(boolean driveSafetySaveVideo, boolean driveSafetyOffline, boolean driveSafetyVideoPlay) {
            Log.d("TAG","checkSwitch ---> path: driveSafetySaveVideo = " + driveSafetySaveVideo +  " driveSafetyOffline = " + driveSafetyOffline +  " driveSafetyVideoPlay = " + driveSafetyVideoPlay);
            //借用这个字段，作为是否录视频开关
            IS_VIDEO_TEST_MODEL = driveSafetySaveVideo;
            RecognitionController.getInstance().setDriveSafetyOfflineSwitch(driveSafetyOffline);
            RecognitionController.getInstance().setDriveSafetyVideoPlay(driveSafetyVideoPlay);
            RecognitionController.getInstance().startOrStopVideoPlaybackChecker(driveSafetyVideoPlay);

            return 0;
        }
    };


    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        AnFileLog.e("wlplatform", "onDestroy start");
        if (wlHardwareHub != null) {
            wlHardwareHub.destroyHardwareGroup();
            wlHardwareHub = null;
        }
        if(wifiConnectHU != null){
            wifiConnectHU.release();
            wifiConnectHU = null;
        }
        if (serverManager != null) {
            serverManager.stop();
            serverManager.deinit();
            serverManager = null;
        }

        if (wlChannel != null) {
            wlChannel.deinit();
        }

        if (videoSaveLocalFs != null) {
            videoSaveLocalFs.close();
            videoSaveLocalFs = null;
        }

        mInited = false;
        handler.removeCallbacksAndMessages(null);

        AnFileLog.e("wlplatform", "onDestroy stop");

        super.onDestroy();
        android.os.Process.killProcess(android.os.Process.myPid());
    }

    class AidlServerListener implements AppConnectListener, WLPCMPolicy.Callback, WLCommandParser.Callback {

        public Object[] playingSoundData = null;
        public String m_SoundConnecter = null;
        static final int WAIT_TIME = 150;

        CheckThread checkThread;

        Boolean bMusicTransportEnd = false;

        int music_order = 0;
        long music_curtime = 0;
        long music_last_update = 0;
        int music_duration = 0;
        String music_source = null;
        String music_artist = null;
        String music_title = null;
        String music_album = null;
        JSONArray music_lyric = null;
        Bitmap music_cover = null;
        boolean mMusicRefreshID3 = false;
        LyricTransfer lyricTransfer = new LyricTransfer();

        /**
         * App连接成功
         *
         * @param connectStr 连接字符串
         */
        @Override
        public void onConnected(final String connectStr) {
            AnFileLog.e("wlplatform", "app onConnected"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            if (wlCommandParser != null) {
                wlCommandParser.register(connectStr, this);
            }
            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onConnected start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onAppConnected(connectStr);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onConnected end");
            });
        }

        /**
         * App切换至前台
         *
         * @param connectStr 连接字符串
         */
        @Override
        public void onForeground(final String connectStr) {
            AnFileLog.e("wlplatform", "app onForground:" + connectStr + "////"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onForeground start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onAppForeground(connectStr);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onForeground end");
            });
        }

        /**
         * App切换至后台
         *
         * @param connectStr 连接字符串
         */
        @Override
        public void onBackground(final String connectStr) {
            AnFileLog.e("wlplatform", "app onBackground:" + connectStr + "////"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onBackground start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onAppBackground(connectStr);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onBackground end");
            });
        }

        /**
         * App断开连接
         *
         * @param connectStr 连接字符串
         */
        @Override
        public void onDisconnected(final String connectStr) {
            AnFileLog.e("wlplatform", "app onDisconnected"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onDisconnected start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onAppDisconnected(connectStr);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onDisconnected end");
            });
        }

        /**
         * App返回连接失败
         *
         * @param connectStr 连接字符串
         * @param errorCode  错误码
         *                   1. WeLink服务无效，有可能是WeLink App没有运行或者没有连接车机
         *                   2. 连接字符串不正确
         *                   3. WLConnector版本太老，无法连接当前的WeLink服务
         *                   4. 重复连接
         *                   5. 连接WeLink服务失败
         *                   6. App不支持该分辨率
         *                   7. 无法申请android.permission.CHANGE_NETWORK_STATE权限
         */
        @Override
        public void onError(final String connectStr, final int errorCode) {
            AnFileLog.e("wlplatform", "app onError"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onError start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onAppError(connectStr, errorCode);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onError end");
            });
        }

        /**
         * 活动触发通知
         *
         * @param connectStr 发送者的连接字符串
         * @param action     action id -- 参照文档《ActionID》
         */
        @Override
        public void onAction(final String connectStr, final int action) {
            AnFileLog.e("wlplatform", "app onAction"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onAction start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onAppAction(connectStr, action);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onAction end");
            });
        }

        @Override
        public void onVideoFormatChange(MediaFormat format) {
            AnFileLog.e("wlplatform", "onVideoFormatChange id: format = " + format.toString());
            mediaFormat = format;
            if (videoSaveLocalFs != null && videoSaveLocalFs instanceof MediaMuxerOutput) {
                try {
                    ((MediaMuxerOutput) videoSaveLocalFs).addTrack(format);
                } catch (Exception e) {
                    //throw new RuntimeException(e);
                    Log.e("wlplatform", "onVideoFormatChange e = " + e);
                }
            }
            //---> 用于生产MP4文件
            //RecognitionController.getInstance().setTrack(mediaFormat);
        }

        @Override
        public void onVideoTime(long time) {
            if (videoSaveLocalFs instanceof MediaMuxerOutput) {
                ((MediaMuxerOutput) videoSaveLocalFs).updateTime(time);
            }
        }

        /**
         * 接收到视频帧数据
         *
         * @param frame 帧数据
         */
        @Override
        public void onVideoFrame(byte[] frame) {
            AnFileLog.e("wlplatform", "onVideoFrame id: " + mGetFrameID + ", type: " + mGetFrameType + ", offset: " + mGetFrameOffset + ", size: " + mGetFrameSize);
//            if (getFrameType(frame) == 1 && frame.length < 48) { //使用P帧加密没考虑小于48字节的情况，因此尝试抛掉小于48字节的P帧，看显示是否会出问题
//                AnFileLog.e("wlplatform", "onFrameData frame-size < 48:" + frame.length);
//                return;
//            }
            if (videoSaveLocalFs != null) {
                videoSaveLocalFs.write(frame);
            }

            if (getFrameType(frame) == 7) {
                lastSPSFrame = new byte[frame.length];
                System.arraycopy(frame, 0, lastSPSFrame, 0, frame.length);
                RecognitionController.getInstance().writeFrame(frame);
            }

            if (getFrameType(frame) == 8) {
                lastPPSFrame = new byte[frame.length];
                System.arraycopy(frame, 0, lastPPSFrame, 0, frame.length);
                RecognitionController.getInstance().writeFrame(frame);
            }

            Log.i("wlplatform", " currentDriverStatus = " + currentDriverStatus);
            // 当前没有正在进行的任务，且挡位是行车档，就要开始识别
            if (currentDriverStatus == 1) {
                RecognitionController.getInstance().writeFrame(frame);
            }


            int frameType = getFrameType(frame);
            AnFileLog.e("she", "onVideoFrame::frameType = " + frameType + "; frame size = " + frame.length);

//            if (getFrameType(frame) == 5 && needCaptureLastFrameFlag && countDownLatch != null) {
//                curCaptureFrame = new byte[frame.length];
//                System.arraycopy(frame, mGetFrameOffset, curCaptureFrame, 0, frame.length);
//                AnFileLog.e("she", "onVideoFrame::countDown ！！！！ ");
//                countDownLatch.countDown();
//            }

            if (getFrameType(frame) == 5) {
                curCaptureFrame = new byte[frame.length];
                System.arraycopy(frame, mGetFrameOffset, curCaptureFrame, 0, frame.length);
            }

            if (mGetFrameID >= 0 && (mGetFrameType == 0 || mGetFrameType == getFrameType(frame)) && mGetFrameID == mFrameCount++) {
                AnFileLog.e("wlplatform", "onFrameData frame-size: " + frame.length);
                mGetFrameID = -1;

                if (frame.length < mGetFrameSize) {
                    AnFileLog.e("wlplatform", "onFrameData frame < 48:" + frame.length + ", get:" + mGetFrameSize);
                    mGetFrameSize = frame.length;
                }

                final byte[] data = new byte[mGetFrameSize];
                System.arraycopy(frame, mGetFrameOffset, data, 0, mGetFrameSize);

                mExecutorService.execute(() -> {
                    AnFileLog.e("wlplatform", "ExecutorService onVideoFrame start");
                    if (mAidlCallback != null) {
                        try {
                            mAidlCallback.onFrameData(data);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                    AnFileLog.e("wlplatform", "ExecutorService onVideoFrame end");
                });
            }

            if (wlChannel != null) {
                AnFileLog.e("auto-fps", "send fps-frame !!!!!!!!");
                wlChannel.sendH264(frame);
            }
        }

        //return SPS: 7, PPS: 8, IDR: 5, P: 1，-1为无效帧
        private int getFrameType(byte[] data) {
            if (data == null || data.length < 5) {
                return -1;
            }

            if (data[0] == 0x0 && data[1] == 0x0 && data[2] == 0x0 && data[3] == 0x1) {
                return (data[4] & 0x1F);
            } else {
                return -1;
            }
        }

        /***********************policy******************************/
        @Override
        public void onSendPCM(byte[] pcm) {
            if (wlChannel != null) {
                wlChannel.sendPCM(pcm);
                AnFileLog.e("wlplatform", "app onPCM send HU PCM");
            }
        }

        @Override
        public void onPlayCommand(byte[] command) {
            if (wlChannel != null) {
                wlChannel.sendMessage(command);
                AnFileLog.e("wlplatform", "app onPCM send HU command");
            }
        }

        /***********************music******************************/
        /**
         * 播放器注册，播放器注册才可能成为激活音乐App
         *
         * @param connectStr 连接字符串
         */
        @Override
        public void onMusicRegister(final String connectStr) {
            AnFileLog.e("wlplatform", "app onMusicRegister"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            initMusicInfo();
            bMusicTransportEnd = true;

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onMusicRegister start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onMusicRegister(connectStr);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onMusicRegister end");
            });
        }

        @Override
        public void onMusicFocus(String s) {
            AnFileLog.e("wlplatform", "app onMusicFocus");
            initMusicInfo();
            bMusicTransportEnd = true;
            if (wlPCMPolicy != null) {
                //中间数据
                wlPCMPolicy.enableFocus(this);
                AnFileLog.e("wlplatform", "app onMusicPCM enableFocus");
            }
        }

        /**
         * 播放器取消注册，播放器取消注册将变为非激活音乐App
         *
         * @param connectStr 连接字符串
         */
        @Override
        public void onMusicUnregister(final String connectStr) {
            AnFileLog.e("wlplatform", "app onMusicUnregister"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            if (wlPCMPolicy != null) {
                //中间数据
                wlPCMPolicy.disableFocus();
                AnFileLog.e("wlplatform", "app onMusicPCM disableFocus");
            }
            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onMusicUnregister start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onMusicUnregister(connectStr);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            });
        }

        /**
         * 音乐ID3信息更新
         *
         * @param artist   演唱者(创建者)
         * @param title    歌曲名(电台名)
         * @param album    专辑名
         * @param lyric    歌词，LRC格式，可以为null
         * @param duration 时长 单位:秒
         * @param cover    歌曲封面，可以为null
         */
        @Override
        public void onMusicID3(final String source, final String artist, final String title, final String album, final String lyric, final int lyricType, final int duration, final Bitmap cover) {
            AnFileLog.e("wlplatform", "app onMusicID3"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            music_source = source;
            music_artist = artist;
            music_title = title;
            music_album = album;

            music_lyric = lyricTransfer.parserLyric(lyric, lyricType);

            music_cover = cover;
            music_duration = duration;

            sendID3ToCar();

            final String lyricString = music_lyric.toString();

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onMusicID3 start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onMusicID3(source, artist, title, album, lyricString, duration, cover);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onMusicID3 end");
            });
        }

        private void initMusicInfo() {
            music_order = 0;
            music_curtime = 0;
            music_duration = 0;
            music_source = null;
            music_artist = null;
            music_title = null;
            music_album = null;
            music_lyric = null;
            music_cover = null;
        }

        private void sendID3ToCar() {
            String sendID3Data;

            //Test
//            String path = Environment.getExternalStorageDirectory().toString() + "/Download/sample.png";
//            AnFileLog.e("wlplatform", "app onMusicID3 path :" + path);
//
//            File file = new File(path);
//            if (file.exists()) {
//                AnFileLog.e("wlplatform", "app onMusicID3 image ok");
//            } else {
//                AnFileLog.e("wlplatform", "app onMusicID3 image !!!!!!!!ok");
//            }
//
//            bitmap = BitmapFactory.decodeFile(path);

            if (music_cover != null) {
                int bitmapw = music_cover.getWidth();
                int bitmaph = music_cover.getHeight();
                Bitmap bitmap = bitmapCompressByte(music_cover);
                byte[] datas = bitmap2Bytes(bitmap);

                sendID3Data = ID3Manager.getID3Data(music_source, music_artist,
                        music_title, music_album, music_lyric, bitmapw, bitmaph,
                        music_duration, music_order);
                AnFileLog.e("wlplatform", "app onMusicID3 cover!= null :" + sendID3Data);

                ID3Manager.sendID3ToCar(getApplicationContext(), sendID3Data, datas);
            } else {
                sendID3Data = ID3Manager.getID3Data(music_source, music_artist,
                        music_title, music_album, music_lyric, 0, 0,
                        music_duration, music_order);
                AnFileLog.e("wlplatform", "app onMusicID3 cover == null :" + sendID3Data);

                ID3Manager.sendID3ToCar(getApplicationContext(), sendID3Data, null);
            }

            mMusicRefreshID3 = true;
        }

        /**
         * 音乐播放顺序更新
         *
         * @param order 播放顺序：参见《车机命令》，0: 循环播放、1: 顺序播放、2: 随机播放、3: 单曲循环播放
         */
        @Override
        public void onMusicOrder(final int order) {
            AnFileLog.e("wlplatform", "app onMusicOrder"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            switch (order) {
                case 0:
                    music_order = order;
                    break;
                case 1:
                    music_order = 3;
                    break;
                case 2:
                    music_order = 1;
                    break;
                case 3:
                    music_order = 2;
                    break;
                default:
                    music_order = order;
                    break;
            }


            ID3Manager.sendID3Progress(getApplicationContext(), music_curtime, music_order);
            AnFileLog.e("wlplatform", "app onMusicID3 refershOrder:" + music_order);

            mExecutorService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onMusicOrder start");
                if (mAidlCallback != null) {
                    try {
                        mAidlCallback.onMusicOrder(order);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                AnFileLog.e("wlplatform", "ExecutorService onMusicOrder end");
            });
        }

        @Override
        public void onMusicPCM(long pos, byte[] pcm) {
            AnFileLog.e("wlplatform", "app onMusicPCM"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());

            if (pcm != null && pcm.length > 0) {
                AnFileLog.e("wlplatform", "app onMusicPCM pos=" + pos + " length" + pcm.length);
            } else {
                AnFileLog.e("wlplatform", "app onMusicPCM pos=" + pos + " pcm == null");
                return;
            }

            if (wlPCMPolicy != null) {
                //中间数据
                AnFileLog.e("wlplatform", "app onMusicPCM send PCM");
                wlPCMPolicy.receiveMusicPCM(pos, pcm);
            }

            if (pcm != null) {
                music_LastPosition = pos;
                startTickThread();
            }
        }

        @Override
        public void onMusicStart(long totalLen, int rate, int bit, int channel) {
            //发送首包
            if (wlPCMPolicy != null) {
                wlPCMPolicy.startMusicPlay(totalLen, rate, bit, channel);
            }
            initMusicInfo();
            mMusicRefreshID3 = true;
            bMusicTransportEnd = false;

            music_TotalLen = totalLen;
            music_Rate = rate;
            music_Bit = bit;
            music_Channel = channel;

            AnFileLog.e("wlplatform", "app onMusic send start");
        }

        @Override
        public void onMusicStop() {
            //清除缓存，发尾包
            stopTickThread();
            music_LastPosition = 0;
            if (wlPCMPolicy != null) {
                wlPCMPolicy.stopMusicPlay();
            }
            AnFileLog.e("wlplatform", "app onMusic send stop");
        }

        @Override
        public void onMusicPause() {
            //暂停发送
            stopTickThread();
            if (wlPCMPolicy != null) {
                wlPCMPolicy.pauseMusicPlay();
            }
            AnFileLog.e("wlplatform", "app onMusic send pause");
        }

        @Override
        public void onMusicResume() {
            //恢复发送
            if (wlPCMPolicy != null) {
                wlPCMPolicy.resumeMusicPlay();
            }
            startTickThread();
            AnFileLog.e("wlplatform", "app onMusic send resume");
        }

        private Thread music_TickThread = null;
        private long music_LastPosition = 0;
        private long music_TotalLen = 0;
        private int music_Rate = 0;
        private int music_Bit = 0;
        private int music_Channel = 0;
        private String sound_lastMark = null;//最后收到的mark
        private int sound_lastPriority = -1;//最后获取的优先级
        private boolean sound_SendEnd = true;//判断是否已发送尾包
        private long sound_PastTime = 0;//一次tts已播放时长
        private long sound_TotalTime = 0;//接收到数据总播放时长
        private long sound_StartTime = 0;//传输通道发送首包的起始时间


        private void stopTickThread() {
            if (music_TickThread != null) {
                AnFileLog.e("wlplatform", "app onMusicTick stop!");
                music_TickThread.interrupt();
                music_TickThread = null;
            }
        }

        private void startTickThread() {
            if (music_TickThread == null) {
                if (music_LastPosition < 0) {
                    //music_LastPosition==-1的直播处理不更新进度
                    return;
                }
                music_TickThread = new Thread() {
                    final long tickPosition = music_LastPosition;
                    final long lastTimeMillis = System.currentTimeMillis();

                    @Override
                    public void run() {
                        AnFileLog.e("wlplatform", "app onMusicTick start!");
                        while (!isInterrupted()) {
                            long tickTimeMillis = System.currentTimeMillis() - lastTimeMillis;
                            long pos = tickPosition + music_Rate * music_Channel * music_Bit / 8 * tickTimeMillis / 1000;
                            AnFileLog.e("wlplatform", "app onMusicTick onTick = " + pos);

                            music_LastPosition = pos;
                            int coe = music_Rate * music_Bit * music_Channel;
                            if (coe <= 0) {
                                return;
                            }
                            music_curtime = ((Math.min(pos, music_TotalLen)) * 8 * 1000) / coe;
                            if (mMusicRefreshID3) {
                                AnFileLog.e("wlplatform", "app onMusicTick sendID3Progress = " + music_curtime);
                                ID3Manager.sendID3Progress(getApplicationContext(), music_curtime, music_order);
                            }
                            mExecutorService.execute(() -> {
                                if (mAidlCallback != null) {
                                    try {
                                        AnFileLog.e("wlplatform", "app onMusicTick aidlCallback = " + (Math.min(pos, music_TotalLen)));
                                        mAidlCallback.onMusicPCM((Math.min(pos, music_TotalLen)), music_TotalLen, music_Rate, music_Bit, music_Channel);
                                    } catch (RemoteException e) {
                                        e.printStackTrace();
                                    }
                                }
                            });

                            if (pos >= music_TotalLen) {
                                AnFileLog.e("wlplatform", "app onMusicTick >total end!");
                                return;
                            }

                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                return;
                            }
                        }
                    }
                };
                music_TickThread.start();
            }
        }

        /***********************sound******************************/
        private void finishSoundTransport() {
            if (wlPCMPolicy != null) {
                wlPCMPolicy.receiveTTSPCM(null);
            }
            initPlayingSoundData();
            AnFileLog.e("wlplatform", "app onSoundPCM HU send End");

        }

        private void interruptSoundTransport() {
            if (wlPCMPolicy != null) {
                wlPCMPolicy.stopTTSPlay();//stop是打断接口
            }
            initPlayingSoundData();
        }

        private void startSoundTransport() {
            mPCMPlaySoundID = (int) playingSoundData[0];
            mPCMPlayConnectStr = m_SoundConnecter;
            //发送新开始
            if (sound_SendEnd) {
                sound_StartTime = System.currentTimeMillis();

                if (wlPCMPolicy != null) {
                    wlPCMPolicy.startTTSPlay(0, (int) playingSoundData[2], (int) playingSoundData[3], (int) playingSoundData[4], sound_lastPriority, this);///////增加打断接口
                }
                AnFileLog.e("wlplatform", "app onSoundPCM HU send start");
            }

            if (wlPCMPolicy != null) {
                wlPCMPolicy.receiveTTSPCM((byte[]) playingSoundData[1]);
            }
            sound_SendEnd = false;
            AnFileLog.e("wlplatform", "app onSoundPCM HU send Center");
        }

        @Override
        public void onSendCommand(String connectStr, String command) {
            if (serverManager != null) {
                serverManager.command(connectStr, command);
            }
        }

        public class CheckThread extends Thread {
            boolean mClose = false;

            public void close() {
                mClose = true;
            }

            @Override
            public void run() {
                AnFileLog.e("wlplatform", "app onSoundPCM check START !!!!!");
                long finishWaitTime = 0;
                try {
                    sound_TotalTime += (long) playingSoundData[5];//收到数据总播放时长
                    sound_PastTime = System.currentTimeMillis() - sound_StartTime;//开始发送到当前时间
                    long differenceTime = sound_TotalTime - sound_PastTime;//收到数据未发送给车机播放时间差
                    AnFileLog.e("wlplatform", "app onSoundPCM check totle:" + sound_TotalTime + " pass:" + sound_PastTime + " diff:" + differenceTime);
                    if (differenceTime > WAIT_TIME) {
                        sleep(differenceTime - WAIT_TIME);
                        finishWaitTime = WAIT_TIME;
                    } else {
                        finishWaitTime = differenceTime;
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                if (!mClose) {
                    AnFileLog.e("wlplatform", "app onSoundPCM send app OK");
                    final int id = (int) playingSoundData[0];

                    if (serverManager != null) {
                        AnFileLog.e("wlplatform", "app onSoundPCM send app Prepare:" + id);
                        serverManager.sendSoundPrepare(m_SoundConnecter, id);
                    }

                    int loop = 0;
                    while (!mClose) {
                        try {
                            sleep(1);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        if (loop >= finishWaitTime) {
                            //关闭通道
                            finishSoundTransport();
                            break;
                        }
                        loop++;
                    }
                }
                AnFileLog.e("wlplatform", "app onSoundPCM check END!!!!!");
            }
        }

        //开启请求PCM数据的线程
        private void startThread() {
            AnFileLog.e("wlplatform", "app onSoundPCM startThread START!!!!!");
            stopThread();
            checkThread = new CheckThread();
            checkThread.start();
            AnFileLog.e("wlplatform", "app onSoundPCM startThread END!!!!!");
        }

        //停掉请求PCM数据的线程
        private void stopThread() {
            AnFileLog.e("wlplatform", "app onSoundPCM stopThread START!!!!!");
            if (checkThread != null) {
                checkThread.close();
                checkThread = null;
            }
            AnFileLog.e("wlplatform", "app onSoundPCM stopThread END!!!!!");
        }

        private void initPlayingSoundData() {
            if (playingSoundData == null) {
                playingSoundData = new Object[6];
            }

            playingSoundData[0] = 0;
            playingSoundData[1] = null;
            playingSoundData[2] = 0;
            playingSoundData[3] = 0;
            playingSoundData[4] = 0;
            playingSoundData[5] = 0L;

            sound_TotalTime = 0;
            sound_StartTime = 0;
            sound_SendEnd = true;
            sound_lastMark = null;
            sound_lastPriority = -1;
            mPcmTTSEnd = true;
            AnFileLog.e("wlplatform", "app onSoundPCM initPlayingSoundData OK!!!!!");
        }

        /**
         * 接收到导航提示声音数据
         *
         * @param connectStr 发送者的连接字符串
         * @param soundID    Sound ID
         * @param pcm        声音数据
         * @param rate       采样率
         * @param bit        采样精度
         * @param channel    声道数
         */
        @Override
        public void onSoundPCM(final String connectStr, final int soundID, final String mark, final byte[] pcm, final int rate, final int bit, final int channel) {

            mSoundService.execute(() -> {
                AnFileLog.e("wlplatform", "ExecutorService onSoundPCM start");
                if (mAidlCallback != null) {

                    int curPriority = sound_lastPriority;
                    if (pcm == null) {
                        AnFileLog.e("wlplatform", "app onSoundPCM PCM==null");
                        stopThread();
                        //关闭通道
                        interruptSoundTransport();
                        return;
                    }

                    if (playingSoundData == null) {
                        initPlayingSoundData();
                    }

                    boolean lastMark = false;
                    if (sound_lastPriority >= 0 && mark.equals(sound_lastMark)) {
                        AnFileLog.e("wlplatform", "app onSoundPCM mark ==");
                        //mark相同，优先级相同
                        if (checkThread != null) {
                            //停止判断结束线程
                            stopThread();
                        }
                        lastMark = true;
                    } else {
                        //mark不等，需要外部判断优先级
                        AnFileLog.e("wlplatform", "app onSoundPCM mark !=");
                        try {
                            AnFileLog.e("wlplatform", "app onSoundPCM onAppReceiveSound");
                            curPriority = mAidlCallback.onAppReceiveSound(connectStr, (String) mark, (pcm.length * 8 * 1000) / (rate * bit * channel));
                            AnFileLog.e("wlplatform", "app onSoundPCM onAppReceiveSound ==" + curPriority);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }

                    if (!lastMark) {//表示播放未结束
                        //播放中
                        AnFileLog.e("wlplatform", "app onSoundPCM playing");
                        if (curPriority > sound_lastPriority) {
                            AnFileLog.e("wlplatform", "app onSoundPCM send app Interrupt");
                            stopThread();
                            //发送打断
                            serverManager.sendSoundInterrupt(m_SoundConnecter, (int) playingSoundData[0], sound_PastTime, (long) playingSoundData[5]);
                            interruptSoundTransport();
                        } else if (curPriority < sound_lastPriority) {
                            AnFileLog.e("wlplatform", "app onSoundPCM send app Reject");
                            int remainPlayingTime = (int) (sound_TotalTime - (System.currentTimeMillis() - sound_StartTime));
                            if (remainPlayingTime < 0) {
                                remainPlayingTime = 0;
                            }
                            serverManager.sendSoundReject(connectStr, soundID, remainPlayingTime);
                            return;
                        }
                    }

                    sound_lastPriority = curPriority;
                    sound_lastMark = mark;
                    mPcmTTSEnd = false;

                    m_SoundConnecter = connectStr;
                    playingSoundData[0] = soundID;//soundid
                    playingSoundData[1] = pcm;//pcm数据
                    playingSoundData[2] = rate;//rate
                    playingSoundData[3] = bit;//bit
                    playingSoundData[4] = channel;//channel
                    playingSoundData[5] = (pcm.length * 8 * 1000L) / (rate * bit * channel);//当前数据时长

                    AnFileLog.e("wlplatform", "app onSoundPCM priority=" + sound_lastPriority + " mark=" + sound_lastMark);

                    startSoundTransport();
                    startThread();
                    AnFileLog.e("wlplatform", "ExecutorService onSoundPCM end");
                }
            });
        }

        /**
         * 开始TBT导航
         *
         * @param connectStr 发送者的连接字符串
         */
        @Override
        public void onNaviTBTBegin(String connectStr) {
            AnFileLog.e("wlplatform", "app onNaviTBTBegin"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            m_TBTConnecter = connectStr;
        }

        /**
         * 接收到导航TBT信息
         *
         * @param connectStr      发送者的连接字符串
         * @param currentRoadName 当前道路名称
         * @param roadName        下一路口名称
         * @param roadDistance    下一路口距离，米
         * @param roadTurnIcon    下一个路口TBT图标ID
         * @param remainDistance  距目的地的剩余距离，小余等于0则导航结束，单位：米
         * @param remainTime      预估到达目的地的所剩时间，单位：分
         */
        @Override
        public void onNaviTBT(String connectStr, String currentRoadName, String roadName, int roadDistance, int roadTurnIcon, int remainDistance, int remainTime) {
            AnFileLog.e("wlplatform", "app onNaviTBT"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            if (m_TBTConnecter == null || !m_TBTConnecter.equals(connectStr)) {
                AnFileLog.e("wlplatform", "app onNaviTBT connectStr error");
                return;
            }

//            {
//                "moduleName": "WeLink",
//                "version": 0,
//                "platform": "android|ios|ce",
//                "command":{
//                    "method":"onTurnbyturn",
//                    "extData":{
//                        "roadName":"无名路",
//                        "roadDistance":"1000m",
//                        "roadTurnIcon":"1",
//                        "remainDistance":"50m",
//                        "remainTime":"01:22:11"
//                    }
//                }
//            }

            if (wlChannel != null) {
                String tbtString = null;
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("moduleName", "WeLink");
                    jsonObject.put("version", 0);
                    jsonObject.put("platform", "android|ios|ce");
                    JSONObject command = new JSONObject();
                    command.put("method", "onTurnbyturn");
                    JSONObject extData = new JSONObject();
                    extData.put("roadName", roadName);
                    extData.put("roadDistance", roadDistance);
                    extData.put("roadTurnIcon", roadTurnIcon);
                    extData.put("remainDistance", remainDistance);
                    extData.put("remainTime", Integer.toString(remainTime));
                    command.put("extData", extData);
                    jsonObject.put("command", command);
                    tbtString = jsonObject.toString();
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                if (tbtString != null) {
                    AnFileLog.e("wlplatform", "app onNaviTBT send =" + tbtString);
                    wlChannel.sendMessage(tbtString.getBytes());
                }
            }
        }

        /**
         * 结束TBT导航
         *
         * @param connectStr 发送者的连接字符串
         */
        @Override
        public void onNaviTBTEnd(final String connectStr) {
            AnFileLog.e("wlplatform", "app onNaviTBTEnd"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
            m_TBTConnecter = null;
        }

        /**
         * 接收到交通摄像头提示
         *
         * @param connectStr 发送者的连接字符串
         */
        @Override
        public void onTrafficCamera(final String connectStr) {
            AnFileLog.e("wlplatform", "app onTrafficCamera"
                    + " pid:" + android.os.Process.myPid()
                    + " tid:" + android.os.Process.myTid());
        }
    }

    private static Bitmap bitmapCompressByte(Bitmap mBitmap) {
        int bmpWidth = mBitmap.getWidth();
        int bmpHeight = mBitmap.getHeight();
        if (bmpWidth >= 800 || bmpHeight >= 800) {
            mBitmap = Bitmap.createScaledBitmap(mBitmap, 640, 640, true);
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        mBitmap.compress(Bitmap.CompressFormat.PNG, 100, baos);
        byte[] bytes = baos.toByteArray();

        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = false;
        options.inSampleSize = 2;
        mBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length, options);
        return mBitmap;
    }

    private static byte[] bitmap2Bytes(Bitmap bm) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bm.compress(Bitmap.CompressFormat.PNG, 100, baos);
        return baos.toByteArray();
    }


}
