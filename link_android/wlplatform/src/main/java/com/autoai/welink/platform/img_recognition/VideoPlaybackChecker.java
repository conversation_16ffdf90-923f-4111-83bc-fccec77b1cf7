package com.autoai.welink.platform.img_recognition;

import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

/**
 * <AUTHOR>
 */
public class VideoPlaybackChecker {
    private static final String TAG = "VideoPlaybackChecker";
    private final Handler handler = new Handler(Looper.getMainLooper());
    private Runnable runnable = null;
    private final AudioManager audioManager;
    private static VideoPlaybackChecker instance;
    private static boolean isMediaPlaying = false;
    private boolean isChecking = false;

    private PlaybackStatusListener listener;

    public interface PlaybackStatusListener {
        void onPlaybackStatusChanged(boolean isPlaying);
    }

    private VideoPlaybackChecker(Context context) {
        audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        runnable = ()->{
            boolean isPlaying = isMediaPlaying();
            Log.d(TAG, "mediaPlaying  isPlaying = " + isPlaying + ", isMediaPlaying = " + isMediaPlaying);
            if (listener != null && isMediaPlaying != isPlaying) {
                listener.onPlaybackStatusChanged(isPlaying);
            }
            isMediaPlaying = isPlaying;
            handler.postDelayed(runnable, 1000);
        };
    }

    public void setPlaybackStatusListener(PlaybackStatusListener listener) {
        this.listener = listener;
    }

    public static synchronized VideoPlaybackChecker getInstance(Context context) {
        if (instance == null) {
            instance = new VideoPlaybackChecker(context.getApplicationContext());
        }
        return instance;
    }

    public void startChecking() {
        if (!isChecking) {
            handler.post(runnable);
            Log.d(TAG, "startChecking");
            isChecking = true;
        }
    }

    public void stopChecking() {
        isChecking = false;
        Log.d(TAG, "stopChecking");
        handler.removeCallbacks(runnable);
    }

    private boolean isMediaPlaying() {
        if (audioManager != null) {
            Log.d("VideoPlaybackChecker", "isMediaPlaying: " + audioManager.isMusicActive());
            //return audioManager.isMusicActive() || audioManager.getStreamVolume(AudioManager.STREAM_MUSIC) > 0;
            return audioManager.isMusicActive();
        }
        return false;
    }
}