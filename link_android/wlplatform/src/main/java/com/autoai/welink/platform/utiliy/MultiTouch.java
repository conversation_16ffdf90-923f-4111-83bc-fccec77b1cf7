package com.autoai.welink.platform.utiliy;

import android.util.SparseArray;
import android.view.MotionEvent;

class MultiTouch {
	public int wPhone, hPhone, wDevice, hDevice;
	public void setPhoneWH(int wPhone,int hPhone){
		this.wPhone=wPhone;
		this.hPhone=hPhone;
	}
	public void setDeviceWH(int wDevice,int hDevice){
		this.wDevice=wDevice;
		this.hDevice=hDevice;
	}

	public SparseArray<TouchObject> tp = new SparseArray<>();//默认是按key的大小去排序的,key是整数是默认是有序的?

	public boolean addPoint(int id, int type, int x, int y) {
		TouchObject touchObject = tp.get(id);
		TouchObject touchObject2 = touchObject;
		if (touchObject == null) {
            touchObject2 = new TouchObject(1);
        }
		touchObject2.id = id;
		touchObject2.type = type;
		touchObject2.touchPoint.x[0] = x;//*wPhone/wDevice;
		touchObject2.touchPoint.y[0] = y;//*hPhone/hDevice;
		tp.put(id, touchObject2);
		return touchObject == null;
	}

	public TouchPoint getAll() {
		TouchPoint touchPoint = new TouchPoint(tp.size());
		for (int i = 0; i < tp.size(); ++i) {
			touchPoint.x[i] = tp.valueAt(i).touchPoint.x[0];
			touchPoint.y[i] = tp.valueAt(i).touchPoint.y[0];
		}
		return touchPoint;
	}

	//互联开始和断开互联时, 要清空所有已缓存的数据
	public void clearCache(){
		tp.clear();
	}

	public void removeUp() {
		int id = -1;
		for (int i = 0; i < tp.size(); ++i) {
			if (tp.valueAt(i).type == MotionEvent.ACTION_UP) {
				id = tp.valueAt(i).id;
				break;
			}
		}
		tp.remove(id);
	}

	public String print() {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < tp.size(); ++i) {
			TouchObject touchObject = tp.valueAt(i);
			sb.append("[id=" + touchObject.id + ",type=" + touchObject.type + ",x=" + touchObject.touchPoint.x[0] + ",y=" + touchObject.touchPoint.y[0] + "],");
		}
		return sb.toString();
	}

	class TouchObject {
		int id;
		int type;
		TouchPoint touchPoint;

		public TouchObject(int cnt) {
			touchPoint = new TouchPoint(cnt);
		}
	}

	class TouchPoint {
		int x[];
		int y[];

		public TouchPoint(int cnt) {
			x = new int[cnt];
			y = new int[cnt];
		}
	}
}
