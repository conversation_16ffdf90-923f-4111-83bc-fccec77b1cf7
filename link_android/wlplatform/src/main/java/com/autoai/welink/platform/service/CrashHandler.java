package com.autoai.welink.platform.service;

public class <PERSON><PERSON><PERSON><PERSON> implements Thread.UncaughtExceptionHandler {
    public interface CrashHandlerCallback {
        void onCrash(Throwable e);
    }

    CrashHandlerCallback callback;

    public CrashHandler(CrashHandlerCallback callback) {
        this.callback = callback;
        Thread.setDefaultUncaughtExceptionHandler(CrashHandler.this);
    }

    @Override
    public void uncaughtException(Thread t, Throwable e) {
        callback.onCrash(e);
    }
}