package com.autoai.welink.platform.utiliy;

import android.graphics.Bitmap;
import android.util.Base64;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


public class BitmapUtils {

    public static final String TAG = "BitmapUtils";

    public static List<String> bitmap2Base64(List<Bitmap> bitmapsList) {
        List<String> list = new ArrayList<>();
        try {
            for (Bitmap bitmap : bitmapsList) {
                list.add(bitmap2Base64(bitmap));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     *转base 64
     * @param bitmap
     * @return
     */
    public static String bitmap2Base64(Bitmap bitmap) {
        if (null == bitmap) throw new NullPointerException();
        // if (null == bitmap) return null;

        //---> 写到流中
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Log.d(TAG,"bitmap2Base64 size = " + bitmap.getByteCount());
        //把bitmap100%高质量压缩 到 output对象里
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);

        String base64InfoStr = Base64.encodeToString(outputStream.toByteArray(), Base64.DEFAULT);
        Log.d(TAG,"base64InfoStr size = " + base64InfoStr.length());
        return base64InfoStr;
    }

    public static byte[] bitmapToByteArray(Bitmap bitmap) {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
        return stream.toByteArray();
    }


    /**
     * 从h264码流钟获取帧
     * @param path
     * @return
     */
    public static Bitmap getBitmapFromH264(String path) {
        Bitmap bitmap = null;
//        FFmpegMediaMetadataRetriever mediaMetadataRetriever = new FFmpegMediaMetadataRetriever();
//        File file = new File(path);//实例化File对象，文件路径为/storage/sdcard/Movies/music1.mp4
//        try {
//            if (file.exists()) {
//                mediaMetadataRetriever.setDataSource(path);//设置数据源为该文件对象指定的绝对路径
//                bitmap = mediaMetadataRetriever.getFrameAtTime();//获得视频的Bitmap对象
//            }
//        } catch (IllegalArgumentException e) {
//            Log.e("转bitmap出错", e.toString(), e);
//        }
//        mediaMetadataRetriever.release();
        return bitmap;
    }


    /**
     * 用于测试确认使用
     *
     * @param bitmap
     * @param path
     */
    public static void saveBitmapToFile(Bitmap bitmap, String path) {
        FileOutputStream fileOutputStream = null;
        try {
            File file = new File(path);
            fileOutputStream = new FileOutputStream(file);
            // 将Bitmap转换为字节数组，并写入文件
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }



}
