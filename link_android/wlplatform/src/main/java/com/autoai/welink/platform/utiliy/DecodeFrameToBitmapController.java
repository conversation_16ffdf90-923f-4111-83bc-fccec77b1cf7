package com.autoai.welink.platform.utiliy;

import android.annotation.SuppressLint;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.graphics.YuvImage;
import android.media.Image;
import android.media.ImageReader;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Surface;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 */
public class DecodeFrameToBitmapController {

    private static final String TAG = "DecodeFrameToBitmapController";


    /**
     * 解码器
     */
    private MediaCodec mDecoder = null;

    /**
     * 需要解码的类型
     * H.264 Advanced Video
     */
    private final static String MIME_TYPE = "video/avc";

    private ImageReader imageReaderTemp;

    private int mediaFormatWidth = 0;
    private int mediaFormatHeight = 0;

    private final int COLOR_FormatI420 = 1;
    private final int COLOR_FormatNV21 = 2;

    private String savePath = null;


    private static DecodeFrameToBitmapController mInstance = null;

    public static DecodeFrameToBitmapController getInstance() {
        if (mInstance == null) {
            mInstance = new DecodeFrameToBitmapController();
        }
        return mInstance;
    }

    HandlerThread handlerThread;
    Handler handler;

    public void init(int width, int height) {
        Log.i(TAG, "DecodeFrameToBitmapController::init");

        handlerThread = new HandlerThread("she") {
            @Override
            protected void onLooperPrepared() {
                super.onLooperPrepared();
                handler = new Handler(handlerThread.getLooper());
            }
        };
        handlerThread.start();

        if (mDecoder != null) {
            Log.e(TAG, "mDecoder != null");
            return;
        }

        setConfig(width, height);

        //todo：step1.初始化 mDecoder
        Log.i(TAG, "DecodeFrameToBitmapController::init --> 初始化 mDecoder");

        //初始化MediaFormat
        MediaFormat mediaFormat = MediaFormat.createVideoFormat(MIME_TYPE, width, height);
        mediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, 1);

//        mediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);
        mediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV422Flexible);

        try {
            //根据需要解码的类型创建解码器
            mDecoder = MediaCodec.createDecoderByType(MIME_TYPE);
            //配置MediaFormat以及需要显示的surface
//            mDecoder.configure(mediaFormat, new Surface(new SurfaceTexture(-1)), null, 0);

        } catch (Exception e) {
            if (mDecoder != null) {
                mDecoder.release();
                mDecoder = null;
            }
        }


        //todo：step2.初始化 ImageReader
        Log.i(TAG, "DecodeFrameToBitmapController::init --> 初始化 ImageReader");
        if (imageReaderTemp == null) {
            int imageFormat = ImageFormat.YUV_420_888;
//            int imageFormat = ImageFormat.RGB_565;
//            int imageFormat = ImageFormat.JPEG;
//            int imageFormat = ImageFormat.YUV_422_888;
            imageReaderTemp = ImageReader.newInstance(
                    width,
                    height,
                    imageFormat,
                    2);
            imageReaderTemp.setOnImageAvailableListener(imageReader -> {


                new Thread(() -> {
                    Image image = imageReader.acquireNextImage();
//                    Image image = imageReader.acquireLatestImage();

                    Log.i(TAG, "DecodeFrameToBitmapController::imageReader OnImageAvailableListener--> 回调执行 (image == null)?  = " + (image == null));
                    Rect crop = image.getCropRect();
                    int format = image.getFormat();
                    int cropWidth = crop.width();
                    int cropHeight = crop.height();

                    Log.i(TAG, "DecodeFrameToBitmapController::imageReader OnImageAvailableListener--> 回调执行 ( format) = " + (format));
                    Log.i(TAG, "DecodeFrameToBitmapController::imageReader OnImageAvailableListener--> 回调执行 (cropWidth) = " + (cropWidth));
                    Log.i(TAG, "DecodeFrameToBitmapController::imageReader OnImageAvailableListener--> 回调执行 ( cropHeight) = " + (cropHeight));
//                    try {
//                        Thread.sleep(250);
//                        Image.Plane[] planes = image.getPlanes();
//                    } catch (Exception e) {
//                        Log.i(TAG, "DecodeFrameToBitmapController::imageReader OnImageAvailableListener-->getPlanes异常了");
//                    }

                    Image.Plane[] planes = image.getPlanes();


//                    ByteBuffer buffer = image.getPlanes()[0].getBuffer();

//                    byte[] data = new byte[buffer.remaining()];
//                    Log.d(TAG, "data-size=" + data.length);
//                    buffer.get(data);
//                    compressToJpeg(savePath, image);
//                        mDisplayThread.compressToJpeg("/sdcard/output_" + System.currentTimeMillis() + ".jpg", image);
                    image.close();
                    imageReader.close();


                }).start();
//                    mDecoder.setOutputSurface(null);
            }, null);
            mDecoder.configure(mediaFormat, imageReaderTemp.getSurface(), null, 0);
            //---> 开启解码器
            mDecoder.start();

            new DisplayThread().start();


//            mDecoder.setOutputSurface(imageReaderTemp.getSurface());
        }


    }

    public void setConfig(int width, int height) {
        Log.i(TAG, "DecodeFrameToBitmapController::setConfig 设置解码器宽高 width = " + width + ";height = " + height);
        this.mediaFormatWidth = width;
        this.mediaFormatHeight = height;
    }

    private boolean compressToJpeg(String fileName, Image image) {

        try {
            FileOutputStream outStream;
            try {
                outStream = new FileOutputStream(fileName);
            } catch (IOException ioe) {
                throw new RuntimeException("Unable to create output file " + fileName, ioe);
            }

            Rect rect = image.getCropRect();
            YuvImage yuvImage = new YuvImage(getDataFromImage(image, COLOR_FormatNV21), ImageFormat.NV21, rect.width(), rect.height(), null);
            yuvImage.compressToJpeg(rect, 100, outStream);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    private byte[] getDataFromImage(Image image, int colorFormat) {
        if (colorFormat != COLOR_FormatI420 && colorFormat != COLOR_FormatNV21) {
            throw new IllegalArgumentException("only support COLOR_FormatI420 " + "and COLOR_FormatNV21");
        }
        if (!isImageFormatSupported(image)) {
            throw new RuntimeException("can't convert Image to byte array, format " + image.getFormat());
        }
        Rect crop = image.getCropRect();
        int format = image.getFormat();
        int width = crop.width();
        int height = crop.height();
        Image.Plane[] planes = image.getPlanes();
        byte[] data = new byte[width * height * ImageFormat.getBitsPerPixel(format) / 8];
        byte[] rowData = new byte[planes[0].getRowStride()];
        Log.i(TAG, "DecodeFrameToBitmapController::getDataFromImage:get data from " + planes.length + " planes");
        int channelOffset = 0;
        int outputStride = 1;
        for (int i = 0; i < planes.length; i++) {
            switch (i) {
                case 0:
                    channelOffset = 0;
                    outputStride = 1;
                    break;
                case 1:
                    if (colorFormat == COLOR_FormatI420) {
                        channelOffset = width * height;
                        outputStride = 1;
                    } else if (colorFormat == COLOR_FormatNV21) {
                        channelOffset = width * height + 1;
                        outputStride = 2;
                    }
                    break;
                case 2:
                    if (colorFormat == COLOR_FormatI420) {
                        channelOffset = (int) (width * height * 1.25);
                        outputStride = 1;
                    } else if (colorFormat == COLOR_FormatNV21) {
                        channelOffset = width * height;
                        outputStride = 2;
                    }
                    break;
            }
            ByteBuffer buffer = planes[i].getBuffer();
            int rowStride = planes[i].getRowStride();
            int pixelStride = planes[i].getPixelStride();
            Log.i(TAG, "DecodeFrameToBitmapController::getDataFromImage  pixelStride " + pixelStride);
            Log.i(TAG, "DecodeFrameToBitmapController::getDataFromImage  rowStride " + rowStride);
            Log.i(TAG, "DecodeFrameToBitmapController::getDataFromImage  width " + width);
            Log.i(TAG, "DecodeFrameToBitmapController::getDataFromImage  height " + height);
            Log.i(TAG, "DecodeFrameToBitmapController::getDataFromImage  buffer size " + buffer.remaining());
            int shift = (i == 0) ? 0 : 1;
            int w = width >> shift;
            int h = height >> shift;
            buffer.position(rowStride * (crop.top >> shift) + pixelStride * (crop.left >> shift));
            for (int row = 0; row < h; row++) {
                int length;
                if (pixelStride == 1 && outputStride == 1) {
                    length = w;
                    buffer.get(data, channelOffset, length);
                    channelOffset += length;
                } else {
                    length = (w - 1) * pixelStride + 1;
                    buffer.get(rowData, 0, length);
                    for (int col = 0; col < w; col++) {
                        data[channelOffset] = rowData[col * pixelStride];
                        channelOffset += outputStride;
                    }
                }
                if (row < h - 1) {
                    buffer.position(buffer.position() + rowStride - length);
                }
            }
            Log.i(TAG, "DecodeFrameToBitmapController::getDataFromImage Finished reading data from plane " + i);
        }
        return data;
    }

    private boolean isImageFormatSupported(Image image) {
        int format = image.getFormat();
        switch (format) {
            case ImageFormat.YUV_420_888:
            case ImageFormat.NV21:
            case ImageFormat.YV12:
                return true;
        }
        return false;
    }


    @SuppressLint("SdCardPath")
    public void capture(byte[] inputRawFrame, String savePath) {
        Log.i(TAG, "DecodeFrameToBitmapController::capture");
        try {
//            this.savePath = savePath;
            //todo：step2.开始解码
            new Thread(() -> {
                int inputBufferIndex = mDecoder.dequeueInputBuffer(1 * 1000);

                if (inputBufferIndex >= 0) {
                    ByteBuffer inputBuffer = mDecoder.getInputBuffer(inputBufferIndex);
                    Log.i(TAG, "DecodeFrameToBitmapController::capture 开始编码");

                    if (inputBuffer != null) {
                        inputBuffer.clear();
                        inputBuffer.put(inputRawFrame, 0, inputRawFrame.length);
                        //解码
                        mDecoder.queueInputBuffer(inputBufferIndex, 0, inputRawFrame.length, 1, 0);
                    }

                }
            }).start();


        } catch (Exception ex) {
            ex.printStackTrace();
        }


    }


    // 独立使用一个线程，循环解码降低延迟
    private class DisplayThread extends Thread {

        private final String TAG = "VideoToFrames";

        private boolean isRunning = true;

        public void stopThread() {
            isRunning = false;
        }

        private final MediaCodec.BufferInfo mBufferInfo = new MediaCodec.BufferInfo();


        @Override
        public void run() {
            try {
                while (isRunning) {
                    try {
                        // 获取输出buffer index
                        // 1000/60 = 16
                        int outputBufferIndex = mDecoder.dequeueOutputBuffer(mBufferInfo, 16000);
                        // LogUtil.d(TAG, "run: dequeue output1 end " + outputBufferIndex);

                        if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                            //此处可以或得到视频的实际分辨率，用以修正宽高比
                            // fix();
                            Log.v(TAG, "INFO_OUTPUT_FORMAT_CHANGED");
                        }

                        //循环解码，直到数据全部解码完成
                        while (outputBufferIndex >= 0) {
                            try {
                                Log.i(TAG, "DecodeFrameToBitmapController::DisplayThread 解码线程工作 outputBufferIndex = " + outputBufferIndex);
                                mDecoder.releaseOutputBuffer(outputBufferIndex, true);
                            } catch (IllegalStateException e) {
                                Log.e(TAG, e.getMessage());
                                break;
                            }
                            outputBufferIndex = mDecoder.dequeueOutputBuffer(mBufferInfo, 500);
                            // LogUtil.d(TAG, "run: dequeue output2 end " + outputBufferIndex);
                        }
                    } catch (IllegalStateException e) {
                        Thread.sleep(50);
                    } catch (NullPointerException e) {
                        Log.w(TAG, "run1: decoder has been released!");
                        Log.w(TAG, e.getMessage());
                        this.stopThread();
                    }
                }
            } catch (InterruptedException e) {
                Log.e(TAG, e.getMessage());
            } catch (NullPointerException e) {
                Log.w(TAG, "run2: decoder has been released!");
                Log.w(TAG, e.getMessage());

                e.printStackTrace();
                this.stopThread();
            }
        }
    }

}
