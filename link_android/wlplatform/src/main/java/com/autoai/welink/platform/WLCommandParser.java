package com.autoai.welink.platform;

public interface WLCommandParser {
    /**
     * command回调接口
     */
    interface Callback {
        /**
         * 发送command到connector端
         * @param connectStr 连接对象字符串
         * @param command 命令协议 json数据
         */
        void onSendCommand(String connectStr, String command);
    }

    /**
     * pcm回调注册
     * @param connectStr 连接对象字符串
     * @param callback 回调对象
     */
    void register(String connectStr, WLCommandParser.Callback callback);

    /**
     * 接收到车机command数据
     * @param command 车机command json数据
     * @return 是否内部处理
     */
    boolean receiveCommand(String command);
}
