package com.autoai.welink.platform;

public interface WLPCMPolicy {
    /**
     * pcm回调接口
     */
    interface Callback {
        void onSendPCM(byte[] pcm);
        void onPlayCommand(byte[] command);
    }

    /**
     * 打开日志功能
     * @param enable true:打开；false：关闭。
     */
    void enableLogCat(boolean enable);
    void enableLogFile(boolean enable);

    /**
     * 音乐播放相关接口
     */
    void enableFocus(Callback callback); //发送开始播放状态
    void disableFocus(); //发送结束播放状态，并停止音乐播放
    void startMusicPlay(long totalLen, int rate, int bit, int channel); //发送头包
    void stopMusicPlay(); //发送尾包，并清除缓存
    void pauseMusicPlay(); //暂停缓存发送
    void resumeMusicPlay(); //恢复缓存发送
    void receiveMusicPCM(long position, byte[] pcm); //接收数据到缓存，超出一定大小则抛弃数据

    /**
     * tts播报相关接口
     */
    void startTTSPlay(long totalLen, int rate, int bit, int channel, int priority, Callback callback);
    void stopTTSPlay();
    void receiveTTSPCM(byte[] pcm);

    /**
     * 释放接口
     */
    void release();
}
