package com.autoai.welink.platform.service;

import android.content.Context;

import com.autoai.welink.channel.WLChannel;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class ID3Manager {

    public static final int PCM_MARK = 0x55AA;//手机端与车机端通信
    public static final int SOUND_MARK_START = 1;//第一个包
    public static final int SOUND_MARK_CENTER = 0;//中间包
    public static final int SOUND_MARK_END = 2;//最后包
    public static final int SOUND_MUSIC = 1;//音源标识各种音乐归为一类本地和qq都算


    public static final int COMMAND_TO_CAR_START = 10;//手机向车机端发送开始的命令号
    public static final int COMMAND_TO_CAR_ID_3 = 11;//手机向车机端发送id3信息
    public static final int COMMAND_REFRESH_ID_3 = 12;//手机刷新 ID3 数据

    /**
     * 发送ID3数据给车机
     *
     * @param context Context
     * @param id3Data ID3数据
     */
    public static void sendID3ToCar(Context context, String id3Data, byte [] bmpAlbum) {
        sendID3ToCar(context, COMMAND_TO_CAR_ID_3, id3Data,bmpAlbum);
    }

    /**
     * 发送ID3数据给车机
     *
     * @param context Context
     * @param command 命令号
     * @param id3Data ID3数据
     */
    private static void sendID3ToCar(Context context, int command, String id3Data,byte [] bmpAlbum) {
        byte[] id3datas = getid3DataByParam(command,id3Data.getBytes(),bmpAlbum);
        WLChannel.getInstance(context).sendPCM(id3datas);
    }


    /**
     * 发送刷新ID3数据给车机
     *
     * @param context Context
     * @param command 命令号
     * @param id3Data ID3数据
     */
    public static void sendID3ProToCar(Context context, int command, String id3Data) {
        byte[] id3prodatas = getid3ProDataByParam(command,id3Data.getBytes());
        WLChannel.getInstance(context).sendPCM(id3prodatas);
    }

    /**
     * 发送播放进度
     *
     * @param ms   毫秒
     * @param mode 播放模式
     */
    public static void sendID3Progress(Context context, long ms, int mode) {
        sendID3ProToCar(context, COMMAND_REFRESH_ID_3, getID3ProgressData(ms, mode));
    }

    /**
     * 获取发送给车机的播放进度ID3数据
     *
     * @param ms 进度
     * @return 数据
     */
    public static String getID3ProgressData(long ms, int mode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android|ios|ce");
            JSONObject command = new JSONObject();
            command.put("method", "onRefreshMusicPro");
            command.put("type", 0);
            JSONObject extData = new JSONObject();
            extData.put("progressBar", ms);
            extData.put("mode", mode);
            command.put("extData", extData);
            jsonObject.put("command", command);
            return jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取发送给车机的ID3数据
     *
     * @param source      音源
     * @param singerName  歌手
     * @param songName    歌曲名
     * @param albumName   专辑名
     * @param lyricArray   歌词列表
     * @param width       专辑图宽度
     * @param height      专辑图高度
     * @param duration    歌曲长度（s)
     * @param mode        播放模式
     * @return ID3数据
     */
    public static String getID3Data(String source, String singerName,
                                    String songName, String albumName, JSONArray lyricArray,
                                    int width, int height, int duration, int mode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android|ios|ce");
            JSONObject command = new JSONObject();
            command.put("method", "onMusicInfo");
            command.put("type", 0);
            JSONObject extData = new JSONObject();

            extData.put("source", source);
            extData.put("singerName", singerName);
            extData.put("songName", songName);
            extData.put("albumName", albumName);
//            extData.put("albumArt", albumArt);
//            extData.put("albumSize", albumArt == null ? 0 : albumArt.length);
            extData.put("albumWide", width);
            extData.put("albumHigh", height);
            extData.put("duration", duration);
            extData.put("playlistNum", 1);
            extData.put("songId", "0");
            extData.put("mode", mode);
            extData.put("lyric", lyricArray);
            command.put("extData", extData);
            jsonObject.put("command", command);
            return jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 把int转化为两个字节byte数组
     *
     * @param a 需要转换的int
     * @return 转换结果
     */
    public static byte[] intTo2Byte(int a) {
        return new byte[]{
                (byte) (a & 0xFF),
                (byte) ((a >> 8) & 0xFF)
        };
    }


    /**
     * int 转为4位的byte数组
     *
     * @param a 要转换的int
     * @return 转换后的byte数组
     */
    public static byte[] intToByteArray(int a) {
        return new byte[]{
                (byte) (a & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 24) & 0xFF)
        };
    }

    /**
     * int转byte
     *
     * @param x 需要转换的int
     * @return 转换后的byte
     */
    public static byte intToByte(int x) {
        return (byte) x;
    }

    /**
     * @param musicMark  音源标识 0-255
     * @param commandNum 命令号  10,40010,40020
     * @param byteLen    音频数据长度
     * @param spare      备用字段
     * @param spare1     备用字段
     * @param spare2     备用字段 时间戳 先按照0走，这个是备用字段
     * @return 返回16位各种参数包头byte数组
     */
    public static byte[] getPackHeadInfo(int musicMark, int commandNum
            , int byteLen, int spare, int spare1, int spare2) {

        byte[] packHeadInfo = new byte[16];
        byte[] _markByte = intTo2Byte(PCM_MARK);//返回标识的数组byte数组
        packHeadInfo[0] = _markByte[0];
        packHeadInfo[1] = _markByte[1];

        byte[] commandNumByte = intTo2Byte(commandNum);//命令号byte数组
        packHeadInfo[2] = commandNumByte[0];
        packHeadInfo[3] = commandNumByte[1];

        byte musicMarkByte = intToByte(musicMark);//音源标识byte数组
        packHeadInfo[4] = musicMarkByte;

        byte spareByte = intToByte(spare);//备用字段byte数组
        packHeadInfo[5] = spareByte;

        byte[] spare1Byte = intTo2Byte(spare1);//备用字段byte数组
        packHeadInfo[6] = spare1Byte[0];
        packHeadInfo[7] = spare1Byte[1];

        byte[] currentTimeByte = intToByteArray(spare2);//时间戳byte数组
        System.arraycopy(currentTimeByte, 0, packHeadInfo, 8, currentTimeByte.length);

        byte[] byteLenByte = intToByteArray(byteLen);//音频数据长度byte数组
        System.arraycopy(byteLenByte, 0, packHeadInfo, 12, byteLenByte.length);
        return packHeadInfo;
    }

    /**
     * @param rate         采样率
     * @param channel      声道数  1单声道, 2 双声道
     * @param bit          采样位数 8,16 8位, 16 位
     * @param playMark     播放标识 0-1,0是顺序播放，1是立即播放
     * @param audioNumMark 音频序列标识  0、1、2、3  默认填写3
     * @return 返回封装8位采样byte数组
     */
    public static byte[] getPlayPCMParamsByte(int rate, int channel, int bit, int playMark, int audioNumMark) {

        byte[] pcmParamsByte = new byte[8];
        pcmParamsByte[0] = intToByte(playMark);//播放标识byte数组
        pcmParamsByte[1] = intToByte(audioNumMark);// 音频序列标识byte数组


        byte channelByte = intToByte(channel);//声道byte数组
        pcmParamsByte[2] = channelByte;
        byte bitByte = intToByte(bit);//采样位数byte数组
        pcmParamsByte[3] = bitByte;

        byte[] rateByte = intToByteArray(rate);//采样率byte数组
        System.arraycopy(rateByte, 0, pcmParamsByte, 4, rateByte.length);

        return pcmParamsByte;
    }

    /**
     * 返回向车机发送封装pcm数据
     *
     * @param data         PCM数据
     * @param rate         采样率
     * @param bit          位数
     * @param channel      声道数
     * @param musicMark    音源标识  1是qq音乐，0是导航
     * @param audioNumMark 音源序列
     * @return 返回向车机发送封装pcm数据
     */
    public static byte[] getPackageDataByParam(byte[] data, int rate, int bit, int channel, int musicMark, int audioNumMark) {

        byte[] newData = null;
        try {
            int byteLen = data.length;//pcm数据长度
            //Long now = System.currentTimeMillis();
            byte[] packHeadInfo = getPackHeadInfo(musicMark, COMMAND_TO_CAR_START, byteLen + 8,
                    0, 0, 0);
            byte[] PcmParamsByte = getPlayPCMParamsByte(rate, channel, bit, 0, audioNumMark);
            if (byteLen <= 0) {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
            } else {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length + byteLen;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
                System.arraycopy(data, 0, newData, 24, data.length);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return newData;
    }

    /**
     * @param command ID3命令号
     * @param Data ID3数据
     * return 返回向车机发送封装id3数据
     */
    public static byte[] getid3DataByParam(int command, byte[] Data,byte [] bmpAlbum){

        byte[] newData=null;
        int id3Len = 0;
        int bmpAlbumLen = 0;
        if(Data != null && Data.length != 0) {
            id3Len = Data.length;
        }
        if(bmpAlbum != null && bmpAlbum.length != 0) {
            bmpAlbumLen = bmpAlbum.length;
        }
        try {
            int bytelen =id3Len +bmpAlbumLen;//pcm数据长度
            //Long now = System.currentTimeMillis();
            byte[] packHeadInfo=  getPackHeadInfo(0, command, bytelen+8,
                    0, 0, 0);
            byte[] PcmParamsByte = getID3PCMParamsByte(id3Len, bmpAlbumLen);
            if (bytelen <= 0) {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
            } else {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length + bytelen;
                int bumpAlbumStart = 24 + Data.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
                System.arraycopy(Data, 0, newData, 24, Data.length);
                if (bmpAlbumLen > 0) {
                    System.arraycopy(bmpAlbum, 0, newData, bumpAlbumStart, bmpAlbum.length);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  newData;
    }

    /**
     * @param command ID3命令号
     * @param Data ID3数据
     * return 返回向车机发送封装id3数据
     */
    public static byte[] getid3ProDataByParam(int command, byte[] Data){

        byte[] newData=null;
        int id3Len = 0;
        int bmpAlbumLen = 0;
        if(Data != null && Data.length != 0) {
            id3Len = Data.length;
        }
        try {
            int bytelen =id3Len +bmpAlbumLen;//pcm数据长度
            //Long now = System.currentTimeMillis();
            byte[] packHeadInfo=  getPackHeadInfo(0, command, bytelen+8,
                    0, 0, 0);
            byte[] PcmParamsByte = getID3PCMParamsByte(id3Len, bmpAlbumLen);
            if (bytelen <= 0) {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
            } else {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length + bytelen;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
                System.arraycopy(Data, 0, newData, 24, Data.length);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  newData;
    }

    /**
     * @param id3Len        id3数据长度
     * @param albumLen      专辑图数据长度
     * @return 返回封装8位采样byte数组
     */
    public static byte[] getID3PCMParamsByte(int id3Len, int albumLen) {

        byte[] pcmParamsByte = new byte[8];

        byte[] id3ParamsByte = intToByteArray(id3Len);//id3 byte数组
        System.arraycopy(id3ParamsByte, 0, pcmParamsByte, 0, id3ParamsByte.length);

        byte[] albumParamsByte = intToByteArray(albumLen);//专辑图byte数组
        System.arraycopy(albumParamsByte, 0, pcmParamsByte, 4, albumParamsByte.length);

        return pcmParamsByte;
    }
}
