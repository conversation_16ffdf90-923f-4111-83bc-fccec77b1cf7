package com.autoai.welink.platform.utiliy;

import java.util.ArrayDeque;
import java.util.Deque;

/**
 * <AUTHOR>
 */
public class BooleanQueue {
    private Deque<Boolean> queue;
    private final int maxSize;

    public BooleanQueue(int size) {
        this.queue = new ArrayDeque<>(size);
        this.maxSize = size;
    }

    public void add(Boolean value) {
        if (queue.size() == maxSize) {
            queue.removeFirst();
        }
        queue.addLast(value);
    }

    public Deque<Boolean> getValues() {
        return queue;
    }

    public boolean allValuesSame() {
        if (queue.isEmpty()) {
            return false;
        }

        if (queue.size() < maxSize) {
            return false;
        }

        Boolean firstValue = queue.peekFirst();
        for (Boolean value : queue) {
            if (!value.equals(firstValue)) {
                return false;
            }
        }
        return true;
    }
}
