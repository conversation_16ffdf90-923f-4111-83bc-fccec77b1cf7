package com.autoai.welink.platform.utiliy;

import android.os.Handler;
import android.os.Looper;

public class TimerUtil {

    private Handler handler;
    private Runnable runnable;
    private boolean isTimerRunning = false;
    private TimerCallback callback;

    public interface TimerCallback {
        void onTimerCompleted();
    }

    public TimerUtil(TimerCallback callback) {
        this.callback = callback;
        handler = new Handler(Looper.getMainLooper());
        runnable = new Runnable() {
            @Override
            public void run() {
                if (callback != null) {
                    callback.onTimerCompleted(); // 调用回调方法
                }
            }
        };
    }

    public void startTimer(long delayMillis) {
        stopTimer();
        if (!isTimerRunning) {
            handler.postDelayed(runnable, delayMillis);
            isTimerRunning = true;
        }
    }

    public void stopTimer() {
        if (isTimerRunning) {
            handler.removeCallbacks(runnable);
            isTimerRunning = false;
        }
    }
}