package com.autoai.welink.platform.utiliy;

import android.os.SystemClock;
import android.view.InputDevice;
import android.view.KeyEvent;
import android.view.MotionEvent;

import java.util.ArrayList;
import java.util.List;

public class TouchTranslator {
    public interface TouchEvent {
        void onTouch(MotionEvent motionEvent);
    }

    private TouchEvent touchEvent;
    private int screenWidth, screenHeight;

    MultiTouch multiTouch = new MultiTouch();
    private int num_down_in_one_comm = 0;
    private int num_up_in_one_comm=0;
    private int eventCode = 0;

    List<Integer> xList = new ArrayList<>();//此集合存放多点触控x坐标
    List<Integer> yList = new ArrayList<>();//此集合存放多点触控y坐标
    private boolean isPortrait = false; //Launcher是否是竖屏

    private final float DEFAULT_SIZE = 1.0f;
    private final int DEFAULT_META_STATE = 0;
    private final int DEFAULT_BUTTONSTATE = 0;
    private final float DEFAULT_PRECISION_X = 1.0f;
    private final float DEFAULT_PRECISION_Y = 1.0f;
    private final int DEFAULT_DEVICE_ID = 1;
    private final int DEFAULT_EDGE_FLAGS = 0;
    private final int DEFAULT_FLAGS = 0;
    private long g_down_time;

    public TouchTranslator(int screenWidth, int screenHeight, int videoWidth, int videoHeight, TouchEvent touchEvent) {
        this.touchEvent = touchEvent;
        this.screenWidth = screenWidth;
        this.screenHeight = screenHeight;
        multiTouch.setPhoneWH(screenWidth, screenHeight);
        multiTouch.setDeviceWH(videoWidth, videoHeight);

        if (this.screenWidth < this.screenHeight) {
            isPortrait = true;
        } else {
            isPortrait = false;
        }
        AnFileLog.e("WelinkPlatform", "TouchTranslator screenWidth="+screenWidth+",screenHeight="+screenHeight
                +",videoWidth="+videoWidth+",videoHeight="+videoHeight+",isPortrait="+isPortrait);
    }

    /**
     * 回控处理
     *
     * @param eventString
     */
    public void doTouchEvent(String eventString) {
        AnFileLog.e("WelinkPlatform", "doTouchEvent "+eventString);
        int size = (screenHeight < screenWidth) ? screenHeight : screenWidth;
        try {
            String[] str = eventString.split(" ");

            if ("motionevent".equals(str[0].trim())) {

                switch (str[3]) {
                    case "down":
                        eventCode = MotionEvent.ACTION_DOWN;
                        break;
                    case "move":
                        eventCode = MotionEvent.ACTION_MOVE;
                        break;
                    case "up":
                        eventCode = MotionEvent.ACTION_UP;
                        break;
                }
                int cx = Integer.parseInt(str[1]);
                int cy = Integer.parseInt(str[2]);
                if (isPortrait) {
                    //mWLCodecController.onTouchEvent(eventCode, cx, cy);
                    parseMotionEvent(eventCode, cx, cy);
                } else {
                    int sx = cy;
                    int sy = size - cx;
                    //mWLCodecController.onTouchEvent(eventCode, sx, sy);
                    parseMotionEvent(eventCode, sx, sy);
                }
            } else if ("motionevent2".equals(str[0].trim())) {

                if ("comm".equals(str[str.length - 1].trim())) {
                    int[] xArr = new int[xList.size()];
                    int[] yArr = new int[yList.size()];

                    for (int i = 0; i < xList.size(); i++) {
                        xArr[i] = xList.get(i);
                        yArr[i] = yList.get(i);
                    }
                    xList.clear();
                    yList.clear();
                    //mWLCodecController.onTouchEvent(eventCode, xArr, yArr);
                    parseMotionEvent(eventCode, xArr, yArr);
                    return;
                }
                int phoneWidth = screenWidth;
                int phoneHeight = screenHeight;
                if (phoneHeight < phoneWidth) {
                    int tmp = phoneHeight;
                    phoneHeight = phoneWidth;
                    phoneWidth = tmp;
                }
                Integer cx = (Integer.parseInt(str[1]) * phoneWidth) >> 11;
                Integer cy = (Integer.parseInt(str[2]) * phoneHeight) >> 11;

                if (isPortrait) {
                    xList.add(Integer.valueOf(cx));
                    yList.add(Integer.valueOf(cy));
                } else {
                    int x = cy;
                    int y = size - cx;

                    xList.add(Integer.valueOf(x));
                    yList.add(Integer.valueOf(y));
                }


                if ("down".equals(str[str.length - 1].trim())) {
                    eventCode = MotionEvent.ACTION_DOWN;
                } else if ("move".equals(str[str.length - 1].trim())) {
                    eventCode = MotionEvent.ACTION_MOVE;
                } else if ("up".equals(str[str.length - 1].trim())) {
                    eventCode = MotionEvent.ACTION_UP;
                }
            } else if ("motionevent3".equals(str[0].trim())) {
                if("comm".equals(str[str.length-1].trim())){
                    int[] xArr = new int[xList.size()];
                    int[] yArr = new int[yList.size()];
                    for (int i = 0; i < xList.size(); i++) {
                        xArr[i] = xList.get(i);
                        yArr[i] = yList.get(i);
                    }
                    xList.clear();
                    yList.clear();
                    if (xArr.length > 0) {
                        if (multiTouch.tp.size() == 1) {
                            //mWLCodecController.onTouchEvent(eventCode, xArr[0], yArr[0]);
                            parseMotionEvent(eventCode, xArr[0], yArr[0]);
                        } else {
                            MultiTouch.TouchPoint tp = multiTouch.getAll();
                            //多点触控现在只通过num_down_in_one_comm和num_up_in_one_comm处理了两个点的down或up在同一个comm段里发来的情况
                            //TODO:如果需要支持完善的三点触控,需要考虑并处理3个点的down或up在同一个comm段里的情况
                            if (num_down_in_one_comm > 1) {
                                parseMotionEvent(eventCode, tp.x[0], tp.y[0]);
                            }
                            parseMotionEvent(eventCode, tp.x, tp.y);
                            if (num_up_in_one_comm > 1) {
                                parseMotionEvent(eventCode, tp.x[0], tp.y[0]);
                            }
                        }
                    }
                    num_down_in_one_comm = 0;
                    num_up_in_one_comm=0;
                    multiTouch.removeUp();
                    return;
                }
                Integer cx = Integer.parseInt(str[1]) * multiTouch.wPhone / multiTouch.wDevice;

                Integer cy = Integer.parseInt(str[2]) * multiTouch.hPhone / multiTouch.hDevice;
                xList.add(cx);
                yList.add(cy);

                if ("down".equals(str[str.length - 1].trim())) {
                    eventCode = MotionEvent.ACTION_DOWN;
                    num_down_in_one_comm++;
                } else if ("move".equals(str[str.length - 1].trim())) {
                    eventCode = MotionEvent.ACTION_MOVE;
                } else if ("up".equals(str[str.length - 1].trim())) {
                    eventCode = MotionEvent.ACTION_UP;
                    num_up_in_one_comm++;
                }

                Integer id = Integer.parseInt(str[3]);
                multiTouch.addPoint(id, eventCode, cx, cy);

            } else if ("keyevent".equals(str[0].trim())) {
                if (str.length >= 1) {
                    int KEY_CODE = Integer.parseInt(str[1]);
                    //mWLCodecController.onKeyEvent(KEY_CODE, KeyEvent.ACTION_DOWN);
                    parseKeyEvent(KEY_CODE, KeyEvent.ACTION_DOWN);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void parseMotionEvent(int type, int x, int y) {
        long now = SystemClock.uptimeMillis();
        switch (type) {
            case MotionEvent.ACTION_DOWN:
                g_down_time = now;
                justMotionEventInput(MotionEvent.ACTION_DOWN, now, x, y, 1.0f);
                break;
            case MotionEvent.ACTION_MOVE:
                justMotionEventInput(MotionEvent.ACTION_MOVE, now, x, y, 1.0f);
                break;
            case MotionEvent.ACTION_UP:
                justMotionEventInput(MotionEvent.ACTION_UP, now, x, y, 0.0f);
                break;
        }
    }

    public void parseMotionEvent(int type, int[] x, int[] y) {
        long now = SystemClock.uptimeMillis();
        switch (type) {
            case MotionEvent.ACTION_DOWN:
                justMotionEventInput(
                        x.length == 2 ? MotionEvent.ACTION_POINTER_DOWN | 0x0100
                                : MotionEvent.ACTION_POINTER_DOWN | 0x0200, now, x, y,
                        1.0f);
                break;
            case MotionEvent.ACTION_MOVE:
                justMotionEventInput(MotionEvent.ACTION_MOVE, now, x, y, 1.0f);
                break;
            case MotionEvent.ACTION_UP:
                justMotionEventInput(
                        x.length == 3 ? MotionEvent.ACTION_POINTER_UP | 0x0200
                                : (x.length == 2 ? MotionEvent.ACTION_POINTER_UP | 0x0100 : MotionEvent.ACTION_UP), now, x, y, 0.0f);
                break;
        }
    }

    private void justMotionEventInput(int action, long when, int x, int y,
                                      float pressure) {

        MotionEvent motionEvent = MotionEvent.obtain(g_down_time, when, action,
                x, y, pressure, DEFAULT_SIZE, DEFAULT_META_STATE,
                DEFAULT_PRECISION_X, DEFAULT_PRECISION_Y, DEFAULT_DEVICE_ID,
                DEFAULT_EDGE_FLAGS);
        motionEvent.setSource(InputDevice.SOURCE_TOUCHSCREEN);
        invokMotionEventeMethod(motionEvent);
    }

    private void justMotionEventInput(int action, long when, int[] x, int[] y,
                                      float pressure) {
        int pointerCount = Math.min(x.length, y.length);
        MotionEvent.PointerProperties[] pointerProperties = new MotionEvent.PointerProperties[pointerCount];
        MotionEvent.PointerCoords[] pointerCoords = new MotionEvent.PointerCoords[pointerCount];

//        StringBuffer logStringBuffer = new StringBuffer("pointerCount = "
//                + pointerCount + "\taction = " + action + "\tdownTime = "
//                + g_down_time + "\teventTime = " + when + "\tpressure = "
//                + pressure);
        for (int i = 0; i < pointerCount; i++) {
            MotionEvent.PointerProperties pps = new MotionEvent.PointerProperties();
            pps.id = i;
            pps.toolType = MotionEvent.TOOL_TYPE_FINGER;
            pointerProperties[i] = pps;

            MotionEvent.PointerCoords pcs = new MotionEvent.PointerCoords();
            pcs.pressure = pressure;
            pcs.x = x[i];
            pcs.y = y[i];
            pointerCoords[i] = pcs;

//            logStringBuffer.append("\tpointerCoords[" + i + "] = [" + x[i]
//                    + ", " + y[i] + "]");
        }

        MotionEvent motionEvent = MotionEvent.obtain(g_down_time, when, action,
                pointerCount, pointerProperties, pointerCoords,
                DEFAULT_META_STATE, DEFAULT_BUTTONSTATE, DEFAULT_PRECISION_X,
                DEFAULT_PRECISION_Y, DEFAULT_DEVICE_ID, DEFAULT_EDGE_FLAGS,
                InputDevice.SOURCE_TOUCHSCREEN, DEFAULT_FLAGS);
        invokMotionEventeMethod(motionEvent);
    }

    private void invokMotionEventeMethod(final MotionEvent motionEvent) {
        AnFileLog.e("wlplatform", "run: TouchTranslator invokMotionEventeMethod x:" + motionEvent.getX() + "     y:" + motionEvent.getY());
        touchEvent.onTouch(motionEvent);
    }

    public void parseKeyEvent(final int keyCode, final int action) {
//        if (mHandler == null) return;
//        mHandler.post(new Runnable() {
//            @Override
//            public void run() {
                long now = SystemClock.uptimeMillis();
                KeyEvent down = new KeyEvent(now, now, action, keyCode, 0);
//                BaseModuleActivity activity = (BaseModuleActivity) mContext;
//                activity.dispatchKeyEventFromHu(down);
//            }
//        });
    }
}
