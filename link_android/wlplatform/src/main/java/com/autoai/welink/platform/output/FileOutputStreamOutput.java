package com.autoai.welink.platform.output;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class FileOutputStreamOutput extends FileOutput {
    private FileOutputStream fileOutputStream;

    public FileOutputStreamOutput(String filePath) {
        super(filePath);
        try {
            fileOutputStream = new FileOutputStream(filePath,true);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void write(byte[] data) {
        if (fileOutputStream != null) {
            try {
                fileOutputStream.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void close() {
        if (fileOutputStream != null) {
            try {
                fileOutputStream.close();
                fileOutputStream = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
