package com.autoai.welink.platform.utiliy;

import android.app.Notification;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.media.MediaPlayer;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.IBinder;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;

import com.autoai.welink.platform.R;

import java.io.IOException;
import java.io.InputStream;
import java.util.Locale;

public class AwakeLock extends Service {
    private final static int NOTIFICATION_ID = android.os.Process.myPid();
    private WifiManager.WifiLock wifiLock = null;
    private MediaPlayer mediaPlayer = null;
    private Thread audioTrackThread = null;

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    WindowManager windowManager;
    @Override
    public void onCreate() {
        super.onCreate();
        WifiManager wifiManager = (WifiManager) this.getSystemService(Context.WIFI_SERVICE);
        if (wifiManager != null) {
            wifiLock = wifiManager.createWifiLock(WifiManager.WIFI_MODE_FULL_HIGH_PERF, "WeLinkPlatform::WifiLock");
            wifiLock.acquire();
        }
    }
    TextView tv;
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (android.os.Build.VERSION.SDK_INT < Build.VERSION_CODES.O ||
                intent == null || intent.getAction() == null) {
            return START_STICKY;
        }

        if ("com.autoai.welink.platform.KeepAwake".equals(intent.getAction())) {
            final Notification notification = intent.getParcelableExtra("Notification");
            if (notification != null) {
                startForeground(NOTIFICATION_ID, notification);
            }
            boolean canDrawOverlays = Settings.canDrawOverlays(this);
            AnFileLog.e("AwakeLock", "canDrawOverlays:" + canDrawOverlays);
            if(windowManager == null && canDrawOverlays) {
                windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
            }else{
                windowManager = null;
            }
            if(windowManager != null) {
                tv = new TextView(this);
                tv.setText("TEST--");
                tv.setTextColor(getColor(android.R.color.holo_orange_dark));
                tv.setAlpha(0.01f);
                windowManager.addView(tv, getPopViewWindowLayoutParams());
            }
            if (wifiLock != null) {
                wifiLock.acquire();
            }

            if (isHuawei() || isHonor() || isXiaomi()) {
                //华为、荣耀、小米手机使用MediaPlayer保活时，会错误输出蓝牙播放状态，因此改为AudioTrack方式
                //MediaPlayer目前还未遇见过无法保活的，但AudioTrack在Nubia手机上无法保活，因此除华为荣耀外，其他手机继续使用MediaPlayer保活

                audioTrackThread = new Thread() {
                    @Override
                    public void run() {
                        playWithAudioTrack(this, R.raw.loud);
                    }
                };
                audioTrackThread.start();
            } else {
                audioTrackThread = new Thread() {
                    @Override
                    public void run() {
                        playWithAudioTrack(this, R.raw.loud);
                    }
                };
                audioTrackThread.start();

                mediaPlayer = MediaPlayer.create(this, R.raw.loud);
                if (mediaPlayer != null) {
                    mediaPlayer.setLooping(true);
                    mediaPlayer.setWakeMode(this, PowerManager.PARTIAL_WAKE_LOCK);
                    mediaPlayer.setVolume(0, 0);
                    mediaPlayer.start();
                }
            }
        }

        return START_NOT_STICKY;
    }
    private ViewGroup.LayoutParams getPopViewWindowLayoutParams(){
        WindowManager.LayoutParams  popViewLayoutParams = new WindowManager.LayoutParams(100, 100, 0, 0, PixelFormat.TRANSPARENT);
        popViewLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        popViewLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        //对齐方式不允许再改了
        popViewLayoutParams.gravity = Gravity.START | Gravity.TOP;
        popViewLayoutParams.x = 10;
        popViewLayoutParams.y = 0;
        return popViewLayoutParams;
    }
    @Override
    public void onDestroy() {
        super.onDestroy();

        if (audioTrackThread != null) {
            if (audioTrackThread.isInterrupted()) {
                audioTrackThread.interrupt();
            }
            audioTrackThread = null;
        }

        if (mediaPlayer != null) {
            if (mediaPlayer.isPlaying()) {
                mediaPlayer.stop();
            }
            mediaPlayer.release();
            mediaPlayer = null;
        }
        if (wifiLock != null && wifiLock.isHeld()) {
            wifiLock.release();
        }
        wifiLock = null;

        stopForeground(true);
    }

    private Context context;
    private boolean isLock;

    public static AwakeLock create(Context context) {
        AwakeLock awakeLock = new AwakeLock();
        awakeLock.context = context;
        awakeLock.isLock = false;

        return awakeLock;
    }

    public synchronized void lock(Notification notification) {
        if (isLock) {
            return;
        }

        isLock = true;

        Intent intent = new Intent("com.autoai.welink.platform.KeepAwake")
                .setPackage(context.getPackageName())
                .putExtra("Notification", notification);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }

    public synchronized void unlock() {
        if (!isLock) {
            return;
        }
        if(windowManager != null && tv != null) {
            windowManager.removeView(tv);
        }
        isLock = false;

        Intent intent = new Intent("com.autoai.welink.platform.KeepAwake")
                .setPackage(context.getPackageName());

        context.stopService(intent);
    }

    private void playWithAudioTrack(Thread thread, int resid) {
        InputStream is = getResources().openRawResource(resid);
        WaveHeader header = new WaveHeader();

        try {
            header.read(is);
        } catch (IOException e) {
            return;
        }

        byte[] music = new byte[header.getSampleRate() * header.getNumChannels() * header.getBitsPerSample() / 8 / 1000];

        AudioTrack at = new AudioTrack(
                new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build(),
                new AudioFormat.Builder()
                        .setSampleRate(header.getSampleRate())
                        .setChannelMask(header.getNumChannels() == 1 ? AudioFormat.CHANNEL_OUT_MONO : AudioFormat.CHANNEL_OUT_STEREO)
                        .setEncoding(header.getBitsPerSample() == 16 ? AudioFormat.ENCODING_PCM_16BIT : AudioFormat.ENCODING_PCM_8BIT)
                        .build(),
                AudioTrack.getMinBufferSize(header.getSampleRate(),
                        header.getNumChannels() == 1 ? AudioFormat.CHANNEL_OUT_MONO : AudioFormat.CHANNEL_OUT_STEREO,
                        header.getBitsPerSample() == 16 ? AudioFormat.ENCODING_PCM_16BIT : AudioFormat.ENCODING_PCM_8BIT),
                AudioTrack.MODE_STREAM,
                AudioManager.AUDIO_SESSION_ID_GENERATE
        );

        at.play();

        while (!thread.isInterrupted()) {
            at.write(music, 0, 0);
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                return;
            }
        }

        at.stop();
        at.release();
    }

    private boolean isHuawei() {
        if (Build.BRAND == null) {
            return false;
        } else {
            return "huawei".equals(Build.BRAND.toLowerCase(Locale.getDefault()));
        }
    }

    private boolean isHonor() {
        if (Build.BRAND == null) {
            return false;
        } else {
            return "honor".equals(Build.BRAND.toLowerCase(Locale.getDefault()));
        }
    }
    private boolean isXiaomi() {
        if (Build.BRAND == null) {
            return false;
        } else {
            return "xiaomi".equals(Build.BRAND.toLowerCase(Locale.getDefault()));
        }
    }
}
