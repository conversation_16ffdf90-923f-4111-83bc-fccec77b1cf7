<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.autoai.welink.platform" >
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <application>
        <service
            android:name="com.autoai.welink.platform.service.WeLinkService"
            android:process=":wlprimary"
            android:exported="false"
            android:permission="com.autoai.welink.platform">
            <intent-filter>
                <action android:name="com.autoai.welink.platform.PrimaryScreen" />
            </intent-filter>
        </service>

        <service
            android:name="com.autoai.welink.platform.utiliy.AwakeLock"
            android:process=":wlprimary"
            android:exported="false"
            android:foregroundServiceType="connectedDevice|dataSync|mediaPlayback"
            android:permission="com.autoai.welink.platform">
            <intent-filter>
                <action android:name="com.autoai.welink.platform.KeepAwake" />
            </intent-filter>
        </service>
    </application>
</manifest>