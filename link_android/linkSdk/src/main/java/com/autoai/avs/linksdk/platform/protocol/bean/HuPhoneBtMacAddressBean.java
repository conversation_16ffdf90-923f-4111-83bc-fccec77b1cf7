package com.autoai.avs.linksdk.platform.protocol.bean;

import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 车机端向手机端发送手机蓝牙MAC地址的协议Bean
 * 用于处理onPhoneBTMacAddress协议消息
 * 
 * <AUTHOR>
 */
public class HuPhoneBtMacAddressBean extends BaseProtocolBean {
    
    /**
     * 手机蓝牙MAC地址
     */
    private String macAddress;
    
    /**
     * 获取MAC地址
     * @return MAC地址字符串，可能为空
     */
    public String getMacAddress() {
        return macAddress;
    }
    
    @Override
    public String toString() {
        return "HuPhoneBtMacAddressBean{" +
                "macAddress='" + macAddress + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }
    
    /**
     * 解析协议数据
     * @param extData 协议扩展数据
     * @throws JSONException JSON解析异常
     */
    @Override
    public void parse(JSONObject extData) throws JSONException {
        if (extData != null) {
            this.macAddress = extData.optString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_MAC_ADDRESS, "");
        } else {
            this.macAddress = "";
        }
    }
}
