package com.autoai.avs.linksdk.platform;

import androidx.annotation.Keep;

/**
 * 定义在三投模块中需要的常量。
 *
 * <AUTHOR>
 */
@Keep
public class WLPlatformConfig {

    private WLPlatformConfig() {
    }

    @Keep
    public static final String WL_PLATFORM_COMMAND_TYPE = "Type";
    @Keep
    public static final String WL_PLATFORM_COMMAND_MUSIC = "Music";
    @Keep
    public static final String WL_PLATFORM_COMMAND_NAVI = "Navi";
    @Keep
    public static final String WL_PLATFORM_COMMAND_ALL = "All";
    @Keep
    public static final String WL_PLATFORM_CONNECT_KEY = "welink-connection";
    @Keep
    public static final String WL_PLATFORM_PACKAGE_NAME = "package-name";
    @Keep
    public static final String WL_PLATFORM_CONNECT_ACTION = "com.autoai.welink.CONNECTION";
    @Keep
    public static final String WL_PLATFORM_REQUEST_ACTION = "com.autoai.welink.REQUEST_CONNECTION";
    @Keep
    public static final String WL_PLATFORM_BUNDLE_PACKAGE_NAME = "packageName";
    @Keep
    public static final String WL_PLATFORM_BUNDLE_CONNECT_KEY = "connectKey";

    /**
     * isConnectcar这个参数别删除，作为public的，其他业务模块在使用了
     */
    @Keep
    public static boolean isConnectcar = false;
    @Keep
    public static final int WL_PLATFORM_OBSERVER_CONNECT_STAET = 100;
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_CONNECT_STATUS = "isConnectCar";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_CONNECT_TYPE = "isAOAConnect";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_BLELINK_ERROR = "onBleLinkError";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_BLELINK_STATUS = "onBleLinkStatus";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_BLELINK_TIMEOUT_SCAN = "onBleLinkScanTimeout";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_BLELINK_TIMEOUT_JOIN = "onBleLinkJoinTimeout";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_LINK_START = "onLinkStart";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_LINK_STOP = "onLinkStop";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_CONNECTOR_CONNECT = "onConnected";
    @Keep
    public static final String WL_PLATFORM_MESSAGE_TAG_CONNECTOR_DISCONNECT = "onDisconnected";
    @Keep
    public static int WL_PLATFORM_BLELINK_CHANNEL_AP32 = 0;


    /**
     * 手机端录制的视频尺寸是否为方形;
     */
    public static boolean videoSizeSquare = false;

    static final boolean IS_VIDEO_TEST_MODEL = false;
}
