package com.autoai.avs.linksdk.platform.audio.transport;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.audio.listener.TransportListener;
import com.autoai.avs.linksdk.platform.pcm.packagePlasticEngine.PackagePlasticEngine;
import com.autoai.link.threadpool.ThreadPoolUtil;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * Welink Tts PCM音频数据分包处理类，主要负责Tts音频数据分包处理。
 *
 * <AUTHOR>
 */
public class TTSTransport extends BaseTransport {
    private volatile boolean isStopTtsThread = false;
    private volatile boolean isFinishTransport = false;
    private final LinkedBlockingQueue<byte[]> mVectorLinkedBlockingQueue = new LinkedBlockingQueue<>(50);
    private PackagePlasticEngine mPackagePlasticEngine;
    private final int mPackagePlasticSize;
    private final Runnable startRunnable = new Runnable() {
        @Override
        public void run() {
            if (isStopTtsThread) {
                return;
            }
            if (!mVectorLinkedBlockingQueue.isEmpty()) {
                byte[] bytes = new byte[0];
                try {
                    bytes = mVectorLinkedBlockingQueue.take();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                WlLinkSdkLog.i("TTSTransportmTTSThread ----onTTSData----111---> mVector size:" + mVectorLinkedBlockingQueue.size()
                        + ",delayTime:" + delayTime);
                mTransportListener.onTTSData(bytes, false);

                if (!isStopTtsThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(onTtsDataRunnable, delayTime, TimeUnit.MILLISECONDS);
                }
            } else {
                WlLinkSdkLog.i("TTSTransportmTTSThread ----onTTSData----222---> mVector size:" + mVectorLinkedBlockingQueue.size()
                        + ",delayTime:0");
                if (!isStopTtsThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(onTtsDataRunnable, 0, TimeUnit.MILLISECONDS);
                }
            }
        }
    };
    private final Runnable onTtsDataRunnable = new Runnable() {
        @Override
        public void run() {
            if (isStopTtsThread) {
                return;
            }
            if (isFinishTransport && mVectorLinkedBlockingQueue.size() == 0 && mPackagePlasticEngine != null) {
                PackagePlasticEngine.PackageELF elf = mPackagePlasticEngine.getTailPackageELF();

                if (elf != null && elf.packageData != null) {
                    WlLinkSdkLog.i("TTSTransportmTTSThread ----onTTSData----333---> mVector size:" + mVectorLinkedBlockingQueue.size());
                    mTransportListener.onTTSData(elf.packageData, true);
                    stopTransportThread();
                } else {
                    WlLinkSdkLog.i("TTSTransportmTTSThread ----onTTSData----333---> mVector size:" + mVectorLinkedBlockingQueue.size());
                    mTransportListener.onTTSData(new byte[0], true);
                    stopTransportThread();
                }
            }
            if (!isStopTtsThread) {
                ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(startRunnable, 0, TimeUnit.MILLISECONDS);
            }
        }
    };

    public TTSTransport(TransportListener mTransportListener, int rate, int bit, int channels) {
        super(mTransportListener, rate, bit, channels);
        if (16000 == rate) {
            mPackagePlasticSize = 1024 * 8;
        } else {
            mPackagePlasticSize = 1024 * 32;
        }
    }

    /**
     * 依据互联状态设置音频数据提前发送的时间
     *
     * @param flag 互联状态
     */
    @Override
    public void setConnectCar(boolean flag) {
        super.setConnectCar(flag);
    }

    /**
     * 接收解码数据进行分包处理
     *
     * @param pcmData 解码数据
     */
    @Override
    public void producePcm(byte[] pcmData) {
        if (mPackagePlasticEngine == null) {
            return;
        }
        List<PackagePlasticEngine.PackageELF> elfList = mPackagePlasticEngine.plastic(pcmData);
        if (elfList != null && elfList.size() > 0) {
            for (PackagePlasticEngine.PackageELF elf : elfList) {
                try {
                    mVectorLinkedBlockingQueue.put(elf.packageData);
                    WlLinkSdkLog.i("TTSTransportproducePcm mVectorLinkedBlockingQeque size:" + mVectorLinkedBlockingQueue.size()
                            + ",ThreadId:" + Thread.currentThread().getId());
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 解码数据传输完毕
     */
    public void finishTransport() {
        WlLinkSdkLog.i("TTSTransportfinishTransport ---------->");
        this.isFinishTransport = true;
    }

    /**
     * 开始分包传输线程
     */
    @Override
    public void startTransportThread() {
        WlLinkSdkLog.i("TTSTransportstartTransportThread -------------->");
        this.isStopTtsThread = false;
        this.isFinishTransport = false;
        synchronized (this) {
            this.mPackagePlasticEngine = new PackagePlasticEngine(mPackagePlasticSize);
            ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(startRunnable, 0, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 继续开始分包传输线程
     */
    @Override
    public void resumeTransportThread() {
        WlLinkSdkLog.i("TTSTransportresumeTransportThread -------------->");
        if (!isStopTtsThread) {
            return;
        }
        this.isStopTtsThread = false;
        ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(startRunnable, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 暂停分包传输线程
     */
    @Override
    public void pauseTransportThread() {
        WlLinkSdkLog.i("TTSTransportpauseTransportThread -------------->");
        if (isStopTtsThread) {
            return;
        }
        this.isStopTtsThread = true;
    }

    /**
     * 停止分包传输线程
     */
    @Override
    public void stopTransportThread() {
        WlLinkSdkLog.i("TTSTransportstopTransportThread -------------->");
        synchronized (this) {
            this.isStopTtsThread = true;
            if (mPackagePlasticEngine != null) {
                mPackagePlasticEngine.destroy();
                mPackagePlasticEngine = null;
            }
            mVectorLinkedBlockingQueue.clear();
        }
    }
}
