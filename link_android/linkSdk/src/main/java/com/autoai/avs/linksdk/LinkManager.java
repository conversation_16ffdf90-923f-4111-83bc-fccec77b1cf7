package com.autoai.avs.linksdk;

import static android.hardware.usb.UsbManager.EXTRA_ACCESSORY;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.autoai.avs.linksdk.listener.LinkState;
import com.autoai.avs.linksdk.listener.LinkStateChangeListener;
import com.autoai.avs.linksdk.platform.WLPlatformManager;
import com.autoai.avs.linksdk.platform.listener.WeLinkPlatformListener;
import com.autoai.avs.linksdk.util.AppUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 互联的管理器, 以及整体和状态交互的统一入口.
 * <p>
 * 使用步骤如下:
 * 1. 通过 {@link #getInstance()} 获取实例, 并添加监听, 监听相应状态并做相应处理;
 * 2. 调用 {@link #startLink(Activity)} 开始互联;
 * 3. 在 Activity.onActivityResult() 方法中, 回调 {@link #onActivityResult(int, int, Intent)} 方法传入结果.
 * 3. 需要断开时调用 {@link #stopLink()} .
 */
public class LinkManager {
    /**
     * 是否调试模式
     */
    public static final boolean IS_DEBUG = BuildConfig.DEBUG;

    private int linkState = LinkState.STATE_IDLE;
    private List<LinkStateChangeListener> linkStateChangeListeners = new ArrayList<>();

    /**
     * 添加状态变化监听
     */
    public void addLinkStateChangeListener(@NonNull LinkStateChangeListener listener) {
        if (linkStateChangeListeners == null) {
            linkStateChangeListeners = new ArrayList<>();
        }
        linkStateChangeListeners.add(listener);
    }

    /**
     * 移除状态变化监听
     */
    public void removeLinkStateChangeListener(@NonNull LinkStateChangeListener listener) {
        if (linkStateChangeListeners != null) {
            linkStateChangeListeners.remove(listener);
        }
    }

    private final WLPlatformManager platformManager;
    private Application application;

    private boolean hasInitPlatform = false;

    private LinkManager() {
        platformManager = WLPlatformManager.getInstance();
    }

    public static LinkManager getInstance() {
        return INSTANCE.instance;
    }

    private static class INSTANCE {
        private static final LinkManager instance = new LinkManager();
    }

    private final WeLinkPlatformListener linkPlatformListener = new WeLinkPlatformListener() {
        /**
         * 开始互联
         */
        public void onLinkStart(boolean isAOA) {
            dispatchLinkState(LinkState.STATE_CONNECTING);
        }

        /**
         * 互联成功
         */
        public void onLinkConnected(String vehicleType, boolean isAOA) {
            dispatchLinkState(LinkState.STATE_CONNECTED);
        }

        /**
         * 断开互联
         */
        public void onLinkDisconnected() {
            dispatchLinkState(LinkState.STATE_DISCONNECT);
        }

        /**
         * 无感互联错误状态提示
         * status - 错误码 1: 设备不支持蓝牙BLE 2: 没有打开蓝牙 3: 没有打开Wi-Fi 4: 需要请求定位权限(ACCESS_FINE_LOCATION) 5: 服务发生异常 6: 创建Wi-Fi Direct GO失败
         */
        public void onBleLinkError(int error) {
            if (error > 0) {
                dispatchLinkState(LinkState.STATE_ERROR);
            }
        }

        /**
         * 无感互联连接中状态提示
         * status - 状态码
         *         0: Wi-Fi Direct GO广播，content: GO签名 1: BLE设备连接中... 2: BLE设备连接成功 3: BLE服务连接中... 4: BLE服务连接成功5: 读取版本中...
         *         6: 版本读取成功，content：版本号 7: 写Wi-Fi Direct GO签名 8: 签名写成功，content：签名9: Wi-Fi Direct GC连接，content: 网络密码10: 未找到对应包名的硬件心跳
         *         -1: 设备连接失败 -2: 服务连接失败 -3: 版本读取失败 -4: 签名写失败 -5: 操作超时
         */
        public void onBleLinkStatus(int status) {
//            if (status < 0) {
//                dispatchLinkState(LinkState.STATE_ERROR);
//            }
        }

        /**
         * 无感互联一直没有扫描到车机超时
         */
        public void onBleLinkScanTimeout() {
            dispatchLinkState(LinkState.STATE_NO_DEVICES);
        }

        /**
         * 无感互联扫描到车机但一直没有互联上超时
         */
        public void onBleLinkJoinTimeout() {
            dispatchLinkState(LinkState.STATE_ERROR);
        }
    };

    private void dispatchLinkState(@LinkState.State int state) {
        linkState = state;
        if (linkStateChangeListeners != null) {
            for (LinkStateChangeListener listener : linkStateChangeListeners) {
                listener.onChange(state);
            }
        }
    }

    /**
     * 获取当前的互联状态
     */
    public int getLinkState() {
        return linkState;
    }

    /**
     * 开始互联:
     * <p>
     * 对于无线连接: 搜索-->发现设备并连接-->启动全投屏(需要动态获取权限)-->完成连接-->退出连接
     * 对于USB连接: (已连接 USB)建立内部连接-->启动全投屏(需要动态获取权限)-->完成连接-->退出连接
     */
    public void startLink(@NonNull Activity activity) {
        WlLinkSdkLog.w("WelinService绑定 WLPlatform startLink 1 ");
        if (!hasInitPlatform) {
            application = activity.getApplication();
            WlLinkSdkLog.w("WelinService绑定 WLPlatform startLink 2 ");
            platformManager.init(activity, null, linkPlatformListener, IS_DEBUG);
            hasInitPlatform = true;
        }
        boolean canAoa = false;
        Intent intent = activity.getIntent();
        if (intent != null) {
            WlLinkSdkLog.d("LinkManager WLPlatform startLink:: intent action:" + activity.getIntent().getAction() + "Extra: " + AppUtils.bundleToString(activity.getIntent().getExtras()));
            if (intent.hasExtra(EXTRA_ACCESSORY)) {
                canAoa = platformManager.startAoaLink(intent);
            } else {
                UsbManager usbManager = (UsbManager) application.getSystemService(Context.USB_SERVICE);
                if (usbManager.getAccessoryList() != null && usbManager.getAccessoryList().length > 0) {
                    WlLinkSdkLog.d("LinkManager Get UsbAccessory from usbManager: " + usbManager.getAccessoryList()[0]);
                    intent.putExtra(EXTRA_ACCESSORY, usbManager.getAccessoryList()[0]);
                    WlLinkSdkLog.w("WelinService绑定 WLPlatform 开始调用startAoaLink");
                    canAoa = platformManager.startAoaLink(intent);
                }
            }
        }

        if (!canAoa) {
            WlLinkSdkLog.d("LinkManager startDirectLink");
            platformManager.startDirectLink();
        }
    }


    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        platformManager.activityResult(requestCode, resultCode, data);
    }

    /**
     * 停止投屏
     */
    public void stopLink() {
        if (platformManager != null) {
            platformManager.stopLink();
            platformManager.stopDirectLink();
        }
    }

    public Context getApplicationContext() {
        if (application != null) {
            return application.getApplicationContext();
        } else {
            return null;
        }
    }

}