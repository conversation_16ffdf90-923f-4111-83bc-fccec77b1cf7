package com.autoai.avs.linksdk.util;

import androidx.annotation.Keep;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Welink 车机sn码功能管理类，主要负责实现依据sn码判断是否支持指定的功能
 *
 */
public class HUSupportManager implements IHUSupportManager{
    private final Map<String, List<String>> sSupportList;
    //车载音源
    public static final String VEHICLE_BORNE_SOUND_SOURCE = "0000001";
    //流量查询
    public static final String ACCOUNT_FLOW_QUERY = "0000002";
    //小地图功能
    public static final String MINI_NAVI_CHANGE = "0000003";
    //安全功能
    public static final String CANDATA_SECURITY = "0000004";

    //车机SN码伟世通
    public static final String HU_SN_CRS = "HPCRS30LHLNARMIMX6DL047";
    //车机SN码安波福
    public static final String HU_SN_MIB2 = "HPSQDZQNX650LHLNTIJ6PLUS083";
    //车机SN码上汽37w
    public static final String HU_SN_37W = "CPSVW37WLHLNNXPIMX6093";

    /**
     * 互联以后从车机端获取到的SN码
     * 车机类型
     */
    private static final MutableLiveData<String> sSNLiveData = new MutableLiveData<>();

    public HUSupportManager() {
        sSupportList = new HashMap<>(8);

        List<String> miblist = new ArrayList<>(8);
        sSupportList.put(HU_SN_MIB2, miblist);

        List<String> crslist = new ArrayList<>(8);
        crslist.add(VEHICLE_BORNE_SOUND_SOURCE);
        crslist.add(ACCOUNT_FLOW_QUERY);
        crslist.add(MINI_NAVI_CHANGE);
        sSupportList.put(HU_SN_CRS, crslist);

        List<String> w37list = new ArrayList<>(8);
        w37list.add(CANDATA_SECURITY);
        sSupportList.put(HU_SN_37W, w37list);
    }

    @Override
    public void setSN(String aSN) {
        sSNLiveData.postValue(aSN);
    }

    /**
     * 获取SN，用于控制皮肤更换，UI更换，不同的车机使用不同的UI和皮肤
     **/
    @Keep
    public static LiveData<String> getSN() {
        return sSNLiveData;
    }

    /**
     * 是否支持属性功能
     **/
    @Override
    public boolean isHuSupport(String aName) {
        String sSN = sSNLiveData.getValue();
        return sSN != null
                && sSupportList != null
                && sSupportList.containsKey(sSN)
                && sSupportList.get(sSN).contains(aName);
    }

    @Override
    public boolean isLandscape() {
        return false;
    }
}
