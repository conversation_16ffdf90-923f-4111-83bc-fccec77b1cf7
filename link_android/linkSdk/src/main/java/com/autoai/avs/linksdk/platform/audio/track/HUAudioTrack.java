package com.autoai.avs.linksdk.platform.audio.track;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.WLConnectManager;
import com.autoai.avs.linksdk.platform.audio.WLAudioConfig;
import com.autoai.avs.linksdk.platform.audio.bean.WLAudioType;
import com.autoai.avs.linksdk.platform.audio.listener.IWLAudioTrack;
import com.autoai.avs.linksdk.platform.audio.listener.WLAudioListener;
import com.autoai.avs.linksdk.platform.bean.PCMBean;
import com.autoai.avs.linksdk.platform.callback.WeLinkMusicCallback;
import com.autoai.avs.linksdk.platform.callback.WeLinkSoundCallback;
import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;
import com.autoai.avs.linksdk.platform.protocol.WLProtocolMannger;
import com.autoai.link.threadpool.ThreadPoolUtil;

import java.util.HashMap;
import java.util.Locale;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * Welink PCM音频车机播报处理类，主要负责车机的音频播报处理。
 *
 * <AUTHOR>
 */
public class HUAudioTrack implements IWLAudioTrack, WeLinkSoundCallback, WeLinkMusicCallback {
    private int audioType;
    private String audioMark;
    private WLAudioListener mListener;
    private final LinkedBlockingQueue<byte[]> mVectorLinkedBlockingQueue = new LinkedBlockingQueue<>();

    private PCMBean mPCMBean;
    private boolean isPlaying = false;
    private boolean isFirstBegin = true;
    private boolean isMusicPause = true;
    private boolean isEnableFocus = false;

    /**
     * 标记从互联开始是否调用过 start 方法;
     */
    private boolean hasStart = false;

    private volatile boolean isLastDate = false;

    private int rate = 0;
    private int bit = 0;
    private int channels = 0;

    private long soundTotalSize = 0;
    private long soundPlaySize = 0;
    private WLConnectManager mConnectManager;

    private void initConnectManager() {
        if (mConnectManager == null) {
            mConnectManager = WLConnectManager.getInstance();
        }
    }

    /**
     * 方法名称：setAudioParams
     * 方法描述：设置播放参数
     * 方法参数：rate 采样频率，bit 采样位数，channels 声道数量,soundTotalSize 播放数据总长度
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 16:08
     */


    public void setAudioParams(int rate, int bit, int channels, long soundTotalSize) {
        WlLinkSdkLog.i("HUAudioTrack setAudioParams --------> rate:" + rate + ",bit:" + bit + ",channels:" + channels + ",soundTotalSize:" + soundTotalSize);

        this.rate = rate;
        this.bit = bit;
        this.channels = channels;
        this.soundTotalSize = soundTotalSize;
        this.soundPlaySize = 0;
    }

    /**
     * 方法名称：setPlayStateListener
     * 方法描述：设置音源类型和回调监听
     * 方法参数：audioType 播放声音类型，mListener 播放回调处理对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */
    @Override
    public void setPlayStateListener(int audioType, WLAudioListener mListener) {
        WlLinkSdkLog.i("HUAudioTracksetPlayStateListener --------> audioType:" + audioType);

        this.audioType = audioType;
        this.mListener = mListener;
        this.audioMark = "" + audioType;
    }

    /**
     * 方法名称：setPlayPosition
     * 方法描述：设置音乐已播放的大小
     * 方法参数：playPosition 播放大小
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/28 15:49
     */

    @Override
    public void setPlayPosition(long playPosition) {
        WlLinkSdkLog.i("HUAudioTracksetPlayPosition -------->playPosition:" + playPosition);

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.isMusicPause = false;
            this.soundPlaySize = playPosition;

            this.mPCMBean = new PCMBean();
            this.mPCMBean.setRate(rate);
            this.mPCMBean.setBit(bit);
            this.mPCMBean.setChannel(channels);
            this.mPCMBean.setTotalLen(soundTotalSize);
        }
    }

    /**
     * 方法名称：setTotalSize
     * 方法描述：更新设置音乐播放的总大小
     * 方法参数：bufferSize 减少播放数据大小
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2021/4/23 15:49
     */

    @Override
    public void setTotalSize(long bufferSize) {
        WlLinkSdkLog.i("HUAudioTracksetTotalSize -------->bufferSize:" + bufferSize);

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.soundTotalSize -= bufferSize;
        }
    }

    /**
     * 方法名称：setPlaySize
     * 方法描述：更新设置音乐已播放的大小
     * 方法参数：bufferSize 添加播放数据大小
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2021/8/5 15:49
     */

    @Override
    public void setPlaySize(long bufferSize) {
        WlLinkSdkLog.i("HUAudioTracksetPlaySize -------->bufferSize:" + bufferSize);

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.soundPlaySize += bufferSize;
        }
    }

    /**
     * 方法名称：start
     * 方法描述：开始更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void start(boolean isConnectcar) {
        WlLinkSdkLog.i("HUAudioTrackstart --------> ");
        //播报音乐
        if (WLAudioType.MUSIC_TAG == audioType) {
            this.isMusicPause = false;

            this.mPCMBean = new PCMBean();
            this.mPCMBean.setRate(rate);
            this.mPCMBean.setBit(bit);
            this.mPCMBean.setChannel(channels);
            this.mPCMBean.setTotalLen(soundTotalSize);

            if (mListener != null && soundPlaySize >= soundTotalSize && isConnectcar) {
                WlLinkSdkLog.i("HUAudioTrackonTick ---------> progress:100 onFinish");
                mListener.onProgress(100);
                mListener.onFinish();
                return;
            }
            initConnectManager();
            if (mConnectManager != null) {
                if (!isEnableFocus) {
                    WlLinkSdkLog.i("HUAudioTrackstart -----registerMusic---> ");
                    mConnectManager.registerMusic(this);
                } else {
                    WlLinkSdkLog.i("HUAudioTrackstart -----startMusic---> ");
                    mConnectManager.startMusic(mPCMBean, soundPlaySize);
                    hasStart = true;
                }
            }
            if (mListener != null && soundTotalSize != 0 && isConnectcar) {
                WlLinkSdkLog.i("HUAudioTrackstart ------ onStart --------> ");
                mListener.onStart();
            }
        }
        //播报TTS
        else {
            this.isPlaying = false;
            this.isFirstBegin = true;
            this.isLastDate = false;
            if (!mVectorLinkedBlockingQueue.isEmpty()) {
                mVectorLinkedBlockingQueue.clear();
            }
        }
    }

    /**
     * 方法名称：pause
     * 方法描述：暂定更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/15 15:49
     */

    @Override
    public void pause(boolean isConnectcar) {
        WlLinkSdkLog.i("HUAudioTrackpause --------> ");
        if (WLAudioType.MUSIC_TAG == audioType) {
            this.isMusicPause = true;
            if (soundTotalSize != 0 && isConnectcar) {
                initConnectManager();
                if (mConnectManager != null) {
                    mConnectManager.pauseMusic();
                }
                if (mListener != null) {
                    WlLinkSdkLog.i("HUAudioTrackpause ------ onPause --------> ");
                    mListener.onPause();
                }
            }
        }
    }

    /**
     * 方法名称：resume
     * 方法描述：继续更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/15 15:49
     */

    @Override
    public void resume(boolean isConnectcar) {
        WlLinkSdkLog.i("HUAudioTrackresume --------> ");
        if (WLAudioType.MUSIC_TAG == audioType) {
            this.isMusicPause = false;
            if (isConnectcar && soundTotalSize != 0) {
                initConnectManager();
                if (!hasStart && mPCMBean != null) {
                    if (mConnectManager != null) {
                        mConnectManager.startMusic(mPCMBean, soundPlaySize);
                    }
                    hasStart = true;
                }
                if (mConnectManager != null) {
                    mConnectManager.resumeMusic();
                }
                if (mListener != null) {
                    WlLinkSdkLog.i("HUAudioTrackresume ------ onResume --------> ");
                    mListener.onResume();
                }
            }
        }
    }

    /**
     * 方法名称：finish
     * 方法描述：结束更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */
    @Override
    public void finish(boolean isConnectcar) {
        WlLinkSdkLog.i("HUAudioTrackfinish --------> ");
    }

    /**
     * 方法名称：write
     * 方法描述：更新数据中
     * 方法参数：bytes PCM数据
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */
    @Override
    public void write(byte[] bytes, boolean isLast) {
        WlLinkSdkLog.i("HUAudioTrackwrite --------> date size:" + bytes.length + ",soundPlaySize:" + soundPlaySize + ",soundTotalSize:" + soundTotalSize + ",isLast:" + isLast);
        this.isLastDate = isLast;
        //更新音乐
        if (WLAudioType.MUSIC_TAG == audioType) {
            if (!isMusicPause) {
                initConnectManager();
                if (mConnectManager != null) {
                    mConnectManager.updateMusicPCM(bytes);
                }
                if (isLastDate && mListener != null) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(() -> {
                        WlLinkSdkLog.i("HUAudioTrackonTick ---------> progress:100 onFinish");
                        mListener.onProgress(100);
                        mListener.onFinish();
                    }, 500, TimeUnit.MILLISECONDS);
                }
            }
        } else {//更新TTS
            if (bytes.length == 0) {
                return;
            }
            if (isPlaying) {
                try {
                    mVectorLinkedBlockingQueue.put(bytes);
                } catch (InterruptedException e) {
                    WlLinkSdkLog.e("HUAudioTrackwrite", e);
                }
            } else {
                initConnectManager();
                if (mConnectManager != null) {
                    mConnectManager.playText(audioMark, bytes, rate, bit, channels, this);
                }
            }
            isPlaying = true;
        }
    }

    /**
     * 方法名称：stop
     * 方法描述：停止播放并清空播放数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void stop(boolean isConnectcar) {
        WlLinkSdkLog.i("HUAudioTrackstop --------> ");

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.isMusicPause = true;
            this.soundPlaySize = 0;
            if (soundTotalSize != 0 && isConnectcar) {
                initConnectManager();
                if (mConnectManager != null) {
                    mConnectManager.stopMusic();
                }
//                if(mListener != null){
//                    WlLinkSdkLog.i(TAG,"stop ------ onFinish --------> ");
//                    mListener.onFinish();
//                }
            }
        } else {
            initConnectManager();
            if (mConnectManager != null) {
                mConnectManager.playText(audioMark, null, rate, bit, channels, this);
            }
            mVectorLinkedBlockingQueue.clear();
            this.isPlaying = false;
            this.isFirstBegin = true;
            this.isLastDate = false;
        }
    }

    /**
     * 方法名称：clear
     * 方法描述：清空播放数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void clear() {
        WlLinkSdkLog.i("HUAudioTrackclear --------> ");

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.isMusicPause = true;
            this.soundPlaySize = 0;
            initConnectManager();
            if (mConnectManager != null) {
                mConnectManager.stopMusic();
            }
        } else {
            initConnectManager();
            if (mConnectManager != null) {
                mConnectManager.playText(audioMark, null, rate, bit, channels, this);
            }
            mVectorLinkedBlockingQueue.clear();
            this.isPlaying = false;
            this.isFirstBegin = true;
            this.isLastDate = false;
        }
    }

    /**
     * 方法名称：release
     * 方法描述：释放播放对象
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void release() {
        WlLinkSdkLog.i("HUAudioTrackrelease --------> ");
        if (WLAudioType.MUSIC_TAG == audioType) {
            this.isMusicPause = true;
            this.soundPlaySize = 0;
            initConnectManager();
            if (mConnectManager != null) {
                mConnectManager.stopMusic();
            }
        } else {
            initConnectManager();
            if (mConnectManager != null) {
                mConnectManager.playText(audioMark, null, rate, bit, channels, this);
            }
            mVectorLinkedBlockingQueue.clear();
            this.isPlaying = false;
            this.isFirstBegin = true;
            this.isLastDate = false;
        }
    }

    /**
     * 方法名称：setVolume
     * 方法描述：设置音量 车机播放暂不支持
     * 方法参数：gain 音量值
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void setVolume(float gain) {
        WlLinkSdkLog.i("HUAudioTracksetVolume --------> gain:" + gain);
    }


    /**
     * 方法名称：updatePlayDevice
     * 方法描述：依据互联状态更新音频数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/05/29 16:01
     */

    @Override
    public void updatePlayDevice(boolean isConnectcar, long playPosition) {
        WlLinkSdkLog.i("HUAudioTrackupdatePlayDevice --------> isConnectcar:" + isConnectcar + ",isEnableFocus:" + isEnableFocus);
        this.soundPlaySize = playPosition;
        if (isConnectcar) {
            if (WLAudioType.MUSIC_TAG == audioType && mPCMBean != null) {
                WLProtocolMannger.getInstance().sendCacheCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE);
                initConnectManager();
                if (mConnectManager != null) {
                    mConnectManager.registerMusic(this);
                }
            } else {
                this.isPlaying = false;
                this.isFirstBegin = true;
                this.isLastDate = false;
                if (!mVectorLinkedBlockingQueue.isEmpty()) {
                    mVectorLinkedBlockingQueue.clear();
                }
            }
        } else {
            if (WLAudioType.MUSIC_TAG != audioType && mListener != null) {
                if (this.isPlaying && this.isLastDate) {
                    WlLinkSdkLog.i("HUAudioTrackWLSound.Callback ------ onFinish --------> ");
                    mListener.onFinish();
                }
            }
            hasStart = false;
        }
    }


    /******************************* WLSound.Callback 功能处理方法 *******************************/

    @Override
    public void onBegin(String mark) {
        WlLinkSdkLog.i("HUAudioTrackWLSound.Callback onBegin ----->");
        if (isFirstBegin) {
            if (mListener != null && WLAudioType.MUSIC_TAG != audioType) {
                WlLinkSdkLog.i("HUAudioTrackWLAudioListener ------ onStart --------> ");
                mListener.onStart();
            }
            isFirstBegin = false;
        }
    }

    @Override
    public void onPrepare(String mark) {
        WlLinkSdkLog.i("HUAudioTrackWLSound.Callback onPrepare ----->size:" + mVectorLinkedBlockingQueue.size());
        if (!mVectorLinkedBlockingQueue.isEmpty()) {
            byte[] bytes = new byte[0];
            try {
                bytes = mVectorLinkedBlockingQueue.take();
            } catch (InterruptedException e) {
                WlLinkSdkLog.e("HUAudioTrackonPrepare", e);
            }
            initConnectManager();
            if (mConnectManager != null) {
                mConnectManager.playText(audioMark, bytes, rate, bit, channels, this);
            }
        } else {
            this.isPlaying = false;
        }
    }

    @Override
    public void onComplete(String mark) {
        WlLinkSdkLog.i("HUAudioTrackWLSound.Callback ------ onComplete --------> ");
        this.isFirstBegin = true;
        this.isLastDate = false;
        if (mListener != null && WLAudioType.MUSIC_TAG != audioType) {
            WlLinkSdkLog.i("HUAudioTrackWLSound.Callback ------ onFinish --------> ");
            mListener.onFinish();
        }
    }

    @Override
    public void onInterrupt(String mark, long playTime, long totalTime) {
        WlLinkSdkLog.i("HUAudioTrackWLSound.Callback onInterrupt ----->");

        if (mListener != null) {
            HashMap<String, Long> obj = new HashMap<>();
            obj.put(WLAudioConfig.WL_AUDIOMANAGER_ERROR_PARAM_PLAYTIME, playTime);
            obj.put(WLAudioConfig.WL_AUDIOMANAGER_ERROR_PARAM_TOTALTIME, totalTime);
            mListener.onError(WLAudioConfig.WL_AUDIOMANAGER_ERROR_CODE_INTERRUPT, obj);
        }
    }

    @Override
    public void onReject(String mark, long waitingTime) {
        WlLinkSdkLog.i("HUAudioTrackWLSound.Callback onReject ----->");

        if (mListener != null) {
            mListener.onError(WLAudioConfig.WL_AUDIOMANAGER_ERROR_CODE_REJECT, waitingTime);
        }
    }

    /******************************* WLMusic.Callback 功能处理方法 *******************************/

    @Override
    public void onEnableFocus() {
        WlLinkSdkLog.i("HUAudioTrackWLMusic.Callback onEnableFocus ----->");
        this.isEnableFocus = true;
        WlLinkSdkLog.i("HUAudioTrackWLMusic.Callback onEnableFocus ---startMusic-->");
        initConnectManager();
        if (!isMusicPause) {
            if (mConnectManager != null) {
                mConnectManager.startMusic(mPCMBean, soundPlaySize);
            }
            hasStart = true;
        }
        if (mConnectManager != null) {
            mConnectManager.updateMusicOrder(-1);
            mConnectManager.updateMusicID3(null);
        }
    }

    @Override
    public void onDisableFocus() {
        WlLinkSdkLog.i("HUAudioTrackWLMusic.Callback onDisableFocus ----->");
        this.isEnableFocus = false;
        if (mListener != null) {
            mListener.onError(WLAudioConfig.WL_AUDIOMANAGER_ERROR_CODE_DISABLEFOCUS, null);
        }
    }

    @Override
    public void onTick(long position) {
        WlLinkSdkLog.i("HUAudioTrackWLMusic.Callback onTick ----->position:" + position);

        this.soundPlaySize = position;
        if (mListener != null && soundTotalSize != 0) {
            double progress = Double.parseDouble(String.format(Locale.getDefault(), "%.2f", soundPlaySize * 100.0 / soundTotalSize));
            if (progress <= 100.0) {
                WlLinkSdkLog.i("HUAudioTrackonTick ---------> progress:" + progress);
                mListener.onProgress(progress);
            }
        }
    }

}
