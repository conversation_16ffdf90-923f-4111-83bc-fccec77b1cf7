package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Welink手机交互协议处理车机端candata数据，此协议主要是用来通知手机端车机端当前的故障详情信息。
 *
 * <AUTHOR>
 */
public class CanDataFZGDSBean extends BaseProtocolBean{

    /**
     * 故障码在结构体中的位置
     */
    private int posion;
    /**
     * warning ID
     */
    private int warningId;
    /**
     *
     */
    private String dynValue;

    public int getPosion() {
        return posion;
    }

    public int getWarningId() {
        return warningId;
    }

    public String getDynValue() {
        return dynValue;
    }

    @Override
    public String toString() {
        return "CanDataFZGDSBean{" +
                "posion=" + posion +
                ", warningId=" + warningId +
                ", dynValue='" + dynValue + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.posion = extData.getInt("posion");
        this.warningId = extData.getInt("warningId");
        this.dynValue = extData.getString("dynValue");
    }


}
