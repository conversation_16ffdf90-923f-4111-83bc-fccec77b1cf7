package com.autoai.avs.linksdk.platform.audio.util;

import org.json.JSONException;
import org.json.JSONObject;

public class Utils {
    /**
     * 通知车机端手机端音乐播放状态
     *
     * @param state 播放状态
     *
     */
    public static String getCarMediaState(int state) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android|ios|ce");
            JSONObject command = new JSONObject();
            command.put("method", "onMediaState");
            JSONObject extData = new JSONObject();
            extData.put("mediaState", state);
            command.put("extData", extData);
            jsonObject.put("command", command);
            return jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }
}
