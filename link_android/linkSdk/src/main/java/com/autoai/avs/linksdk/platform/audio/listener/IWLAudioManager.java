package com.autoai.avs.linksdk.platform.audio.listener;

import android.media.AudioTrack;
import android.media.MediaFormat;

import androidx.annotation.Keep;

import com.autoai.avs.linksdk.platform.audio.bean.WLDeviceType;


/**
 * Welink PCM音频播报功能实现接口，主要定义对外提供的功能接口。
 *
 * <AUTHOR>
 */
@Keep
public interface IWLAudioManager {

    AudioTrack initAudioTrack(int rate, int bit, int channels,long duration);

    AudioTrack initAudioTrack(MediaFormat format);

    AudioTrack resetAudioTrack();

    void setPlayStateListener(int audioType, WLAudioListener mListener);

    void setPlayProgress(double progress);

    void start();

    void pause();

    void resume();

    void finish();

    void update(byte[] bytes);

    void clear();

    void stop();

    void error(int errorCode);

    void release();

    void setVolume(float gain);

    WLDeviceType getPlayDevice();

}
