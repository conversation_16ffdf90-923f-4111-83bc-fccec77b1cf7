package com.autoai.avs.linksdk.util.messagebus;

import com.autoai.avs.linksdk.util.messagebus.bean.ParamSet;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

final class TagMessage {

   private Object event;
   private String tag;

    TagMessage(Object event, String tag) {
        this.event = event;
        this.tag = tag;
    }

    public Object getEvent() {
        return event;
    }

    public void setEvent(Object event) {
        this.event = event;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof TagMessage) {
            TagMessage tagMessage = (TagMessage) obj;
            // 增加对 ParamSet 类型数据中 字段的对比，paramSet 对象中Map集合中的数据一致 才能认为是同一个消息
            if (Objects.equals(tagMessage.event.getClass(), event.getClass()) && event instanceof ParamSet){
                ParamSet eventParam = (ParamSet) event;
                ParamSet messageEventParam = (ParamSet) tagMessage.event;
                Map map = messageEventParam.getmParamList();
                Set set = eventParam.getmParamList().keySet();
                Object[] objects = set.toArray();
                String s =null;
                if (objects.length > 0) {
                     s = (String) objects[0];
                }
              return map.containsKey(s);
            }
            return Objects.equals(tagMessage.event.getClass(), event.getClass())
                    && Objects.equals(tagMessage.tag, tag);
        }
        return false;
    }

    boolean isSameType(final Class<?> eventType, final String tag) {
        return Objects.equals(event.getClass(), eventType)
                && Objects.equals(this.tag, tag);
    }

    @Override
    public String toString() {
        return "event: " + event + ", tag: " + tag;
    }
}
