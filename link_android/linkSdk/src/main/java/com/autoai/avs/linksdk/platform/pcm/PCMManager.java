package com.autoai.avs.linksdk.platform.pcm;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.pcm.utils.ByteUtil;

/**
 * 传输给车机PCM音乐控制类
 * Created by goujh on 2018/3/19.
 */
public class PCMManager {

    private static PCMManager mPCMManager = null;

    public static PCMManager getInstance() {
        if (mPCMManager == null) {
            synchronized (PCMManager.class) {
                if (mPCMManager == null) {
                    mPCMManager = new PCMManager();
                }
            }
        }
        return mPCMManager;
    }

    /**
     * 发送PCM数据到车机端
     *
     * @param data         PCM数据包
     * @param rate         采样率
     * @param bit          采样位数
     * @param channel      声道数
     * @param mark         音源标识 0：导航；1：音乐；2：微信（包括 tts 转换来的）；3：tts 天气预报；4：tts 语音助手的提示语；5：tts 新闻
     * @param audioNumMark 音频位置
     */
    public byte[] sendPackageToCar(byte[] data, int rate, int bit, int channel, int mark, int audioNumMark) {
        WlLinkSdkLog.d("pcm sendPackageToCar  -------------->");

        byte[] pcmByte = ByteUtil.getPackageDataByParam(data, rate, bit, channel, mark, audioNumMark);
        if (audioNumMark == PcmConfigs.SOUND_MARK_START) {
            WlLinkSdkLog.d("pcm PcmManager  声音开始标志----" + mark);
        } else if (audioNumMark == PcmConfigs.SOUND_MARK_END) {
            WlLinkSdkLog.d("pcm PcmManager  声音结束标志----" + mark);
        }
        WlLinkSdkLog.d("pcm mark:: " + mark + "---" + pcmByte.length + "-----" + "audioMark::" + audioNumMark + " rate::  " + rate);
        return pcmByte;
    }

//    /**
//     * 通知车机端手机端音乐播放状态
//     *
//     * @param state 播放状态
//     *
//     */
//    public byte[] notifyCarMediaState(int state) {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("moduleName", "WeLink");
//            jsonObject.put("version", 0);
//            jsonObject.put("platform", "android|ios|ce");
//            JSONObject command = new JSONObject();
//            command.put("method", "onMediaState");
//            JSONObject extData = new JSONObject();
//            extData.put("mediaState", state);
//            command.put("extData", extData);
//            jsonObject.put("command", command);
//            return jsonObject.toString().getBytes();
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

}
