package com.autoai.avs.linksdk.platform.pcm;

/**
 * pcm相关常量
 *
 */
public class PcmConfigs {

    public static  boolean DEBUG = false;
    public static final int PCM_MARK = 0x55AA;//手机端与车机端通信
    public static final int SOUND_MARK_START = 1;//第一个包
    public static final int SOUND_MARK_CENTER = 0;//中间包
    public static final int SOUND_MARK_END = 2;//最后包
    public static final int SOUND_MARK_RANDOM = 3;//随机包


    public static final int SOUND_NAV = 0;//音源标识导航
    public static final int SOUND_MUSIC = 1;//音源标识各种音乐归为一类本地和qq都算
    public static final int SOUND_WECHAT = 2;//音源标识微信
    public static final int SOUND_WEATHER = 3;//音源标识天气预报
    public static final int SOUND_VOICE_ASSISTANT = 4;//音源标识语音助手提示语
    public static final int SOUND_NEWS = 5;//音源标识新闻
    public static final int SOUND_BACKGROUND_VOICE_ASSISTANT = 6;//音源标识后台语音助手提示音


    public static final int COMMAND_TO_CAR_START = 10;//手机向车机端发送开始的命令号
    public static final int COMMAND_TO_CAR_ID_3 = 11;//手机向车机端发送id3信息
    public static final int COMMAND_REFRESH_ID_3 = 12;//手机刷新 ID3 数据。
    public static final int COMMAND_TO_PHONE_START = 40010;//车机向手机发送开始的命令号
    public static final int COMMAND_TO_PHONE_ENDS = 40020;//车机向手机发送结束的命令号
    public static  String PcmMusicUrl= "";// Environment.getExternalStorageDirectory().toString()+"/TestMusic/";


    public static final String supplyNewsEndPackage ="supplyNewsEndPackage";

    /**
     * 音频播放状态
     * 1：开始播放
     * 2：停止播放
     */
    public static final int MEDIA_STATE_START = 1;
    public static final int MEDIA_STATE_STOP = 2;


}
