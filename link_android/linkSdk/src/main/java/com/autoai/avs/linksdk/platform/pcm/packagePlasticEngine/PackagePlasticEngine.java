package com.autoai.avs.linksdk.platform.pcm.packagePlasticEngine;


import java.util.ArrayList;
import java.util.List;

/**
 * 分包
 * Created by sulw on 2018/11/6.
 */
public class PackagePlasticEngine {

    private int packageSize;

    private volatile int packageId;

    private byte[] tail;

    private int tailLength;

    public PackagePlasticEngine(int packageSize) {
        if (packageSize <= 0) {
            throw new IllegalArgumentException("packageSize 必须大于0");
        }

        this.packageSize = packageSize;
        tail = new byte[packageSize];
    }

    public synchronized List<PackageELF> plastic(byte[] packageData) {

        List<PackageELF> res = null;

        if (packageData != null && packageData.length > 0) {
            if (packageData.length + tailLength < packageSize) {

                System.arraycopy(packageData, 0, tail, tailLength, packageData.length);
                tailLength = packageData.length + tailLength;

            } else {

                byte[] tempData = new byte[packageData.length + tailLength];

                System.arraycopy(tail, 0, tempData, 0, tailLength);
                System.arraycopy(packageData, 0, tempData, tailLength, packageData.length);

                int n = tempData.length / packageSize;

                res = new ArrayList<>();
                int startPosition = 0;
                for (int i = 0; i < n; i++) {
                    PackageELF packageELF = new PackageELF();
                    packageELF.packageId = packageId++;
                    packageELF.packageData = new byte[packageSize];

                    startPosition = i * packageSize;
                    System.arraycopy(tempData, startPosition, packageELF.packageData, 0, packageSize);

                    res.add(packageELF);
                }

                tailLength = tempData.length - n * packageSize;
                System.arraycopy(tempData, n * packageSize, tail, 0, tailLength);
            }
        }

        return res;
    }

    public synchronized int getTailLength() {
        return tailLength;
    }

    public synchronized PackageELF getTailPackageELF() {
        PackageELF elf = null;
        if (0 != tailLength) {
            elf = new PackageELF();
            byte[] res = new byte[tailLength];
            System.arraycopy(tail, 0, res, 0, tailLength);
            elf.packageId = packageId++;
            elf.packageData = res;
        }

        tailLength = 0;

        return elf;
    }

    public synchronized void destroy() {
        packageSize = 0;
        packageId = 0;
        tailLength = 0;
        tail = null;
    }

    public static class PackageELF {
        public int packageId;
        public byte[] packageData;
    }
}
