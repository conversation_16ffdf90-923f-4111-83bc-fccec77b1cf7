package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端消息中心控制手机端 launcher播放选中微信联系人的消息。
 * @param wechatMsgId 当前微信联系人id
 */

public class PlayWXMsgBean extends BaseProtocolBean{

    private String wechatMsgId;

    public String getWechatMsgId() {
        return wechatMsgId;
    }

    @Override
    public String toString() {
        return "PlayWXMsgBean{" +
                "wechatMsgId='" + wechatMsgId + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.wechatMsgId = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_WXMSGID);
    }


}
