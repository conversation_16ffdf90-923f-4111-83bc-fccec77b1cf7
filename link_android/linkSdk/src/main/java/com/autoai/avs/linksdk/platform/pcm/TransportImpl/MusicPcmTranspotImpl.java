package com.autoai.avs.linksdk.platform.pcm.TransportImpl;

import android.os.SystemClock;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.pcm.BasePcmTransport;
import com.autoai.avs.linksdk.platform.pcm.PCMManager;
import com.autoai.avs.linksdk.platform.pcm.PcmConfigs;
import com.autoai.avs.linksdk.platform.pcm.bean.PcmdataBean;
import com.autoai.avs.linksdk.platform.pcm.packagePlasticEngine.PackagePlasticEngine;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.autoai.welink.platform.WLPCMPolicy;

import java.util.List;
import java.util.Vector;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

public class MusicPcmTranspotImpl extends BasePcmTransport {
    private final Vector<PcmdataBean> mVector = new Vector<>();

    private PackagePlasticEngine musicPackagePlasticEngine;

    private volatile boolean isStopPcmThread = false;//是否停止pcm循环

    private final AtomicLong sendPosition = new AtomicLong(0);//已发送数据位置

    private volatile long delayTime = 0;           //发送延迟时间差
    private final AtomicLong supportPlayTime = new AtomicLong(0);
    private volatile long delayPlayTime;
    private volatile long firstSendTime;

    private static final int isCacheCount = 1;
    private final AtomicLong isCacheIndex = new AtomicLong(0);

    private final WLPCMPolicy.Callback callback;
    private final Runnable musicPcmTask = new Runnable() {

        @Override
        public void run() {
            if (!mVector.isEmpty()) {
                PcmdataBean pcmData = mVector.remove(0);
                if (callback != null) {
                    byte[] bytes = PCMManager.getInstance().sendPackageToCar(pcmData.getPcm(),
                            rate, bit, channel, PcmConfigs.SOUND_MUSIC, pcmData.getPos());
                    callback.onSendPCM(bytes);
                    if (rate == 0 || bit == 0 || channel == 0) {
                        delayTime = 0;
                    } else {
                        if (isCacheIndex.get() < isCacheCount) {
                            isCacheIndex.addAndGet(1L);
                            delayTime = 0;
                        } else {
                            long playTime = (pcmData.getPcm().length * 8000L) / ((long) rate * bit * channel);
                            if (firstSendTime == 0) {
                                delayTime = playTime;
                                firstSendTime = SystemClock.elapsedRealtime();
                            } else {
                                delayPlayTime = SystemClock.elapsedRealtime() - firstSendTime;
                                delayTime = playTime - (delayPlayTime - supportPlayTime.get());
                            }
                            supportPlayTime.set(supportPlayTime.get() + playTime);
                        }
                    }
                    sendPosition.set(sendPosition.get() + pcmData.getPcm().length);
                    WlLinkSdkLog.d("MusicPcmTranspotImpl onSendPCM --111-->  length:" + bytes.length + ",rate:" + rate + ",bit:" + bit + ",channel:" + channel
                            + ",pos:" + pcmData.getPos() + ",size:" + mVector.size() + ",delayPlayTime:" + delayPlayTime + ",supportPlayTime:"
                            + supportPlayTime.get() + ",delayTime:" + delayTime + ",isCacheIndex:" + isCacheIndex);
                }
                if (!isStopPcmThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(musicPcmTask, delayTime, TimeUnit.MILLISECONDS);
                }
            } else {
                WlLinkSdkLog.d("MusicPcmTranspotImpl onSendPCM --222--> size:" + mVector.size() + ",delayTime:0" + ",isCacheIndex:" + isCacheIndex);
                if (!isStopPcmThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(musicPcmTask, 0, TimeUnit.MILLISECONDS);
                }
            }
        }
    };

    public MusicPcmTranspotImpl(WLPCMPolicy.Callback callback) {
        this.callback = callback;
    }

    @Override
    public void producePcm(byte[] pcmData, int mark, int type, String... strings) {
        WlLinkSdkLog.d("MusicPcmTranspotImpl producePcm pcmData:" + pcmData.length + ",mark:" + mark + ",type:" + type + ",isStopPcmThread:" + isStopPcmThread);
        if (mark == PcmConfigs.SOUND_MARK_START) {
            if (musicPackagePlasticEngine != null) {
                musicPackagePlasticEngine.destroy();
                musicPackagePlasticEngine = null;
            }
            musicPackagePlasticEngine = new PackagePlasticEngine(35304);
            this.sendPosition.set(0);
            mVector.add(new PcmdataBean(pcmData, mark));
        } else if (mark == PcmConfigs.SOUND_MARK_CENTER) {
            List<PackagePlasticEngine.PackageELF> packageELFList = musicPackagePlasticEngine.plastic(pcmData);
            if (packageELFList != null && packageELFList.size() > 0) {
                for (PackagePlasticEngine.PackageELF packageELF : packageELFList) {
                    mVector.add(new PcmdataBean(packageELF.packageData, mark));
                }
            }
        } else if (mark == PcmConfigs.SOUND_MARK_END) {
            mVector.add(new PcmdataBean(pcmData, mark));
        }
    }

    @Override
    public long getSendPCMPosition() {
        return sendPosition.get();
    }


    @Override
    public void clearPcmData() {
        WlLinkSdkLog.d("MusicPcmTranspotImpl clearPcmData");
        if (musicPackagePlasticEngine != null) {
            musicPackagePlasticEngine.getTailPackageELF();
        }
        mVector.clear();
    }

    @Override
    public void startTransportThread() {
        WlLinkSdkLog.d("MusicPcmTranspotImpl startTransportThread");
        mVector.clear();
        isStopPcmThread = false;
        this.isCacheIndex.set(0);
        this.firstSendTime = 0;
        this.supportPlayTime.set(0);
        this.delayPlayTime = 0;
        ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(musicPcmTask, 0, TimeUnit.MILLISECONDS);
    }

    public void pauseTransportThread() {
        WlLinkSdkLog.d("MusicPcmTranspotImpl pauseTransportThread");
        isStopPcmThread = true;

        byte[] bytes = PCMManager.getInstance().sendPackageToCar(new byte[0],
                rate, bit, channel, PcmConfigs.SOUND_MUSIC, PcmConfigs.SOUND_MARK_END);
        callback.onSendPCM(bytes);

//        byte[] mediaByte = PCMManager.getInstance().notifyCarMediaState(PcmConfigs.MEDIA_STATE_STOP);
//        callback.onPlayCommand(mediaByte);
    }

    public void resumeTransportThread() {
        WlLinkSdkLog.d("MusicPcmTranspotImpl resumeTransportThread");
        isStopPcmThread = false;
        this.isCacheIndex.set(0);
        this.firstSendTime = 0;
        this.supportPlayTime.set(0);
        this.delayPlayTime = 0;

//        byte[] mediaByte = PCMManager.getInstance().notifyCarMediaState(PcmConfigs.MEDIA_STATE_START);
//        callback.onPlayCommand(mediaByte);

        byte[] bytes = PCMManager.getInstance().sendPackageToCar(new byte[0],
                rate, bit, channel, PcmConfigs.SOUND_MUSIC, PcmConfigs.SOUND_MARK_START);
        callback.onSendPCM(bytes);
        ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(musicPcmTask, 0, TimeUnit.MILLISECONDS);
    }

    @Override
    public void cancelTransportThread(int type) {
        WlLinkSdkLog.d("MusicPcmTranspotImpl cancelTransportThread");
        isStopPcmThread = true;
        if (musicPackagePlasticEngine != null) {
            musicPackagePlasticEngine.destroy();
            musicPackagePlasticEngine = null;
        }
        mVector.clear();
        callback.onSendPCM(PCMManager.getInstance().sendPackageToCar(new byte[0], rate, bit, channel, type, 2));
    }

    @Override
    public void release() {
        WlLinkSdkLog.d("MusicPcmTranspotImpl release");
    }
}
