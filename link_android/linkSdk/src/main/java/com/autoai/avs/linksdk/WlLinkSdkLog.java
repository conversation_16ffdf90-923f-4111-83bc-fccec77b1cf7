package com.autoai.avs.linksdk;

import com.autoai.link.baselog.WeLinkLog;
//import com.autoai.welink.auto.WlConnectorLog;
//import com.autoai.welink.autoproxy.WlServerLog;
//import com.autoai.welink.channel.WlChannelLog;
//import com.autoai.welink.wireless.WLHardwareLog;
import com.autoai.welink.auto.v1.AnFileLog;
//import com.autoai.welink.screen.WlScreenLog;
//import com.autoai.welink.platform.WlPlatformLog;

/**
 * 多资源模块日志管理
 *
 * <AUTHOR>
 */
public class WlLinkSdkLog {
    private static final WeLinkLog LOG = new WeLinkLog() {
        @Override
        protected String configTag() {
            return "WL_LOG_LinkSdk";
        }

        @Override
        protected String configLogName() {
            return WlLinkSdkLog.class.getSimpleName() + ".java";
        }
    };

    private WlLinkSdkLog() {
    }

    public static boolean isIsLoggable() {
        return LOG.isFileLoggable();
    }

    public static void setIsLoggable(boolean isLoggable) {
        LOG.setIsLoggable(isLoggable);
        com.autoai.welink.channel.utiliy.AnFileLog.enableLogCat(isLoggable);
        com.autoai.welink.platform.utiliy.AnFileLog.enableLogCat(isLoggable);
        com.autoai.welink.wireless.utiliy.AnFileLog.enableLogCat(isLoggable);
        com.autoai.welink.screen.AnFileLog.enableLogCat(isLoggable);
        com.autoai.welink.auto.v1.AnFileLog.enableLogCat(isLoggable);
        com.autoai.welink.autoproxy.protocol.AnFileLog.enableLogCat(isLoggable);
//        WlChannelLog.setIsLoggable(isLoggable);
//        WlConnectorLog.setIsLoggable(isLoggable);
//        WLHardwareLog.setIsLoggable(isLoggable);
//        WlPlatformLog.setIsLoggable(isLoggable);
//        WlScreenLog.setIsLoggable(isLoggable);
//        WlServerLog.setIsLoggable(isLoggable);
    }

    public static boolean isIsFileLoggable() {
        return LOG.isFileLoggable();
    }

    public static void setIsFileLoggable(boolean isFileLoggable) {
        LOG.setFileLoggable(isFileLoggable);
    }

    public static void v(String msg) {
        LOG.v(msg);
    }

    public static void v(String msg, Throwable t) {
        LOG.v(msg, t);
    }

    public static void d(String msg) {
        LOG.d(msg);
    }

    public static void d(String msg, Throwable t) {
        LOG.d(msg, t);
    }

    public static void i(String msg) {
        LOG.i(msg);
    }

    public static void i(String msg, Throwable t) {
        LOG.i(msg, t);
    }

    public static void w(String msg) {
        LOG.w(msg);
    }

    public static void w(String msg, Throwable t) {
        LOG.w(msg, t);
    }

    public static void e(String msg) {
        LOG.e(msg);
    }

    public static void e(String msg, Throwable t) {
        LOG.e(msg, t);
    }
}
