package com.autoai.avs.linksdk.platform.pcm;

/**
 * pcm传输基类
 */
public abstract class BasePcmTransport {
    public abstract void producePcm(byte[] pcmData, int mark, int type,String... strings);

    protected int rate;//采样率
    protected int bit;//比特率
    protected int channel;//通道数
    /**
     * 设置不同音源type采样率
     * @param sampleRate
     * @param bitsPerSample
     * @param numChannels
     */
    public void setVoiceHeadInfo(int sampleRate, int bitsPerSample, int numChannels) {
        this.rate = sampleRate;
        this.bit = bitsPerSample;
        this.channel = numChannels;
    }

    /**
     * 开启传输pcm线程
     */
    public abstract void startTransportThread();

    /**
     * 关闭传输pcm线程
     */
    public abstract void cancelTransportThread(int type);


    public long getSendPCMPosition() {
        return 0;
    }

    /**
     * 清除pcm数据
     * 子类自己实现
     */
    public void clearPcmData(){

    }

    public void release(){

    }

}
