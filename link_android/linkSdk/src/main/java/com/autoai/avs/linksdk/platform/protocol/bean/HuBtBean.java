package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端蓝牙状态向手机端launcher发送的协议。
 * @param btState   类型
 *                  true：车机端蓝牙已开启
 *                  false：车机端蓝牙未开启
 * @param btMacName 车机端蓝牙mac地址
 */

public class HuBtBean extends BaseProtocolBean{

    private boolean btState;
    private String btMacName;

    public boolean isBtState() {
        return btState;
    }

    public String getBtMacName() {
        return btMacName;
    }

    @Override
    public String toString() {
        return "HuBtBean{" +
                "btState=" + btState +
                ", btMacName='" + btMacName + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.btState = extData.getBoolean(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_BTSTATE);
        this.btMacName = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_BTMACNAME);
    }


}
