package com.autoai.avs.linksdk.util.messagebus.bean;

import java.util.Map;

public class ParamSetForResult<T> extends ParamSet<T> {
    private final ParamForResult.ForResultCallback<T> mForResult;

    public ParamSetForResult(Map<String,T> mParamList, ParamForResult.ForResultCallback<T> mForResult) {
        super(mParamList);
        this.mForResult = mForResult;
    }

    public ParamForResult.ForResultCallback<T> getmForResult() {
        return mForResult;
    }

    public interface ForResultCallback<T> {
        void onResult(String param, T t);
    }
}
