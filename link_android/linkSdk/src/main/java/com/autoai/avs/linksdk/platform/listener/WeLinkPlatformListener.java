package com.autoai.avs.linksdk.platform.listener;

import androidx.annotation.Keep;

/**
 * Welink 三投平台手车连接状态接口，主要在平台应用中定义手车互联状态接口。
 *
 * <AUTHOR>
 */
@Keep
public interface WeLinkPlatformListener {
    /**
     * 开始互联
     */
    void onLinkStart(boolean isAOA);

    /**
     * 互联成功
     */
    void onLinkConnected(String vehicleType,boolean isAOA);

    /**
     * 断开互联
     */
    void onLinkDisconnected();

    /**
     * 无感互联错误状态提示
     * status - 错误码 1: 设备不支持蓝牙BLE 2: 没有打开蓝牙 3: 没有打开Wi-Fi 4: 需要请求定位权限(ACCESS_FINE_LOCATION) 5: 服务发生异常 6: 创建Wi-Fi Direct GO失败
     */
    void onBleLinkError(int error);

    /**
     * 无感互联连接中状态提示
     * status - 状态码
     *         0: Wi-Fi Direct GO广播，content: GO签名 1: BLE设备连接中... 2: BLE设备连接成功 3: BLE服务连接中... 4: BLE服务连接成功5: 读取版本中...
     *         6: 版本读取成功，content：版本号 7: 写Wi-Fi Direct GO签名 8: 签名写成功，content：签名9: Wi-Fi Direct GC连接，content: 网络密码10: 未找到对应包名的硬件心跳
     *         -1: 设备连接失败 -2: 服务连接失败 -3: 版本读取失败 -4: 签名写失败 -5: 操作超时
     */
    void onBleLinkStatus(int status);

    /**
     * 无感互联一直没有扫描到车机超时
     */
    void onBleLinkScanTimeout();

    /**
     * 无感互联扫描到车机但一直没有互联上超时
     */
    void onBleLinkJoinTimeout();

}
