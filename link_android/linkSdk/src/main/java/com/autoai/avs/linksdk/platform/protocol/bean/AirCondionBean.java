package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;

/**
 * 此协议主要是用来传输车机端相关数据给手机，包括：空调数据、音量数据、静音模式数据
 */

public class AirCondionBean extends BaseProtocolBean{

    /**
     * 音量模式 类型 int 1：invalid 无效值 2：unmute 非静音 3：mute 静音
     */
    private int huVolumeState;

    /**
     *  音量大小 类型 int
     */
    private int huVolume;


    private ZoneArr[] zoneArr;

    public int getHuVolumeState() {
        return huVolumeState;
    }

    public int getHuVolume() {
        return huVolume;
    }

    public ZoneArr[] getZoneArr() {
        return zoneArr;
    }

    @Override
    public String toString() {
        return "AirCondionBean{" +
                "huVolumeState=" + huVolumeState +
                ", huVolume=" + huVolume +
                ", zoneArr=" + Arrays.toString(zoneArr) +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.huVolumeState = extData.getInt("huVolumeState");
        this.huVolume = extData.getInt("huVolume");

        if (extData.has("zoneArr")) {
            JSONArray jsonArray = extData.getJSONArray("zoneArr");
            this.zoneArr = new ZoneArr[jsonArray.length()];
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);

                ZoneArr mZoneArr = new ZoneArr();
                mZoneArr.setZoneNum(jsonObject.getInt("zoneNum"));
                mZoneArr.setZoneState(jsonObject.getInt("zoneState"));
                mZoneArr.setAirTemperatureType(jsonObject.getInt("airTemperatureType"));
                mZoneArr.setTemperatureArea(jsonObject.getDouble("temperatureArea"));
                mZoneArr.setAirVolume(jsonObject.getInt("airVolume"));

                if (jsonObject.has("climateStyle")) {
                    JSONObject climateStyle = jsonObject.getJSONObject("climateStyle");

                    mZoneArr.setClimateMode(climateStyle.getInt("climateMode"));
                    mZoneArr.setAutoMode(climateStyle.getInt("autoMode"));
                }
                this.zoneArr[i] = mZoneArr;
            }
        }
    }

}
