package com.autoai.avs.linksdk.util.messagebus;

import android.annotation.SuppressLint;

import androidx.annotation.Keep;

import com.autoai.avs.linksdk.WlLinkSdkLog;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;
import io.reactivex.Scheduler;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.internal.functions.Functions;
import io.reactivex.internal.operators.flowable.FlowableInternalHelper;
import io.reactivex.processors.FlowableProcessor;
import io.reactivex.processors.PublishProcessor;

@Keep
public final class MessageCenter {

    private final FlowableProcessor<Object> mBus;

    private final Consumer<Throwable> mOnError = throwable -> WlLinkSdkLog.e("MessageCenter", throwable);

    private MessageCenter() {
        mBus = PublishProcessor.create().toSerialized();
    }

    private static class Holder {
        private static final MessageCenter MESSAGE_CENTER = new MessageCenter();
    }

    public static MessageCenter getDefault() {
        return Holder.MESSAGE_CENTER;
    }

    public void post(final Object event) {
        post(event, "", false);
    }

    public void post(final Object event, final String tag) {
        post(event, tag, false);
    }

    public void postSticky(final Object event) {
        post(event, "", true);
    }

    public void postSticky(final Object event, final String tag) {
        post(event, tag, true);
    }

    private void post(final Object event,
                      final String tag,
                      final boolean isSticky) {

        TagMessage msgEvent = new TagMessage(event, tag);
        if (isSticky) {
            CacheUtils.getInstance().addStickyEvent(msgEvent);
        }
        mBus.onNext(msgEvent);
    }

    public <T> void subscribe(final Object subscriber,
                              final Callback<T> callback) {
        subscribe(subscriber, "", false, null, callback);
    }

    public <T> void subscribe(final Object subscriber,
                              final Scheduler scheduler,
                              final Callback<T> callback) {
        subscribe(subscriber, "", false, scheduler, callback);
    }

    public <T> void subscribe(final Object subscriber,
                              final String tag,
                              final Callback<T> callback) {
        subscribe(subscriber, tag, false, null, callback);
    }

    public <T> void subscribe(final Object subscriber,
                              final String tag,
                              final Scheduler scheduler,
                              final Callback<T> callback) {
        subscribe(subscriber, tag, false, scheduler, callback);
    }

    public <T> void subscribeSticky(final Object subscriber,
                                    final Callback<T> callback) {
        subscribe(subscriber, "", true, null, callback);
    }

    public <T> void subscribeSticky(final Object subscriber,
                                    final Scheduler scheduler,
                                    final Callback<T> callback) {
        subscribe(subscriber, "", true, scheduler, callback);
    }

    public <T> void subscribeSticky(final Object subscriber,
                                    final String tag,
                                    final Callback<T> callback) {
        subscribe(subscriber, tag, true, null, callback);
    }

    public <T> void subscribeSticky(final Object subscriber,
                                    final String tag,
                                    final Scheduler scheduler,
                                    final Callback<T> callback) {
        subscribe(subscriber, tag, true, scheduler, callback);
    }

    @SuppressLint("CheckResult")
    private <T> void subscribe(final Object subscriber,
                               final String tag,
                               final boolean isSticky,
                               final Scheduler scheduler,
                               final Callback<T> callback) {
        final Class<T> eventType = getClassFromCallback(callback);
        final Consumer<T> onNext = callback::onEvent;
        if (isSticky) {
            final List<TagMessage> stickyEvent = CacheUtils.getInstance().findStickyEvent(eventType, tag);
            if (stickyEvent != null && !stickyEvent.isEmpty()) {
                for (TagMessage tagMessage : stickyEvent) {
                    Flowable<T> stickyFlowable = Flowable.create(emitter -> emitter.onNext(eventType.cast(tagMessage.getEvent())),
                            BackpressureStrategy.LATEST);
                    if (scheduler != null) {
                        //noinspection ResultOfMethodCallIgnored
                        stickyFlowable.observeOn(scheduler);
                    }
                    CacheUtils.getInstance().addDisposable(subscriber, subscribe(stickyFlowable, onNext, mOnError));
                }
            }
        }
        CacheUtils.getInstance().addDisposable(subscriber, subscribe(toFlowable(eventType, tag, scheduler), onNext, mOnError));
    }

    private <T> Flowable<T> toFlowable(final Class<T> eventType,
                                       final String tag,
                                       final Scheduler scheduler) {
        Flowable<T> flowable = mBus.ofType(TagMessage.class)
                .filter(tagMessage -> tagMessage.isSameType(eventType, tag))
                .map(TagMessage::getEvent)
                .cast(eventType);
        if (scheduler != null) {
            return flowable.observeOn(scheduler);
        }
        return flowable;
    }

    private static <T> Disposable subscribe(Flowable<T> flowable,
                                            Consumer<? super T> onNext,
                                            Consumer<? super Throwable> onError) {

        MyLambdaSubscriber<T> ls = new MyLambdaSubscriber<>(onNext, onError, Functions.EMPTY_ACTION, FlowableInternalHelper.RequestMax.INSTANCE);
        //noinspection UnstableApiUsage
        flowable.subscribe(ls);
        return ls;
    }

    @SuppressWarnings("unchecked")
    private static <T> Class<T> getClassFromCallback(final Callback<T> callback) {
        if (callback == null) {
            return null;
        }
        Type mySuperClass = callback.getClass().getGenericInterfaces()[0];
        Type type = ((ParameterizedType) mySuperClass).getActualTypeArguments()[0];
        while (type instanceof ParameterizedType) {
            type = ((ParameterizedType) type).getRawType();
        }
        String className = type.toString();
        WlLinkSdkLog.i("MessageCenter getClassFromCallback: className" + className);
        if (className.startsWith("class")) {
            className = className.substring(6);
        }
        try {
            return (Class<T>) Class.forName(className);
        } catch (ClassNotFoundException e) {
            WlLinkSdkLog.e("MessageCenter getClassFromCallback: ", e);
            return null;
        }
    }

    public void unregister(final Object subscriber) {
        CacheUtils.getInstance().removeDisposables(subscriber);
    }

    public void unregisterAll() {
        CacheUtils.getInstance().removeAll();
    }


    public interface Callback<T> {
        void onEvent(T t);
    }
}