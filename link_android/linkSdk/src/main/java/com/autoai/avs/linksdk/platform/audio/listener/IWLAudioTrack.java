package com.autoai.avs.linksdk.platform.audio.listener;

/**
 * Welink PCM音频车机和手机播报功能实现接口，主要定义手机和车机实现的功能接口。
 *
 * <AUTHOR>
 */

public interface IWLAudioTrack {

    void setPlayStateListener(int audioType, WLAudioListener mListener);

    void setPlayPosition(long playPosition);

    void setTotalSize(long bufferSize);

    void setPlaySize(long bufferSize);

    void start(boolean isConnectcar);

    void pause(boolean isConnectcar);

    void resume(boolean isConnectcar);

    void finish(boolean isConnectcar);

    void write(byte[] bytes,boolean isLast);

    void clear();

    void stop(boolean isConnectcar);

    void release();

    void setVolume(float gain);

    void updatePlayDevice(boolean isConnectcar,long playPosition);
}
