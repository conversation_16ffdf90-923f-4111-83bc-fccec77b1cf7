package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是通过点击车机端常住条上面的按钮来打开手机端launcher的某个界面或者应用
 * @param downKey 类型
 *                1：跳转到launcher的首界面
 *                2：打开导航界面；
 *                3：打开电话界面；
 *                4：打开音乐界面；
 *                5：打开消息界面；
 *                6：打开更多界面；
 *                7：打开语音识别；
 *                8：launcher切到前台；
 *                9: MOS 账号界面
 */
public class HuChannelBean extends BaseProtocolBean{

    private String pressDownKey;

    public String getPressDownKey() {
        return pressDownKey;
    }

    @Override
    public String toString() {
        return "HuChannelBean{" +
                "pressDownKey='" + pressDownKey + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.pressDownKey = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_DOWNKEY);
    }


}
