package com.autoai.avs.linksdk.platform.pcm.TransportImpl;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.pcm.BasePcmTransport;
import com.autoai.avs.linksdk.platform.pcm.PCMManager;
import com.autoai.avs.linksdk.platform.pcm.PcmConfigs;
import com.autoai.avs.linksdk.platform.pcm.bean.PcmdataBean;
import com.autoai.avs.linksdk.platform.pcm.packagePlasticEngine.PackagePlasticEngine;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.autoai.welink.platform.WLPCMPolicy;

import java.util.List;
import java.util.Vector;
import java.util.concurrent.TimeUnit;

/**
 * 该类用来传输 VR,微信，后台VR 产出的pcm
 */
public class TTSPcmTransportImpl extends BasePcmTransport {
    private final Vector<PcmdataBean> mVector = new Vector<>(); //pcm 数据存储容器
    private PackagePlasticEngine packagePlasticEngine;
    private final WLPCMPolicy.Callback callback;
    private PcmRunnable pcmRunnable;
    private int currentType;
    private int mPackagePlasticSize = 1024 * 8;

    public TTSPcmTransportImpl(WLPCMPolicy.Callback callback) {
        this.callback = callback;
    }

    /**
     * 接收pcm数据
     *
     * @param pcmData pcm数据
     * @param mark    数据的标志（首包，中包，尾包）
     * @param type    TTS  文字类型pcm或微信语音类型pcm
     * @param strings 扩展参数
     */
    @Override
    public void producePcm(byte[] pcmData, int mark, int type, String... strings) {
        //是否停止pcm循环
        boolean isStartPcmThread = false;
        WlLinkSdkLog.d("TTSPcmTransportImpl producePcm pcmData:" + pcmData.length + ",mark:" + mark + ",type:" + type + ",isStartPcmThread:" + isStartPcmThread);
        switch (mark) {
            case PcmConfigs.SOUND_MARK_START:
                dealStartPackage(pcmData, mark, type);
                break;
            case PcmConfigs.SOUND_MARK_CENTER:
                dealCenterPackage(pcmData, mark, type);
                break;
            case PcmConfigs.SOUND_MARK_END:
                dealEndPackage(new byte[0], type);
                break;
            default:
                break;

        }
    }

    /**
     * 处理首包
     *
     * @param pcmData
     */
    private void dealStartPackage(byte[] pcmData, int mark, int type) {
        WlLinkSdkLog.d("TTSPcmTransportImpl dealNaviStartPackage pcmData:" + pcmData.length + ",mark:" + mark + ",type:" + type);
        if (packagePlasticEngine == null)
            packagePlasticEngine = new PackagePlasticEngine(mPackagePlasticSize);
        sendMessageToCar(pcmData, mark, type);
    }

    /**
     * 处理中包（数据包）
     *
     * @param pcmData
     */
    private void dealCenterPackage(byte[] pcmData, int mark, int type) {
        currentType = type;
        WlLinkSdkLog.d("TTSPcmTransportImpl dealCenterPackage pcmData:" + pcmData.length + ",mark:" + mark + ",type:" + type);
        if (packagePlasticEngine != null && pcmData.length > 0) {
            List<PackagePlasticEngine.PackageELF> packageELFList = packagePlasticEngine.plastic(pcmData);
            if (packageELFList != null && packageELFList.size() > 0) {
                for (PackagePlasticEngine.PackageELF packageELF : packageELFList) {
                    sendMessageToCar(packageELF.packageData, mark, type);
                }
            }
        }
    }

    /**
     * 处理尾包
     *
     * @param pcmData 空包，
     * @param type
     */
    private void dealEndPackage(byte[] pcmData, int type) {
        WlLinkSdkLog.d("TTSPcmTransportImpl dealEndPackage pcmData:" + pcmData.length + ",mark:" + PcmConfigs.SOUND_MARK_END + ",type:" + type);
        if (packagePlasticEngine != null) {
            PackagePlasticEngine.PackageELF packageELF = packagePlasticEngine.getTailPackageELF();
            if (packageELF != null && packageELF.packageData != null) {
                sendMessageToCar(packageELF.packageData, PcmConfigs.SOUND_MARK_CENTER, type);
            }
        }
        sendMessageToCar(pcmData, PcmConfigs.SOUND_MARK_END, type);
    }

    /**
     * 设置起始音频序列
     *
     * @param pcm          PCM数据
     * @param markPosition 是否是第一个数据包
     */
    private void sendMessageToCar(byte[] pcm, int markPosition, int type) {
        WlLinkSdkLog.d("TTSPcmTransportImpl sendMessageToCar pcmData:" + pcm.length + ",markPosition:" + markPosition + ",type:" + type);
        mVector.add(new PcmdataBean(pcm, markPosition, type));
    }

    /**
     * 设置 比特率 采样率 声道数
     *
     * @param sampleRate    比特率
     * @param bitsPerSample 采样率
     * @param numChannels   声道数
     */
    @Override
    public void setVoiceHeadInfo(int sampleRate, int bitsPerSample, int numChannels) {
        this.rate = sampleRate;
        this.bit = bitsPerSample;
        this.channel = numChannels;
        if (16000 == rate) {
            mPackagePlasticSize = 1024 * 8;
        } else {
            mPackagePlasticSize = 1024 * 32;
        }
    }


    private static class PcmRunnable implements Runnable {

        public PcmRunnable(TTSPcmTransportImpl pcmTransport, int rate, int bit, int channel) {
            this.pcmTransport = pcmTransport;
            this.rate = rate;
            this.bit = bit;
            this.channel = channel;
        }

        public void setStartPcmThread(boolean startPcmThread) {
            isStartPcmThread = startPcmThread;
        }

        private boolean isStartPcmThread;

        TTSPcmTransportImpl pcmTransport;
        private int rate;
        private int bit;
        private int channel;

        public void setParameters(int rate, int bit, int channel) {
            this.rate = rate;
            this.bit = bit;
            this.channel = channel;
        }

        @Override
        public void run() {
            if (!pcmTransport.mVector.isEmpty()) {
                PcmdataBean pcmData = pcmTransport.mVector.remove(0);

                long delayTime = 0;
                if (!(rate == 0 || bit == 0 || channel == 0)) {
                    delayTime = (pcmData.getPcm().length * 8000L) / ((long) rate * bit * channel) - 10L;
                }

                if (pcmTransport.callback != null) {
                    byte[] bytes = PCMManager.getInstance().sendPackageToCar(pcmData.getPcm(),
                            rate, bit, channel, pcmData.getType(), pcmData.getPos());
                    WlLinkSdkLog.d("TTSPcmTransportImpl onSendPCM :" + bytes.length + "rate: " + rate + "bit:" + bit + "channel: " + channel + "pos "
                            + pcmData.getPos() + "type " + pcmData.getType() + "thread id " + Thread.currentThread().getId());
                    pcmTransport.callback.onSendPCM(bytes);
                }
                if (isStartPcmThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(PcmRunnable.this, delayTime, TimeUnit.MILLISECONDS);
                }
            } else {
                PackagePlasticEngine.PackageELF packageELF = null;
                if (pcmTransport.packagePlasticEngine != null) {
                    packageELF = pcmTransport.packagePlasticEngine.getTailPackageELF();
                }
                if (packageELF != null && packageELF.packageData != null) {
                    byte[] bytes = PCMManager.getInstance().sendPackageToCar(packageELF.packageData,
                            rate, bit, channel, pcmTransport.currentType, PcmConfigs.SOUND_MARK_CENTER);
                    pcmTransport.callback.onSendPCM(bytes);
                    WlLinkSdkLog.d("TTSPcmTransportImpl onSendPCM 222:" + "rate: " + rate + "bit:" + bit + "channel: " + channel + "pos "
                            + "type " + pcmTransport.currentType + "length = " + bytes.length + "thread id " + Thread.currentThread().getId());
                }
                if (isStartPcmThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(PcmRunnable.this, 0, TimeUnit.MILLISECONDS);
                }
            }
        }
    }

    @Override
    public void clearPcmData() {
        if (packagePlasticEngine != null) {
            packagePlasticEngine.getTailPackageELF();
        }
        mVector.clear();
    }

    /**
     * 开启pcm传输线程
     */
    @Override
    public void startTransportThread() {
        if (pcmRunnable == null) {
            pcmRunnable = new PcmRunnable(this, rate, bit, channel);
        } else {
            pcmRunnable.setParameters(rate, bit, channel);
        }
        pcmRunnable.setStartPcmThread(true);
        ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(pcmRunnable, 0, TimeUnit.MILLISECONDS);
        WlLinkSdkLog.d("TTSPcmTransportImpl onSendPCM : startTransportThread ");
    }

    /**
     * 取消pcm传输线程
     */
    @Override
    public void cancelTransportThread(int type) {
        mVector.clear();
        if (pcmRunnable != null) {
            byte[] bytes = PCMManager.getInstance().sendPackageToCar(new byte[0],
                    rate, bit, channel, type, 2);
            callback.onSendPCM(bytes);
            pcmRunnable.setStartPcmThread(false);
            pcmRunnable = null;
        }
        WlLinkSdkLog.d("TTSPcmTransportImpl onSendPCM : cancelTransportThread ");
    }

    @Override
    public void release() {
    }
}
