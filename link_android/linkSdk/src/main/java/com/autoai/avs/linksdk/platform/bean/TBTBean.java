package com.autoai.avs.linksdk.platform.bean;

/**
 * Welink 三投三方TBT信息实体bean，主要封装TBT需要的参数信息。
 *
 * <AUTHOR>
 */
public class TBTBean {
    /**
     * 当前道路名称
     */
    private String currentRoadName;
    /**
     * 下一个路口名称
     */
    private String roadName;
    /**
     * 下一个路口距离，单位：米
     */
    private int roadDistance;
    /**
     * 下一个路口TBT图标ID
     */
    private int roadTurnIcon;
    /**
     * 距目的地的剩余距离，小余等于0则导航结束，单位：米
     */
    private int remainDistance;
    /**
     * 预估到达目的地的所剩时间，单位：分
     */
    private int remainTime;

    public String getCurrentRoadName() {
        return currentRoadName;
    }

    public void setCurrentRoadName(String currentRoadName) {
        this.currentRoadName = currentRoadName;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public int getRoadDistance() {
        return roadDistance;
    }

    public void setRoadDistance(int roadDistance) {
        this.roadDistance = roadDistance;
    }

    public int getRoadTurnIcon() {
        return roadTurnIcon;
    }

    public void setRoadTurnIcon(int roadTurnIcon) {
        this.roadTurnIcon = roadTurnIcon;
    }

    public int getRemainDistance() {
        return remainDistance;
    }

    public void setRemainDistance(int remainDistance) {
        this.remainDistance = remainDistance;
    }

    public int getRemainTime() {
        return remainTime;
    }

    public void setRemainTime(int remainTime) {
        this.remainTime = remainTime;
    }

    @Override
    public String toString() {
        return "TBTBean{" +
                "currentRoadName='" + currentRoadName + '\'' +
                ", roadName='" + roadName + '\'' +
                ", roadDistance=" + roadDistance +
                ", roadTurnIcon=" + roadTurnIcon +
                ", remainDistance=" + remainDistance +
                ", remainTime=" + remainTime +
                '}';
    }
}
