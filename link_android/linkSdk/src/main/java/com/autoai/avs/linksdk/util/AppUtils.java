package com.autoai.avs.linksdk.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Bundle;

import com.autoai.avs.linksdk.WlLinkSdkLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class AppUtils {
    /**
     * setMapbarMobStat 用于统计
     *
     * @param packName 包名
     */
    public static void setMapbarMobStat(Context context, String packName) {
//		if(packName == null || context == null)
//			return ;
//		PackageManager pm = context.getPackageManager();
//		try {
//			ApplicationInfo info = pm.getApplicationInfo(packName, 0);
//			String appName = (String) pm.getApplicationLabel(info);
//			StatisticsManager.onEvent_Aitalk_OpenApp(context,appName);
//		} catch (NameNotFoundException e) {
//			e.printStackTrace();
//		}
    }


    /**
     * 判断APK是否已经安装
     *
     * @param packageName :包名
     * @return 如果安装了则返回true，否则返回false
     */
    public static boolean isAppInstalled(Context context, String packageName) {
        PackageManager pm = context.getPackageManager();
        boolean installed = false;
        try {
            pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            installed = true;
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return installed;
    }

    /**
     * 随机获取数组里面的数据
     * @return String 随机数
     */
    public static String getRandomData(String[] dataArr) {
        int index = (int) (Math.random() * dataArr.length);
        return dataArr[index];
    }

    public enum RecordType {
        /**
         * 车机录音
         */
        carRecording,
        /**
         * 直接走手机录音
         */
        phoneRecording,
    }


    /**
     * 根据List获取到对应的JSONArray
     */
    public static JSONArray getJSONArrayByList(List<?> list) {
        JSONArray jsonArray = new JSONArray();
        if (list == null || list.isEmpty()) {
            return jsonArray;
        }

        for (Object object : list) {
            try {
                JSONObject obj = new JSONObject(object.toString());
                jsonArray.put(obj);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return jsonArray;
    }

    public static String filterPhoneNumber(String number) {
        String regEx = "[0-9]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(number);
        StringBuilder builder = new StringBuilder();
        while (m.find()) {
            builder.append(m.group());
        }
        return builder.toString();
    }

    public static String getUUID(Context context) {
        String UDID = "";
        try {
            if (context != null) {
//                final TelephonyManager tm = (TelephonyManager) context.getApplicationContext()
//                        .getSystemService(Context.TELEPHONY_SERVICE);

                @SuppressLint("HardwareIds") final String androidId = ""
                        + android.provider.Settings.Secure.getString(
                        context.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);
                final String tmDevice = "" + androidId;
                final String tmSerial = "" + androidId;
                UUID deviceUuid = new UUID(androidId.hashCode(), ((long) tmDevice.hashCode() << 32) | tmSerial.hashCode());
                String uuid = deviceUuid.toString();
                UDID = deviceUuid.toString().replace("-", "");
            }
        } catch (Exception e) {
            return "";
        }
        return UDID;
    }

    public static String getPackageName(Context context) {
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    context.getPackageName(), 0);
            return packageInfo.packageName;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取机型制造商品牌
     */
    public static String getDeviceBrand() {
        String manufacturer = Build.MANUFACTURER;
        String phone_type = null;
        if (manufacturer != null && manufacturer.length() > 0) {
            phone_type = manufacturer.toLowerCase();
            WlLinkSdkLog.d("AppUtils getDeviceBrand: " + phone_type);
        }
        return phone_type;
    }

    /**
     * Bundle 转 String
     */
    public static String bundleToString(Bundle bundle) {
        if (bundle != null) {
            StringBuilder sb = new StringBuilder();
            for (String key : bundle.keySet()) {
                sb.append(key).append(":").append(bundle.get(key)).append(", ");
            }

            if (sb.length() > 1) {
                sb.delete(sb.length() - 1, sb.length());
            }

            return sb.toString();
        } else {
            return "null";
        }
    }
}