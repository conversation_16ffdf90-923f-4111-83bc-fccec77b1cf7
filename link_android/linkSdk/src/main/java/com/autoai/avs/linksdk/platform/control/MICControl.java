package com.autoai.avs.linksdk.platform.control;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.welink.auto.WLMicrophone;

/**
 * Welink 三投三方MIC功能接口，主要提供MIC实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class MICControl {

    private final WLMicrophone mWLMicrophone;

    public MICControl(WLMicrophone mWLMicrophone) {
        this.mWLMicrophone = mWLMicrophone;
    }

    /**
     * 方法名称：startMicrophone
     * 方法描述：开始使用车机mic资源
     */
    public void startMicrophone(WLMicrophone.Callback callback) {
        WlLinkSdkLog.i("MICControl startMicrophone ------->");
        if (mWLMicrophone == null) {
            return;
        }
        mWLMicrophone.request(callback);
        WlLinkSdkLog.i("MICControl startMicrophone ----22222222--->");
    }


    /**
     * 方法名称：stopMicrophone
     * 方法描述：停止使用车机mic资源
     */
    public void stopMicrophone() {
        WlLinkSdkLog.i("MICControl stopMicrophone ------->");
        if (mWLMicrophone == null) {
            return;
        }
        mWLMicrophone.stop();
        WlLinkSdkLog.i("MICControl stopMicrophone ----222222--->");
    }

    /**
     * 方法名称：isSupportMicrophone
     * 方法描述：判断车机是否支持录音
     */
    public boolean isSupportMicrophone() {
        WlLinkSdkLog.i("MICControl stopMicrophone ------->");
        return mWLMicrophone != null;
    }
}
