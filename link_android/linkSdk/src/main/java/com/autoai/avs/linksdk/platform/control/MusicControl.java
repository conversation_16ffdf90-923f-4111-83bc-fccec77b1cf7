package com.autoai.avs.linksdk.platform.control;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.bean.ID3Bean;
import com.autoai.avs.linksdk.platform.bean.PCMBean;
import com.autoai.welink.auto.WLMusic;

/**
 * Welink 三投三方音乐功能接口，主要提供音乐实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class MusicControl {
    private final WLMusic mWLMusic;

    /**
     * 当前播放音乐位置
     */
    private long playPosition = 0;
    /**
     * 为了解决超出播放总大小问题，设置控制标识
     */
    private boolean isPlaying = true;
    /**
     * 当前播放音乐pcm参数信息
     */
    private PCMBean mPCMBean;
    /**
     * 当前播放音乐ID3参数信息
     */
    private static ID3Bean mID3Bean = null;
    /**
     * 当前音乐的播放顺序
     */
    private static int mOrder = -1;


    public MusicControl(WLMusic mWLMusic) {
        this.mWLMusic = mWLMusic;
    }

    /**
     * 开启音乐，注册音乐回调监听处理类
     */
    public void registerMusic(WLMusic.Callback callback) {
        WlLinkSdkLog.i("MusicControl registerMusic -------------->");
        if (mWLMusic == null) {
            return;
        }
        mWLMusic.register(callback);
        mWLMusic.requestFocus();
    }


    /**
     * 关闭音乐，注册音乐回调监听处理类
     */
    public void unRegisterMusic() {
        WlLinkSdkLog.i("MusicControl unRegisterMusic -------------->");
        if (mWLMusic == null) {
            return;
        }

        mWLMusic.register(null);
    }


    /**
     * 开始音乐播放
     */

    public void startMusic(PCMBean mPCMBean, long playPosition) {
        WlLinkSdkLog.i("MusicControl startMusic -------------->mPCMBean:" + mPCMBean.toString() + ",playPosition:" + playPosition);
        if (mWLMusic == null) {
            return;
        }
        this.mPCMBean = mPCMBean;
        this.playPosition = playPosition;
        this.mWLMusic.start(mPCMBean.getTotalLen(), mPCMBean.getRate(), mPCMBean.getBit(), mPCMBean.getChannel());
        this.isPlaying = playPosition < mPCMBean.getTotalLen();
    }

    /**
     * 暂停音乐播放
     */
    public void pauseMusic() {
        WlLinkSdkLog.i("MusicControl pauseMusic -------------->");
        if (mWLMusic == null) {
            return;
        }
        this.mWLMusic.pause();
    }

    /**
     * 恢复音乐播放
     */
    public void resumeMusic() {
        WlLinkSdkLog.i("MusicControl resumeMusic -------------->");
        if (mWLMusic == null) {
            return;
        }
        this.mWLMusic.resume();
    }

    /**
     * 停止音乐播放
     */
    public void stopMusic() {
        WlLinkSdkLog.i("MusicControl stopMusic -------------->");
        if (mWLMusic == null) {
            return;
        }
        this.mWLMusic.stop();
    }

    /**
     * 手机端给车机端发送Id3数据信息
     */
    public void updateMusicID3(ID3Bean bean) {
        WlLinkSdkLog.i("MusicControl updateMusicID3 -------------->bean：" + (bean == null ? "null" : bean.toString()));

        if (mWLMusic == null) {
            return;
        }
        if (bean == null) {
            if (mID3Bean != null) {
                mWLMusic.updateID3(mID3Bean.getSource(), mID3Bean.getArtist(), mID3Bean.getTitle(),
                        mID3Bean.getAlbum(), mID3Bean.getLyric(), mID3Bean.getLyricType(), mID3Bean.getDuration(), mID3Bean.getCover());
            }
        } else {
            mID3Bean = bean;
            mWLMusic.updateID3(bean.getSource(), bean.getArtist(), bean.getTitle(),
                    bean.getAlbum(), bean.getLyric(), bean.getLyricType(), bean.getDuration(), bean.getCover());
        }
    }


    /**
     * 更新音乐播报顺序
     */
    public void updateMusicOrder(int order) {
        WlLinkSdkLog.i("MusicControl updateMusicOrder -------------->order:" + order);

        if (mWLMusic == null) {
            return;
        }
        if (order == -1) {
            if (mOrder != -1) {
                mWLMusic.updateOrder(mOrder);
            }
        } else {
            mOrder = order;
            mWLMusic.updateOrder(order);
        }
    }

    /**
     * 手机端给车机端发送pcm音频数据信息
     */
    public long updateMusicPCM(byte[] pcm) {
        WlLinkSdkLog.i("MusicControl updateMusicPCM -------------->isPlaying:" + isPlaying + ",playPosition:" + playPosition);
        if (mWLMusic == null || mPCMBean == null) {
            return -1;
        }

        if (!isPlaying) {
            return playPosition;
        }

        if (pcm != null) {
            WlLinkSdkLog.i("MusicControl updateMusicPCM -- 传输播放中 ---->playPosition:" + playPosition + ",mPCMBean:" + mPCMBean);
            mWLMusic.updatePCM(playPosition, pcm);
            this.playPosition += pcm.length;
        } else {
            WlLinkSdkLog.i("MusicControl updateMusicPCM -- 传输暂停播放 ---->playPosition:" + playPosition + ",mPCMBean:" + mPCMBean);
            mWLMusic.updatePCM(playPosition, null);
        }

        if (playPosition >= mPCMBean.getTotalLen()) {
            isPlaying = false;
        }

        return playPosition;
    }
}
