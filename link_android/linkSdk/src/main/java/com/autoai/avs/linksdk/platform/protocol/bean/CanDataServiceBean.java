package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Welink手机交互协议处理车机端candata数据，此协议主要是用来实现车况检查功能。
 *
 * <AUTHOR>
 */

public class CanDataServiceBean extends BaseProtocolBean{

    /**
     * distance字段是否可用
     * 0：不可用
     */
    private int distanceStatus;
    /**
     * 里程数
     */
    private int distance;
    /**
     * 距离单位
     * 0：km 1：m
     */
    private int distanceUnit;
    /**
     * time字段是否可用
     * 0：不可用
     */
    private int timeStatus;
    /**
     * 天数
     */
    private int time;

    public int getDistanceStatus() {
        return distanceStatus;
    }

    public int getDistance() {
        return distance;
    }

    public int getDistanceUnit() {
        return distanceUnit;
    }

    public int getTimeStatus() {
        return timeStatus;
    }

    public int getTime() {
        return time;
    }

    @Override
    public String toString() {
        return "CanDataServiceBean{" +
                "distanceStatus=" + distanceStatus +
                ", distance=" + distance +
                ", distanceUnit=" + distanceUnit +
                ", timeStatus=" + timeStatus +
                ", time=" + time +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.distanceStatus = extData.getInt("distanceStatus");
        this.distance = extData.getInt("distance");
        this.distanceUnit = extData.getInt("distanceUnit");
        this.timeStatus = extData.getInt("timeStatus");
        this.time = extData.getInt("time");
    }


}
