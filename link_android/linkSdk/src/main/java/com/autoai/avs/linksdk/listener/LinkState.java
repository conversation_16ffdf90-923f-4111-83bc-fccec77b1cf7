package com.autoai.avs.linksdk.listener;

import androidx.annotation.IntDef;

public class LinkState {
    /**
     * 互联状态: 默认的空闲状态
     */
    public static final int STATE_IDLE = 0;
    /**
     * 互联状态: 无线互联的搜索车机的状态
     */
    public static final int STATE_SEARCHING = 1;
    /**
     * 互联状态: 正在连接中
     */
    public static final int STATE_CONNECTING = 2;
    /**
     * 互联状态: 成功连接
     */
    public static final int STATE_CONNECTED = 3;
    /**
     * 互联状态: 断开连接
     */
    public static final int STATE_DISCONNECT = 4;

    /**
     * 互联状态: 无线连接, 未发现设备
     */
    public static final int STATE_NO_DEVICES = 5;

    /**
     * 互联状态: 未知异常造成的操作, 此时也属于未连接状态
     */
    public static final int STATE_ERROR = 9;

    @IntDef({
            STATE_IDLE,
            STATE_SEARCHING,
            STATE_CONNECTING,
            STATE_CONNECTED,
            STATE_DISCONNECT,
            STATE_NO_DEVICES,
            STATE_ERROR
    })
    public @interface State {
    }

}
