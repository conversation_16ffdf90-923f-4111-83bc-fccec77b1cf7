package com.autoai.avs.linksdk.platform.audio.track;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.audio.WLAudioConfig;
import com.autoai.avs.linksdk.platform.audio.bean.WLAudioType;
import com.autoai.avs.linksdk.platform.audio.listener.IWLAudioTrack;
import com.autoai.avs.linksdk.platform.audio.listener.WLAudioListener;
import com.autoai.link.threadpool.ThreadPoolUtil;

import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * Welink PCM音频手机播报处理类，主要负责手机的音频播报处理。
 *
 * <AUTHOR>
 */
public class MUAudioTrack implements IWLAudioTrack {
    private AudioTrack audioTrack;

    private int audioType;
    private WLAudioListener mListener;

    private int rate = 0;
    private int bit = 0;
    private int channelType = 0;

    private long soundTotalSize = 0;
    private long soundPlaySize = 0;

    public AudioTrack getAudioTrack() {
        return audioTrack;
    }

    /**
     * 方法名称：setAudioParams
     * 方法描述：设置播放参数
     * 方法参数：rate 采样频率（采样率），bit 采样位数（采样精度），channels 声道数量（声道）,soundTotalSize 播放数据总长度
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 16:08
     */


    public void setAudioParams(int rate, int bit, int channelType, long soundTotalSize) {
        WlLinkSdkLog.i("MUAudioTrack setAudioParams --------> rate:" + rate + ",bit:" + bit + ",channelType:" + channelType + ",soundTotalSize:" + soundTotalSize);

        this.rate = rate;
        this.bit = bit;
        this.channelType = channelType;
        this.soundTotalSize = soundTotalSize;
        this.soundPlaySize = 0;

        if (audioTrack != null) {
            audioTrack.flush();
            audioTrack.release();
            audioTrack = null;
        }
    }


    /**
     * 方法名称：setPlayStateListener
     * 方法描述：设置音源类型和回调监听
     * 方法参数：audioType 播放声音类型，mListener 播放回调处理对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void setPlayStateListener(int audioType, WLAudioListener mListener) {
        WlLinkSdkLog.i("MUAudioTrack setPlayStateListener --------> audioType:" + audioType);

        this.audioType = audioType;
        this.mListener = mListener;

        int bufferSize = AudioTrack.getMinBufferSize(rate, channelType, AudioFormat.ENCODING_PCM_16BIT);
        //音源类型为音乐则扩大缓存区，避免播放卡顿
        if (WLAudioType.MUSIC_TAG == audioType) {
            bufferSize = bufferSize * getMinBufferZoom(rate);
        }
        audioTrack = new AudioTrack(AudioManager.STREAM_MUSIC, rate, channelType, AudioFormat.ENCODING_PCM_16BIT, bufferSize, AudioTrack.MODE_STREAM);
    }

    /**
     * 方法名称：setPlayPosition
     * 方法描述：设置音乐已播放的大小
     * 方法参数：playPosition 播放大小
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/28 15:49
     */

    @Override
    public void setPlayPosition(long playPosition) {
        WlLinkSdkLog.i("MUAudioTrack setPlayPosition -------->playPosition:" + playPosition);

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.soundPlaySize = playPosition;
        }
    }

    /**
     * 方法名称：setTotalSize
     * 方法描述：更新设置音乐播放的总大小
     * 方法参数：bufferSize 减少播放数据大小
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2021/4/23 15:49
     */

    @Override
    public void setTotalSize(long bufferSize) {
        WlLinkSdkLog.i("MUAudioTrack setTotalSize -------->bufferSize:" + bufferSize);

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.soundTotalSize -= bufferSize;
        }
    }

    /**
     * 方法名称：setPlaySize
     * 方法描述：更新设置音乐已播放的大小
     * 方法参数：bufferSize 添加播放数据大小
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2021/8/5 15:49
     */

    @Override
    public void setPlaySize(long bufferSize) {
        WlLinkSdkLog.i("MUAudioTrack setPlaySize -------->bufferSize:" + bufferSize);

        if (WLAudioType.MUSIC_TAG == audioType) {
            this.soundPlaySize += bufferSize;
        }
    }

    /**
     * 方法名称：start
     * 方法描述：开始更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void start(boolean isConnectcar) {
        WlLinkSdkLog.i("MUAudioTrack start --------> ");

        if (audioTrack == null) {
            return;
        }

        //STATE_INITIALIZED 表示 AudioTrack 已经是可以使用了。
        //STATE_UNINITIALIZED 表示 AudioTrack 创建时没有成功地初始化。
        //STATE_NO_STATIC_DATA 表示当前是使用 MODE_STATIC ，但是还没往缓冲区中写入数据。当接收数据之后会变为 STATE_INITIALIZED 状态。

        if (audioTrack.getState() == AudioTrack.STATE_UNINITIALIZED) {
            return;
        }

        if (WLAudioType.MUSIC_TAG == audioType && soundPlaySize >= soundTotalSize && !isConnectcar) {
            if (mListener != null) {
                WlLinkSdkLog.i("MUAudioTrack write ---------> progress:100 onFinish");
                mListener.onProgress(100);
                mListener.onFinish();
            }
            return;
        }

        //PLAYSTATE_STOPPED 停止
        //PLAYSTATE_PAUSED 暂停
        //PLAYSTATE_PLAYING 正在播放

        if (audioTrack.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            audioTrack.flush();
            audioTrack.play();
        }

        if (mListener != null && !isConnectcar) {
            WlLinkSdkLog.i("MUAudioTrack start ------ onStart --------> ");
            mListener.onStart();
        }
    }

    /**
     * 方法名称：onPause
     * 方法描述：暂定更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/15 15:49
     */


    @Override
    public void pause(boolean isConnectcar) {
        WlLinkSdkLog.i("MUAudioTrack pause --------> ");
        if (WLAudioType.MUSIC_TAG == audioType && soundTotalSize != 0) {
            if (audioTrack != null && audioTrack.getState() == AudioTrack.STATE_INITIALIZED && audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                audioTrack.pause();
            }
            if (mListener != null && !isConnectcar) {
                WlLinkSdkLog.i("MUAudioTrack pause ------ onPause --------> ");
                mListener.onPause();
            }
        }
    }

    /**
     * 方法名称：resume
     * 方法描述：继续更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/15 15:49
     */

    @Override
    public void resume(boolean isConnectcar) {
        WlLinkSdkLog.i("MUAudioTrack resume --------> ");
        if (WLAudioType.MUSIC_TAG == audioType && soundTotalSize != 0) {
            if (audioTrack != null && audioTrack.getState() == AudioTrack.STATE_INITIALIZED && audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PAUSED) {
                audioTrack.play();
            }
            if (mListener != null && !isConnectcar) {
                WlLinkSdkLog.i("MUAudioTrack resume ------ onResume --------> ");
                mListener.onResume();
            }
        }
    }

    /**
     * 方法名称：finish
     * 方法描述：结束更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Override
    public void finish(boolean isConnectcar) {
        WlLinkSdkLog.i("MUAudioTrack finish --------> ");
    }

    /**
     * 方法名称：write
     * 方法描述：更新数据中
     * 方法参数：bytes PCM数据
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void write(byte[] bytes, boolean isLast) {
        if (audioTrack == null) {
            return;
        }
        if (audioTrack.getState() == AudioTrack.STATE_UNINITIALIZED) {
            return;
        }
        if (audioTrack.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            return;
        }

        int errorCode = audioTrack.write(bytes, 0, bytes.length);
        if (errorCode < 0) {
            if (mListener != null) {
                mListener.onError(WLAudioConfig.WL_AUDIOMANAGER_ERROR_CODE_TRACK_WIRITE, errorCode);
            }
        } else {
            this.soundPlaySize += bytes.length;
            WlLinkSdkLog.i("MUAudioTrack write --------> date size:" + bytes.length + ",soundPlaySize:" + soundPlaySize + ",soundTotalSize:" + soundTotalSize + ",isLast:" + isLast);
            if (WLAudioType.MUSIC_TAG == audioType && soundTotalSize != 0) {
                if (isLast) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(() -> {
                        audioTrack.stop();
                        audioTrack.flush();
                        if (mListener != null) {
                            WlLinkSdkLog.i("MUAudioTrack write ---------> progress:100 onFinish");
                            mListener.onProgress(100);
                            mListener.onFinish();
                        }
                    }, 500, TimeUnit.MILLISECONDS);
                } else {
                    if (mListener != null) {
                        double progress = Double.parseDouble(String.format(Locale.getDefault(), "%.2f", soundPlaySize * 100.0 / soundTotalSize));
                        if (progress <= 100.0) {
                            WlLinkSdkLog.i("MUAudioTrack write ---------> progress:" + progress);
                            mListener.onProgress(progress);
                        }
                    }
                }
            } else {
                if (isLast) {
                    audioTrack.stop();
                    audioTrack.flush();
                    if (mListener != null) {
                        WlLinkSdkLog.i("MUAudioTrack write ---------> onFinish");
                        mListener.onFinish();
                    }
                }
            }
        }
    }

    /**
     * 方法名称：stop
     * 方法描述：停止播放并清空播放数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void stop(boolean isConnectcar) {
        WlLinkSdkLog.i("MUAudioTrack stop --------> ");
        this.soundPlaySize = 0;
        if (audioTrack != null) {
            if (audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                audioTrack.stop();
                audioTrack.flush();
//                if(mListener != null && !isConnectcar){
//                    WlLinkSdkLog.i("stop ------ onFinish --------> ");
//                    mListener.onFinish();
//                }
            }
        }
    }

    /**
     * 方法名称：clear
     * 方法描述：清空播放数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void clear() {
        WlLinkSdkLog.i("MUAudioTrack clear --------> ");
        this.soundPlaySize = 0;
        if (audioTrack != null) {
            if (audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                audioTrack.stop();
                audioTrack.flush();
            }
        }
    }

    /**
     * 方法名称：release
     * 方法描述：释放播放对象
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void release() {
        WlLinkSdkLog.i("MUAudioTrack release --------> ");
        this.soundPlaySize = 0;
        if (audioTrack != null) {
            if (audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                audioTrack.stop();
                audioTrack.flush();
            }
            audioTrack.release();
            audioTrack = null;
        }
    }


    /**
     * 方法名称：setVolume
     * 方法描述：设置音量
     * 方法参数：gain 音量值
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */


    @Override
    public void setVolume(float gain) {
        WlLinkSdkLog.i("MUAudioTrack setVolume --------> gain:" + gain);
        if (audioTrack != null) {
            audioTrack.setVolume(gain);
        }
    }

    /**
     * 方法名称：updatePlayDevice
     * 方法描述：依据互联状态更新音频数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/05/29 16:01
     */

    @Override
    public void updatePlayDevice(boolean isConnectcar, long playPosition) {
        WlLinkSdkLog.i("MUAudioTrack updatePlayDevice --------> isConnectcar:" + isConnectcar + ",playPosition:" + playPosition);
        this.soundPlaySize = playPosition;
    }

    /**
     * 方法名称：getMinBufferZoom
     * 方法描述：依据音频数据采样频率参数获取手机播放器最下缓存倍数
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2021/04/6 10:01
     */

    private int getMinBufferZoom(int rate) {
        int zoom = 10;

        switch (rate) {
            case 192000:
            case 96000:
            case 88200:
            case 64000:
            case 48000:
            case 44100:
                zoom = 10;
                break;
            case 32000:
                zoom = 20;
                break;
            case 22050:
                zoom = 30;
                break;
            case 16000:
            case 11025:
                zoom = 40;
                break;
            case 8000:
            case 6000:
                zoom = 50;
                break;
        }
        WlLinkSdkLog.i("MUAudioTrack getMinBufferZoom --------> zoom:" + zoom);
        return zoom;
    }
}
