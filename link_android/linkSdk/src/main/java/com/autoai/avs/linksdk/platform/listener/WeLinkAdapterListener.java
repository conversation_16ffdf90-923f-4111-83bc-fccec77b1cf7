package com.autoai.avs.linksdk.platform.listener;

import android.app.Activity;
import android.app.Notification;
import android.content.Intent;
import android.view.MotionEvent;

import androidx.annotation.Keep;

/**
 * Welink 三投平台手车连接状态接口，主要在平台应用中定义手车互联状态接口。
 *
 * <AUTHOR>
 */
@Keep
public interface WeLinkAdapterListener {
    void onLinkConnected(Activity mContext, Notification mNotification,int huScreenWidth, int huScreenHeight, int densityDpi, String vehicleType);
    void onLinkDisconnected();
    void onLinkTouch(MotionEvent motionEvent);
    void activityResult(int requestCode, int resultCode, Intent data);
}
