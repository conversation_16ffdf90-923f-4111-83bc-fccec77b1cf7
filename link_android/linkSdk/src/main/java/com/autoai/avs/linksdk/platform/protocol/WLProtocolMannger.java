package com.autoai.avs.linksdk.platform.protocol;

import android.text.TextUtils;

import androidx.annotation.Keep;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.WLConnectManager;
import com.autoai.avs.linksdk.platform.protocol.control.HUProtocolControl;
import com.autoai.avs.linksdk.platform.protocol.control.MUCacheControl;
import com.autoai.avs.linksdk.platform.protocol.control.MUProtocolControl;
import com.autoai.avs.linksdk.platform.protocol.listener.HUCommandListener;
import com.autoai.avs.linksdk.platform.protocol.model.NaviInfoModel;

import org.json.JSONObject;

/**
 * Welink手机交互协议管理类，主要统一处理手机发送端的消息和接收车机端接收的消息。
 *
 * <AUTHOR>
 */
@Keep
public class WLProtocolMannger {
    private static final String TAG = "Protocol_WLProtocolMannger";

    private MUCacheControl muCacheControl;
    private MUProtocolControl muProtocolControl;
    private HUProtocolControl huProtocolControl;

    private static WLProtocolMannger mWLProtocolMannger;

    private WLProtocolMannger() {
        this.muCacheControl = new MUCacheControl();
        this.muProtocolControl = new MUProtocolControl(muCacheControl);
        this.huProtocolControl = new HUProtocolControl();
    }

    @Keep
    public static WLProtocolMannger getInstance() {
        if (mWLProtocolMannger == null) {
            synchronized (WLProtocolMannger.class) {
                if (mWLProtocolMannger == null) {
                    mWLProtocolMannger = new WLProtocolMannger();
                }
            }
        }
        return mWLProtocolMannger;
    }

    public void onDestory() {
        this.muCacheControl = null;
        this.muProtocolControl = null;
        this.huProtocolControl = null;
    }

    public static void Release() {
        if (mWLProtocolMannger != null) {
            mWLProtocolMannger.onDestory();
            mWLProtocolMannger = null;
        }
    }

    /**
     * 添加指定方法的监听用来接收手机端的协议消息广播
     */
    @Keep
    public void addMethodListener(String methodTag, HUCommandListener mListener) {
        WlLinkSdkLog.i("WLConnectManager addMethodListener ---------> methodTag:" + methodTag);
        huProtocolControl.addListener(methodTag, mListener);
    }

    /**
     * 添加指定方法的监听用来接收手机端的协议消息广播,并支持是否接收已有的缓存消息
     */
    @Keep
    public void addMethodListener(String methodTag, HUCommandListener mListener, boolean isCache) {
        WlLinkSdkLog.i("WLConnectManager addMethodListener ---------> methodTag:" + methodTag + ",isCache:" + isCache);
        huProtocolControl.addListener(methodTag, mListener);
        if (isCache) {
            String cacheCommand = muCacheControl.getMethodProtocol(methodTag);
            if (!TextUtils.isEmpty(cacheCommand)) {
                try {
                    JSONObject obj = new JSONObject(cacheCommand);
                    obj = obj.getJSONObject(WLProtocolConfig.WL_PROTOCOL_FIELD_COMMAND);
                    JSONObject extData = obj.getJSONObject(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA);
                    huProtocolControl.parseCommand(methodTag, extData);
                } catch (Exception e) {
                    WlLinkSdkLog.e("WLConnectManager addMethodListener exception:" + e.getMessage());
                }
            }
        }
    }

    /**
     * 移除指定方法的监听禁止接收手机端的协议消息广播
     */
    @Keep
    public void removeMethodListener(String methodTag, HUCommandListener mListener) {
        WlLinkSdkLog.i("WLConnectManager addMethodListener ---------> methodTag:" + methodTag);
        huProtocolControl.removeListener(methodTag, mListener);
    }

    /**
     * 清空所有方法的监听禁止接收手机端的协议消息广播
     */
    @Keep
    public void clearAllMethodListener() {
        WlLinkSdkLog.i("WLConnectManager clearMethodListener --------->");
        huProtocolControl.clearListener();
    }

    /**
     * 添加协议消息到协议缓存消息容器中
     */
    public void addMethodProtocol(String method, String message) {
        WlLinkSdkLog.i("WLConnectManager addMethodProtocol ---------> method:" + method + ",message:" + message);
        //添加到缓存数据消息中
        muCacheControl.addMethodProtocol(method, message);
    }

    /**
     * 发送默认字符串格式消息
     * 方法参数：command - 字符串消息
     */
    @Keep
    public void sendCommondMessage(String command) {
        WlLinkSdkLog.i("WLConnectManager sendCommondMessage ---------> command:" + command);
        muProtocolControl.sendMessageDataCommand(command);
    }

    /**
     * 要发送缓存的协议消息方法名称
     * 方法参数：method - 消息方法名称
     */
    @Keep
    public void sendCacheCommondMessage(String method) {
        WlLinkSdkLog.i("WLConnectManager sendCacheCommondMessage ---------> method:" + method);
        String command = muCacheControl.getMethodProtocol(method);
        if (!TextUtils.isEmpty(command)) {
            muProtocolControl.sendMessageDataCommand(command);
        }
    }

    /**
     * 发送默认字符串格式消息
     * method - 消息方法名称
     * command - 字符串消息
     * isCache - 是否添加到缓存
     */
    @Keep
    public void sendCommondMessage(String method, String command, boolean isCache) {
        WlLinkSdkLog.i("WLConnectManager sendCommondMessage ---------> method:" + method + ",command:" + command + ",isCache:" + isCache);
        if (isCache) {
            //添加到缓存数据消息中
            muCacheControl.addMethodProtocol(method, command);
        }
        muProtocolControl.sendMessageDataCommand(command);
    }

    /**
     * 发送candata字符串格式消息
     * 方法参数：command - 字符串消息
     */
    @Keep
    public void sendCanDataMessage(String command) {
        WlLinkSdkLog.i("WLConnectManager sendCanDataMessage ---------> command:" + command);
        muProtocolControl.sendCanDataCommand(command);
    }

    /**
     * 要发送缓存的协议消息方法名称
     * 方法参数：method - 消息方法名称
     */
    @Keep
    public void sendCacheCanDataMessage(String method) {
        WlLinkSdkLog.i("WLConnectManager sendCacheCanDataMessage ---------> method:" + method);
        String command = muCacheControl.getMethodProtocol(method);
        if (!TextUtils.isEmpty(command)) {
            muProtocolControl.sendCanDataCommand(command);
        }
    }

    /**
     * 发送candata字符串格式消息
     * method - 消息方法名称
     * command - 字符串消息
     * isCache - 是否添加到缓存
     */
    @Keep
    public void sendCanDataMessage(String method, String command, boolean isCache) {
        WlLinkSdkLog.i("WLConnectManager sendCanDataMessage ---------> method:" + method + ",command:" + command + ",isCache:" + isCache);
        if (isCache) {
            //添加到缓存数据消息中
            muCacheControl.addMethodProtocol(method, command);
        }
        muProtocolControl.sendCanDataCommand(command);
    }


    /**
     * 给车机端发送手机端自定义按键
     * 方法参数：pressDownKey 手机端自定义按键
     */
    @Keep
    public void sendMuChannelToCar(int pressDownKey) {
        WlLinkSdkLog.i("WLConnectManager sendMuChannelToCar ---------> pressDownKey:" + pressDownKey);
        muProtocolControl.sendMuChannelToCar(pressDownKey);
    }

    /**
     * 给车机端发送WeLink退出消息
     * 方法参数：isExitWelink - 是否退出
     */
    @Keep
    public void sendExitWelinkToCar(boolean isExitWelink) {
        WlLinkSdkLog.i("WLConnectManager sendExitWelinkToCar ---------> isExitWelink:" + isExitWelink);
        muProtocolControl.sendExitWelinkToCar(isExitWelink);
    }

    /**
     * 给车机端发送手机屏幕大小消息
     * 方法参数：width - 手机宽，height - 手机高
     */
    @Keep
    public void sendScreenSizeToCar(int width, int height) {
        WlLinkSdkLog.i("WLConnectManager sendScreenSizeToCar ---------> width:" + width + ",height:" + height);
        muProtocolControl.sendScreenSizeToCar(width, height);
    }


    /**
     * 给车机端发送手机互联状态消息
     * 方法参数：isLink - 互联状态
     */
    @Keep
    public void sendAutoLinkToCar(boolean isLink) {
        WlLinkSdkLog.i("WLConnectManager sendAutoLinkToCar ---------> isLink:" + isLink);
        muProtocolControl.sendAutoLinkToCar(isLink);
    }


    /**
     * 给车机端发送手机拨打电话消息
     * 方法参数：number - 电话号码
     */
    @Keep
    public void sendTelphoneToCar(String number) {
        WlLinkSdkLog.i("WLConnectManager sendTelphoneToCar ---------> number:" + number);
        muProtocolControl.sendTelphoneToCar(number);
    }


    /**
     * 此协议主要是点击手机端常驻条的home键时，通知车机端置为后台；
     */
    @Keep
    public void sendHomeKeyToCar() {
        WlLinkSdkLog.i("WLConnectManager sendTelphoneToCar --------->");
        muProtocolControl.sendHomeKeyToCar();
    }


    /**
     * 此协议主要是手机端通知车机端停止当前音频pcm播报
     * 方法参数：pcmInfo 消息状态
     */
    @Keep
    public void sendStopPlayPCMToCar(int pcmInfo) {
        WlLinkSdkLog.i("WLConnectManager sendStopPlayPCMToCar ---------> pcmInfo:" + pcmInfo);
        muProtocolControl.sendStopPlayPCMToCar(pcmInfo);
    }


    /**
     * 此协议主要是手机端通知车机端开启静音模式和取消静音模式
     * 方法参数：state - 1 开启静音模式（静音模式下，除了VR的声音，其他声音都不进行播报），2 退出静音模式
     */
    @Keep
    public void sendChangeMuteToCar(int state) {
        WlLinkSdkLog.i("WLConnectManager sendChangeMuteToCar ---------> state:" + state);
        muProtocolControl.sendChangeMuteToCar(state);
    }


    /**
     * 此协议主要是手机端通知车机端打开车载媒体
     */
    @Keep
    public void sendStartMediaToCar() {
        WlLinkSdkLog.i("WLConnectManager sendStartMediaToCar --------->");
        muProtocolControl.sendStartMediaToCar();
    }


    /**
     * 消息拦截通知
     * 方法参数：type - 0-微信，1-QQ，2-短信
     */
    @Keep
    public void sendMsgReceiveToCar(int type) {
        WlLinkSdkLog.i("WLConnectManager sendMsgReceiveToCar ---------> type:" + type);
        muProtocolControl.sendMsgReceiveToCar(type);
    }


    /**
     * 微信给车机发送命令消息
     */
    @Keep
    public void sendWeChatResponseToCar(int code) {
        WlLinkSdkLog.i("WLConnectManager sendWeChatResponseToCar ---------> code:" + code);
        muProtocolControl.sendWeChatResponseToCar(code);
    }

    /**
     * 给车机发送蓝牙连接状态消息
     */
    @Keep
    public void sendBluetoothStateToCar(boolean state) {
        WlLinkSdkLog.i("WLConnectManager sendBluetoothStateToCar ---------> state:" + state);
        muProtocolControl.sendBluetoothStateToCar(state);
    }

    /**
     * 给车机发送手机端音乐播放状态
     * 方法参数：state 播放状态 1(MEDIA_STATE_START)：开始播放；2(MEDIA_STATE_START)：停止播放
     */
    @Keep
    public void sendMediaStateToCar(int state) {
        WlLinkSdkLog.i("WLConnectManager sendBluetoothStateToCar ---------> state:" + state);
        muProtocolControl.sendMediaStateToCar(state);
    }

    /**
     * 给车机发送手机端地图中心点数据的
     * 方法参数：miniMapState 小地图当前状态；centerPoix 中心点x轴坐标；centerPoiy centerPoiy；showwidth 展示的宽；showhigh 展示的高
     */
    @Keep
    public void sendMobileNaviStateToCar(boolean miniMapState, int centerPoix, int centerPoiy, int showwidth, int showhigh) {
        WlLinkSdkLog.i("WLConnectManager sendMobileNaviStateToCar ---------> miniMapState:" + miniMapState + ",centerPoix:" + centerPoix + ",centerPoiy:" + centerPoiy + ",showwidth:" + showwidth + ",showhigh:" + showhigh);
        muProtocolControl.sendMobileNaviStateToCar(miniMapState, centerPoix, centerPoiy, showwidth, showhigh);
    }

    /**
     * 音量调节控制
     * 方法参数：controlVolume 具体调节 - requestType 车机调节类型
     */
    @Keep
    public void sendControlVolumeToCar(int controlVolume, int requestType) {
        WlLinkSdkLog.i("WLConnectManager sendControlVolumeToCar ---------> controlVolume:" + controlVolume + ",requestType:" + requestType);
        muProtocolControl.sendControlVolumeToCar(controlVolume, requestType);
    }

    /**
     * 方法描述：音量静音控制
     * 方法参数：controlVolume 具体调节 - requestType 车机调节类型
     */
    @Keep
    public void sendMuteVolumeToCar(int muteVolume, int requestType) {
        WlLinkSdkLog.i("WLConnectManager sendMuteVolumeToCar ---------> muteVolume:" + muteVolume + ",requestType:" + requestType);
        muProtocolControl.sendMuteVolumeToCar(muteVolume, requestType);
    }

    /**
     * 方法描述：空调开关控制
     * 方法参数：aircoSwitch 空调开关 类型 int 1：关闭空调 2：开启空调
     * 方法参数：requestType 车机调节类型 类型 int
     * 方法参数：aircoSwitch 温区 类型 int []
     */
    @Keep
    public void sendAircoSwitchToCar(int aircoSwitch, int requestType, int[] zoneArr) {
        WlLinkSdkLog.i("WLConnectManager sendAircoSwitchToCar ---------> aircoSwitch:" + aircoSwitch + ",requestType:" + requestType);
        muProtocolControl.sendAircoSwitchToCar(aircoSwitch, requestType, zoneArr);
    }

    /**
     * 方法描述：空调温度控制
     * 方法参数：airTemperatureType 温度单位 类型 int 1：摄氏度单位 2：华氏度单位
     * 方法参数：aircoTemp 空调温度调节 类型 double
     * 方法参数：requestType 车机调节类型 类型 int
     * 方法参数：zoneArr 温区 类型 int[]
     */

    @Keep
    public void sendAircoTempToCar(int airTemperatureType, double aircoTemp, int requestType, int[] zoneArr) {
        WlLinkSdkLog.i("WLConnectManager sendAircoTempToCar ---------> airTemperatureType:" + airTemperatureType + ",aircoTemp:" + aircoTemp + ",requestType:" + requestType);
        muProtocolControl.sendAircoTempToCar(airTemperatureType, aircoTemp, requestType, zoneArr);
    }

    /**
     * 空调风量控制
     * 方法参数：风量模式 类型 int 1：automatical 2：manual
     * 方法参数：空调风量调节 类型 int automatical模式下： 1：soft 2：medium 3：intensive
     * 方法参数：arequestType 车机调节类型 类型 int
     * 方法参数：zoneArr  温区 类型 int []
     */

    @Keep
    public void sendAircoBlowingrateToCar(int climateMode, int aircoBlowingrate, int requestType, int[] zoneArr) {
        WlLinkSdkLog.i("WLConnectManager sendAircoBlowingrateToCar ---------> climateMode:" + climateMode + ",aircoBlowingrate:" + aircoBlowingrate + ",requestType:" + requestType);
        muProtocolControl.sendAircoBlowingrateToCar(climateMode, aircoBlowingrate, requestType, zoneArr);
    }


    /**
     * 发送TBT协议
     * 方法参数：model - 实体bean
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/10/30 9:53
     */

    @Keep
    public void sendNaviTBTInfo(NaviInfoModel model) {
        WlLinkSdkLog.i("WLConnectManager sendNaivTBTInfo ---------> model:" + model.toString());
        muProtocolControl.sendNaviTBTInfo(model);
    }

//    /**
//     * 方法名称：sendMusicProInfo
//     * 方法描述：发送ID3协议
//     * 方法参数：type - 音乐类型 0：music 1：redio ，progressBar - 当前播放歌曲的进度信息，单位（ms），mode -播放模式
//     * 返回类型：
//     * 创建人：wangzc
//     * 创建时间：2020/10/30 9:53
//     */

//    @Keep
//    public void sendMusicProInfo(int type, int progressBar, int mode) {
//         WlLinkSdkLog.i( "WLConnectManager sendMusicProInfo ---------> type:" + type + ",progressBar:" + progressBar + ",mode:" + mode);
//        muProtocolControl.sendMusicProInfo(type,progressBar,mode);
//    }

//    /**
//     * 方法名称：sendMusicID3Info
//     * 方法描述：发送ID3协议
//     * 方法参数：type - 类型，model - 实体bean
//     * 返回类型：
//     * 创建人：wangzc
//     * 创建时间：2020/10/30 9:53
//     */

//    @Keep
//    public void sendMusicID3Info(int type, MusicInfoModel model) {
//         WlLinkSdkLog.i( "WLConnectManager sendMusicID3Info ---------> model:" + model.toString());
//        muProtocolControl.sendMusicID3Info(type,model);
//    }

    /**
     * 手机端释放车机端 mic 资源
     */
    @Keep
    public void sendReleaseMic() {
        WlLinkSdkLog.i("WLConnectManager sendReleaseMic --------->");
        muProtocolControl.sendReleaseMic();
    }

    /**
     * 手机端申请车机端 mic 资源
     */
    @Keep
    public void sendApplyMic() {
        WlLinkSdkLog.i("WLConnectManager sendApplyMic --------->");
        muProtocolControl.sendApplyMic();
    }

    /**
     * 手机端通知车机端导航状态 1 开始导航；2 结束导航
     */
    @Keep
    public void sendNaviState(int state) {
        WlLinkSdkLog.i("WLConnectManager sendNaviState --------->");
        muProtocolControl.sendNaviState(state);
    }

    /**
     * 接收并解析处理platform平台传递过的车机消息
     */
    public void onReceiveHUCommand(String command) {
        WlLinkSdkLog.i("WLConnectManager onReceiveHUCommand ---------> command:" + command);
        try {
            JSONObject obj = new JSONObject(command);
            obj = obj.getJSONObject(WLProtocolConfig.WL_PROTOCOL_FIELD_COMMAND);
            String method = obj.getString(WLProtocolConfig.WL_PROTOCOL_FIELD_METHOD);
            if (!TextUtils.isEmpty(method)) {
                //缓存方法最新协议
                muCacheControl.addMethodProtocol(method, command);
                //处理请求协议
                if (WLProtocolConfig.HU_PROTOCOL_METHOD_REQ_APP_DATA.equals(method)) {
                    JSONObject extData = obj.getJSONObject(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA);
                    onReceiveHURequest(extData);
                }
                //处理普通协议
                else {
                    JSONObject extData = obj.getJSONObject(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA);
                    huProtocolControl.parseCommand(method, extData);
                }
            }
        } catch (Exception e) {
            WlLinkSdkLog.e("WLConnectManager onReceiveHUCommand exception:" + e.getMessage());
        }
    }


    /**
     * 接收并解析处理platform平台传递过的车机车机请求消息
     */
    private void onReceiveHURequest(JSONObject extData) {
        WlLinkSdkLog.i("WLConnectManager onReceiveHURequest ---------> extData:" + extData);
        try {
            int reqAction = extData.getInt("reqAction");
            switch (reqAction) {
                case 1: //播放模式和音乐ID3信息数据
                    WLConnectManager connectManager = WLConnectManager.getInstance();
                    connectManager.updateMusicID3(null);
                    connectManager.updateMusicOrder(-1);
                    break;
                case 2: //WeLink 通知车机系统当前 WeLink 音乐播放/暂停状态
                {
                    String data = muCacheControl.getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE);
                    if (!TextUtils.isEmpty(data)) {
                        sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, data, false);
                    }
                }
                break;
                case 3: //导航信息TBT
                {
                    String data = muCacheControl.getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_TBT);
                    if (!TextUtils.isEmpty(data)) {
                        sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_TBT, data, false);
                    }
                }
                break;
                case 4: //导航状态
                {
                    String data = muCacheControl.getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_STATE);
                    if (!TextUtils.isEmpty(data)) {
                        sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_STATE, data, false);
                    }
                }
                break;

            }
        } catch (Exception e) {
            WlLinkSdkLog.e("WLConnectManager onReceiveHURequest exception:" + e.getMessage());
        }
    }

}
