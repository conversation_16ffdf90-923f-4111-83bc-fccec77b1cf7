package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端发送车机端 VR 状态向手机端 launcher发的协议。
 * @param vrState 车机端VR开启状态
 *              true 开启车机端VR
 *              false 关闭车机端VR
 */

public class HuVRBean extends BaseProtocolBean{

    private boolean vrState;

    public boolean isVrState() {
        return vrState;
    }

    @Override
    public String toString() {
        return "HuVRBean{" +
                "vrState=" + vrState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.vrState = extData.getBoolean(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_VR);
    }


}
