package com.autoai.avs.linksdk.platform.audio.transport;

import android.os.SystemClock;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.audio.listener.TransportListener;
import com.autoai.avs.linksdk.platform.pcm.packagePlasticEngine.PackagePlasticEngine;
import com.autoai.link.threadpool.ThreadPoolUtil;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Welink 音乐PCM音频数据分包处理类，主要负责音乐音频数据分包处理。
 *
 * <AUTHOR>
 */
public class MusicTransport extends BaseTransport {
    private volatile boolean isStopMusicThread = false;
    private volatile boolean isFinishTransport = false;
    private final LinkedBlockingQueue<byte[]> mMusicLinkedBlockingQueue = new LinkedBlockingQueue<>(50);
    private PackagePlasticEngine mPackagePlasticEngine;

    private final AtomicLong supportPlayTime = new AtomicLong(0);
    private final AtomicLong delayPlayTime = new AtomicLong(0);
    private final AtomicLong firstSendTime = new AtomicLong(0);

    private static final int isCacheCount = 3;
    private final AtomicLong isCacheIndex = new AtomicLong(0);

    private final Runnable mMusicStartRunnable = new Runnable() {
        @Override
        public void run() {
            if (isStopMusicThread) {
                return;
            }
            if (!mMusicLinkedBlockingQueue.isEmpty()) {
                byte[] bytes = new byte[0];
                try {
                    bytes = mMusicLinkedBlockingQueue.take();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                mTransportListener.onMusicData(bytes, false);
                long sleepTime;
                if (isCacheIndex.get() < isCacheCount) {
                    isCacheIndex.addAndGet(1L);
                    sleepTime = 0;
                } else {
                    long playTime = (bytes.length * 8000L) / ((long) rate * bit * channels);
                    if (firstSendTime.get() == 0) {
                        delayPlayTime.set(0);
                        sleepTime = playTime;
                        firstSendTime.set(SystemClock.elapsedRealtime());
                    } else {
                        delayPlayTime.set(SystemClock.elapsedRealtime() - firstSendTime.get());
                        sleepTime = playTime - (delayPlayTime.get() - supportPlayTime.get());
                    }
                    supportPlayTime.set(supportPlayTime.get() + playTime);
                }

                WlLinkSdkLog.d("MusicTransport mMusicThread ----onMusicData--111--> mVector size:" + mMusicLinkedBlockingQueue.size() + ",sleepTime:" + sleepTime
                        + ",firstSendTime:" + firstSendTime + ",supportPlayTime:" + supportPlayTime + ",delayPlayTime:" + delayPlayTime + ",isCacheIndex:" + isCacheIndex);

                if (!isStopMusicThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(mMusicOnDataRunnable, sleepTime, TimeUnit.MILLISECONDS);
                }
            } else {
                WlLinkSdkLog.d("MusicTransport mMusicThread ----onMusicData--222--> mVector size:" + mMusicLinkedBlockingQueue.size() + ",sleepTime:0" + ",isCacheIndex:" + isCacheIndex);
                if (!isStopMusicThread) {
                    ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(mMusicOnDataRunnable, 0, TimeUnit.MILLISECONDS);
                }
            }
        }
    };
    private final Runnable mMusicOnDataRunnable = new Runnable() {
        @Override
        public void run() {
            if (isStopMusicThread) {
                return;
            }
            if (isFinishTransport && mMusicLinkedBlockingQueue.size() == 0 && mPackagePlasticEngine != null) {
                PackagePlasticEngine.PackageELF elf = mPackagePlasticEngine.getTailPackageELF();
                if (elf != null && elf.packageData != null) {
                    WlLinkSdkLog.d("MusicTransport mMusicThread ----onMusicData--3333--> mVector size:" + mMusicLinkedBlockingQueue.size());
                    mTransportListener.onMusicData(elf.packageData, true);
                }
            }
            if (!isStopMusicThread) {
                ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(mMusicStartRunnable, 0, TimeUnit.MILLISECONDS);
            }
        }
    };

    public MusicTransport(TransportListener mTransportListener, int rate, int bit, int channels) {
        super(mTransportListener, rate, bit, channels);
    }

    /**
     * 依据互联状态设置音频数据提前发送的时间
     *
     * @param flag 互联状态
     */
    @Override
    public void setConnectCar(boolean flag) {
        super.setConnectCar(flag);
        this.isCacheIndex.set(0);
        this.firstSendTime.set(0);
        this.supportPlayTime.set(0);
        this.delayPlayTime.set(0);
    }

    /**
     * 接收解码数据进行分包处理
     *
     * @param pcmData 解码数据
     */
    @Override
    public void producePcm(byte[] pcmData) {
        WlLinkSdkLog.i("MusicTransport producePcm mPackagePlasticEngine is null: " + (mPackagePlasticEngine == null ? "true" : "false") + ",length:" + pcmData.length);
        if (mPackagePlasticEngine == null) {
            return;
        }
        List<PackagePlasticEngine.PackageELF> list = mPackagePlasticEngine.plastic(pcmData);
        WlLinkSdkLog.i("MusicTransport producePcm list size: " + ((list == null) ? " null " : list.size()));

        if (list != null && list.size() > 0) {
            for (PackagePlasticEngine.PackageELF elf : list) {
                try {
                    mMusicLinkedBlockingQueue.put(elf.packageData);
                    WlLinkSdkLog.v("MusicTransport producePcm mMusicLinkedBlockingQueue size:" + mMusicLinkedBlockingQueue.size());
                } catch (InterruptedException e) {
                    WlLinkSdkLog.i("MusicTransport producePcm mMusicLinkedBlockingQueue.put.exception:" + e.getMessage());
                }
            }
        }
    }

    /**
     * 解码数据传输完毕
     */
    public void finishTransport() {
        WlLinkSdkLog.i("MusicTransport finishTransport ---------->");
        this.isFinishTransport = true;
    }

    /**
     * 开始分包传输线程
     */
    @Override
    public void startTransportThread() {
        WlLinkSdkLog.i("MusicTransport startTransportThread -------------->");
        this.isStopMusicThread = false;
        this.isFinishTransport = false;
        synchronized (this) {
            this.mPackagePlasticEngine = new PackagePlasticEngine(35304);

            this.isCacheIndex.set(0);
            this.firstSendTime.set(0);
            this.supportPlayTime.set(0);
            this.delayPlayTime.set(0);
            ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(mMusicStartRunnable, 0, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 继续开始分包传输线程
     */
    @Override
    public void resumeTransportThread() {
        WlLinkSdkLog.i("MusicTransport resumeTransportThread -------------->");
        if (!isStopMusicThread) {
            return;
        }
        this.isStopMusicThread = false;

        this.isCacheIndex.set(0);
        this.firstSendTime.set(0);
        this.supportPlayTime.set(0);
        this.delayPlayTime.set(0);
        ThreadPoolUtil.getInstance().getScheduledExecutor().schedule(mMusicStartRunnable, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 暂停分包传输线程
     */
    @Override
    public void pauseTransportThread() {
        WlLinkSdkLog.i("MusicTransport pauseTransportThread -------------->");
        if (isStopMusicThread) {
            return;
        }
        this.isStopMusicThread = true;
    }

    /**
     * 停止分包传输线程
     */
    @Override
    public void stopTransportThread() {
        WlLinkSdkLog.i("MusicTransport stopTransportThread -------------->");
        synchronized (this) {
            this.isStopMusicThread = true;
            if (mPackagePlasticEngine != null) {
                mPackagePlasticEngine.destroy();
                mPackagePlasticEngine = null;
            }
            mMusicLinkedBlockingQueue.clear();
        }
    }
}

