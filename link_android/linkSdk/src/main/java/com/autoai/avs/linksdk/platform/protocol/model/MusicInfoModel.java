package com.autoai.avs.linksdk.platform.protocol.model;

import java.util.ArrayList;
import java.util.List;

public class MusicInfoModel {
    /**
     *音源
     */
    private String source;
    /**
     *演唱者（创建者）
     */
    private String singerName;
    /**
     *歌曲名（电台名）
     */
    private String songName;
    /**
     *专辑名
     */
    private String albumName;
    /**
     * 歌曲封面图片宽
     */
    private int albumWide;
    /**
     *歌曲封面图片高
     */
    private int albumHigh;
    /**
     *当前播放歌曲总长度,单位为 s
     */
    private int duration;
    /**
     *当前列表歌曲播放数量
     */
    private int playlistNum;
    /**
     *当前播放的歌曲 ID
     */
    private String songId;
    /**
     *播放模式   类型 int 0:顺序循环播放  1：随机播放 2：单曲播放 3:顺序播放
     */
    private int mode;
    /**
     *歌词数组
     */
    private List<MusicLyricModel> lyric = new ArrayList<MusicLyricModel>();

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSingerName() {
        return singerName;
    }

    public void setSingerName(String singerName) {
        this.singerName = singerName;
    }

    public String getSongName() {
        return songName;
    }

    public void setSongName(String songName) {
        this.songName = songName;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public int getAlbumWide() {
        return albumWide;
    }

    public void setAlbumWide(int albumWide) {
        this.albumWide = albumWide;
    }

    public int getAlbumHigh() {
        return albumHigh;
    }

    public void setAlbumHigh(int albumHigh) {
        this.albumHigh = albumHigh;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getPlaylistNum() {
        return playlistNum;
    }

    public void setPlaylistNum(int playlistNum) {
        this.playlistNum = playlistNum;
    }

    public String getSongId() {
        return songId;
    }

    public void setSongId(String songId) {
        this.songId = songId;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public List<MusicLyricModel> getLyric() {
        return lyric;
    }

    public void setLyric(List<MusicLyricModel> lyric) {
        this.lyric = lyric;
    }

    @Override
    public String toString() {
        return "MusicInfo_Model{" +
                "source='" + source + '\'' +
                ", singerName='" + singerName + '\'' +
                ", songName='" + songName + '\'' +
                ", albumName='" + albumName + '\'' +
                ", albumWide=" + albumWide +
                ", albumHigh=" + albumHigh +
                ", duration=" + duration +
                ", playlistNum=" + playlistNum +
                ", songId='" + songId + '\'' +
                ", mode=" + mode +
                ", lyric=" + lyric +
                '}';
    }
}
