package com.autoai.avs.linksdk.platform.bean;

/**
 * Welink 三投三方PCM信息实体bean，主要封装PCM需要的参数信息。
 *
 * <AUTHOR>
 */
public class PCMBean {
    /**
     * 声音数据总长度
     */
    private long totalLen;
    /**
     * 采样率
     */
    private int rate;
    /**
     * 采样精度
     */
    private int bit;
    /**
     * 声道数
     */
    private int channel;

    public long getTotalLen() {
        return totalLen;
    }

    public void setTotalLen(long totalLen) {
        this.totalLen = totalLen;
    }

    public int getRate() {
        return rate;
    }

    public void setRate(int rate) {
        this.rate = rate;
    }

    public int getBit() {
        return bit;
    }

    public void setBit(int bit) {
        this.bit = bit;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    @Override
    public String toString() {
        return "PCMBean{" +
                "totalLen=" + totalLen +
                ", rate=" + rate +
                ", bit=" + bit +
                ", channel=" + channel +
                '}';
    }
}
