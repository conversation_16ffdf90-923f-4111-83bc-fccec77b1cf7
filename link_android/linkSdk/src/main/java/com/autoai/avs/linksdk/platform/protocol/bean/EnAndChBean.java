package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 *此协议主要是用来实现车机端通知手机d端进行中英文切换。
 * @param controlState 中英文 int 1:中文 2：英文
 */
public class EnAndChBean extends BaseProtocolBean{

    private int controlState;

    public int getControlState() {
        return controlState;
    }

    @Override
    public String toString() {
        return "EnAndChBean{" +
                "controlState=" + controlState +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.controlState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_CONTROLSTATE);
    }


}
