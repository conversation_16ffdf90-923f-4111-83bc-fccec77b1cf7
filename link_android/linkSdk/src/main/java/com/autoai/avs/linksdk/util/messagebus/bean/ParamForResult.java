package com.autoai.avs.linksdk.util.messagebus.bean;

import java.util.List;

public class ParamForResult<T> extends Param {
    private final ForResultCallback<T> mForResult;

    public ParamForResult(List<String> mParamList, ForResultCallback<T> mForResult) {
        super(mParamList);
        this.mForResult = mForResult;
    }

    public ForResultCallback<T> getmForResult() {
        return mForResult;
    }

    public interface ForResultCallback<T> {
        void onResult(String tag, T t);
    }
}
