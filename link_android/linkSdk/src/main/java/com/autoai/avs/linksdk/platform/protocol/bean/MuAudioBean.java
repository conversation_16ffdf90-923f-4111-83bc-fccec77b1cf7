package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是通过车机来控制手机端launcher的音源播放
 * @param audioState 类型
 *                   1：暂停播放
 *                   2：恢复播放
 *                   3：上一首
 *                   4：下一首
 *                   5: 随机模式
 *                   6：顺序循环模式
 *                   7：顺序模式
 *                   8：单曲循环模式
 *                   10: 从车载媒体入口进入SVW Link，自动跳转到音频模块
 */

public class MuAudioBean extends BaseProtocolBean{

    private int audioState;

    public int getAudioState() {
        return audioState;
    }

    @Override
    public String toString() {
        return "MuAudioBean{" +
                "audioState=" + audioState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.audioState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_AUDIO);
    }


}
