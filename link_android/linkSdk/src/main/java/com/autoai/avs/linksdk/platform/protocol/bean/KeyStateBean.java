package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 车机发送车机按键状态给手机端，SaciLink 语音开始和 SaciLink 语音结束。
 * keyState 按键状态 int 1：开始 SaciLink 语音 int 2：结束 SaciLink 语音 int 3：行车记录仪抓拍
 */
public class KeyStateBean extends BaseProtocolBean{

    private int keyState;

    public int getKeyState() {
        return keyState;
    }

    public void setKeyState(int keyState) {
        this.keyState = keyState;
    }

    @Override
    public String toString() {
        return "KeyStateBean{" +
                "keyState=" + keyState +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.keyState = extData.getInt("keyState");
    }


}
