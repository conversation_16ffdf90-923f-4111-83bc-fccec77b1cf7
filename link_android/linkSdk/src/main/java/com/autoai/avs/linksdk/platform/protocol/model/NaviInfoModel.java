package com.autoai.avs.linksdk.platform.protocol.model;

/**
 *
 */
public class NaviInfoModel {

    /**
     * 电子眼信息编号
     * 类型 int
     * 字段属性：可选
     */
    private int digitalCode;
    /**
     * 当前速度
     * 类型 int 单位 Km/h
     */
    private int currentSpeed;
    /**
     * 当到下一引导的进度百分比
     * 类型 double
     * 字段属性：可选
     */
    private double nextProgress;
    /**
     * 当前路名
     * 类型 String
     * 字段属性：可选
     */
    private String currentRoadName;
    /**
     * 导航指引信息编号
     * 类型 int
     * 字段属性：必选
     */
    private int naviCode;
    /**
     * 下一引导地点名称
     * 类 型 string
     * 字段属性：必选
     */
    private String roadName;
    /**
     * 下一引导的剩余距离
     * 类型 int
     * 字段属性：必选
     */
    private int roadDistance;
    /**
     * roadDistance 字段单位说明
     * 类型 int
     * 1：m
     * 2：km
     * 字段属性：必选
     */
    private int roadDistanceFlag;
    /**
     * 全路程剩余距离
     * 类型 int
     * 字段属性：必选
     */
    private int remainDistance;
    /**
     * remainDistance 字段单位说明
     * 类型 int
     * 1：m
     * 2：km
     * 字段属性：必选
     */
    private int remainDistanceFlag;
    /**
     * 全路程剩余时间
     * 类 型 string
     * 字段属性：必选
     */
    private String remainTime;

    public int getDigitalCode() {
        return digitalCode;
    }

    public void setDigitalCode(int digitalCode) {
        this.digitalCode = digitalCode;
    }

    public int getCurrentSpeed() {
        return currentSpeed;
    }

    public void setCurrentSpeed(int currentSpeed) {
        this.currentSpeed = currentSpeed;
    }

    public double getNextProgress() {
        return nextProgress;
    }

    public void setNextProgress(double nextProgress) {
        this.nextProgress = nextProgress;
    }

    public String getCurrentRoadName() {
        return currentRoadName;
    }

    public void setCurrentRoadName(String currentRoadName) {
        this.currentRoadName = currentRoadName;
    }

    public int getNaviCode() {
        return naviCode;
    }

    public void setNaviCode(int naviCode) {
        this.naviCode = naviCode;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public int getRoadDistance() {
        return roadDistance;
    }

    public void setRoadDistance(int roadDistance) {
        this.roadDistance = roadDistance;
    }

    public int getRoadDistanceFlag() {
        return roadDistanceFlag;
    }

    public void setRoadDistanceFlag(int roadDistanceFlag) {
        this.roadDistanceFlag = roadDistanceFlag;
    }

    public int getRemainDistance() {
        return remainDistance;
    }

    public void setRemainDistance(int remainDistance) {
        this.remainDistance = remainDistance;
    }

    public int getRemainDistanceFlag() {
        return remainDistanceFlag;
    }

    public void setRemainDistanceFlag(int remainDistanceFlag) {
        this.remainDistanceFlag = remainDistanceFlag;
    }

    public String getRemainTime() {
        return remainTime;
    }

    public void setRemainTime(String remainTime) {
        this.remainTime = remainTime;
    }

    @Override
    public String toString() {
        return "TBT_Model{" +
                "digitalCode=" + digitalCode +
                ", currentSpeed=" + currentSpeed +
                ", nextProgress=" + nextProgress +
                ", currentRoadName='" + currentRoadName + '\'' +
                ", naviCode=" + naviCode +
                ", roadName='" + roadName + '\'' +
                ", roadDistance=" + roadDistance +
                ", roadDistanceFlag=" + roadDistanceFlag +
                ", remainDistance=" + remainDistance +
                ", remainDistanceFlag=" + remainDistanceFlag +
                ", remainTime='" + remainTime + '\'' +
                '}';
    }
}
