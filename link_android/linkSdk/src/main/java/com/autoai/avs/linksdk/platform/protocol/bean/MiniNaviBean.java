package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端显示小地图向手机端launcher发送的协议
 * @param naviState 类型
 *                  1：打开
 *                  2：关闭
 *                  3: 放大
 *                  4： 缩小
 * @param pageState 类型
 *                  0：默认界面
 *                  1：进入音乐界面
 *                  2：进入导航界面
 */

public class MiniNaviBean extends BaseProtocolBean{

    private int naviState;
    private int pageState;

    public int getNaviState() {
        return naviState;
    }

    public int getPageState() {
        return pageState;
    }

    @Override
    public String toString() {
        return "MiniNaviBean{" +
                "naviState=" + naviState +
                ", pageState=" + pageState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.naviState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_NAVI);
        this.pageState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_PAGE);
    }


}
