package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端发送车机端导航状态给手机端 launcher发的协议。
 * @param naviState 车机端导航开启状态
 *                  true 开启车机端导航
 *                  false 关闭车机端导航
 */

public class HuNaviBean extends BaseProtocolBean{

    private boolean naviState;

    public boolean isNaviState() {
        return naviState;
    }

    @Override
    public String toString() {
        return "HuNaviBean{" +
                "naviState=" + naviState +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.naviState = extData.getBoolean(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_NAVI);
    }


}
