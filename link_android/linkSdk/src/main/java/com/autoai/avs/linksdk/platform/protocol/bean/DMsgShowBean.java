package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端 D类消息的 H5 界面通知手机端 launcher来进行展示
 * @param haddress h5地址
 */

public class DMsgShowBean extends BaseProtocolBean{

    private String haddress;

    public String getHaddress() {
        return haddress;
    }

    @Override
    public String toString() {
        return "DMsgShowBean{" +
                "haddress='" + haddress + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.haddress = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_HADDRESS);
    }


}
