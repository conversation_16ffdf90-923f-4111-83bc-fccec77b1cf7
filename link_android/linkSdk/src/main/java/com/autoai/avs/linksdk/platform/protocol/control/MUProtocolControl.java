package com.autoai.avs.linksdk.platform.protocol.control;

import android.text.TextUtils;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.WLPlatformManager;
import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;
import com.autoai.avs.linksdk.platform.protocol.model.MusicInfoModel;
import com.autoai.avs.linksdk.platform.protocol.model.MusicLyricModel;
import com.autoai.avs.linksdk.platform.protocol.model.NaviInfoModel;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * Welink手机交互协议手机端发送协议处理类，主要定义封装发送各种消息接口。
 *
 * <AUTHOR>
 */
public class MUProtocolControl {
    private final MUCacheControl muCacheControl;

    public MUProtocolControl(MUCacheControl muCacheControl) {
        this.muCacheControl = muCacheControl;
    }

    /**
     * 给车机端发送手机端自定义按键
     *
     * @param pressDownKey 手机端自定义按键
     */
    public void sendMuChannelToCar(int pressDownKey) {
        WlLinkSdkLog.i("MUProtocolControlsendMuChannelToCar ---------> pressDownKey:" + pressDownKey);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_DOWNKEY, pressDownKey);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MUCHANNEL, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlsendMuChannelToCar exception:" + e.getMessage());
        }
    }


    /**
     * 给车机端发送WeLink退出消息
     *
     * @param isExitWelink - 是否退出
     */
    public void sendExitWelinkToCar(boolean isExitWelink) {
        WlLinkSdkLog.i("MUProtocolControlsendExitWelinkToCar ---------> isExitWelink:" + isExitWelink);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_EXIT, isExitWelink);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_EXITWELINK, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlsendExitWelinkToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给车机端发送手机屏幕大小消息
     *
     * @param width - 手机宽，height - 手机高
     */
    public void sendScreenSizeToCar(int width, int height) {
        WlLinkSdkLog.i("MUProtocolControlsendScreenSizeToCar ---------> width:" + width + ",height:" + height);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_WIDTH, width);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_HEIGHT, height);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_SCREENCHANGE, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlsendScreenSizeToCar exception:" + e.getMessage());
        }
    }


    /**
     * 给车机端发送手机互联状态消息
     *
     * @param isLink - 互联状态
     */
    public void sendAutoLinkToCar(boolean isLink) {
        WlLinkSdkLog.i("MUProtocolControlsendAutoLinkToCar ---------> isLink:" + isLink);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_STATE, isLink);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AUTOLINK, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlsendAutoLinkToCar exception:" + e.getMessage());
        }
    }


    /**
     * 给车机端发送手机拨打电话消息
     *
     * @param number - 电话号码
     */
    public void sendTelphoneToCar(String number) {
        WlLinkSdkLog.i("MUProtocolControlsendTelphoneToCar ---------> number:" + number);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_PHONE, number);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_CALLNUMBER, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlsendTelphoneToCar exception:" + e.getMessage());
        }
    }


    /**
     * 此协议主要是点击手机端常驻条的home键时，通知车机端置为后台；
     */
    public void sendHomeKeyToCar() {
        WlLinkSdkLog.i("MUProtocolControlsendTelphoneToCar --------->");
        try {
            JSONObject extData = new JSONObject();
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_HOMEKEY, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendHomeKeyToCar exception:" + e.getMessage());
        }
    }


    /**
     * 此协议主要是手机端通知车机端停止当前音频pcm播报
     */
    public void sendStopPlayPCMToCar(int pcmInfo) {
        WlLinkSdkLog.i("MUProtocolControlsendStopPlayPCMToCar ---------> pcmInfo:" + pcmInfo);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_PCMINFO, pcmInfo);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_STOPPLAY, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlsendStopPlayPCMToCar exception:" + e.getMessage());
        }
    }


    /**
     * 此协议主要是手机端通知车机端开启静音模式和取消静音模式
     *
     * @param state - 1 开启静音模式（静音模式下，除了VR的声音，其他声音都不进行播报），2 退出静音模式
     */
    public void sendChangeMuteToCar(int state) {
        WlLinkSdkLog.i("MUProtocolControlsendChangeMuteToCar ---------> state:" + state);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MUTE, state);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MUTE, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlsendChangeMuteToCar exception:" + e.getMessage());
        }
    }


    /**
     * 此协议主要是手机端通知车机端打开车载媒体
     */
    public void sendStartMediaToCar() {
        WlLinkSdkLog.i("MUProtocolControlsendStartMediaToCar --------->");
        try {
            JSONObject extData = new JSONObject();
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_STARTMEDIA, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendStartMediaToCar exception:" + e.getMessage());
        }
    }


    /**
     * 消息拦截通知
     *
     * @param type - 0-微信，1-QQ，2-短信
     */
    public void sendMsgReceiveToCar(int type) {
        WlLinkSdkLog.i("MUProtocolControlsendMsgReceiveToCar ---------> type:" + type);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_TYPE, type);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MSGRECEIVE, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendMsgReceiveToCar exception:" + e.getMessage());
        }
    }


    /**
     * 微信给车机发送命令消息
     */
    public void sendWeChatResponseToCar(int code) {
        WlLinkSdkLog.i("MUProtocolControlsendWeChatResponseToCar ---------> code:" + code);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_CODE, code);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_WXRESPONSE, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendWeChatResponseToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给车机发送蓝牙连接状态消息
     */
    public void sendBluetoothStateToCar(boolean state) {
        WlLinkSdkLog.i("MUProtocolControlsendBluetoothStateToCar ---------> state:" + state);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_BLUETOOTH, state);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_BLUETOOTH, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendBluetoothStateToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给车机发送手机端音乐播放状态
     *
     * @param state 播放状态 1(MEDIA_STATE_START)：开始播放；2(MEDIA_STATE_START)：停止播放
     */
    public void sendMediaStateToCar(int state) {
        WlLinkSdkLog.i("MUProtocolControlsendMediaStateToCar ---------> state:" + state);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MEDIA, state);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, extData);
            //添加到缓存数据消息中
            muCacheControl.addMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, data);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendMediaStateToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给车机发送手机端地图中心点数据的
     *
     * @param miniMapState 小地图当前状态；centerPoix 中心点x轴坐标；centerPoiy centerPoiy；showwidth 展示的宽；showhigh 展示的高
     */
    public void sendMobileNaviStateToCar(boolean miniMapState, int centerPoix, int centerPoiy, int showwidth, int showhigh) {
        WlLinkSdkLog.i("MUProtocolControlsendMobileNaviStateToCar ---------> miniMapState:" + miniMapState + ",centerPoix:" + centerPoix + ",centerPoiy:" + centerPoiy + ",showwidth:" + showwidth + ",showhigh:" + showhigh);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MAP, miniMapState);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_POIX, centerPoix);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_POIY, centerPoiy);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_SHOW_WIDTH, showwidth);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_SHOW_HIGH, showhigh);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MOBILENAVI, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendMediaStateToCar exception:" + e.getMessage());
        }
    }

    /**
     * 音量调节控制
     *
     * @param controlVolume 具体调节
     * @param requestType   车机调节类型
     */
    public void sendControlVolumeToCar(int controlVolume, int requestType) {
        WlLinkSdkLog.i("MUProtocolControlsendControlVolumeToCar ---------> controlVolume:" + controlVolume + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_CONTROL_VOLUME, controlVolume);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_CONTROL_VOLUME, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendControlVolumeToCar exception:" + e.getMessage());
        }
    }

    /**
     * 音量静音控制
     *
     * @param muteVolume  具体调节 -
     * @param requestType 车机调节类型
     */
    public void sendMuteVolumeToCar(int muteVolume, int requestType) {
        WlLinkSdkLog.i("MUProtocolControlsendMuteVolumeToCar ---------> muteVolume:" + muteVolume + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MUTE_VOLUME, muteVolume);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MUTE_VOLUME, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendMuteVolumeToCar exception:" + e.getMessage());
        }
    }

    /**
     * 空调开关控制
     *
     * @param aircoSwitch 空调开关 类型 int 1：关闭空调 2：开启空调
     * @param requestType 车机调节类型 类型 int
     * @param aircoSwitch 温区 类型 int []
     */
    public void sendAircoSwitchToCar(int aircoSwitch, int requestType, int[] zoneArr) {
        WlLinkSdkLog.i("MUProtocolControlsendAircoSwitchToCar ---------> aircoSwitch:" + aircoSwitch + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIRCO_SWITCH, aircoSwitch);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);

            JSONArray jsonArray = new JSONArray();
            if (zoneArr != null) {
                for (int j : zoneArr) {
                    jsonArray.put(j);
                }
            }
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_ZONEARR, jsonArray);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AIRCON_SWITCH, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendAircoSwitchToCar exception:" + e.getMessage());
        }
    }

    /**
     * 空调温度控制
     *
     * @param airTemperatureType 温度单位 类型 int 1：摄氏度单位 2：华氏度单位
     * @param aircoTemp          空调温度调节 类型 double
     * @param requestType        车机调节类型 类型 int
     * @param zoneArr            温区 类型 int[]
     */
    public void sendAircoTempToCar(int airTemperatureType, double aircoTemp, int requestType, int[] zoneArr) {
        WlLinkSdkLog.i("MUProtocolControlsendAircoTempToCar ---------> airTemperatureType:" + airTemperatureType + ",aircoTemp:" + aircoTemp + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIR_TEMP_TYPE, airTemperatureType);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIRCO_TEMP, aircoTemp);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);

            JSONArray jsonArray = new JSONArray();
            if (zoneArr != null) {
                for (int j : zoneArr) {
                    jsonArray.put(j);
                }
            }
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_ZONEARR, jsonArray);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AIRCON_TEMP, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendAircoTempToCar exception:" + e.getMessage());
        }
    }

    /**
     * 空调风量控制
     *
     * @param climateMode      风量模式         类型 int 1：automatical 2：manual
     * @param aircoBlowingrate 空调风量调节       类型 int automatical模式下： 1：soft 2：medium 3：intensive
     * @param requestType      车机调节类型 类型 int
     * @param zoneArr          温区 类型 int []
     */
    public void sendAircoBlowingrateToCar(int climateMode, int aircoBlowingrate, int requestType, int[] zoneArr) {
        WlLinkSdkLog.i("MUProtocolControlsendAircoTempToCar ---------> climateMode:" + climateMode + ",aircoBlowingrate:" + aircoBlowingrate + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_CLIMATE_MODE, climateMode);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIRCO_BLOWINGRATE, aircoBlowingrate);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);

            JSONArray jsonArray = new JSONArray();
            if (zoneArr != null) {
                for (int j : zoneArr) {
                    jsonArray.put(j);
                }
            }
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_ZONEARR, jsonArray);

            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AIRCON_BLOWINGGRATE, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendAircoTempToCar exception:" + e.getMessage());
        }
    }

    /**
     * 发送TBT协议
     *
     * @param model - 实体bean
     */

    public void sendNaviTBTInfo(NaviInfoModel model) {
        WlLinkSdkLog.i("MUProtocolControlsendNaviTBTInfo ---------> model:" + model.toString());
        try {
            JSONObject extData = new JSONObject();
            extData.put("digitalCode", model.getDigitalCode());
            extData.put("currentSpeed", model.getCurrentSpeed());
            extData.put("nextProgress", model.getNextProgress());
            extData.put("currentRoadName", model.getCurrentRoadName());
            extData.put("naviCode", model.getNaviCode());
            extData.put("roadName", model.getRoadName());
            extData.put("roadDistance", model.getRoadDistance());
            extData.put("roadDistanceFlag", model.getRoadDistanceFlag());
            extData.put("remainDistance", model.getRemainDistance());
            extData.put("remainDistanceFlag", model.getRoadDistanceFlag());
            extData.put("remainTime", model.getRemainTime());

            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_TBT, extData);
            //添加到缓存数据消息中
            muCacheControl.addMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_TBT, data);

            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendNaviTBTInfo exception:" + e.getMessage());
        }
    }

    /**
     * 发送ID3协议
     *
     * @param type - 音乐类型 0：music 1：redio ，progressBar - 当前播放歌曲的进度信息，单位（ms），mode -播放模式
     */

    public void sendMusicProInfo(int type, int progressBar, int mode) {
        WlLinkSdkLog.i("MUProtocolControlsendMusicProInfo ---------> type:" + type + ",progressBar:" + progressBar + ",mode:" + mode);
        try {
            JSONObject extData = new JSONObject();
            extData.put("progressBar", progressBar);
            extData.put("mode", mode);

            JSONObject commandObject = new JSONObject();
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_METHOD, WLProtocolConfig.MU_PROTOCOL_METHOD_MUSIC_REFERSH);
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_TYPE, type);
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA, extData);

            String data = getCommandProtocol(commandObject);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendMusicProInfo exception:" + e.getMessage());
        }
    }

    /**
     * 发送ID3协议
     *
     * @param type - 类型，model - 实体bean
     */

    public void sendMusicID3Info(int type, MusicInfoModel model) {
        WlLinkSdkLog.i("MUProtocolControlsendMusicID3Info ---------> model:" + model.toString());
        try {
            JSONObject extData = new JSONObject();
            extData.put("source", model.getSource());
            extData.put("singerName", model.getSingerName());
            extData.put("songName", model.getSongName());
            extData.put("albumName", model.getAlbumName());
            extData.put("albumWide", model.getAlbumWide());
            extData.put("albumHigh", model.getAlbumHigh());
            extData.put("duration", model.getDuration());
            extData.put("playlistNum", model.getPlaylistNum());
            extData.put("songId", model.getSongId());
            extData.put("mode", model.getMode());

            List<MusicLyricModel> lyrics = model.getLyric();

            JSONArray jsonArray = new JSONArray();
            if (lyrics != null) {
                for (int i = 0; i < lyrics.size(); i++) {
                    jsonArray.put(lyrics.get(i));
                }
            }
            extData.put("lyric", jsonArray);

            JSONObject commandObject = new JSONObject();
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_METHOD, WLProtocolConfig.MU_PROTOCOL_METHOD_MUSIC_INFO);
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_TYPE, type);
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA, extData);

            String data = getCommandProtocol(commandObject);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendMusicID3Info exception:" + e.getMessage());
        }
    }

    /**
     * 手机端释放车机端 mic 资源
     */

    public void sendReleaseMic() {
        WlLinkSdkLog.i("MUProtocolControlsendReleaseMic --------->");
        try {
            JSONObject extData = new JSONObject();
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_RELEASE_MIC, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendReleaseMic exception:" + e.getMessage());
        }
    }

    /**
     * 手机端申请车机端 mic 资源
     */
    public void sendApplyMic() {
        WlLinkSdkLog.i("MUProtocolControlsendApplyMic --------->");
        try {
            JSONObject extData = new JSONObject();
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_APPLY_MIC, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendApplyMic exception:" + e.getMessage());
        }
    }

    /**
     * 手机端通知车机端导航状态 1 开始导航；2 结束导航
     */
    public void sendNaviState(int state) {
        WlLinkSdkLog.i("MUProtocolControlsendNaviState --------->");
        try {
            JSONObject extData = new JSONObject();
            extData.put("naviState", state);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_STATE, extData);
            //添加到缓存数据消息中
            muCacheControl.addMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_NAVI_STATE, data);

            sendMessageDataCommand(data);
        } catch (Exception e) {
            WlLinkSdkLog.e("MUProtocolControlsendNaviState exception:" + e.getMessage());
        }
    }


    /**
     * 获取指定方法的协议数据
     *
     * @param methodName - 发送协议的方法,extData - JSONObject类型数据参数
     */
    private String getMethodProtocol(String methodName, JSONObject extData) {
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_MODULENAME, WLProtocolConfig.WL_PROTOCOL_VALUE_MODULENAME);
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_VERSION, WLProtocolConfig.WL_PROTOCOL_VALUE_VERSION);
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_PLATFORM, WLProtocolConfig.WL_PROTOCOL_VALUE_PLATFORM);

            JSONObject commandObject = new JSONObject();
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_METHOD, methodName);
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA, extData);

            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_COMMAND, commandObject);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlgetMethodProtocol exception:" + e.getMessage());
        }

        return jsonObject.toString();
    }

    /**
     * 获取指定命令的协议数据
     *
     * @param commandObject - JSONObject类型数据参数
     */
    private String getCommandProtocol(JSONObject commandObject) {
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_MODULENAME, WLProtocolConfig.WL_PROTOCOL_VALUE_MODULENAME);
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_VERSION, WLProtocolConfig.WL_PROTOCOL_VALUE_VERSION);
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_PLATFORM, WLProtocolConfig.WL_PROTOCOL_VALUE_PLATFORM);

            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_COMMAND, commandObject);
        } catch (JSONException e) {
            WlLinkSdkLog.e("MUProtocolControlgetCommandProtocol exception:" + e.getMessage());
        }

        return jsonObject.toString();
    }


    /**
     * 手机给车机发送消息协议数据消息
     */
    public void sendMessageDataCommand(String command) {
        WlLinkSdkLog.i("MUProtocolControlsendMessageCommand ---------> command:" + command);
        if (!TextUtils.isEmpty(command)) {
            WLPlatformManager platformManager = WLPlatformManager.getInstance();
            platformManager.sendMessageDataToCar(command);
        }
    }

    /**
     * 手机给车机发送candata协议数据消息
     */
    public void sendCanDataCommand(String command) {
        WlLinkSdkLog.i("MUProtocolControlsendCanDataCommand ---------> command:" + command);
        if (!TextUtils.isEmpty(command)) {
            WLPlatformManager platformManager = WLPlatformManager.getInstance();
            platformManager.sendCanDataToCar(command);
        }
    }
}
