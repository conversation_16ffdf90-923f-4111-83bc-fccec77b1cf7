package com.autoai.avs.linksdk.platform;

import static android.content.Context.NOTIFICATION_SERVICE;
import static android.content.Context.RECEIVER_EXPORTED;
import static com.autoai.avs.linksdk.platform.WLPlatformConfig.IS_VIDEO_TEST_MODEL;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.Rect;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.Display;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.WindowManager;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.FragmentActivity;

import com.autoai.avs.linksdk.R;
import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.bridge.BridgeActivity;
import com.autoai.avs.linksdk.config.Configs;
import com.autoai.avs.linksdk.platform.bean.ConnectorBean;
import com.autoai.avs.linksdk.platform.bean.WLPlatformState;
import com.autoai.avs.linksdk.platform.listener.WeLinkAdapterListener;
import com.autoai.avs.linksdk.platform.listener.WeLinkPlatformListener;
import com.autoai.avs.linksdk.platform.observer.PlatformObserverable;
import com.autoai.avs.linksdk.platform.pcm.WLPCMPolicyImpl;
import com.autoai.avs.linksdk.platform.protocol.WLProtocolMannger;
import com.autoai.avs.linksdk.util.AppUtils;
import com.autoai.avs.linksdk.util.HUSupportManager;
import com.autoai.avs.linksdk.util.IHUSupportManager;
import com.autoai.avs.linksdk.util.SpUtil;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.autoai.welink.platform.WLPlatform;
import com.autoai.welink.platform.WLPlatformListener;
import com.autoai.welink.screen.WLScreen;
import com.autoai.welink.screen.WLScreenListener;
import com.wedrive.welink.android.security.callbacks.ISecurity;
import com.wedrive.welink.android.security.creator.SecurityFactory;
import com.wedrive.welink.android.security.impl.aes.AESProxy;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/**
 * 主要负责实现并提供平台应用功能接口。
 *
 * <AUTHOR>
 */
@Keep
public class WLPlatformManager {
    private Activity mContext;
    //
    private WLPlatform mWLPlatform;
    private WLScreen mWLScreen;
    //
    private Notification mNotification;
    private PlatformReceiver mPlatformReceiver;
    private WLPlatformState mPlatformCarState = WLPlatformState.STATUS_NONE;

    private IHUSupportManager mHUSupportManager;

    private boolean isEnableExternal = false;
    private boolean isBleInit = false;

    private ISecurity mSecurity;
    private boolean isFirstFrameData = false;
    private boolean isCanDataSecurity = false;
    private final List<String> sendCanDatas = new ArrayList<>();
    private final List<byte[]> receiveCanDatas = new ArrayList<>();

    private int huScreenWidth;
    private int huScreenHeight;
    private int densityDpi;
    private String vehicleType;
    private String vehicleVersion = "";

//    private String mFrameFilePath;
//    private Object lock = new Object();

    private WeLinkPlatformListener mWeLinkPlatformListener;
    private WeLinkAdapterListener mWeLinkAdapterListener;

    private final HashMap<String, ConnectorBean> mAssignKeys = new HashMap<>();

    private static final int WL_PLATFORM_SECURITY_MAX_SIZE = 48;

    private static final int WL_PLATFORM_HANDLER_WHAR_DELAYED_TIME = 1000;
    private static final int WL_PLATFORM_BLE_LINK_SCAN_TIMEOUT = 14 * 1000;

    private static final int WL_PLATFORM_HANDLER_WHAR_SEND = 100;
    private static final int WL_PLATFORM_HANDLER_WHAR_BLE_SCAN = 101;
    private static final int WL_PLATFORM_HANDLER_WHAR_BLE_JOIN = 102;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            if (msg == null) {
                return;
            }
            if (WL_PLATFORM_HANDLER_WHAR_SEND == msg.what) {
                Bundle data = msg.getData();
                if (data != null) {
                    String packageName = data.getString(WLPlatformConfig.WL_PLATFORM_BUNDLE_PACKAGE_NAME);
                    String connectKey = data.getString(WLPlatformConfig.WL_PLATFORM_BUNDLE_CONNECT_KEY);
                    Intent connect = new Intent(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
                    connect.putExtra(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY, connectKey);
                    connect.setPackage(packageName);
                    mContext.sendBroadcast(connect);
                }
            } else if (WL_PLATFORM_HANDLER_WHAR_BLE_SCAN == msg.what) {
                if (mWeLinkPlatformListener != null) {
                    mWeLinkPlatformListener.onBleLinkScanTimeout();
                }
            } else if (WL_PLATFORM_HANDLER_WHAR_BLE_JOIN == msg.what) {
                if (mWeLinkPlatformListener != null) {
                    mWeLinkPlatformListener.onBleLinkJoinTimeout();
                }
            }
        }
    };

    private WLPlatformManager() {
    }

    public static WLPlatformManager getInstance() {
        return WLPlatformManager.INSTANCE.instance;
    }

    private static class INSTANCE {
        private static final WLPlatformManager instance = new WLPlatformManager();
    }

    /**
     * 初始化平台服务管理类
     *
     * @param context              - 上下文
     * @param linkPlatformListener 三投平台手车连接状态接口
     */
    @Keep
    public void init(Activity context, Notification notification, WeLinkPlatformListener linkPlatformListener, boolean isDebug) {
        WlLinkSdkLog.i("WLPlatformManager init -------------->");
        enableDebug(isDebug);
        mContext = context;
        mHUSupportManager = new HUSupportManager();
        mNotification = notification != null ? notification : createDefaultNotification();
        mWeLinkPlatformListener = linkPlatformListener;
        //
        mWLPlatform = WLPlatform.create(mContext, mNotification, new WLPlatformListener() {

            @Override
            public void onLinkConnected(int mScreenWidth, int mScreenHeight, int mDensityDpi, String mVehicleType, String mVehicleVersion, String mBtMacAddress, boolean isAOA) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkConnected -------------->mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi + ",mVehicleType:" + mVehicleType + ",mVehicleVersion:" + mVehicleVersion + ",mBtMacAddress:" + mBtMacAddress + ",isAOA:" + isAOA);
                if (mWLPlatform == null) {
                    return;
                }

//        mFrameFilePath = CommonUtil.dateTimeFormat("yyyy-MM-dd HH:mm:ss") + ".mp4";

                mPlatformCarState = WLPlatformState.STATUS_CAR_CONNECTED;
                mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
                mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
                if (!TextUtils.equals(mVehicleType, vehicleType)) {
                    SpUtil.putString(mContext, Configs.A7_SN, mVehicleType);
                }

                calculateHuVideoSize(mScreenWidth, mScreenHeight);

//        huScreenWidth = mScreenWidth;
//        huScreenHeight = mScreenHeight;

                densityDpi = mDensityDpi;
                vehicleType = mVehicleType;
                vehicleVersion = mVehicleVersion;
                mHUSupportManager.setSN(vehicleType);
                isFirstFrameData = true;
                isCanDataSecurity = mHUSupportManager.isHuSupport(HUSupportManager.CANDATA_SECURITY);
                mAssignKeys.clear();
                mWLPlatform.init(new Rect(0, 0, huScreenWidth, huScreenHeight), WLPCMPolicyImpl.class.getName(), null);
                mWLPlatform.start();
                if (IS_VIDEO_TEST_MODEL) {
                    // 内部测试用, 保存手机录制的视频
                    mWLPlatform.toggleLocalVideo(true, mContext.getExternalFilesDir(null) + "/" + System.currentTimeMillis() + ".mp4");
                }

                mWLScreen.start(huScreenWidth, huScreenHeight, mDensityDpi);
//                stopWifiLink();
                if (mWeLinkPlatformListener != null) {
                    mWeLinkPlatformListener.onLinkConnected(vehicleType, isAOA);
                }
            }

            @Override
            public void onLinkUnconnected(boolean isCrash) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkUnconnected -------------->isCrash:" + isCrash);
                if (mWLPlatform == null) {
                    return;
                }
                mPlatformCarState = WLPlatformState.STATUS_CAR_DISCONNECTED;

                mWLPlatform.stop();
                mWLPlatform.deinit();
//        startWifiLink();

                isEnableExternal = false;
                isFirstFrameData = false;
                isCanDataSecurity = false;
                sendCanDatas.clear();
                receiveCanDatas.clear();
                mSecurity = null;

                if (mWeLinkPlatformListener != null) {
                    mWeLinkPlatformListener.onLinkDisconnected();
                }

                WLPlatformConfig.isConnectcar = false;
                PlatformObserverable.getInstance().notifyObserver(WLPlatformConfig.WL_PLATFORM_OBSERVER_CONNECT_STAET, false);

                if (IS_VIDEO_TEST_MODEL) {
                    mWLPlatform.toggleLocalVideo(false, null);
                }

                if (mWLScreen != null) {
                    mWLScreen.stop();
                }

                //解决无感互联发生异常断开时导致usb互联失败的问题
                Intent intent = mContext.getIntent();
                if (intent != null && intent.getAction() != null && intent.getAction().equals(UsbManager.ACTION_USB_ACCESSORY_ATTACHED)) {
                    if (intent.hasExtra("UsbAccessory")) {
                        UsbAccessory usbAccessory = intent.getParcelableExtra("UsbAccessory");
                        Intent usbIntent = new Intent();
                        usbIntent.setAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
                        usbIntent.putExtra("accessory", usbAccessory);
                        startAoaLink(usbIntent);
                        mContext.setIntent(null);
                    } else {
                        startDirectLink();
                        mContext.setIntent(null);
                    }
                }
            }

            @Override
            public void onAppConnected(String connectStr) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppConnected -------------->connectStr:" + connectStr);
                mWLPlatform.mirror(connectStr);
                if (!isCanDataSecurity) {
                    WlLinkSdkLog.i("WLPlatformManager onLinkConnected --------start------>1111111");
                    mWLPlatform.start();
                } else {
                    WlLinkSdkLog.i("WLPlatformManager onLinkConnected --------start------>2222222");
                    mWLPlatform.start(0, 1, 0, 48);
                }
                if (isPlatformApp(connectStr)) {
                    WLPlatformConfig.isConnectcar = true;
                    PlatformObserverable.getInstance().notifyObserver(WLPlatformConfig.WL_PLATFORM_OBSERVER_CONNECT_STAET, true);
                }
            }

            @Override
            public void onAppDisconnected(String connectStr) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppDisconnected -------------->connectStr:" + connectStr);
                mWLPlatform.revoke(connectStr);
            }

            @Override
            public void onAppError(String connectStr, int errorCode) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppError -------------->errorCode :" + errorCode);
            }

            @Override
            public void onAppForeground(String connectStr) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppForeground -------------->");
            }

            @Override
            public void onAppBackground(String connectStr) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppBackground -------------->");
            }

            @Override
            public void onAppAction(String connectStr, int action) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppAction -------------->");
                switch (action) {
                    case WLPlatformListener.ACTION_REQUEST_MICROPHONE:
                        //请求车机MIC声音
                        mWLPlatform.openMicrophone(connectStr);
                        break;
                    case WLPlatformListener.ACTION_ABANDON_MICROPHONE:
                        //停止车机MIC声音
                        mWLPlatform.openMicrophone(null);
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onLinkHUMessageData(String data) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkHUMessageData -------------->data:" + data);
                if (!TextUtils.isEmpty(data)) {
                    WLProtocolMannger.getInstance().onReceiveHUCommand(data);
                }
            }

            @Override
            public void onLinkHUCanData(byte[] bytes) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkHUCanData -------------->size:" + bytes.length + ",isCanDataSecurity" + isCanDataSecurity);
                if (bytes.length > 0) {
                    byte[] decodes = bytes;
                    //针对需要加密处理的项目进行二次解密处理
                    if (isCanDataSecurity) {
                        if (mSecurity == null) {
                            receiveCanDatas.add(decodes);
                            WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkHUCanData -------------->add");
                            return;
                        }
                        decodes = mSecurity.decode(decodes);
                        if (decodes == null) {
                            WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkHUCanData -------------->null");
                            return;
                        }
                    }
                    String data = new String(decodes);
                    WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkHUCanData -------------->data:" + data);
                    WLProtocolMannger.getInstance().onReceiveHUCommand(data);
                }
            }

            @Override
            public void onLinkTouch(MotionEvent motionEvent) {
                Dialog mDialog = getOverlayDialog();

                if (mDialog != null && mDialog.isShowing()) {
                    if (mDialog.dispatchTouchEvent(motionEvent)) {
                        WlLinkSdkLog.i("WLPlatformManager onLinkTouch -------- 11111111111 -------------->motionEvent:" + motionEvent);
                        return;
                    }
                }

                if (isEnableExternal) {
                    WlLinkSdkLog.i("WLPlatformManager onLinkTouch -------- 22222222222 -------------->motionEvent:" + motionEvent);
                    if (mWeLinkAdapterListener != null) {
                        mWeLinkAdapterListener.onLinkTouch(motionEvent);
                    }
                } else {
                    WlLinkSdkLog.i("WLPlatformManager onLinkTouch -------- 33333333333 -------------->motionEvent:" + motionEvent);
                    mWLPlatform.touch(motionEvent);
                }
            }

            @Override
            public int onAppSound(String connectStr, String mark, int duration) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppSound -------------->mark:" + mark + ",duration:" + duration);
                int audioType = 0;
                if (isPlatformApp(connectStr)) {
                    try {
                        audioType = Integer.parseInt(mark);
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                }
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onAppSound -------------->audioType:" + audioType);
                return audioType;
            }

            @Override
            public void onMusicRegister(String connectStr) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onMusicRegister -------------->");

            }

            @Override
            public void onMusicUnregister(String connectStr) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onMusicUnregister -------------->");

            }

            @Override
            public void onMusicID3(String source, String artist, String title, String album, String lyric, int duration, Bitmap cover) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onMusicID3 -------------->");

            }

            @Override
            public void onMusicOrder(int order) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onMusicOrder -------------->");

            }

            @Override
            public void onMusicPCM(long position, long totalLen, int rate, int bit, int channel) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onMusicPCM -------------->totalLen:" + totalLen + ",position:" + position);

            }


            @Override
            public void onFrameData(byte[] bytes) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onFrameData -------------->size:" + bytes.length + ",isFirstFrameData:" + isFirstFrameData);

//        synchronized (lock){
//            CommonUtil.writeByteArray2SDCard(Environment.getExternalStorageDirectory()+ "/Test/video/", mFrameFilePath,bytes,true);
//        }
                if (isCanDataSecurity && isFirstFrameData) {
                    isFirstFrameData = false;

                    int length = bytes.length;
                    int count = WL_PLATFORM_SECURITY_MAX_SIZE / length;
                    int extra = WL_PLATFORM_SECURITY_MAX_SIZE % length;

                    byte[] result = new byte[count * length + extra];
                    int i = 0;
                    //长度不够48补齐，超过48只取前48
                    while (count-- > 0) {
                        System.arraycopy(bytes, 0, result, i * length, length);
                        i++;
                    }
                    if (extra > 0) {
                        System.arraycopy(bytes, 0, result, i * length, extra);
                    }
                    //创建加密处理对象
                    mSecurity = new SecurityFactory().create(new AESProxy(result));

                    //处理接收车机端发送过来的缓存candata数据协议
                    {
                        Iterator<byte[]> iterator = receiveCanDatas.iterator();
                        while (iterator.hasNext()) {
                            byte[] data = iterator.next();
                            onLinkHUCanData(data);
                            iterator.remove();
                        }
                    }
                    //处理发送手机发送过去的缓存candata数据协议
                    {
                        Iterator<String> iterator = sendCanDatas.iterator();
                        while (iterator.hasNext()) {
                            String data = iterator.next();
                            sendCanDataToCar(data);
                            iterator.remove();
                        }
                    }
                }

            }

            @Override
            public void onLinkSuspend() {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkSuspend -------------->");
                mWLPlatform.stop();
            }

            @Override
            public void onLinkResume() {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkResume -------------->");
                if (!isCanDataSecurity) {
                    WlLinkSdkLog.i("WLPlatformManager onLinkConnected --------start------>1111111");
                    mWLPlatform.start();
                } else {
                    WlLinkSdkLog.i("WLPlatformManager onLinkConnected --------start------>2222222");
                    mWLPlatform.start(0, 1, 0, 48);
                }
            }

            @Override
            public void onLinkAOAReady() {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onLinkAOAReady -------------->");
            }

            @Override
            public void onHardwareGroupError(int error) {
                WlLinkSdkLog.e("WLPlatformManager WLPlatformListener -------- onHardwareGroupError -------------->error:" + error);
                //error - 错误码
                // 1: 设备不支持蓝牙BLE
                // 2: 没有打开蓝牙
                // 3: 没有打开Wi-Fi
                // 4: 需要请求定位权限(ACCESS_FINE_LOCATION)
                // 5: 服务发生异常
                // 6: 创建Wi-Fi Direct GO失败
                if (mWeLinkPlatformListener != null) {
                    mWeLinkPlatformListener.onBleLinkError(error);
                }
            }

            @Override
            public void onHardwareGroupStatusChanged(int status, String content) {
                WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- onHardwareGroupStatusChanged -------------->status:" + status + ",content:" + content);
                if (WLPlatformState.STATUS_CAR_CONNECTED == mPlatformCarState) {
                    return;
                }
                //status - 状态码
                // 0: Wi-Fi Direct GO广播，content: GO签名 1: BLE设备连接中... 2: BLE设备连接成功 3: BLE服务连接中... 4: BLE服务连接成功5: 读取版本中...
                // 6: 版本读取成功，content：版本号 7: 写Wi-Fi Direct GO签名 8: 签名写成功，content：签名9: Wi-Fi Direct GC连接，content: 网络密码10: 未找到对应包名的硬件心跳
                // -1: 设备连接失败 -2: 服务连接失败 -3: 版本读取失败 -4: 签名写失败 -5: 操作超时
                switch (status) {
                    case 0:
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
                        mHandler.sendEmptyMessageDelayed(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN, WL_PLATFORM_BLE_LINK_SCAN_TIMEOUT);
                        break;
                    case 1:
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
                        mHandler.sendEmptyMessageDelayed(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN, WL_PLATFORM_BLE_LINK_SCAN_TIMEOUT);
                        break;
                    case 9:
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
                        break;
                    default:
                        break;
                }
                if (mWeLinkPlatformListener != null) {
                    mWeLinkPlatformListener.onBleLinkStatus(status);
                }
            }
        });
        mWLScreen = new WLScreen(mContext, new WLScreenListener() {

            @Override
            public void currentAppPackageName(String packageName) {

            }

            @Override
            public void onRotation(int rotation, int width, int height) {

            }
        }, true, createDefaultScreenNotification());

        registerReceiver();
//        requestDirectLink();
    }

    /**
     * 连接三方app
     *
     * @param packageName - 应用包名
     * @param connectKey  三投应用连接码
     */
    @Keep
    public void requestConnect(String packageName, String connectKey) {
        WlLinkSdkLog.i("WLPlatformManager requestConnect -------------->packageName:" + packageName + ",connectKey:" + connectKey);

        //连接车机状态下
        if (this.mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED) {
            mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_SEND);
            Message message = mHandler.obtainMessage();
            Bundle data = new Bundle();
            data.putString(WLPlatformConfig.WL_PLATFORM_BUNDLE_PACKAGE_NAME, packageName);
            data.putString(WLPlatformConfig.WL_PLATFORM_BUNDLE_CONNECT_KEY, connectKey);
            message.what = WL_PLATFORM_HANDLER_WHAR_SEND;
            message.setData(data);
            mHandler.sendMessageDelayed(message, WL_PLATFORM_HANDLER_WHAR_DELAYED_TIME);
        }
    }

    /**
     * 申请wifi无感互联功能
     */
    @Keep
    public void requestDirectLink() {
        WlLinkSdkLog.i("WLPlatformManager requestDirectLink -------------->");
        if (Configs.SUPPORT_BLE_WIFI_LINK) {
            isBleInit = true;
            final String[] permissions;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                permissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_CONNECT};
            } else {
                permissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION};
            }

            BridgeActivity.showActivityBridge(mContext, new BridgeActivity.BridgeCallback() {
                @Override
                public void onReady(FragmentActivity activity) {
                    activity.requestPermissions(permissions, 333);
                }

                @RequiresApi(api = Build.VERSION_CODES.N)
                @Override
                public void onPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
                    if (requestCode == 333) {
                        if (Arrays.stream(grantResults).allMatch(value -> value == PackageManager.PERMISSION_GRANTED)) {
                            WlLinkSdkLog.i("WLPlatformManager requestDirectLink --------onPermissionGranted------>");
                            startDirectLink();
//                            registerDirectActivityListener();
                        }
                    }
                }
            });
        }
    }

    /**
     * 获取平台和车机互联状态
     */
    @Keep
    public WLPlatformState getPlatformState() {
        return this.mPlatformCarState;
    }

    /**
     * 开始连接车机
     *
     * @param intent - 互联意图
     */
    @Keep
    public boolean startAoaLink(Intent intent) {
        WlLinkSdkLog.i("WLPlatformManager startLink -------------->");

        if (this.mWLPlatform == null) {
            return false;
        }
        //如果在连接车机中或者已连接车机，不在进行连接车机操作
        if (this.mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED) {
            return false;
        }
        this.stopDirectLink();
        this.mPlatformCarState = WLPlatformState.STATUS_CAR_CONNECTING;
        WlLinkSdkLog.i("WLPlatformManager startLink -------11111111------->");
        ThreadPoolUtil.getInstance().getScheduledExecutor().execute(() -> mWLPlatform.link(intent));
        if (this.mWeLinkPlatformListener != null) {
            this.mWeLinkPlatformListener.onLinkStart(true);
        }
        return true;
    }

    /**
     * 开始断开连接车机
     */
    @Keep
    public void stopLink() {
        WlLinkSdkLog.i("WLPlatformManager stopLink -------------->");
        if (this.mWLPlatform == null) {
            return;
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (this.mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return;
        }
        this.mPlatformCarState = WLPlatformState.STATUS_CAR_DISCONNECTING;
        WlLinkSdkLog.i("WLPlatformManager stopLink -------11111111------->");
        this.mWLPlatform.unlink();
    }

    /**
     * 获取app连接码
     *
     * @param packageName - 应用包名
     * @param cap         三投应用互联能力码
     */
    @Keep
    public String getConnectKey(String packageName, int cap) {
        WlLinkSdkLog.i("WLPlatformManager getConnectKey -------------->packageName:" + packageName + ",cap:" + cap);

        if (this.mWLPlatform == null) {
            return null;
        }
        String connectKey = this.mWLPlatform.assign(packageName, cap);

        ConnectorBean bean = new ConnectorBean();
        bean.setConnectKey(connectKey);
        bean.setPackageName(packageName);
        bean.setCap(cap);

        this.mAssignKeys.put(connectKey, bean);
        return connectKey;
    }

    /**
     * 依据范围获取投屏显示容器
     */
    @Keep
    public Surface getEnableExternal(Rect rect) {
        WlLinkSdkLog.i("WLPlatformManager getDisplaySurface -------------->");

        if (this.mWLPlatform == null) {
            return null;
        }
        this.isEnableExternal = true;
        return this.mWLPlatform.enableExternal(rect);
    }


    /**
     * 取消获取投屏显示容器
     */
    @Keep
    public void getDisEnableExternal() {
        WlLinkSdkLog.i("WLPlatformManager getDisplaySurface -------------->");

        if (this.mWLPlatform == null) {
            return;
        }
        this.isEnableExternal = false;
        this.mWLPlatform.disableExternal();
    }

    /**
     * 获取Overlay显示容器
     */
    @Keep
    public Dialog getOverlayDialog() {
        WlLinkSdkLog.i("WLPlatformManager getOverlayDialog -------------->");

        if (this.mWLPlatform == null) {
            return null;
        }
        return this.mWLPlatform.getOverlay();
    }


    /**
     * 手机给车机发送消息交互协议
     */
    @Keep
    public void sendMessageDataToCar(String protocolData) {
        WlLinkSdkLog.i("WLPlatformManager sendMessageDataToCar -------------->protocolData:" + protocolData);

        if (this.mWLPlatform == null) {
            return;
        }
        if (!TextUtils.isEmpty(protocolData)) {
            this.mWLPlatform.sendMessageData(protocolData);
        }
    }


    /**
     * 手机给车机发送candata交互协议
     */
    @Keep
    public void sendCanDataToCar(String protocolData) {
        WlLinkSdkLog.i("WLPlatformManager sendCanDataToCar -------------->protocolData:" + protocolData + ",isCanDataSecurity:" + isCanDataSecurity);

        if (this.mWLPlatform == null) {
            return;
        }
        if (!TextUtils.isEmpty(protocolData)) {
            byte[] encodes = protocolData.getBytes();
            //针对需要加密处理的项目进行二次加密处理
            if (isCanDataSecurity) {
                if (mSecurity == null) {
                    sendCanDatas.add(protocolData);
                    WlLinkSdkLog.i("WLPlatformManager WLPlatformListener -------- sendCanDataToCar -------------->add");
                    return;
                }
                encodes = mSecurity.encode(encodes);
            }
            this.mWLPlatform.sendCanData(encodes);
        }
    }

    /**
     * 设置适配三方投屏回调接口
     */
    @Keep
    public void registerAdapterListener(WeLinkAdapterListener weLinkAdapterListener) {
        WlLinkSdkLog.i("WLPlatformManager registerAdapterListener -------------->");
        this.mWeLinkAdapterListener = weLinkAdapterListener;
    }

    /**
     * 取消设置适配三方投屏回调接口
     */
    @Keep
    public void unRregisterAdapterListener() {
        WlLinkSdkLog.i("WLPlatformManager unRregisterAdapterListener -------------->");
        this.mWeLinkAdapterListener = null;
    }

    /**
     * 通知适配层互联成功状态
     */
    @Keep
    public void connectedAdapter() {
        WlLinkSdkLog.i("WLPlatformManager connectedAdapter -------------->");
        if (this.mWeLinkAdapterListener != null) {
            WlLinkSdkLog.i("WLPlatformManager connectedAdapter ------11111111111-------->");
            this.mWeLinkAdapterListener.onLinkConnected(mContext, mNotification, huScreenWidth, huScreenHeight, densityDpi, vehicleType);
        }
    }

    /**
     * 通知适配层互联断开状态
     */
    @Keep
    public void disconnectedAdapter() {
        WlLinkSdkLog.i("WLPlatformManager disconnectedAdapter -------------->");
        if (this.mWeLinkAdapterListener != null) {
            this.mWeLinkAdapterListener.onLinkDisconnected();
        }
    }

    /**
     * 控制是否进入三方投屏方式
     */
    @Keep
    public void setExternalCastScreen(boolean isExternalCastScreen) {
        WlLinkSdkLog.i("WLPlatformManager setExternalCastScreen -------------->");
    }

    /**
     * 检测权限
     */
    @Keep
    public void activityResult(int requestCode, int resultCode, Intent data) {
        WlLinkSdkLog.i("WLPlatformManager activityResult -------------->");
        if (this.mWeLinkAdapterListener != null) {
            this.mWeLinkAdapterListener.activityResult(requestCode, resultCode, data);
        }
        if (mWLScreen != null) {
            ThreadPoolUtil.getInstance().getDefaultExecutor().execute(() -> {
                mWLScreen.onActivityResult(requestCode, resultCode, data);
                if (WLPlatformConfig.videoSizeSquare) {
                    int size = Math.max(huScreenWidth, huScreenHeight);
                    Surface surface = mWLPlatform.enableExternal(new Rect(0, 0, size, size), true);
                    mWLScreen.setSurface(surface, size, size, densityDpi);
                } else {
                    Surface surface = mWLPlatform.enableExternal(new Rect(0, 0, huScreenWidth, huScreenHeight), true);
                    mWLScreen.setSurface(surface, huScreenWidth, huScreenHeight, densityDpi);
                }
                WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
                Display display = wm.getDefaultDisplay();

                Rect rect = calculateScreen();
                updateStates(display.getRotation(), rect);
                // 开始投屏
                mWLPlatform.start();
            });
        }
    }

    private Rect calculateScreen() {
        WlLinkSdkLog.i("WLPlatformManager ScreenControl ---- calculateScreen ------------>");
        WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        Point mPoint = new Point();
        display.getRealSize(mPoint);

        int width = mPoint.x;
        int height = mPoint.y;

        float wBase = huScreenWidth * 1.0f / width;
        float hBase = huScreenHeight * 1.0f / height;

        float mBase = Math.min(wBase, hBase);

        width = Math.round(width * mBase) / 2 * 2;
        height = Math.round(height * mBase) / 2 * 2;
        WlLinkSdkLog.i("WLPlatformManager huScreenWidth = " + huScreenWidth + " huScreenHeight = " + huScreenHeight + " w = " + width + " h = " + height);
        int mOffsetX = (huScreenWidth - width) / 2;
        int mOffsetY = (huScreenHeight - height) / 2;

        return new Rect(mOffsetX, mOffsetY, mOffsetX + width, mOffsetY + height);
    }

    /**
     * 是否输出log日志
     */
    @Keep
    public void enableDebug(boolean isDebug) {
        WlLinkSdkLog.i("WLPlatformManager enableDebug --------------> isDebug:" + isDebug);

//        if (this.mWLPlatform == null) {
//            return;
//        }
//         WlLinkSdkLog.i( "WLPlatformManager WLPlatform version:"+ WLPlatform.getVersion());
//         WlLinkSdkLog.i( "WLPlatformManager WLHardwareHub version:"+ WLHardwareHub.getVersion());
//         WlLinkSdkLog.i( "WLPlatformManager WLServer version:"+ WLServer.getVersion());
//         WlLinkSdkLog.i( "WLPlatformManager WLConnector version:"+ WLConnector.getVersion());
//         WlLinkSdkLog.i( "WLPlatformManager WLChannel version:"+ WLChannel.getVersion());

//        mWLPlatform.enableLogCat(isDebug);
//        mWLPlatform.enableLogFile(isDebug);
    }

    /**
     * 获取车机端link软件版本号
     */
    @Keep
    public String getCarLinkVersion() {
        if (this.mWLPlatform == null) {
            return "";
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (this.mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return "";
        }
        return vehicleVersion;
    }


    /**
     * 根据车机传回的车机屏幕信息, 计算需要录制的视频尺寸
     */
    private void calculateHuVideoSize(int mScreenWidth, int mScreenHeight) {
        WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        Point mPoint = new Point();
        display.getRealSize(mPoint);

        int width = mPoint.x;
        int height = mPoint.y;
        // 如果配置为方形视频, 则以车机屏幕的最大边为方形边长, 进行录制;
        // 以车机屏幕宽高为基准, 手机屏幕保持宽高比不变进行压缩, 容纳到车机屏幕内部为准.
        if (WLPlatformConfig.videoSizeSquare) {
            this.huScreenWidth = Math.max(mScreenWidth, mScreenHeight);
            this.huScreenHeight = Math.max(mScreenWidth, mScreenHeight);
        } else {
            float lz = Math.max(mScreenHeight, mScreenWidth) / (float) Math.max(width, height);
            float sz = Math.min(mScreenHeight, mScreenWidth) / (float) Math.min(width, height);
            //编码器不支持单像素
            this.huScreenWidth = Math.round(width * Math.min(lz, sz)) / 2 * 2;
            this.huScreenHeight = Math.round(height * Math.min(lz, sz)) / 2 * 2;
        }
        /*
            进行16位对齐。在编码h264视频流的时候，因为h264的编码块大小通常是16x16
         */
        WlLinkSdkLog.i("WLPlatformManager calculateHuVideoSize, mode square: " + WLPlatformConfig.videoSizeSquare + ", width: " + this.huScreenWidth + ", height: " + this.huScreenHeight);
    }

    private void updateStates(int angle, Rect rect) {
        WlLinkSdkLog.d("WLPlatformManager ScreenControl ---- updateStates: angle = [" + angle + "], mRect = [" + rect + "]");
        try {
            JSONObject jsonObject = new JSONObject();

            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android");

            JSONObject command = new JSONObject();
            command.put("method", "hidTouch");

            JSONObject extData = new JSONObject();
            extData.put("enabled", true);
            extData.put("angle", angle);
            extData.put("x", rect.left);
            extData.put("y", rect.top);
            extData.put("w", rect.width());
            extData.put("h", rect.height());
            command.put("extData", extData);

            JSONObject phoneHome = new JSONObject();
            phoneHome.put("x", 0);
            phoneHome.put("y", 0);
            phoneHome.put("w", 0);
            phoneHome.put("h", 0);
            command.put("phoneHome", phoneHome);

            JSONObject phoneBack = new JSONObject();
            phoneBack.put("x", 0);
            phoneBack.put("y", 0);
            phoneBack.put("w", 0);
            phoneBack.put("h", 0);
            command.put("phoneBack", phoneBack);

            JSONObject huHome = new JSONObject();
            huHome.put("x", 0);
            huHome.put("y", 0);
            huHome.put("w", 0);
            huHome.put("h", 0);
            command.put("huHome", huHome);
            command.put("videoMode", WLPlatformConfig.videoSizeSquare ? 0 : 1);

            jsonObject.put("command", command);

            String hidMessage = jsonObject.toString();
            WlLinkSdkLog.i("WLPlatformManager ScreenControl ---- updateStates ------------>hidMessage：" + hidMessage);
            WLProtocolMannger.getInstance().sendCommondMessage(hidMessage);
        } catch (JSONException e) {
            WlLinkSdkLog.e("WLPlatformManager updateStates exception:" + e.getMessage());
        }
    }

    /**
     * 开启监听三方app请求连接广播
     */
    private void registerReceiver() {
        WlLinkSdkLog.i("WLPlatformManager registerReceiver -------------->");
        if (this.mPlatformReceiver == null) {
            this.mPlatformReceiver = new PlatformReceiver(mContext, mWLPlatform);
            IntentFilter intentFilter = new IntentFilter(WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION);
            intentFilter.addAction(Intent.ACTION_CONFIGURATION_CHANGED);
            if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.TIRAMISU){
                mContext.registerReceiver(mPlatformReceiver, intentFilter,RECEIVER_EXPORTED);
            }else {
                mContext.registerReceiver(mPlatformReceiver, intentFilter);
            }
        }
    }

    /**
     * 取消监听三方app请求连接广播
     */
    private void unRegisterReceiver() {
        WlLinkSdkLog.i("WLPlatformManager unRegisterReceiver -------------->");
        if (this.mPlatformReceiver != null) {
            this.mContext.unregisterReceiver(this.mPlatformReceiver);
            this.mPlatformReceiver = null;
        }
    }

    /**
     * 创建前台服务广播通知
     */
    private Notification createDefaultNotification() {
        PendingIntent pendingIntent;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), PendingIntent.FLAG_UPDATE_CURRENT);
        }
        Notification.Builder builder = new Notification.Builder(mContext).setContentIntent(pendingIntent).setSmallIcon(R.drawable.ic_launcher).setTicker("started").setWhen(System.currentTimeMillis())
                .setContentTitle("AvsLink").setContentText("AvsLink正在运行中");
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel notificationChannel = new NotificationChannel(mContext.getPackageName(), "AvsLink", NotificationManager.IMPORTANCE_MIN);
            NotificationManager manager = (NotificationManager) mContext.getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            builder.setChannelId(mContext.getPackageName());
        }
        return builder.build();
    }

    /**
     * 创建前台服务广播通知
     */
    @SuppressLint("WrongConstant")
    private Notification createDefaultScreenNotification() {
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), PendingIntent.FLAG_UPDATE_CURRENT);
        }
        Notification.Builder builder = new Notification.Builder(mContext).setContentIntent(pendingIntent).setSmallIcon(R.drawable.ic_launcher).setTicker("started").setWhen(System.currentTimeMillis())
                .setContentTitle("AvsLink").setContentText("AvsLink正在录制屏幕");
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel notificationChannel = new NotificationChannel(mContext.getPackageName(), "AvsLink", NotificationManager.IMPORTANCE_MIN);
            NotificationManager manager = (NotificationManager) mContext.getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            builder.setChannelId(mContext.getPackageName());
        }
        return builder.build();
    }

    /**
     * 判断当前连接的是否是平台app
     */
    private boolean isPlatformApp(String connectStr) {
        ConnectorBean bean = this.mAssignKeys.get(connectStr);
        return mContext.getPackageName().equals(bean.getPackageName());
    }


    public boolean isBleInit() {
        return isBleInit;
    }

    /**
     * 开启wifi无感互联功能
     */
    @Keep
    public void startDirectLink() {
        WlLinkSdkLog.i("WLPlatformManager startDirectLink -------------->");
        if (mWLPlatform == null) {
            return;
        }
        if (Configs.SUPPORT_BLE_WIFI_LINK) {
            WlLinkSdkLog.i("WLPlatformManager startDirectLink -------2222222------->");
            mWLPlatform.destroyHardwareGroup();
            mWLPlatform.createHardwareGroup(WLPlatformConfig.WL_PLATFORM_BLELINK_CHANNEL_AP32, -80, 50, null);
        }
    }

    /**
     * 关闭wifi无感互联功能
     */
    @Keep
    public void stopDirectLink() {
        WlLinkSdkLog.i("WLPlatformManager stopDirectLink -------------->");
        if (this.mWLPlatform == null) {
            return;
        }
        if (Configs.SUPPORT_BLE_WIFI_LINK) {
            WlLinkSdkLog.i("WLPlatformManager stopDirectLink -------2222222------->");
            this.mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
            this.mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
            this.mWLPlatform.destroyHardwareGroup();
        }
    }

    /**
     * ble 继续扫描和 添加WiFi dricet 创建成功监听
     */
    public void showGroupInfo(){
        if (this.mWLPlatform == null) {
            return;
        }
        mWLPlatform.showGroupInfo();
    }
    /**
     * ble 停止扫描和 移除WiFi dricet 创建成功监听
     */
    public void hideGroupInfo(){
        if (this.mWLPlatform == null) {
            return;
        }
        mWLPlatform.hideGroupInfo();
    }
    /**
     * 销毁平台服务管理类
     */
    @Keep
    public void destroy() {
        WlLinkSdkLog.i("WLPlatformManager onDestory -------------->");

        if (this.mWLPlatform != null) {
            this.stopDirectLink();
            this.mWLPlatform.release();
            this.mWLPlatform = null;
        }
        WLProtocolMannger.getInstance().clearAllMethodListener();
        WLProtocolMannger.Release();
        this.mPlatformCarState = WLPlatformState.STATUS_NONE;
        mWeLinkPlatformListener = null;
        this.mContext = null;

        if (mWLScreen != null) {
            mWLScreen.stop();
        }
    }

    private static class PlatformReceiver extends BroadcastReceiver {
        private final Context mContext;
        private final WLPlatform mWLPlatform;

        PlatformReceiver(Context context, WLPlatform wlPlatform) {
            mContext = context;
            mWLPlatform = wlPlatform;
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            Bundle bundle = intent.getExtras();
            WlLinkSdkLog.d("WLPlatformManager PlatformReceiver.onReceive: action=" + intent.getAction() + ", data: " + AppUtils.bundleToString(intent.getExtras()));

            switch (intent.getAction()) {
                case WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION:
                    if (mWLPlatform != null && bundle != null) {
                        String pkgName = bundle.getString(WLPlatformConfig.WL_PLATFORM_PACKAGE_NAME);
                        if (!TextUtils.isEmpty(pkgName)) {
                            String connectStr = mWLPlatform.assign(pkgName, WLPlatform.WL_CAP_MUSIC);
                            Intent connect = new Intent(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
                            connect.putExtra(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY, connectStr);
                            connect.setPackage(pkgName);
                            mContext.sendBroadcast(connect);
                        }
                    }
                    break;
                case Intent.ACTION_CONFIGURATION_CHANGED:
                    // 屏幕切换
                    // 以手机屏幕为准, 机型屏幕录制
                    ThreadPoolUtil.getInstance().getDefaultExecutor().execute(() -> {
                        WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
                        Display display = wm.getDefaultDisplay();

                        Rect rect = WLPlatformManager.getInstance().calculateScreen();
                        WLPlatformManager.getInstance().updateStates(display.getRotation(), rect);
                    });
                    break;
                default:
                    break;
            }
        }
    }
}
     