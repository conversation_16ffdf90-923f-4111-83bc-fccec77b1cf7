package com.autoai.avs.linksdk.platform.observer;

import java.util.ArrayList;
import java.util.List;

public class PlatformObserverable {

    private final List<PlatformObserver> observers = new ArrayList<>();

    private static class ObserverableInstance {
        private static final PlatformObserverable INSTANCE = new PlatformObserverable();
    }

    public static PlatformObserverable getInstance() {
        return ObserverableInstance.INSTANCE;
    }

    /**
     * 方法名称：attachObserver
     * 方法描述：添加平台服务监听对象
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/05/29 14:03
     */

    public void attachObserver(PlatformObserver observer){
        if(!observers.contains(observer)){
            observers.add(observer);
        }
    }

    /**
     * 方法名称：detachObserver
     * 方法描述：移除平台服务监听对象
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/05/29 14:03
     */

    public void detachObserver(PlatformObserver observer){
        if(!observers.isEmpty()){
            observers.remove(observer);
        }
    }

    /**
     * 方法名称：notifyUserObserver
     * 方法描述：通知更新微信用户登录状态
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/05/29 14:03
     */

    public void notifyObserver(int state, Object obj){
        for(int i = 0; i < observers.size(); i++) {
            observers.get(i).notifyObserver(state,obj);
        }
    }
}
