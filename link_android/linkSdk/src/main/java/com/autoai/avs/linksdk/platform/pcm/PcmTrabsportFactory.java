package com.autoai.avs.linksdk.platform.pcm;

import android.content.Context;
import android.util.SparseArray;

/**
 *pcm传输类创建工厂类
 */
public class PcmTrabsportFactory {

    public static final int MUSIC_PCM_TRABSPORT= 1;//音乐pcm传输类标识
    public static final int TTS_PCM_TRANSPORT = 2;//VR 微信 后台VR pcm传输类标识

    final static public int LOCAL_MUSIC_TYPE = 0;
    final static public int QQ_MUSIC_TYPE = 1;
    final static public int XM_MUXIC_TYPE = 2;


    private static SparseArray<BasePcmTransport> basePcmTransportArray =new SparseArray<>();
    public  static BasePcmTransport cratePcmTransport(int type, Context context){
        if (basePcmTransportArray.get(type) != null){
            return basePcmTransportArray.get(type);
        }
        BasePcmTransport pcmTransport=null;
        switch (type){
            case MUSIC_PCM_TRABSPORT:
//                pcmTransport = new MusicPcmTranspotImpl(context);
                break;
            case TTS_PCM_TRANSPORT :
//                pcmTransport =new TTSPcmTransportImpl(context);
              break;
        }
        basePcmTransportArray.put(type,pcmTransport);
        return  pcmTransport;
    }



    /**
     * 释放对应type的pcm传输类
     * @param type
     * @return
     */
    public static boolean releasePcmTrabsport(int type){
        if (basePcmTransportArray.get(type) != null){
            basePcmTransportArray.remove(type);
            return true;
        }
        return false;
    }
    /**
     * 释放资源
     */
    public static void release(){
        basePcmTransportArray.clear();
        basePcmTransportArray =null;
    }
}
