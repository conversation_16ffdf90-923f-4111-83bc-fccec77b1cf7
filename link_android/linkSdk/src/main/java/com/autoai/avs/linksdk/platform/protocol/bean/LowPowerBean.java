package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端电量状态时向手机端 launcher 发送的协议。
 * 类型 int 0：正常 1：一级报警 2: 二级报警 3：忽略
 */
public class LowPowerBean extends BaseProtocolBean{

    private int state;

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "LowPowerBean{" +
                "state=" + state +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.state = extData.getInt("state");
    }


}
