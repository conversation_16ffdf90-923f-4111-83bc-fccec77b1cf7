package com.autoai.avs.linksdk.platform.audio.transport;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.audio.listener.TransportListener;

/**
 * 数据传输基类
 *
 * <AUTHOR>
 */
public class BaseTransport {
    /**
     * 提前发送的数据量 单位ms
     */
    protected static final long MAX_ADVANCE_AUDIO_VALUE = 100;
    protected int rate;
    protected int bit;
    protected int channels;
    protected int delayTime = 10;
    protected TransportListener mTransportListener;

    public BaseTransport(TransportListener transportListener, int rate, int bit, int channels) {
        this.mTransportListener = transportListener;
        this.rate = rate;
        this.bit = bit;
        this.channels = channels;
    }

    /**
     * 依据互联状态设置音频数据提前发送的时间
     *
     * @param flag 互联状态
     */
    public void setConnectCar(boolean flag) {
        WlLinkSdkLog.i("BaseTransport setConnectCar  ----------> flag:" + flag);
    }

    /**
     * 接收解码数据进行分包处理
     *
     * @param pcmData 解码数据
     */
    public void producePcm(byte[] pcmData) {
    }

    public void startTransportThread() {

    }

    public void resumeTransportThread() {

    }

    public void pauseTransportThread() {

    }

    public void stopTransportThread() {

    }
}
