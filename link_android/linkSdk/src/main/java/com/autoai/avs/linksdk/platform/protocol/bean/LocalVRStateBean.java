package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端本地VR模式向手机端 launcher 发送的协议。
 * 类型 int 1：打开 2: 关闭
 */
public class LocalVRStateBean extends BaseProtocolBean{

    private int localVRState;

    public int getLocalVRState() {
        return localVRState;
    }

    public void setLocalVRState(int localVRState) {
        this.localVRState = localVRState;
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.localVRState = extData.getInt("localVRState");
    }


}
