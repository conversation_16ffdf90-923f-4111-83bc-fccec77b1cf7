package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是用于车机端向手机端发送数据请求动作。
 * @param reqAction 类型
 *                   1：id3 相关信息，如：演唱者、歌曲名、歌手名； -->对应 WeLink音频数据通信协议 5
 *                   2：WeLink 通知车机系统当前 WeLink 音乐播放/暂停状态； -->对应协议 4.11
 *                   3：导航信息，如 turnbyturn; -->对应协议 4.10
 *                   4：导航状态; -->对应协议 4.12
 */

public class ReqAppData extends BaseProtocolBean{

    private int reqAction;

    public int getReqAction() {
        return reqAction;
    }

    @Override
    public String toString() {
        return "ReqAppData{" +
                "reqAction=" + reqAction +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.reqAction = extData.getInt("reqAction");
    }


}
