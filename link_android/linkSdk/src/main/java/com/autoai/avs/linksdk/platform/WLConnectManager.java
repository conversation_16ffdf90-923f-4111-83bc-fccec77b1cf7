package com.autoai.avs.linksdk.platform;

import static android.content.Context.RECEIVER_EXPORTED;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Keep;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.bean.CarBean;
import com.autoai.avs.linksdk.platform.bean.ID3Bean;
import com.autoai.avs.linksdk.platform.bean.PCMBean;
import com.autoai.avs.linksdk.platform.bean.TBTBean;
import com.autoai.avs.linksdk.platform.bean.WLCommandType;
import com.autoai.avs.linksdk.platform.bean.WLConnectState;
import com.autoai.avs.linksdk.platform.callback.WeLinkBTCallback;
import com.autoai.avs.linksdk.platform.callback.WeLinkMICCallback;
import com.autoai.avs.linksdk.platform.callback.WeLinkMusicCallback;
import com.autoai.avs.linksdk.platform.callback.WeLinkSoundCallback;
import com.autoai.avs.linksdk.platform.control.BTPhoneControl;
import com.autoai.avs.linksdk.platform.control.MICControl;
import com.autoai.avs.linksdk.platform.control.MusicControl;
import com.autoai.avs.linksdk.platform.control.TBTControl;
import com.autoai.avs.linksdk.platform.control.TTSControl;
import com.autoai.avs.linksdk.platform.listener.WeLinkCommandListener;
import com.autoai.avs.linksdk.platform.listener.WeLinkConnectListener;
import com.autoai.welink.auto.WLConnectListener;
import com.autoai.welink.auto.WLConnector;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 主要负责为三方应用提供功能实现接口。
 *
 * <AUTHOR>
 */
@Keep
public class WLConnectManager implements WLConnectListener {
    private Context mContext;
    private WLConnector mWLConnector;

    private MusicControl mMusicControl;
    private TTSControl mTTSControl;
    private MICControl mMICControl;
    private TBTControl mTBTControl;
    private BTPhoneControl mBTPhoneControl;

    private ConnectReceiver mConnectReceiver;
    private WLConnectState mWLConnectState = WLConnectState.STATUS_NONE;

    private WeLinkConnectListener mWeLinkConnectListener;
    private final HashMap<String, ArrayList<WeLinkCommandListener>> mCommandListeners = new HashMap<>();

    private WLConnectManager() {
    }


    public static WLConnectManager getInstance() {
        return WLConnectManager.INSTANCE.instance;
    }

    private static class INSTANCE {
        @SuppressLint("StaticFieldLeak")
        private static final WLConnectManager instance = new WLConnectManager();
    }

    /**
     * 初始化服务
     */
    @Keep
    public void init(Context mContext, WeLinkConnectListener mWeLinkConnectListener) {
        WlLinkSdkLog.i( "WLConnectManagerinit -------------->");
        this.mContext = mContext;
        this.mWeLinkConnectListener = mWeLinkConnectListener;
        registerReceiver();
    }

    /**
     * 方法名称：requestConnect
     * 方法描述：三方申请连接服务
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/6 17:56
     */

    @Keep
    public void requestConnect() {
        WlLinkSdkLog.i("WLConnectManagerrequestConnect -------------->");
        Intent intent = new Intent(WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION);
        intent.putExtra(WLPlatformConfig.WL_PLATFORM_PACKAGE_NAME, mContext.getPackageName());
        mContext.sendBroadcast(intent);
    }

    /**
     * 三方开启连接服务
     *
     * @param connectKey - 三方应用互联能力字符串
     */
    @Keep
    public void startConnect(String connectKey) {
        WlLinkSdkLog.i("WLConnectManagerstartConnect ---------->connectKey:" + connectKey);

        if (TextUtils.isEmpty(connectKey)) {
            return;
        }
        mWLConnectState = WLConnectState.STATUS_WELINK_CONNECTING;
        WLConnector.connect(mContext, connectKey, WLConnectManager.this);
    }

    /**
     * 方法名称：startDisConnect
     * 方法描述：三方断开连接服务
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/6 10:30
     */

    @Keep
    public void startDisConnect() {
        WlLinkSdkLog.i("WLConnectManagerstartDisConnect ---------->");
        if (mWLConnector == null) {
            return;
        }
        if (mWLConnectState == WLConnectState.STATUS_WELINK_CONNECTED) {
            mWLConnectState = WLConnectState.STATUS_WELINK_DISCONNECTING;
            mWLConnector.disconnect();
        }
    }

    /**
     * 方法名称：getHuCarInfo
     * 方法描述：获取互联车机信息
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/05/21 10:04
     */

    @Keep
    public CarBean getHuCarInfo() {
        WlLinkSdkLog.i("WLConnectManagergetHuCarInfo ------->");

        if (mWLConnector == null) {
            return null;
        }

        CarBean mCarBean = new CarBean();

        mCarBean.setHuScreenWidth(mWLConnector.getConfiguration().getHUScreenWidth());
        mCarBean.setHuScreenHeight(mWLConnector.getConfiguration().getHUScreenHeight());
        mCarBean.setDensityDpi(mWLConnector.getConfiguration().getHUDensityDpi());
        mCarBean.setUserID(mWLConnector.getConfiguration().getUserID());
        mCarBean.setVehicleID(mWLConnector.getConfiguration().getVehicleID());
        mCarBean.setVehicleType(mWLConnector.getConfiguration().getVehicleType());

        WlLinkSdkLog.i("WLConnectManagergetHuCarInfo ------->CarBean:" + mCarBean.toString());
        return mCarBean;
    }

    /**
     * 方法名称：registerMusic
     * 方法描述：注册音乐回调监听处理对象
     * 方法参数：callback - 音乐回调对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/15 15:56
     */

    @Keep
    public void registerMusic(WeLinkMusicCallback callback) {
        WlLinkSdkLog.i("WLConnectManagerregisterMusic -------------->");

        if (mMusicControl == null) {
            return;
        }
        mMusicControl.registerMusic(callback);
    }

    /**
     * 方法名称：unRegisterMusic
     * 方法描述：取消注册音乐回调监听处理类
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 13:37
     */

    @Keep
    public void unRegisterMusic() {
        WlLinkSdkLog.i("WLConnectManagerunRegisterMusic -------------->");

        if (mMusicControl == null) {
            return;
        }
        mMusicControl.unRegisterMusic();
    }

    /**
     * 方法名称：startMusic
     * 方法描述：开始播放音乐初始化数据
     * 方法参数：mPCMBean - 音乐音频格式参数实体类
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/12/9 17:15
     */

    @Keep
    public void startMusic(PCMBean mPCMBean, long playPosition) {
        WlLinkSdkLog.i("WLConnectManagerstartMusic -------------->mPCMBean:" + mPCMBean.toString());

        if (mMusicControl == null) {
            return;
        }
        mMusicControl.startMusic(mPCMBean, playPosition);
    }

    /**
     * 方法名称：pauseMusic
     * 方法描述：暂停音乐播放
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/12/8 17:15
     */

    @Keep
    public void pauseMusic() {
        WlLinkSdkLog.i("WLConnectManagerpauseMusic -------------->");
        if (mMusicControl == null) {
            return;
        }
        mMusicControl.pauseMusic();
    }

    /**
     * 恢复音乐播放
     */
    @Keep
    public void resumeMusic() {
        WlLinkSdkLog.i("WLConnectManagerresumeMusic -------------->");
        if (mMusicControl == null) {
            return;
        }
        mMusicControl.resumeMusic();
    }

    /**
     * 方法名称：stopMusic
     * 方法描述：停止音乐播放
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/12/8 17:15
     */

    @Keep
    public void stopMusic() {
        WlLinkSdkLog.i("WLConnectManagerstopMusic -------------->");
        if (mMusicControl == null) {
            return;
        }
        mMusicControl.stopMusic();
    }

    /**
     * 方法名称：updateMusicID3
     * 方法描述：手机端给车机端发送Id3数据信息
     * 方法参数：mID3Bean - 音乐ID3格式参数实体类
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/15 15:06
     */

    @Keep
    public void updateMusicID3(ID3Bean mID3Bean) {
        WlLinkSdkLog.i("WLConnectManagerupdateMusicID3 -------------->mID3Bean：" + (mID3Bean == null ? "null" : mID3Bean.toString()));

        if (mMusicControl == null) {
            return;
        }
        mMusicControl.updateMusicID3(mID3Bean);
    }

    /**
     * 方法名称：updateMusicOrder
     * 方法描述：更新音乐播报顺序
     * 方法参数：order - 音乐播放顺序类型 0: 循环播放、1: 顺序播放、2: 随机播放、3: 单曲循环播放
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/19 11:19
     */

    @Keep
    public void updateMusicOrder(int order) {
        WlLinkSdkLog.i("WLConnectManagerupdateMusicOrder -------------->");

        if (mMusicControl == null) {
            return;
        }
        mMusicControl.updateMusicOrder(order);
    }

    /**
     * 方法名称：updateMusicPCM
     * 方法描述：手机端给车机端发送pcm音频数据信息
     * 方法参数：pcm - 音乐PCM播放数据
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/15 15:07
     */

    @Keep
    public long updateMusicPCM(byte[] pcm) {
        WlLinkSdkLog.i("WLConnectManagerupdateMusicPCM -------------->");

        if (mMusicControl == null) {
            return -1;
        }
        return mMusicControl.updateMusicPCM(pcm);
    }

    /**
     * 方法名称：playText
     * 方法描述：播报文本资源
     * 方法参数：pcm - tts播放PCM播放数据，callback tts播放回调对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 10:57
     */

    @Keep
    public void playText(String mark, byte[] pcm, int rate, int bit, int channel, WeLinkSoundCallback callback) {
        WlLinkSdkLog.i("WLConnectManagerplayText -------------->" + "mTTSControl is null " + (null == mTTSControl));

        if (mTTSControl == null) {
            return;
        }
        mTTSControl.playText(mark, pcm, rate, bit, channel, callback);
    }

    /**
     * 方法名称：startWLTBT
     * 方法描述：启动TBT
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:02
     */

    @Keep
    public void startWLTBT() {
        WlLinkSdkLog.i("WLConnectManagerstartWLTBT -------------->");

        if (mTBTControl == null) {
            return;
        }
        mTBTControl.startWLTBT();
    }

    /**
     * 方法名称：stopWLTBT
     * 方法描述：关闭TBT
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:02
     */

    @Keep
    public void stopWLTBT() {
        WlLinkSdkLog.i("WLConnectManagerstopWLTBT -------------->");

        if (mTBTControl == null) {
            return;
        }
        mTBTControl.stopWLTBT();
    }

    /**
     * 方法名称：updataWLTBT
     * 方法描述：更新导航路口信息
     * 方法参数：mTBTBean - 导航路口图信息
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:10
     */

    @Keep
    public void updataWLTBT(TBTBean mTBTBean) {
        WlLinkSdkLog.i("WLConnectManagerupdataWLTBT -------------->");

        if (mTBTControl == null) {
            return;
        }
        mTBTControl.updataWLTBT(mTBTBean);
    }

    /**
     * 方法名称：startMicrophone
     * 方法描述：开始使用车机mic资源
     * 方法参数：callback - MIC接收回调对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:18
     */

    @Keep
    public void startMicrophone(WeLinkMICCallback callback) {
        WlLinkSdkLog.i("WLConnectManagerstartMicrophone -------------->");

        if (mMICControl == null) {
            return;
        }
        mMICControl.startMicrophone(callback);
    }

    /**
     * 方法名称：stopMicrophone
     * 方法描述：停止使用车机mic资源
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:18
     */

    @Keep
    public void stopMicrophone() {
        WlLinkSdkLog.i("WLConnectManagerstopMicrophone -------------->");

        if (mMICControl == null) {
            return;
        }
        mMICControl.stopMicrophone();
    }

    /**
     * 方法名称：isSupportMicrophone
     * 方法描述：判断车机是否支持录音
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/5/9 9:38
     */

    @Keep
    public boolean isSupportMicrophone() {
        WlLinkSdkLog.i("WLConnectManagerstopMicrophone -------------->");

        if (mMICControl == null) {
            return false;
        }
        return mMICControl.isSupportMicrophone();
    }

    /**
     * 方法名称：startMicrophone
     * 方法描述：拨打车机蓝牙电话
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/5/9 13:47
     */

    @Keep
    public void dialBTPhone(String number, WeLinkBTCallback callback) {
        WlLinkSdkLog.i("WLConnectManagerdialBTPhone ------->");

        if (mBTPhoneControl == null) {
            return;
        }
        mBTPhoneControl.dialBTPhone(number, callback);
    }

    /**
     * 方法名称：hangUpBTPhone
     * 方法描述：挂断车机蓝牙电话
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/5/9 13:47
     */

    @Keep
    public void hangUpBTPhone() {
        WlLinkSdkLog.i("WLConnectManagerhangUpBTPhone ------->");

        if (mBTPhoneControl == null) {
            return;
        }
        mBTPhoneControl.hangUpBTPhone();
    }

    /**
     * 方法名称：isSupportBTPhone
     * 方法描述：判断车机是否支持拨打电话
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/5/9 9:38
     */

    @Keep
    public boolean isSupportBTPhone() {
        WlLinkSdkLog.i("WLConnectManagerisSupportBTPhone ------->");

        if (mBTPhoneControl == null) {
            return false;
        }
        return mBTPhoneControl.isSupportBTPhone();
    }

    /**
     * 方法名称：registerCommand
     * 方法描述：添加指定类型的命令回调监听
     * 方法参数：commondType - 车机命令类型 mWeLinkCommandListener - 接收车机命令回调处理对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/14 11:10
     */

    @Keep
    public void registerCommand(WLCommandType mWLCommandType, WeLinkCommandListener mListener) {
        String commondType = mWLCommandType.getType();
        WlLinkSdkLog.i("WLConnectManagerregisterCommand -------------->commondType:" + commondType);

        if (mCommandListeners.containsKey(commondType)) {
            ArrayList<WeLinkCommandListener> list = mCommandListeners.get(commondType);
            if (!list.contains(mListener)) {
                list.add(mListener);
            }
        } else {
            ArrayList<WeLinkCommandListener> list = new ArrayList<>();
            list.add(mListener);
            mCommandListeners.put(commondType, list);
        }
    }

    /**
     * 方法名称：unRegisterCommand
     * 方法描述：移除指定类型的命令回调监听
     * 方法参数：commondType - 车机命令类型 mWeLinkCommandListener - 接收车机命令回调处理对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/14 11:10
     */

    @Keep
    public void unRegisterCommand(WLCommandType mWLCommandType, WeLinkCommandListener mListener) {
        String commondType = mWLCommandType.getType();
        WlLinkSdkLog.i("WLConnectManagerunRegisterCommand -------------->commondType:" + commondType);

        if (mCommandListeners.containsKey(commondType)) {
            ArrayList<WeLinkCommandListener> list = mCommandListeners.get(commondType);
            if (list.contains(mListener)) {
                list.remove(mListener);
            }
        }
    }

    /**
     * 方法名称：onDestory
     * 方法描述：销毁三方服务管理类
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/6 10:38
     */

    @Keep
    public void destroy() {
        WlLinkSdkLog.i("WLConnectManageronDestory -------------->");

        if (mWLConnector != null) {
            mWLConnector.release();
        }
        unRregisterReceiver();
        mContext = null;
        mMusicControl = null;
        mTTSControl = null;
        mMICControl = null;
        mTBTControl = null;
        mBTPhoneControl = null;
        mCommandListeners.clear();
        mWLConnectState = WLConnectState.STATUS_NONE;
        mWeLinkConnectListener = null;
    }

    /******************************* WLConnector回调接口功能处理方法 *******************************/

    @Override
    public boolean onConnected(WLConnector wlConnector) {
        WlLinkSdkLog.i("WLConnectManagerWLConnectListener -------- onConnected ------------>");

        mWLConnectState = WLConnectState.STATUS_WELINK_CONNECTED;
        mWLConnector = wlConnector;

        mMusicControl = new MusicControl(mWLConnector.getConfiguration().getMusicCapability());
        mTTSControl = new TTSControl(mWLConnector.getConfiguration().getSoundCapability());
        mMICControl = new MICControl(mWLConnector.getConfiguration().getMicrophoneCapability());
        mTBTControl = new TBTControl(mWLConnector.getConfiguration().getTBTInfoCapability());
        mBTPhoneControl = new BTPhoneControl(mWLConnector.getConfiguration().getBluetoothPhoneCapability());

        if (mWeLinkConnectListener != null) {
            mWeLinkConnectListener.onConnected(mWLConnector.getConfiguration().getDisplayCapability());
        }
        WLPlatformManager platformManager = WLPlatformManager.getInstance();
        platformManager.connectedAdapter();
        return true;
    }

    @Override
    public void onDisconnected() {
        WlLinkSdkLog.i("WLConnectManagerWLConnectListener -------- onDisconnected ------------>");

        mWLConnectState = WLConnectState.STATUS_WELINK_DISCONNECTED;

        if (mWLConnector != null) {
            mWLConnector.release();
            mWLConnector = null;
        }

        mMusicControl = null;
        mTTSControl = null;
        mMICControl = null;
        mTBTControl = null;
        mBTPhoneControl = null;

        if (mWeLinkConnectListener != null) {
            mWeLinkConnectListener.onDisconnected();
        }
        WLPlatformManager platformManager = WLPlatformManager.getInstance();
        if (platformManager != null) {
            platformManager.disconnectedAdapter();
        }
    }

    @Override
    public void onError(int errorCode) {
        WlLinkSdkLog.i("WLConnectManagerWLConnectListener -------- onError ------------>errorCode:" + errorCode);

        mWLConnectState = WLConnectState.STATUS_WELINK_DISCONNECTED;

        if (mWLConnector != null) {
            mWLConnector.release();
            mWLConnector = null;
        }

        mMusicControl = null;
        mTTSControl = null;
        mMICControl = null;
        mTBTControl = null;
        mBTPhoneControl = null;

        if (mWeLinkConnectListener != null) {
            mWeLinkConnectListener.onConnectError(errorCode);
        }
        WLPlatformManager platformManager = WLPlatformManager.getInstance();
        if (platformManager != null) {
            platformManager.disconnectedAdapter();
        }
    }

    @Override
    public void onCommand(String command) {
        WlLinkSdkLog.i("WLConnectManagerWLConnectListener -------- onCommand ------------>command:" + command);

        if (!mCommandListeners.isEmpty()) {
            try {
                JSONObject jsonObject = new JSONObject(command);
                if (jsonObject.has(WLPlatformConfig.WL_PLATFORM_COMMAND_TYPE)) {
                    String type = jsonObject.getString(WLPlatformConfig.WL_PLATFORM_COMMAND_TYPE);
                    ArrayList<WeLinkCommandListener> list = mCommandListeners.get(type);
                    if (list != null && !list.isEmpty()) {
                        for (WeLinkCommandListener mListener : list) {
                            mListener.onReceiveCommand(command);
                        }
                    }
                }
            } catch (JSONException e) {
                WlLinkSdkLog.e("WLConnectManagerWLConnectListener exception:" + e.getMessage());
            }
        }
    }

    /******************************* 内部私有接口功能处理方法 *******************************/

    /**
     * 方法名称：registerReceiver
     * 方法描述：开启监听平台的连接广播
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/7 10:59
     */

    private void registerReceiver() {
        WlLinkSdkLog.i("WLConnectManagerregisterReceiver -------------->");

        if (mConnectReceiver == null) {
            mConnectReceiver = new ConnectReceiver();
            IntentFilter intentFilter = new IntentFilter(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
            if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.TIRAMISU){
                mContext.registerReceiver(mConnectReceiver, intentFilter,RECEIVER_EXPORTED);
            }else {
                mContext.registerReceiver(mConnectReceiver, intentFilter);
            }
        }

    }

    /**
     * 方法名称：unRregisterReceiver
     * 方法描述：取消监听平台的连接广播
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/7 10:59
     */

    private void unRregisterReceiver() {
        WlLinkSdkLog.i("WLConnectManagerunRregisterReceiver -------------->");

        if (mConnectReceiver != null) {
            mContext.unregisterReceiver(mConnectReceiver);
            mConnectReceiver = null;
        }
    }

    private class ConnectReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                return;
            }
            if (intent.getAction().equals(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION)) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    String connectKey = bundle.getString(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY);
                    if (!TextUtils.isEmpty(connectKey)) {
                        startConnect(connectKey);
                    }
                }
            }
        }
    }

}
