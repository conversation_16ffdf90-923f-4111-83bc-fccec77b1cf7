package com.autoai.avs.linksdk.platform.protocol.control;

import com.autoai.avs.linksdk.WlLinkSdkLog;

import java.util.HashMap;

/**
 * Welink手机交互协议手机端发送协议缓存处理类，主要定义封装缓存管理发送各种消息接口。
 *
 * <AUTHOR>
 */
public class MUCacheControl {
    private static final String TAG = "Protocol_MUCacheControl";

    private final HashMap<String, String> mProtocolCaches = new HashMap<>();

    public void addMethodProtocol(String method, String message) {
        WlLinkSdkLog.i("MUCacheControladdMethodProtocol ---------> method:" + method + ",message:" + message);
        mProtocolCaches.put(method, message);
    }

    public String getMethodProtocol(String method) {
        WlLinkSdkLog.i("MUCacheControlgetMethodProtocol ---------> method:" + method);
        return mProtocolCaches.get(method);
    }
}
