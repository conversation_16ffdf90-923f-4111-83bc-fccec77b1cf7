package com.autoai.avs.linksdk.platform.pcm;


import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.pcm.TransportImpl.MusicPcmTranspotImpl;
import com.autoai.avs.linksdk.platform.pcm.TransportImpl.TTSPcmTransportImpl;
import com.autoai.welink.platform.WLPCMPolicy;

public class WLPCMPolicyImpl implements WLPCMPolicy {
    private Callback musicCallback;
    private MusicPcmTranspotImpl musicPcmTranspot;
    private TTSPcmTransportImpl ttsPcmTransport;
    private long totalLen;
    private int ttsPcmType = -1;

    /**
     * 打开日志功能
     *
     * @param enable true:打开；false：关闭。
     */
    @Override
    public void enableLogCat(boolean enable) {

    }

    @Override
    public void enableLogFile(boolean enable) {

    }

    @Override
    public void enableFocus(Callback callback) {
        WlLinkSdkLog.d("WLPCMPolicyImpl  ---enableFocus ---");
        this.musicCallback = callback;
    }

    @Override
    public void disableFocus() {
        WlLinkSdkLog.d("WLPCMPolicyImpl  ---disableFocus ---");
        this.stopMusicPlay();
    }

//    @Override
//    public void enableLogCat(boolean enable) {
//        WlLinkSdkLog.setIsLoggable(true);
//        WlLinkSdkLog.d("WLPCMPolicyImpl  ---enableLogCat --- enable:" + enable);
//
//    }
//
//    @Override
//    public void enableLogFile(boolean enable) {
//        WlLinkSdkLog.setIsLoggable(true);
//        WlLinkSdkLog.d("WLPCMPolicyImpl  ---enableLogFile --- enable:" + enable);
//
//    }

    @Override
    public void startMusicPlay(long totalLen, int rate, int bit, int channel) {
        WlLinkSdkLog.d("WLPCMPolicyImpl  ---startMusicPlay ---" + "rate = " + rate + "--bit = " + bit + "--channel = " + channel + "--totalLen = " + totalLen);
        if (musicCallback == null) {
            return;
        }

        //发送开始播放状态
//        byte[] mediaByte = PCMManager.getInstance().notifyCarMediaState(PcmConfigs.MEDIA_STATE_START);
//        musicCallback.onPlayCommand(mediaByte);

        if (musicPcmTranspot == null) {
            musicPcmTranspot = new MusicPcmTranspotImpl(musicCallback);
        }
        this.totalLen = totalLen;
        musicPcmTranspot.startTransportThread();
        musicPcmTranspot.setVoiceHeadInfo(rate, bit, channel);
        musicPcmTranspot.producePcm(new byte[0], PcmConfigs.SOUND_MARK_START, 0);
    }

    @Override
    public void pauseMusicPlay() {
        WlLinkSdkLog.d("WLPCMPolicyImpl  ---onSendPCM pauseMusicPlay ---");
        if (musicPcmTranspot != null) {
            musicPcmTranspot.pauseTransportThread();
        }
    }

    @Override
    public void resumeMusicPlay() {
        WlLinkSdkLog.d("WLPCMPolicyImpl  ---onSendPCM resumeMusicPlay ---");
        if (musicPcmTranspot != null) {
            musicPcmTranspot.resumeTransportThread();
        }
    }

    @Override
    public void stopMusicPlay() {
        WlLinkSdkLog.d("WLPCMPolicyImpl  ---onSendPCM stopMusicPlay ---");
        if (musicCallback == null) {
            return;
        }

        if (musicPcmTranspot != null) {
            musicPcmTranspot.cancelTransportThread(PcmConfigs.SOUND_MUSIC);
        }

        //发送结束播放状态
//        byte[] mediaByte = PCMManager.getInstance().notifyCarMediaState(PcmConfigs.MEDIA_STATE_STOP);
//        musicCallback.onPlayCommand(mediaByte);
    }

    @Override
    public void receiveMusicPCM(long position, byte[] pcm) {
        WlLinkSdkLog.d("WLPCMPolicyImpl onSendPCM  ---receiveMusicPCM --- position " + position + "totalLen: " + totalLen);
        if (musicPcmTranspot != null) {
            musicPcmTranspot.producePcm(pcm, PcmConfigs.SOUND_MARK_CENTER, 0);
            if (position >= 0 && totalLen <= position + pcm.length) {
                musicPcmTranspot.producePcm(new byte[0], PcmConfigs.SOUND_MARK_END, 0);
            }
        }
    }

    @Override
    public void startTTSPlay(long totalLen, int rate, int bit, int channel, int type, Callback callback) {
        WlLinkSdkLog.d("WLPCMPolicyImpl onSendPCM : ---startTTSPlay ---" + "rate = " + rate + "--bit = " + bit + "--channel = " + channel + "type : " + type);
        if (callback == null || ttsPcmType >= 0) {
            return;
        }
        if (ttsPcmTransport == null) {
            ttsPcmTransport = new TTSPcmTransportImpl(callback);
        }
        ttsPcmType = type;
        ttsPcmTransport.setVoiceHeadInfo(rate, bit, channel);
        ttsPcmTransport.startTransportThread();
        ttsPcmTransport.producePcm(new byte[0], PcmConfigs.SOUND_MARK_START, type);
    }

    @Override
    public void stopTTSPlay() {
        WlLinkSdkLog.d("WLPCMPolicyImpl  onSendPCM :---stopTTSPlay ---ttsPcmType:" + ttsPcmType);
        if (ttsPcmTransport != null && ttsPcmType >= 0) {
            ttsPcmTransport.cancelTransportThread(ttsPcmType);
        }
        ttsPcmType = -1;
    }

    @Override
    public void receiveTTSPCM(byte[] pcm) {
        WlLinkSdkLog.d("WLPCMPolicyImpl  onSendPCM :---receiveTTSPCM ---length:" + (pcm != null ? pcm.length : 0) + ",ttsPcmType:" + ttsPcmType);
        if (ttsPcmTransport != null && ttsPcmType >= 0) {
            if (null == pcm || pcm.length == 0) {
                ttsPcmTransport.producePcm(new byte[0], PcmConfigs.SOUND_MARK_END, ttsPcmType);
                ttsPcmType = -1;
            } else {
                ttsPcmTransport.producePcm(pcm, PcmConfigs.SOUND_MARK_CENTER, ttsPcmType);
            }
        }
    }

    @Override
    public void release() {
        WlLinkSdkLog.d("WLPCMPolicyImpl WLPCMPolicyImpl_release");
        if (musicPcmTranspot != null) {
            musicPcmTranspot.release();
        }
        if (ttsPcmTransport != null) {
            ttsPcmTransport.release();
            ttsPcmType = -1;
        }
    }
}
