package com.autoai.avs.linksdk.bridge;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;


/**
 * 完全透明 Activity, 主要作用是为服务条款的 DialogFragment 以及登录弹窗提供显示凭依; 方便外部应用程序调用
 */
public class BridgeActivity extends FragmentActivity {
    private static final String TAG = "BridgeActivity";
    private static BridgeCallback bridgeCallback;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    /**
     * 申请需要依赖 Activity 的操作, 并在 callback 中进行相应的动作即可;
     *
     * 注意: 对于不需要 activityResult 的场景, 需要在操作完成时, 调用 activity.finish() 关闭对应 Activity.
     *
     * @param context
     * @param callback
     */
    public static void showActivityBridge(@NonNull Context context, @NonNull BridgeCallback callback) {
//        // 为防止多次弹窗冲突, 此处做限制处理
//        if (readyCallback != null) {
//            Log.d(TAG, "showActivityBridge: callback not null, request ignore!");
//            return;
//        }

        bridgeCallback = callback;
        Intent intent = new Intent(context, BridgeActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "showActivityBridge: Environment ready!");
        if (bridgeCallback != null) {
            bridgeCallback.onReady(this);
        }
    }

    public interface BridgeCallback {
        /**
         * Activity 环境已准备好, 可以再次方法中进行相应操作了, 比如权限申请, Dialog 弹出等.
         * @param activity
         */
        void onReady(FragmentActivity activity);
        void onPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (bridgeCallback != null) {
            bridgeCallback.onPermissionsResult(requestCode, permissions, grantResults);
        }
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        bridgeCallback = null;
        Log.d(TAG, "onDestroy, readyCallback reset.");
    }
}
