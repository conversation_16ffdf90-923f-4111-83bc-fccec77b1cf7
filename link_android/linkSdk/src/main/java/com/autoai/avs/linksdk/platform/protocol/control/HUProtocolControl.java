package com.autoai.avs.linksdk.platform.protocol.control;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;
import com.autoai.avs.linksdk.platform.protocol.bean.AirCondionBean;
import com.autoai.avs.linksdk.platform.protocol.bean.BaseProtocolBean;
import com.autoai.avs.linksdk.platform.protocol.bean.CanDataBaseBean;
import com.autoai.avs.linksdk.platform.protocol.bean.CanDataFZGBean;
import com.autoai.avs.linksdk.platform.protocol.bean.CanDataFZGDSBean;
import com.autoai.avs.linksdk.platform.protocol.bean.CanDataSIAOILBean;
import com.autoai.avs.linksdk.platform.protocol.bean.CanDataServiceBean;
import com.autoai.avs.linksdk.platform.protocol.bean.DMsgShowBean;
import com.autoai.avs.linksdk.platform.protocol.bean.EnAndChBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HardWareBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuAppFront;
import com.autoai.avs.linksdk.platform.protocol.bean.HuAudioBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuBtBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuBtPhoneBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuPhoneBtMacAddressBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuChannelBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuLampBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuMicState;
import com.autoai.avs.linksdk.platform.protocol.bean.HuNaviBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuNormalBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuPModeBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuRVCBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuStandByBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuUpdateBean;
import com.autoai.avs.linksdk.platform.protocol.bean.HuVRBean;
import com.autoai.avs.linksdk.platform.protocol.bean.KeyStateBean;
import com.autoai.avs.linksdk.platform.protocol.bean.LocalVRStateBean;
import com.autoai.avs.linksdk.platform.protocol.bean.LowPowerBean;
import com.autoai.avs.linksdk.platform.protocol.bean.MXMsgSwitchBean;
import com.autoai.avs.linksdk.platform.protocol.bean.MiniNaviBean;
import com.autoai.avs.linksdk.platform.protocol.bean.MuAudioBean;
import com.autoai.avs.linksdk.platform.protocol.bean.PlayWXMsgBean;
import com.autoai.avs.linksdk.platform.protocol.bean.ReqAppData;
import com.autoai.avs.linksdk.platform.protocol.bean.ResponseBean;
import com.autoai.avs.linksdk.platform.protocol.listener.HUCommandListener;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Welink手机交互协议处理车机端消息处理类，主要定义封装监听接收处理各种消息状态。
 *
 * <AUTHOR>
 */
public class HUProtocolControl {
    private final HashMap<String, ArrayList<HUCommandListener>> mListeners = new HashMap<>();

    /**
     * 添加指定方法的监听用来接收手机端的协议消息广播
     */
    public void addListener(String methodTag, HUCommandListener mListener) {
        WlLinkSdkLog.i("HUProtocolControl addListener ---------> methodTag:" + methodTag);

        if (mListeners.containsKey(methodTag)) {
            ArrayList<HUCommandListener> list = mListeners.get(methodTag);
            if (list != null && !list.contains(mListener)) {
                list.add(mListener);
            }
        } else {
            ArrayList<HUCommandListener> list = new ArrayList<>();
            list.add(mListener);
            mListeners.put(methodTag, list);
        }
    }


    /**
     * 移除指定方法的监听禁止接收手机端的协议消息广播
     */
    public void removeListener(String methodTag, HUCommandListener mListener) {
        WlLinkSdkLog.i("HUProtocolControl removeListener ---------> methodTag:" + methodTag);
        if (mListeners.containsKey(methodTag)) {
            ArrayList<HUCommandListener> list = mListeners.get(methodTag);
            if (list != null) {
                list.remove(mListener);
            }
        }
    }

    /**
     * 清空所有方法的监听禁止接收手机端的协议消息广播
     */
    public void clearListener() {
        WlLinkSdkLog.i("HUProtocolControl clearListener --------->");
        mListeners.clear();
    }

    /**
     * 获取指定方法的协议数据
     */
    public void parseCommand(String methodName, JSONObject extData) {
        WlLinkSdkLog.i("HUProtocolControl parseCommand ---------> methodName:" + methodName + ",extData:" + extData.toString());
        try {
            BaseProtocolBean mBaseProtocolBean = getCommandBean(methodName);
            if (mBaseProtocolBean != null) {
                mBaseProtocolBean.parse(extData);
                sendCommand(methodName, mBaseProtocolBean);
            }
        } catch (Exception e) {
            WlLinkSdkLog.e("HUProtocolControl parseCommand exception:" + e.getMessage());
        }
    }

    /**
     * 给注册接收的协议广播回调处理消息
     */
    private void sendCommand(String methodName, BaseProtocolBean mBaseProtocolBean) {
        WlLinkSdkLog.i("HUProtocolControl sendCommand ---------> methodName:" + methodName);

        ArrayList<HUCommandListener> list = mListeners.get(methodName);

        if (list != null && !list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                list.get(i).onReceiveCommand(mBaseProtocolBean);
            }
        }
    }

    /**
     * 根据方法名称返回指定的协议实体bean
     */
    private BaseProtocolBean getCommandBean(String methodName) {
        WlLinkSdkLog.i("HUProtocolControl getCommandBean ---------> methodName:" + methodName);
        BaseProtocolBean mBaseProtocolBean = null;
        switch (methodName) {
            case WLProtocolConfig.HU_PROTOCOL_METHOD_UPDATEINFO:
                mBaseProtocolBean = new HuUpdateBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_RESUMEUPDAE:
                mBaseProtocolBean = new HuNormalBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_HUCHANNEL:
                mBaseProtocolBean = new HuChannelBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CONTROLAUDIO:
                mBaseProtocolBean = new MuAudioBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_HARDWAREKEY:
                mBaseProtocolBean = new HardWareBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_MININAVI:
                mBaseProtocolBean = new MiniNaviBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_MMSGHSHOW:
                mBaseProtocolBean = new DMsgShowBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_WXMSGSWITCH:
                mBaseProtocolBean = new MXMsgSwitchBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_PLAYWXMSG:
                mBaseProtocolBean = new PlayWXMsgBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_BTPHONE:
                mBaseProtocolBean = new HuBtPhoneBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_BTSTATE:
                mBaseProtocolBean = new HuBtBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_PHONE_BT_MAC_ADDRESS:
                mBaseProtocolBean = new HuPhoneBtMacAddressBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_PMODESTATE:
                mBaseProtocolBean = new HuPModeBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_RVCSTATE:
                mBaseProtocolBean = new HuRVCBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_STANDBYSTATE:
                mBaseProtocolBean = new HuStandByBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_INTERAUDIO:
                mBaseProtocolBean = new HuAudioBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_MOSPHERELAMP:
                mBaseProtocolBean = new HuLampBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_NAVISTATE:
                mBaseProtocolBean = new HuNaviBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_VRSTATE:
                mBaseProtocolBean = new HuVRBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_BASE:
                mBaseProtocolBean = new CanDataBaseBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_SERVICE:
                mBaseProtocolBean = new CanDataServiceBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_SIAOIL:
                mBaseProtocolBean = new CanDataSIAOILBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_FZG:
                mBaseProtocolBean = new CanDataFZGBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_FZGDS:
                mBaseProtocolBean = new CanDataFZGDSBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_ENANDCH:
                mBaseProtocolBean = new EnAndChBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_RESPONSE:
                mBaseProtocolBean = new ResponseBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_AIRCONDION:
                mBaseProtocolBean = new AirCondionBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_KEY_STATE_INFO:
                mBaseProtocolBean = new KeyStateBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_LOCAL_VR:
                mBaseProtocolBean = new LocalVRStateBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_APP_FRONT:
                mBaseProtocolBean = new HuAppFront();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_LOW_POWER:
                mBaseProtocolBean = new LowPowerBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_MIC_STATE:
                mBaseProtocolBean = new HuMicState();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_REQ_APP_DATA:
                mBaseProtocolBean = new ReqAppData();
                break;
            default:
                break;
        }
        if (mBaseProtocolBean != null) {
            mBaseProtocolBean.setMethodName(methodName);
        }
        return mBaseProtocolBean;
    }
}
