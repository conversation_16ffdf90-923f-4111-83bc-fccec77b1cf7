package com.autoai.avs.linksdk.config;


import com.autoai.avs.linksdk.util.AppUtils;

import java.util.HashMap;

public class Configs {
    /**
     * 是否支持: 无线互联
     */
    public static final boolean SUPPORT_WIFI_LINK = false;
    /**
     * 是否支持: USB 互联
     */
    public static final boolean SUPPORT_USB_LINK = true;

    /**
     * 是否支持: 无感互联
     */
    public static final boolean SUPPORT_BLE_WIFI_LINK = true;

    private Configs() {
    }

    private static String shellMainActivityClassName = "com.autoai.welink.logiclib.activity.ModuleActivity";
    private static String shellMainDialogClassName = "com.autoai.welink.logiclib.santou.dialog.ShellDialogManager";
    /**
     * 键盘是否已经初始化
     */
    private static boolean isKeyboardStart = false;
    /**
     * 语音唤醒开关
     */
    private static boolean isOpenArouse = false;
    /**
     * 是否在导航中
     */
    private static boolean isNaving = false;
    /**
     * 录音状态，时车机录音还是sco，还是手机录音
     */
    private static AppUtils.RecordType recordingType = AppUtils.RecordType.phoneRecording;
    /**
     * 导航的广播
     */
    private static boolean naviBrocast = false;
    /**
     * 语音识别悬浮窗
     */
    private static boolean isShowAitalkView = false;
    /**
     * usb录音状态
     */
    private static boolean isUSBRecordState = false;
    /**
     * 保存车机是否互联成功手机，与mainapplication有区别，这个主要用再UI上的互联与不互联的判断
     */
    private static boolean isConnectCar = false;

    private static boolean isAitalkMute = false;
    /**
     * 车机是否是待机状态
     */
    private static boolean isStandbyState = false;
    /**
     * QQ音乐页面退出时是否正在显示dialog
     */
    private static boolean isShowDialog;
    /**
     * 判断车机端是否将音乐调成静音
     */
    private static boolean isHUMusicMute;
    /**
     * 首次进入app 是否已请求位置权限
     */
    private static boolean isRequestLocationPermission = false;

    /**
     * 适配手机列表,是要适配常驻条第三方应用音乐和其他的不能切换的问题
     */
    private static HashMap<String, String> adapterPhoneList = new HashMap<>(8);

    public static String getShellMainActivityClassName() {
        return shellMainActivityClassName;
    }

    public static void setShellMainActivityClassName(String shellMainActivityClassName) {
        Configs.shellMainActivityClassName = shellMainActivityClassName;
    }

    public static String getShellMainDialogClassName() {
        return shellMainDialogClassName;
    }

    public static void setShellMainDialogClassName(String shellMainDialogClassName) {
        Configs.shellMainDialogClassName = shellMainDialogClassName;
    }

    private static String getAdapterPhoneList(String aPhoneName) {
        if (adapterPhoneList.isEmpty()) {
            adapterPhoneList.put(ADAPTER_PHONE_OPPO_R9S, IS_ADAPTER_PHONE_STATE);
            adapterPhoneList.put(ADAPTER_PHONE_OPPO_R9TM, IS_ADAPTER_PHONE_STATE);
            adapterPhoneList.put(ADAPTER_PHONE_OPPO_R9PLUS, IS_ADAPTER_PHONE_STATE);
            adapterPhoneList.put(ADAPTER_PHONE_OPPO_R11PLUS, IS_ADAPTER_PHONE_STATE);
        }
        return adapterPhoneList.get(aPhoneName);
    }


    private static String strLimit = "";

    public static boolean isIsKeyboardStart() {
        return isKeyboardStart;
    }

    public static void setIsKeyboardStart(boolean isKeyboardStart) {
        Configs.isKeyboardStart = isKeyboardStart;
    }

    public static boolean isIsOpenArouse() {
        return isOpenArouse;
    }

    public static void setIsOpenArouse(boolean isOpenArouse) {
        Configs.isOpenArouse = isOpenArouse;
    }

    public static boolean isIsNaving() {
        return isNaving;
    }

    public static void setIsNaving(boolean isNaving) {
        Configs.isNaving = isNaving;
    }

    public static AppUtils.RecordType getRecordingType() {
        return recordingType;
    }

    public static void setRecordingType(AppUtils.RecordType recordingType) {
        Configs.recordingType = recordingType;
    }

    public static boolean isNaviBrocast() {
        return naviBrocast;
    }

    public static void setNaviBrocast(boolean naviBrocast) {
        Configs.naviBrocast = naviBrocast;
    }

    public static boolean isIsShowAitalkView() {
        return isShowAitalkView;
    }

    public static void setIsShowAitalkView(boolean isShowAitalkView) {
        Configs.isShowAitalkView = isShowAitalkView;
    }

    public static boolean isIsUSBRecordState() {
        return isUSBRecordState;
    }

    public static void setIsUSBRecordState(boolean isUSBRecordState) {
        Configs.isUSBRecordState = isUSBRecordState;
    }

    public static boolean isIsConnectCar() {
        return isConnectCar;
    }

    public static void setIsConnectCar(boolean isConnectCar) {
        Configs.isConnectCar = isConnectCar;
    }

    public static boolean isIsAitalkMute() {
        return isAitalkMute;
    }

    public static void setIsAitalkMute(boolean isAitalkMute) {
        Configs.isAitalkMute = isAitalkMute;
    }

    public static boolean isIsStandbyState() {
        return isStandbyState;
    }

    public static void setIsStandbyState(boolean isStandbyState) {
        Configs.isStandbyState = isStandbyState;
    }

    public static boolean isIsShowDialog() {
        return isShowDialog;
    }

    public static void setIsShowDialog(boolean isShowDialog) {
        Configs.isShowDialog = isShowDialog;
    }

    public static boolean isIsHUMusicMute() {
        return isHUMusicMute;
    }

    public static void setIsHUMusicMute(boolean isHUMusicMute) {
        Configs.isHUMusicMute = isHUMusicMute;
    }

    public static boolean isIsRequestLocationPermission() {
        return isRequestLocationPermission;
    }

    public static void setIsRequestLocationPermission(boolean isRequestLocationPermission) {
        Configs.isRequestLocationPermission = isRequestLocationPermission;
    }

    public static String getStrLimit() {
        return strLimit;
    }

    public static void setStrLimit(String strLimit) {
        Configs.strLimit = strLimit;
    }

    /**
     * 保存录音模式的key
     */
    public static final String KEY_RECORDTYP = "recordingtype";

    public static final String VOICE_WEB_PATH = "https://wdservice.mapbar.com/welink/getWelinkJsp";
    /**
     * 声音线程的标准级别，代码中无法设置为该优先级，值为 -16。
     */
    public static final int THREAD_PRIORITY_AUDIO = -19;

    public static final String DB_NAME = "DB_AS.db";

    public static final int WECHAT = 0;

    public static final int VIEW_POSITION_NONE = -1;
    public static final int VIEW_POSITION_MAIN = 1;
    public static final int VIEW_POSITION_USER = 2;
    public static final int VIEW_POSITION_PHONE = 3;
    public static final int VIEW_POSITION_MESSAGE = 4;
    public static final int VIEW_POSITION_MORE = 5;
    public static final int VIEW_POSITION_AITALK = 6;
    public static final int VIEW_POSITION_NEWS = 7;
    public static final int VIEW_POSITION_SETTING = 10014;
    public static final int VIEW_POSITION_QPLAY = 10016;
    public static final int VIEW_POSITION_LOCALMUSCIPAGE = 10017;
    public static final int VIEW_POSITION_XIMAPAGE = 10018;
    /**
     * 天气页面
     */
    public static final int VIEW_POSITION_WEATHER = 10019;
    public static final int VIEW_POSITION_QSEARCH = 10022;
    public static final int VIEW_POSITION_HELP = 10023;

    /**
     * 设备系统
     */
    public static final String HEADER_DEVICE_TYPE = "android";
    /**
     * 用于onSendData方法中的code参数，
     * 播放上一首歌曲
     */
    public static final int MAINPAGEPLAYPRE = 22;
    /**
     * 播放下一首歌曲
     */
    public static final int MAINPAGEPLAYNEXT = 23;
    /**
     * 播放
     */
    public static final int MAINPAGEPLAYMUSIC = 24;
    /**
     * 打开音乐
     */
    public static final int MAINPAGEOPENMUSIC = 25;
    /**
     * 顺序播放
     */
    public static final int SEQUENTIAL_MUSIC = 26;
    /**
     * 随机播放
     */
    public static final int RANDOM_MUSIC = 27;
    /**
     * 单曲循环
     */
    public static final int SINGLE_MUSIC = 28;
    /**
     * 更新本地详情界面UI
     */
    public static final int UPDATELOCALICON = 29;
    /**
     * 暂停
     */
    public static final int PAUSE_MUSIC = 30;
    /**
     * 继续播放
     */
    public static final int CONTINUE_MUSIC = 31;
    /**
     * 语音识别关闭后继续播放
     */
    public static final int AITALK_CONTINUE_MUSIC = 32;
    /**
     * 有网络自动去连接qq音乐
     */
    public static final int ICT_QQMUSIC = 34;
    /**
     * QQ音乐搜索指令
     */
    public static final int QQ_SEARCH_MUSIC = 35;
    /**
     * 给音乐播放界面发送指令
     */
    public static final int SENDMUSICOPENPAGE = 38;
    /**
     * 更新动画
     */
    public static final int UPDATEANIMA = 39;
    /**
     * 电话来了，播放按钮暂停
     */
    public static final int CALLMUSICPAUSE = 40;
    /**
     * 电话挂断，播放按钮播放
     */
    public static final int CALLMUSICPLAY = 41;
    /**
     * 通知qplaypage刷新帧动画
     */
    public static final int LOCAL_MUSIC_ANIMATION = 42;
    /**
     * 语音搜索本地音乐
     */
    public static final int SEARCH_LOCAL_MUSIC = 43;
    /**
     * 顺序循环播放
     */
    public static final int LOOP_MUSIC = 44;
    /**
     * 如果从limit界面切到非limit界面，则通知车机隐藏轻导航
     */
    public static final int STOPNAVINFOTOCAR = 45;
    /**
     * qq音乐断开同步
     */
    public static final int CONNECT_STATE_INTERRUPT = 46;
    /**
     * 更新首页的定位的widget
     */
    public static final int UPDATE_HOME_LOCATION_WIDGET = 47;
    /**
     * 更新qq音乐图片
     */
    public static final int UPDATEQQPICTURE = 48;
    /**
     * 显示控制面板qq音乐加载动画
     */
    public static final int SHOW_QPLAY_LOADING_DIALOG = 49;
    /**
     * 停止控制面板qq乐加载动画
     */
    public static final int STOP_QPLAY_LOADING_DIALOG = 50;
    /**
     * 活动界面弹出
     */
    public static final int ACTIONJIEMIAN = 51;
    /**
     * 活动界面关掉
     */
    public static final int ACTIONCLOSE = 52;
    /**
     * 更新喜马拉雅图片
     */
    public static final int UPDATEXMPICTURE = 53;
    /**
     * 加载更多喜马拉雅数据
     */
    public static final int LOADMOREDATA = 54;
    /**
     * 无网络的时候喜马拉雅动画停止
     */
    public static final int UPDATEXMICON = 55;
    /**
     * 首页跳转到天气页面
     */
    public static final int TAG_MAIN_WEATHER = 56;
    /**
     * 天气收藏夹携带数据到天气页面
     */
    public static final int TAG_WETHER_FAVOURITE = 57;
    /**
     * 更新音乐进度
     */
    public static final int UPDATE_MUSIC_PROGRESS = 58;
    /**
     * 获取喜马拉雅音乐的专辑图信息
     */
    public static final int ID3_ALBUM_BITMAP = 59;
    /**
     * 更新音乐帧动画
     */
    public static final int UPDATE_ANIMATION = 60;
    /**
     * 更新车机前后台消息
     */
    public static final int UPDATE_HUAPPFRONT = 61;
    /**
     * 更新车机倒车影像和开关车门
     */
    public static final int UPDATE_RVC_OPENDOOR = 62;
    /**
     * 更新车机待机状态
     */
    public static final int UPDATE_STANDBY = 63;
    /**
     * 更新网络状态-连接成功
     */
    public static final int UPDATE_NET_CONNECTED = 64;
    /**
     * 更新网络状态-断开网络
     */
    public static final int UPDATE_NET_DISCONNECT = 65;
    /**
     * 首页命令
     * 切换到本地音乐界面
     */
    public static final int HOME_GOTO_LOCALMUSCI = 223;
    /**
     * 切换到qq音乐界面
     */
    public static final int HOME_GOTO_QQMUSIC = 224;
    /**
     * 切换到qq音乐我喜欢界面
     */
    public static final int HOME_GOTO_QQMUSICLIKE = 225;
    /**
     * 设置界面命令
     * 语音开关
     */
    public static final int SETTING_SPEECH_RECOGNITION = 302;
    /**
     * 微信消息设置界面
     */
    public static final int SETTING_MESSAGE_PAGE = 303;
    /**
     * 切换指定模块界面
     */
    public static final int SETTING_GOTO_PAGE = 304;

    /**
     * 关于界面命令
     * 切换指定模块界面
     */
    public static final int HELP_GOTO_PAGE = 501;
    /**
     * 更新设置页面
     */
    public static final int SETTING_HAS_NEW = 502;

    /**
     * 通知栏通知ID
     * launcher升级的通知栏id
     */
    public static final int LAUNCHER_ID = 100;
    /**
     * 车机升级的通知栏id
     */
    public static final int CAR_ID = 99;
    /**
     * 华为mate8的设备号
     */
    public static final String ADAPTER_PHONE_HUAWEI_NXTAL10 = "HUAWEI NXT-AL10";
    /**
     * OPPO R9s
     */
    public static final String ADAPTER_PHONE_OPPO_R9S = "OPPO R9s";
    /**
     * OPPO R9tm
     */
    public static final String ADAPTER_PHONE_OPPO_R9TM = "OPPO R9tm";
    /**
     * OPPO R9s Plus
     */
    public static final String ADAPTER_PHONE_OPPO_R9PLUS = "OPPO R9s Plus";
    /**
     * OPPO R11s Plus
     */
    public static final String ADAPTER_PHONE_OPPO_R11PLUS = "OPPO R11s Plus";
    /**
     * htc HTC D826t
     */
    public static final String ADAPTER_PHONE_HTC = "htc HTC D826t";
    /**
     * 判断是否是适配手机标志位
     */
    private static final String IS_ADAPTER_PHONE_STATE = "isAdapterPhone";
    /**
     * 手机端与车机端通信
     */
    public static final int PCM_MARK = 0x55AA;
    /**
     * 手机向车机端发送开始的命令号
     */
    public static final int COMMAND_TO_CAR_START = 10;
    /**
     * 手机向车机端发送id3信息
     */
    public static final int COMMAND_TO_CAR_ID_3 = 11;
    /**
     * 手机刷新 ID3 数据。
     */
    public static final int COMMAND_REFRESH_ID_3 = 12;
    /**
     * 车机向手机发送开始的命令号
     */
    public static final int COMMAND_TO_PHONE_START = 40010;
    /**
     * 车机向手机发送结束的命令号
     */
    public static final int COMMAND_TO_PHONE_ENDS = 40020;
    /**
     * 音源标识导航
     */
    public static final int SOUND_NAV = 0;
    /**
     * 音源标识各种音乐归为一类本地和qq都算
     */
    public static final int SOUND_MUSIC = 1;
    /**
     * 音源标识微信
     */
    public static final int SOUND_WECHAT = 2;
    /**
     * 音源标识天气预报
     */
    public static final int SOUND_WEATHER = 3;
    /**
     * 音源标识语音助手提示语
     */
    public static final int SOUND_VOICE_ASSISTANT = 4;
    /**
     * 音源标识新闻
     */
    public static final int SOUND_NEWS = 5;
    /**
     * 音源标识后台语音助手提示音
     */
    public static final int SOUND_BACKGROUND_VOICE_ASSISTANT = 6;
    /**
     * 第一个包
     */
    public static final int SOUND_MARK_START = 1;
    /**
     * 中间包
     */
    public static final int SOUND_MARK_CENTER = 0;
    /**
     * 最后包
     */
    public static final int SOUND_MARK_END = 2;
    /**
     * 新闻首页对应的指令
     */
    public static final int OPENNEWSRECEIVE = 24;
    /**
     * 语音打开对应喜马拉雅对应id的播放界面
     */
    public static final int OPENXMLY = 209;
    /**
     * 动态权限申请码
     * 必要权限申请码
     */
    public static final int PERMISSION_MUST_REQ = 101;
    /**
     * 动态权限申请码
     * 拨打电话申请码
     */
    public static final int PERMISSION_CALL_REQ = 103;
    /**
     * 动态权限申请码
     * 位置权限申请码
     */
    public static final int PERMISSION_LOCATION_REQ = 105;
    /**
     * 动态权限申请码
     * 通话记录权限申请码
     */
    public static final int PERMISSION_CALL_LOG_REQ = 106;
    /**
     * 动态权限申请码
     * 联系人权限申请码
     */
    public static final int PERMISSION_CONTENCTS_REQ = 107;

    public static final String MESSAGE_INCOMING_CALL = "incomingCall";
    public static final String MESSAGE_ENDING_CALL = "endingCall";
    public static final String XIAOMI_PHONE = "xiaomi";
    public static final String A7_SN = "a7_sn";
    //车机SN码A7
    public static final String HU_SN_A7 = "123445";
    /*
     * 支持无感互联 车机码
     **/
    public static final String[] HU_SN_BLE = new String[]{"123445", "HPASIPLH83LNCSRATLAS7PCM093"};
}
