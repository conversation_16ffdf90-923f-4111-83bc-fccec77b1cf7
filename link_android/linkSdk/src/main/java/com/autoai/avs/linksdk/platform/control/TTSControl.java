package com.autoai.avs.linksdk.platform.control;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.welink.auto.WLSound;

/**
 * Welink 三投三方TTS功能接口，主要提供TTS实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class TTSControl {
    private final WLSound mWLSound;

    public TTSControl(WLSound mWLSound) {
        this.mWLSound = mWLSound;
    }

    /**
     * 播报文本资源
     */
    public void playText(String mark, byte[] pcm, int rate, int bit, int channel, WLSound.Callback callback) {
        WlLinkSdkLog.i("TTSControl playText -------------->rate:" + rate + ",bit:" + bit + ",channel:" + channel + ",mark:" + mark + ",pcm length: " + ((pcm == null) ? " null " : pcm.length + ""));
        if (mWLSound == null) {
            return;
        }
        mWLSound.play(mark, pcm, rate, bit, channel, callback);
    }
}
