package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端蓝牙电话状态向手机端launcher发送的协议
 * @param btPhoneState 类型
 *                    1：开始蓝牙电话
 *                    2：结束蓝牙电话
 */

public class HuBtPhoneBean extends BaseProtocolBean{

    private int btPhoneState;

    public int getBtPhoneState() {
        return btPhoneState;
    }

    @Override
    public String toString() {
        return "HuBtPhoneBean{" +
                "btPhoneState=" + btPhoneState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.btPhoneState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_BTPHONE);
    }


}
