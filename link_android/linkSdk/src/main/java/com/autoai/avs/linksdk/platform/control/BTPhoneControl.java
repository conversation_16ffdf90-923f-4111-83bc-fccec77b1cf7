package com.autoai.avs.linksdk.platform.control;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.welink.auto.WLBluetoothPhone;

/**
 * Welink 三投三方BTPhone功能接口，主要提供BTPhone实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class BTPhoneControl {

    private final WLBluetoothPhone mWLBluetoothPhone;

    public BTPhoneControl(WLBluetoothPhone mWLBluetoothPhone) {
        this.mWLBluetoothPhone = mWLBluetoothPhone;
    }

    /**
     * 方法名称：startMicrophone
     * 方法描述：拨打车机蓝牙电话
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:18
     */


    public void dialBTPhone(String number, WLBluetoothPhone.Callback callback) {
        WlLinkSdkLog.i("BTPhoneControl dialBTPhone ------->");
        if (mWLBluetoothPhone == null) {
            return;
        }
        mWLBluetoothPhone.dial(number, callback);
    }

    /**
     * 方法名称：hangUpBTPhone
     * 方法描述：挂断车机蓝牙电话
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/5/9 13:47
     */


    public void hangUpBTPhone() {
        WlLinkSdkLog.i("BTPhoneControl hangUpBTPhone ------->");
        if (mWLBluetoothPhone == null) {
            return;
        }
        mWLBluetoothPhone.hangUp();
    }

    /**
     * 方法名称：isSupportBTPhone
     * 方法描述：判断车机是否支持蓝牙电话
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/5/9 13:24
     */

    public boolean isSupportBTPhone() {
        WlLinkSdkLog.i("BTPhoneControl isSupportBTPhone ------->");
        return mWLBluetoothPhone != null;
    }
}
