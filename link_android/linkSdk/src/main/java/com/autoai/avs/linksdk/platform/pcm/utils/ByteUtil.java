package com.autoai.avs.linksdk.platform.pcm.utils;


import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.pcm.PcmConfigs;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;

public class ByteUtil {

    /**
     * @return 解密两个字节的byte数组
     */
    public static int byte2ArToInt(byte[] b) {
        return (b[0] & 0xFF) |
                (b[1] & 0xFF) << 8;
    }

    /**
     * 把int转化为两个字节byte数组
     *
     * @param a 需要转换的int
     * @return 转换结果
     */
    public static byte[] intTo2Byte(int a) {
        return new byte[]{
                (byte) (a & 0xFF),
                (byte) ((a >> 8) & 0xFF)
        };
    }

    public static int byteArrayToInt(byte[] b) {
        return b[0] & 0xFF |
                (b[1] & 0xFF) << 8 |
                (b[2] & 0xFF) << 16 |
                (b[3] & 0xFF) << 24;
    }


    /**
     * int 转为4位的byte数组
     *
     * @param a 要转换的int
     * @return 转换后的byte数组
     */
    public static byte[] intToByteArray(int a) {
        return new byte[]{
                (byte) (a & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 24) & 0xFF)
        };
    }

    /**
     * int转byte
     *
     * @param x 需要转换的int
     * @return 转换后的byte
     */
    public static byte intToByte(int x) {
        return (byte) x;
    }

    /**
     * byte转int
     *
     * @param b 要转换的byte
     * @return 转换后的int
     */
    public static int byteToInt(byte b) {
        //Java 总是把 byte 当做有符处理；我们可以通过将其和 0xFF 进行二进制与得到它的无符值
        return b & 0xFF;
    }

    /**
     * @param musicMark  音源标识 0-255
     * @param commandNum 命令号  10,40010,40020
     * @param byteLen    音频数据长度
     * @param spare      备用字段
     * @param spare1     备用字段
     * @param spare2     备用字段 时间戳 先按照0走，这个是备用字段
     * @return 返回16位各种参数包头byte数组
     */
    public static byte[] getPackHeadInfo(int musicMark, int commandNum
            , int byteLen, int spare, int spare1, int spare2) {

        byte[] packHeadInfo = new byte[16];
        byte[] _markByte = intTo2Byte(PcmConfigs.PCM_MARK);//返回标识的数组byte数组
        packHeadInfo[0] = _markByte[0];
        packHeadInfo[1] = _markByte[1];

        byte[] commandNumByte = intTo2Byte(commandNum);//命令号byte数组
        packHeadInfo[2] = commandNumByte[0];
        packHeadInfo[3] = commandNumByte[1];

        byte musicMarkByte = intToByte(musicMark);//音源标识byte数组
        packHeadInfo[4] = musicMarkByte;

        byte spareByte = intToByte(spare);//备用字段byte数组
        packHeadInfo[5] = spareByte;

        byte[] spare1Byte = intTo2Byte(spare1);//备用字段byte数组
        packHeadInfo[6] = spare1Byte[0];
        packHeadInfo[7] = spare1Byte[1];

        byte[] currentTimeByte = intToByteArray(spare2);//时间戳byte数组
        System.arraycopy(currentTimeByte, 0, packHeadInfo, 8, currentTimeByte.length);

        byte[] byteLenByte = intToByteArray(byteLen);//音频数据长度byte数组
        System.arraycopy(byteLenByte, 0, packHeadInfo, 12, byteLenByte.length);
        return packHeadInfo;
    }

    /**
     * @param rate         采样率
     * @param channel      声道数  1单声道, 2 双声道
     * @param bit          采样位数 8,16 8位, 16 位
     * @param playMark     播放标识 0-1,0是顺序播放，1是立即播放
     * @param audioNumMark 音频序列标识  0、1、2、3  默认填写3
     * @return 返回封装8位采样byte数组
     */
    public static byte[] getPlayPCMParamsByte(int rate, int channel, int bit, int playMark, int audioNumMark) {

        byte[] pcmParamsByte = new byte[8];
        pcmParamsByte[0] = intToByte(playMark);//播放标识byte数组
        pcmParamsByte[1] = intToByte(audioNumMark);// 音频序列标识byte数组


        byte channelByte = intToByte(channel);//声道byte数组
        pcmParamsByte[2] = channelByte;
        byte bitByte = intToByte(bit);//采样位数byte数组
        pcmParamsByte[3] = bitByte;

        byte[] rateByte = intToByteArray(rate);//采样率byte数组
        System.arraycopy(rateByte, 0, pcmParamsByte, 4, rateByte.length);

        return pcmParamsByte;
    }

    /**
     * @param packageData
     * @return 把传过来的封装好的包根据已有协议进行解包
     */
    public static byte[] getUnpackingData(byte[] packageData) {
        byte[] newDatas = null;
        try {
            if (packageData == null || packageData.length <= 0) {
                WlLinkSdkLog.d("pcm getCommandNumByBytes()====接收到车机信息为空！");
                return newDatas;
            }
            if (!isMarkRight(packageData)) {//判断标记
                return newDatas;
            }
            int musicMark = byteToInt(packageData[4]); //音源标识
            int spare = byteToInt(packageData[5]); //第一个备用字段;
            byte[] temptype = new byte[2];
            temptype[0] = packageData[6];
            temptype[1] = packageData[7];
            int spare1 = byte2ArToInt(temptype); //第二个备用字段;

            byte[] temp_timeByte = new byte[4];
            for (int i = 0; i < temp_timeByte.length; i++) {
                temp_timeByte[i] = packageData[8 + i];
            }
            int times_spare = byteArrayToInt(temp_timeByte);//时间备用字段


            byte[] temp_bytelen = new byte[4];
            for (int i = 0; i < temp_bytelen.length; i++) {
                temp_bytelen[i] = packageData[12 + i];
            }
            int bytelen = byteArrayToInt(temp_bytelen);//包的数据长度

            WlLinkSdkLog.d("pcm 音源标识:" + musicMark + ",spare:" + spare + ",spare1:" + spare1 + ""
                    + ",times_spare:" + times_spare + ",bytelen:" + bytelen);

            int playMark = byteToInt(packageData[16]);//播放标识
            int muiscMark = byteToInt(packageData[17]);//音频标识

            int channl = byteToInt(packageData[18]); //声道;
            int bit_music = byteToInt(packageData[19]); //bit;

            byte[] temp_Rate = new byte[4];
            for (int i = 0; i < temp_Rate.length; i++) {
                temp_Rate[i] = packageData[20 + i];
            }
            int rate = byteArrayToInt(temp_Rate);//包的数据长度
            if (bytelen > 8) {
                newDatas = new byte[bytelen - 8];
                for (int i = 0; i < newDatas.length; i++) {
                    newDatas[i] = packageData[24 + i];
                }
                WlLinkSdkLog.d("pcm playMark:" + playMark + ",muiscMark:" + muiscMark + ",channl:" + channl + ","
                        + "bit_music:" + bit_music + ",rate:" + rate + "pcm数据的长度:" + newDatas.length + ",bytelen:" + bytelen);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return newDatas;
    }


    /**
     * 根据车机发过来数据包来判断当前是那个命令号
     *
     * @param datas 车机发送的消息
     * @return 0说明收到车机的包是空的或者解析错误
     */
    public static int getCommandNumByBytes(byte[] datas) {
        int commandNum = 0;
        try {
            if (datas == null || datas.length <= 0) {
                WlLinkSdkLog.d("pcm getCommandNumByBytes()====接收到车机信息为空！");
                return commandNum;
            }
            if (!isMarkRight(datas)) {
                return commandNum;
            }
            byte[] tempType = new byte[2];
            tempType[0] = datas[2];
            tempType[1] = datas[3];
            commandNum = byte2ArToInt(tempType);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return commandNum;
    }

    /**
     * 根据车机发过来数据包来判断当前是那个音源
     *
     * @param datas 车机发送的消息
     * @return int[]  int[0] 音源  int[1]命令号，开始或者结束
     */
    public static int[] getCommandSource(byte[] datas) {
        int[] intArr = new int[2];
        try {
            if (datas == null || datas.length <= 0) {
                return null;
            }
            if (!isMarkRight(datas)) {
                return null;
            }
            byte sourceByte = datas[4];
            intArr[0] = byteToInt(sourceByte);

            byte[] tempType = new byte[2];
            tempType[0] = datas[2];
            tempType[1] = datas[3];
            intArr[1] = byte2ArToInt(tempType);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return intArr;
    }

    /**
     * 判断标识是否正确
     *
     * @param datas 返回的信息
     * @return 返回识别码是否正确
     */
    public static boolean isMarkRight(byte[] datas) {
        boolean isRight = false;
        try {
            byte[] markTempType = new byte[2];
            markTempType[0] = datas[0];
            markTempType[1] = datas[1];
            int _markValue = byte2ArToInt(markTempType);
            if (PcmConfigs.PCM_MARK != _markValue) {
                WlLinkSdkLog.d("byteUtil 识别标识符不一致！");
                return isRight;
            }
            isRight = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isRight;
    }

    /**
     * 根据车机发过来数据包来，来判断当前的音源标识
     *
     * @param datas 车机发送的消息
     * @return -1说明当前解析错误或者是空包
     */
    public static int getMusicMarkByBytes(byte[] datas) {
        int musicMark = -1;
        try {
            if (datas == null || datas.length <= 0) {
                WlLinkSdkLog.d("pcm getCommandNumByBytes()====接收到车机信息为空！");
                return musicMark;
            }
            if (!isMarkRight(datas)) {
                return musicMark;
            }
            byte[] tempType = new byte[1];
            tempType[0] = datas[4];
            musicMark = byteToInt(tempType[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return musicMark;
    }

    /**
     * 返回向车机发送封装pcm数据
     *
     * @param data         PCM数据
     * @param rate         采样率
     * @param bit          位数
     * @param channel      声道数
     * @param musicMark    音源标识  1是qq音乐，0是导航
     * @param audioNumMark 音源序列
     * @return 返回向车机发送封装pcm数据
     */
    public static byte[] getPackageDataByParam(byte[] data, int rate, int bit, int channel, int musicMark, int audioNumMark) {

        byte[] newData = null;
        try {
            int byteLen = data.length;//pcm数据长度
            //Long now = System.currentTimeMillis();
            byte[] packHeadInfo = ByteUtil.getPackHeadInfo(musicMark, PcmConfigs.COMMAND_TO_CAR_START, byteLen + 8,
                    0, 0, 0);
            byte[] PcmParamsByte = ByteUtil.getPlayPCMParamsByte(rate, channel, bit, 0, audioNumMark);
            if (byteLen <= 0) {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
//				for (int i = 0; i < newData.length; i++) {
//					if (i <= 15) {
//						newData[i] = packHeadInfo[i];
//					} else {
//						newData[i] = PcmParamsByte[i - 16];
//					}
//				}

            } else {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length + byteLen;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
                System.arraycopy(data, 0, newData, 24, data.length);
//				for (int i = 0; i < newData.length; i++) {
//					if (i <= 15) {
//						newData[i] = packHeadInfo[i];
//					} else if (i >= 16 && i <= 23) {
//						newData[i] = PcmParamsByte[i - 16];
//					} else {
//						newData[i] = data[i - 24];
//					}
//				}
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return newData;
    }

    /**
     * @param command ID3命令号
     * @param Data    ID3数据
     *                return 返回向车机发送封装id3数据
     */
    public static byte[] getid3DataByParam(int command, byte[] Data, byte[] bmpAlbum) {

        byte[] newData = null;
        int id3Len = 0;
        int bmpAlbumLen = 0;
        if (Data != null && Data.length != 0)
            id3Len = Data.length;
        if (bmpAlbum != null && bmpAlbum.length != 0)
            bmpAlbumLen = bmpAlbum.length;
        try {
            int bytelen = id3Len + bmpAlbumLen;//pcm数据长度
            //Long now = System.currentTimeMillis();
            byte[] packHeadInfo = ByteUtil.getPackHeadInfo(0, command, bytelen + 8,
                    0, 0, 0);
            byte[] PcmParamsByte = ByteUtil.getID3PCMParamsByte(id3Len, bmpAlbumLen);
            if (bytelen <= 0) {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
//				for (int i = 0; i < newData.length; i++) {
//					if (i <= 15) {
//						newData[i] = packHeadInfo[i];
//					} else {
//						newData[i] = PcmParamsByte[i - 16];
//					}
//				}

            } else {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length + bytelen;
                int bumpAlbumStart = 24 + Data.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
                System.arraycopy(Data, 0, newData, 24, Data.length);
                System.arraycopy(bmpAlbum, 0, newData, bumpAlbumStart, bmpAlbum.length);
//				for (int i = 0; i < newData.length; i++) {
//					if (i <= 15) {
//						newData[i] = packHeadInfo[i];
//					} else if (i <= 23) {
//						newData[i] = PcmParamsByte[i - 16];
//					} else {
//						newData[i] = data[i - 24];
//					}
//				}
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return newData;
    }

    /**
     * @param command ID3命令号
     * @param Data    ID3数据
     *                return 返回向车机发送封装id3数据
     */
    public static byte[] getid3ProDataByParam(int command, byte[] Data) {

        byte[] newData = null;
        int id3Len = 0;
        int bmpAlbumLen = 0;
        if (Data != null && Data.length != 0)
            id3Len = Data.length;
        try {
            int bytelen = id3Len + bmpAlbumLen;//pcm数据长度
            //Long now = System.currentTimeMillis();
            byte[] packHeadInfo = ByteUtil.getPackHeadInfo(0, command, bytelen + 8,
                    0, 0, 0);
            byte[] PcmParamsByte = ByteUtil.getID3PCMParamsByte(id3Len, bmpAlbumLen);
            if (bytelen <= 0) {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
            } else {
                int newDataLength = packHeadInfo.length + PcmParamsByte.length + bytelen;
                newData = new byte[newDataLength];
                System.arraycopy(packHeadInfo, 0, newData, 0, packHeadInfo.length);
                System.arraycopy(PcmParamsByte, 0, newData, 16, PcmParamsByte.length);
                System.arraycopy(Data, 0, newData, 24, Data.length);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return newData;
    }

    private final static char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    /**
     * 将byte数组转换为16进制的字符串
     *
     * @param data 数组
     * @return 字符串
     */
    public static String bytesToHex(byte[] data) {
        char[] hexChars = new char[data.length * 2];
        for (int j = 0; j < data.length; j++) {
            int v = data[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * 将byte数组转换为16进制的字符串
     *
     * @param data 数组
     * @return 字符串
     */
    public static String printHexBinary(byte[] data) {
        StringBuilder r = new StringBuilder(data.length * 2);
        for (byte b : data) {
            r.append(HEX_ARRAY[(b >> 4) & 0xF]);
            r.append(HEX_ARRAY[(b & 0xF)]);
            r.append(",");
        }
        return r.toString();
    }


    /**
     * 写音乐PCM文件
     *
     * @param content
     * @param append
     */
    public static void writeFile(byte[] content, boolean append, String SongID) {
        String recordpath = PcmConfigs.PcmMusicUrl + "test" + SongID + ".pcm";
        File fd = new File(PcmConfigs.PcmMusicUrl);
        if (!fd.exists()) {
            try {
                fd.mkdirs();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        File f = new File(recordpath);
        RandomAccessFile raf = null;
        try {
            if (f.exists()) {
                if (!append) {
                    f.delete();
                    f.createNewFile();
                }
            } else {
                f.createNewFile();
            }
            if (f.canWrite()) {
                raf = new RandomAccessFile(f, "rw");
                raf.seek(raf.length());
                raf.write(content);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (raf != null) {
                try {
                    raf.close();
                } catch (IOException var1) {
                    var1.printStackTrace();
                }
            }
        }
    }

    /**
     * @param id3Len   id3数据长度
     * @param albumLen 专辑图数据长度
     * @return 返回封装8位采样byte数组
     */
    public static byte[] getID3PCMParamsByte(int id3Len, int albumLen) {

        byte[] pcmParamsByte = new byte[8];

        byte[] id3ParamsByte = intToByteArray(id3Len);//id3 byte数组
        System.arraycopy(id3ParamsByte, 0, pcmParamsByte, 0, id3ParamsByte.length);

        byte[] albumParamsByte = intToByteArray(albumLen);//专辑图byte数组
        System.arraycopy(albumParamsByte, 0, pcmParamsByte, 4, albumParamsByte.length);

        return pcmParamsByte;
    }
}
