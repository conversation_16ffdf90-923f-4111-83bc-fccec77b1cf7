package com.autoai.avs.linksdk.platform.audio;

import android.media.AudioFormat;
import android.media.AudioTrack;
import android.media.MediaFormat;

import androidx.annotation.Keep;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.WLPlatformConfig;
import com.autoai.avs.linksdk.platform.audio.bean.WLAudioType;
import com.autoai.avs.linksdk.platform.audio.bean.WLDeviceType;
import com.autoai.avs.linksdk.platform.audio.listener.IWLAudioManager;
import com.autoai.avs.linksdk.platform.audio.listener.TransportListener;
import com.autoai.avs.linksdk.platform.audio.listener.WLAudioListener;
import com.autoai.avs.linksdk.platform.audio.track.HUAudioTrack;
import com.autoai.avs.linksdk.platform.audio.track.MUAudioTrack;
import com.autoai.avs.linksdk.platform.audio.transport.MusicTransport;
import com.autoai.avs.linksdk.platform.audio.transport.TTSTransport;
import com.autoai.avs.linksdk.platform.audio.util.Utils;
import com.autoai.avs.linksdk.platform.observer.PlatformObserver;
import com.autoai.avs.linksdk.platform.observer.PlatformObserverable;
import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;
import com.autoai.avs.linksdk.platform.protocol.WLProtocolMannger;


/**
 * Welink PCM音频播报处理类，主要负责手机和车机的音频播报处理。
 *
 * <AUTHOR>
 */
@Keep
public class WLAudioManager implements IWLAudioManager, TransportListener, PlatformObserver {
    private int audioType;
    private WLAudioListener mListener;
    private boolean isConnectcar = false;

    private HUAudioTrack mHUAudioTrack;
    private MUAudioTrack mMUAudioTrack;

    private MusicTransport mMusicTransport;
    private TTSTransport mTTSTransport;

    private int rate = 0;
    private int bit = 0;
    private int channels = 0;
    private int channelType = 0;
    private long duration = 0;

    private long totalPosition = 0;
    private long playPosition = 0;

    private int isThrowIndex = 0;
    private static final int isThrowCount = 1;

    @Keep
    public WLAudioManager() {
        WlLinkSdkLog.i("WLAudioManager  WLAudioManager  -------->");
        this.isConnectcar = WLPlatformConfig.isConnectcar;
        PlatformObserverable.getInstance().attachObserver(this);
    }

    /**
     * 方法名称：notifyObserver
     * 方法描述：监听平台广播消息通知
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/05/29 16:13
     */

    @Override
    public void notifyObserver(int state, Object obj) {
        if (WLPlatformConfig.WL_PLATFORM_OBSERVER_CONNECT_STAET == state) {
            boolean flag = (Boolean) obj;
            WlLinkSdkLog.i("WLAudioManager notifyObserver -------->isConnectcar:" + flag);
            if (mMusicTransport != null) {
                mMusicTransport.setConnectCar(flag);
            }

            if (mTTSTransport != null) {
                mTTSTransport.setConnectCar(flag);
            }

            if (mMUAudioTrack != null) {
                mMUAudioTrack.updatePlayDevice(flag, playPosition);
            }

            if (mHUAudioTrack != null) {
                mHUAudioTrack.updatePlayDevice(flag, playPosition);
            }

            this.isConnectcar = flag;
        }
    }

    /**
     * 方法名称：initAudioTrack
     * 方法描述：依据播放参数初始化
     * 方法参数：rate 采样频率（采样率），bit 采样位数（采样精度），channels 声道数量（声道）,duration 数据播放时长-单位毫秒
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public AudioTrack initAudioTrack(int rate, int bit, int channels, long duration) {
        WlLinkSdkLog.i("WLAudioManager initAudioTrack --------> rate:" + rate + ",bit:" + bit + ",channels:" + channels + ",duration:" + duration);

        this.rate = rate;
        this.bit = bit;
        this.channels = channels;
        this.duration = duration;

        switch (channels) {
            case AudioFormat.CHANNEL_OUT_MONO:
                this.channels = 1;
                break;
            case AudioFormat.CHANNEL_OUT_STEREO:
                this.channels = 2;
                break;
        }

        if (this.channels == 1) {
            this.channelType = AudioFormat.CHANNEL_OUT_MONO;
        } else {
            this.channelType = AudioFormat.CHANNEL_IN_STEREO;
        }

        return resetAudioTrack();
    }

    /**
     * 方法名称：initAudioTrack
     * 方法描述：依据播放格式初始化
     * 方法参数：format PCM数据格式
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     * 备注：该方法不再使用，后期废弃
     */

    @Keep
    @Override
    public AudioTrack initAudioTrack(MediaFormat format) {
        WlLinkSdkLog.i("WLAudioManager initAudioTrack --------> format:" + format.toString());

        try {
            this.bit = 16;
            this.rate = format.getInteger(MediaFormat.KEY_SAMPLE_RATE);
            this.channels = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT);
            this.duration = format.getLong(MediaFormat.KEY_DURATION) / 1000;
//            this.bit = format.getInteger(MediaFormat.KEY_BIT_RATE);
        } catch (Exception e) {
            WlLinkSdkLog.e("WLAudioManager initAudioTrack --------> exception:" + e.getMessage());
        }

        switch (this.channels) {
            case AudioFormat.CHANNEL_OUT_MONO:
                this.channels = 1;
                break;
            case AudioFormat.CHANNEL_OUT_STEREO:
                this.channels = 2;
                break;
        }

        if (this.channels == 1) {
            this.channelType = AudioFormat.CHANNEL_OUT_MONO;
        } else {
            this.channelType = AudioFormat.CHANNEL_IN_STEREO;
        }

        return resetAudioTrack();
    }

    /**
     * 方法名称：resetAudioTrack
     * 方法描述：重置PCM播放状态
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public AudioTrack resetAudioTrack() {
        WlLinkSdkLog.i("WLAudioManager resetAudioTrack --------> rate:" + rate + ",bit:" + bit + ",channels:" + channels + ",duration:" + duration + ",channelType:" + channelType);

        if (mMusicTransport != null) {
            mMusicTransport.stopTransportThread();
            mMusicTransport = null;
        }

        if (mTTSTransport != null) {
            mTTSTransport.stopTransportThread();
            mTTSTransport = null;
        }

        if (mMUAudioTrack != null) {
            mMUAudioTrack.release();
            mMUAudioTrack = null;
        }

        if (mHUAudioTrack != null) {
            mHUAudioTrack.release();
            mHUAudioTrack = null;
        }

        this.playPosition = 0;
        this.totalPosition = rate * channels * bit * duration / 8 / 1000L;

        this.mMUAudioTrack = new MUAudioTrack();
        this.mMUAudioTrack.setAudioParams(rate, bit, channelType, totalPosition);
        this.mMUAudioTrack.setPlayStateListener(audioType, mListener);

        this.mHUAudioTrack = new HUAudioTrack();
        this.mHUAudioTrack.setAudioParams(rate, bit, channels, totalPosition);
        this.mHUAudioTrack.setPlayStateListener(audioType, mListener);

        return mMUAudioTrack.getAudioTrack();
    }

    /**
     * 方法名称：setPlayStateListener
     * 方法描述：设置音源类型和回调监听
     * 方法参数：audioType 播放声音类型，mListener 播放回调处理对象
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void setPlayStateListener(int audioType, WLAudioListener mListener) {
        WlLinkSdkLog.i("WLAudioManager setPlayStateListener --------> audioType:" + audioType);

        this.audioType = audioType;
        this.mListener = mListener;

        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.setPlayStateListener(audioType, mListener);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.setPlayStateListener(audioType, mListener);
        }
    }

    /**
     * 方法名称：setPlayProgress
     * 方法描述：设置音乐播放的进度
     * 方法参数：progress 播放进度 范围 0-100
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/28 15:49
     */

    @Keep
    @Override
    public void setPlayProgress(double progress) {
        this.playPosition = (long) (progress / 100.0 * totalPosition);
        WlLinkSdkLog.i("WLAudioManager setPlayProgress --------> progress:" + progress + ",playPosition:" + playPosition + ",totalPosition:" + totalPosition);

        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.setPlayPosition(playPosition);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.setPlayPosition(playPosition);
        }
    }

    /**
     * 方法名称：start
     * 方法描述：开始更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void start() {
        WlLinkSdkLog.d("WLAudioManager start --------> playPosition:" + playPosition + ",totalPosition:" + totalPosition);

        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.start(isConnectcar);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.start(isConnectcar);
        }
        if (WLAudioType.MUSIC_TAG == audioType) {
            //发送开始音乐播放协议并添加到缓存消息中
            WLProtocolMannger.getInstance().sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, Utils.getCarMediaState(1), true);
            this.isThrowIndex = 0;
            if (mMusicTransport != null) {
                mMusicTransport.stopTransportThread();
                mMusicTransport = null;
            }
            mMusicTransport = new MusicTransport(this, rate, bit, channels);
            mMusicTransport.startTransportThread();
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.stopTransportThread();
                mTTSTransport = null;
            }
            mTTSTransport = new TTSTransport(this, rate, bit, channels);
            mTTSTransport.startTransportThread();
        }
    }

    /**
     * 方法名称：pause
     * 方法描述：暂定更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/15 15:49
     */

    @Keep
    @Override
    public void pause() {
        WlLinkSdkLog.d("WLAudioManager pause --------> ");

        if (WLAudioType.MUSIC_TAG == audioType) {
            //发送停止音乐播放协议并添加到缓存消息中
            WLProtocolMannger.getInstance().sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, Utils.getCarMediaState(2), true);
            if (mMusicTransport != null) {
                mMusicTransport.pauseTransportThread();
            }
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.pauseTransportThread();
            }
        }
        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.pause(isConnectcar);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.pause(isConnectcar);
        }
    }

    /**
     * 方法名称：resume
     * 方法描述：继续更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/6/15 15:49
     */

    @Keep
    @Override
    public void resume() {
        WlLinkSdkLog.d("WLAudioManager resume --------> ");

        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.resume(isConnectcar);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.resume(isConnectcar);
        }

        if (WLAudioType.MUSIC_TAG == audioType) {
            //发送开始音乐播放协议并添加到缓存消息中
            WLProtocolMannger.getInstance().sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, Utils.getCarMediaState(1), true);
            if (mMusicTransport != null) {
                mMusicTransport.resumeTransportThread();
            }
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.resumeTransportThread();
            }
        }
    }

    /**
     * 方法名称：stop
     * 方法描述：停止播放
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void stop() {
        WlLinkSdkLog.i("WLAudioManager stop --------> ");

        if (WLAudioType.MUSIC_TAG == audioType) {
            //发送停止音乐播放协议并添加到缓存消息中
            WLProtocolMannger.getInstance().sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, Utils.getCarMediaState(2), true);
            if (mMusicTransport != null) {
                mMusicTransport.stopTransportThread();
                mMusicTransport = null;
            }
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.stopTransportThread();
                mTTSTransport = null;
            }
        }
        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.stop(isConnectcar);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.stop(isConnectcar);
        }
    }

    /**
     * 方法名称：finish
     * 方法描述：结束更新数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void finish() {
        WlLinkSdkLog.d("WLAudioManager finish --------> ");

        if (WLAudioType.MUSIC_TAG == audioType) {
            if (mMusicTransport != null) {
                mMusicTransport.finishTransport();
            }
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.finishTransport();
            }
        }
        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.finish(isConnectcar);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.finish(isConnectcar);
        }
    }

    /**
     * 方法名称：update
     * 方法描述：更新数据中
     * 方法参数：bytes PCM数据
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void update(byte[] bytes) {
        WlLinkSdkLog.i("WLAudioManager update -------->isConnectcar:" + isConnectcar + ",length:" + bytes.length);
        if (WLAudioType.MUSIC_TAG == audioType) {
            //避免切换音乐播放下一首时结束到解码上一首的数据
            if (isThrowIndex < isThrowCount) {
                if (mMUAudioTrack != null) {
                    this.mMUAudioTrack.setPlaySize(bytes.length);
                }
                if (mHUAudioTrack != null) {
                    this.mHUAudioTrack.setPlaySize(bytes.length);
                }
                isThrowIndex++;
                WlLinkSdkLog.i("WLAudioManager update ----throw data---->isThrowIndex:" + isThrowIndex);
                return;
            }

            if (mMusicTransport != null) {
                mMusicTransport.producePcm(bytes);
            }
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.producePcm(bytes);
            }
        }
    }

    /**
     * 方法名称：onMusicData
     * 方法描述：经过分包处理之后返回的音乐音频数据
     * 方法参数：bytes PCM数据
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Override
    public void onMusicData(byte[] data, boolean isLast) {
        this.playPosition += data.length;
        WlLinkSdkLog.i("WLAudioManager onMusicData --------> playPosition:" + playPosition + ",data size:" + data.length + ",isConnectcar:" + isConnectcar + ",isLast:" + isLast);

        if (isConnectcar) {
            if (WLAudioType.MUSIC_TAG == audioType && mHUAudioTrack != null) {
                mHUAudioTrack.write(data, isLast);
            }
        } else {
            if (WLAudioType.MUSIC_TAG == audioType && mMUAudioTrack != null) {
                mMUAudioTrack.write(data, isLast);
            }
        }
    }

    /**
     * 方法名称：onTTSData
     * 方法描述：经过分包处理之后返回的tts音频数据
     * 方法参数：bytes PCM数据
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Override
    public void onTTSData(byte[] data, boolean isLast) {
        this.playPosition += data.length;
        WlLinkSdkLog.i("WLAudioManager onTTSData --------> playPosition:" + playPosition + ",data size:" + data.length + ",isConnectcar:" + isConnectcar + ",isLast:" + isLast);

        if (isConnectcar) {
            if (WLAudioType.MUSIC_TAG != audioType && mHUAudioTrack != null) {
                mHUAudioTrack.write(data, isLast);
            }
        } else {
            if (WLAudioType.MUSIC_TAG != audioType && mMUAudioTrack != null) {
                mMUAudioTrack.write(data, isLast);
            }
        }
    }

    /**
     * 方法名称：clear
     * 方法描述：清空播放数据
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void clear() {
        WlLinkSdkLog.i("WLAudioManager clear --------> ");
        if (WLAudioType.MUSIC_TAG == audioType) {
            if (mMusicTransport != null) {
                mMusicTransport.stopTransportThread();
                mMusicTransport = null;
            }
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.stopTransportThread();
                mTTSTransport = null;
            }
        }
        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.clear();
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.clear();
        }
    }

    /**
     * 方法名称：error
     * 方法描述：解码错误回调
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2021/8/9 9:49
     */

    @Override
    public void error(int errorCode) {
        WlLinkSdkLog.i("WLAudioManager error --------> errorCode:" + errorCode);
        if (WLAudioType.MUSIC_TAG == audioType) {
            //发送停止音乐播放协议并添加到缓存消息中
            WLProtocolMannger.getInstance().sendCommondMessage(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, Utils.getCarMediaState(2), true);
        }
    }

    /**
     * 方法名称：release
     * 方法描述：释放播放对象
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void release() {
        WlLinkSdkLog.i("WLAudioManager release --------> ");

        if (WLAudioType.MUSIC_TAG == audioType) {
            if (mMusicTransport != null) {
                mMusicTransport.stopTransportThread();
                mMusicTransport = null;
            }
        } else {
            if (mTTSTransport != null) {
                mTTSTransport.stopTransportThread();
                mTTSTransport = null;
            }
        }
        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.release();
            this.mMUAudioTrack = null;
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.release();
            this.mHUAudioTrack = null;
        }
        PlatformObserverable.getInstance().detachObserver(this);
    }

    /**
     * 方法名称：setVolume
     * 方法描述：设置音量
     * 方法参数：gain 音量值
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public void setVolume(float gain) {
        WlLinkSdkLog.i("WLAudioManager setVolume --------> gain:" + gain);

        if (mMUAudioTrack != null) {
            this.mMUAudioTrack.setVolume(gain);
        }
        if (mHUAudioTrack != null) {
            this.mHUAudioTrack.setVolume(gain);
        }
    }

    /**
     * 方法名称：getPlayDevice
     * 方法描述：获取播放设备类型
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/1/13 15:49
     */

    @Keep
    @Override
    public WLDeviceType getPlayDevice() {
        WlLinkSdkLog.i("WLAudioManager getPlayDevice --------> isConnectCar:" + isConnectcar);

        if (isConnectcar) {
            return WLDeviceType.CAR;
        } else {
            return WLDeviceType.PHONE;
        }
    }


}
