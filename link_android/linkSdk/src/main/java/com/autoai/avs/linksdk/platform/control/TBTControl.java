package com.autoai.avs.linksdk.platform.control;

import com.autoai.avs.linksdk.WlLinkSdkLog;
import com.autoai.avs.linksdk.platform.bean.TBTBean;
import com.autoai.welink.auto.WLTBTInfo;

/**
 * Welink 三投三方TBT功能接口，主要提供TBT实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class TBTControl {
    private final WLTBTInfo mWLTBTInfo;

    public TBTControl(WLTBTInfo mWLTBTInfo) {
        this.mWLTBTInfo = mWLTBTInfo;
    }

    /**
     * 启动TBT
     */
    public void startWLTBT() {
        WlLinkSdkLog.i("TBTControl startWLTBT -------------->");
        if (mWLTBTInfo == null) {
            return;
        }

        mWLTBTInfo.startTBT();
    }

    /**
     * 关闭TBT
     */
    public void stopWLTBT() {
        WlLinkSdkLog.i("TBTControl stopWLTBT -------------->");
        if (mWLTBTInfo == null) {
            return;
        }

        mWLTBTInfo.stopTBT();
    }


    /**
     * 更新导航路口信息
     */
    public void updataWLTBT(TBTBean mTBTBean) {
        WlLinkSdkLog.i("TBTControl updataWLTBT -------------->mTBTBean:" + mTBTBean.toString());
        if (mWLTBTInfo == null) {
            return;
        }

        mWLTBTInfo.updateTBT(mTBTBean.getCurrentRoadName(), mTBTBean.getRoadName(), mTBTBean.getRoadDistance(),
                mTBTBean.getRoadTurnIcon(), mTBTBean.getRemainDistance(), mTBTBean.getRemainTime());
    }
}
