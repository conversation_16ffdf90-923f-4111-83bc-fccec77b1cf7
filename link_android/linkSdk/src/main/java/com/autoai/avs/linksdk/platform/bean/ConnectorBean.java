package com.autoai.avs.linksdk.platform.bean;

/**
 * Welink 三投三方app信息实体bean，主要封装三方app需要的参数信息。
 *
 * <AUTHOR>
 */
public class ConnectorBean {

    private String packageName;
    private String connectKey;
    private int cap;

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getConnectKey() {
        return connectKey;
    }

    public void setConnectKey(String connectKey) {
        this.connectKey = connectKey;
    }

    public int getCap() {
        return cap;
    }

    public void setCap(int cap) {
        this.cap = cap;
    }

    @Override
    public String toString() {
        return "ConnectorBean{" +
                "packageName='" + packageName + '\'' +
                ", connectKey='" + connectKey + '\'' +
                ", cap=" + cap +
                '}';
    }
}
