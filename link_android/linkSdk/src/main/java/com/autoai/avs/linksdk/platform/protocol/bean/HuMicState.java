package com.autoai.avs.linksdk.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 车机端通知手机端当前申请的车机端 mic 资源状态。
 * 车机端获取车机 mic 状态 类型 boolean true 获取成功 ； false 获取失败
 */
public class HuMicState extends BaseProtocolBean{

    private boolean huMicState;

    public boolean isHuMicState() {
        return huMicState;
    }

    public void setHuMicState(boolean huMicState) {
        this.huMicState = huMicState;
    }

    @Override
    public String toString() {
        return "HuMicState{" +
                "huMicState=" + huMicState +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.huMicState = extData.getBoolean("huMicState");
    }


}
