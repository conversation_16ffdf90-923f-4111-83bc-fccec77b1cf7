package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端发送氛围灯类型向手机端 launcher发的协议。
 * @param vehicle_info 车型代码
 * @param part_no 车机型号
 * @param color_type 氛围灯色值类型
 *                  1：使用RGB色值
 *                  2：使用select色值
 * @param color_r  r色值
 * @param color_g  g色值
 * @param color_b  b色值
 * @param color_select_value  自定义色值
 */

public class HuLampBean extends BaseProtocolBean{

    private int vehicleInfo;
    private String partNo;
    private int colorType;
    private int colorR;
    private int colorG;
    private int colorB;
    private int colorSelectValue;

    public int getVehicleInfo() {
        return vehicleInfo;
    }

    public String getPartNo() {
        return partNo;
    }

    public int getColorType() {
        return colorType;
    }

    public int getColorR() {
        return colorR;
    }

    public int getColorG() {
        return colorG;
    }

    public int getColorB() {
        return colorB;
    }

    public int getColorSelectValue() {
        return colorSelectValue;
    }

    @Override
    public String toString() {
        return "HuLampBean{" +
                "vehicleInfo=" + vehicleInfo +
                ", partNo='" + partNo + '\'' +
                ", colorType=" + colorType +
                ", colorR=" + colorR +
                ", colorG=" + colorG +
                ", colorB=" + colorB +
                ", colorSelectValue=" + colorSelectValue +
                ", methodName="+getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.vehicleInfo = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_VEHICLE);
        this.partNo = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_PARTNO);
        this.colorType = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORTYPE);

        if(extData.has(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORRGB)){
            JSONObject rgb = extData.getJSONObject(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORRGB);
            this.colorR = rgb.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORR);
            this.colorG = rgb.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORG);
            this.colorB = rgb.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORB);
        }

        this.colorSelectValue = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORSELECT);
    }


}
