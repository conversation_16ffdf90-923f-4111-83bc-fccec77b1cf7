package com.autoai.avs.linksdk.platform.protocol.bean;


import com.autoai.avs.linksdk.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端消息中心 控制手机端 launcher是否同步更新微信消息状态 。
 * @param isSwitch  类型
 *                  true 开始传输
 *                  false 结束传输
 */

public class MXMsgSwitchBean extends BaseProtocolBean{

    private boolean isSwitch;

    public boolean isSwitch() {
        return isSwitch;
    }

    @Override
    public String toString() {
        return "MXMsgSwitchBean{" +
                "isSwitch=" + isSwitch +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.isSwitch = extData.getBoolean(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_WXMSGSWITCH);
    }


}
