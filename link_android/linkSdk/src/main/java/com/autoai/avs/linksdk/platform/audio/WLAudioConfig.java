package com.autoai.avs.linksdk.platform.audio;

import androidx.annotation.Keep;

/**
 * Welink PCM音频播报常量类，主要定义音频播报模块涉及到的常量。
 *
 * <AUTHOR>
 */
@Keep
public class WLAudioConfig {
    private WLAudioConfig() {}
    @Keep
    public static final int WL_AUDIOMANAGER_ERROR_CODE_INTERRUPT = 100;
    @Keep
    public static final int WL_AUDIOMANAGER_ERROR_CODE_REJECT = 101;
    @Keep
    public static final int WL_AUDIOMANAGER_ERROR_CODE_DISABLEFOCUS = 102;
    @Keep
    public static final int WL_AUDIOMANAGER_ERROR_CODE_TRACK_WIRITE = 103;
    @Keep
    public static final String WL_AUDIOMANAGER_ERROR_PARAM_PLAYTIME = "playTime";
    @Keep
    public static final String WL_AUDIOMANAGER_ERROR_PARAM_TOTALTIME = "totalTime";


}
