plugins {
    id 'com.android.library'
}
apply from: "${project.rootDir}/library.gradle"
apply from: "${project.rootDir}/maven_push.gradle"

dependencies {
    implementation "com.autoai.link.threadpool:threadpool:${project.myVersions.threadpool}"
    implementation "com.autoai.link.baselog:baselog:${project.myVersions.baselog}"
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation 'com.autoai.welink.lib:security:1.0.1'
    implementation 'com.lambdapioneer.argon2kt:argon2kt:1.1.0'

    // 仅 messageBus 内部引用, 后续一并移除
    implementation 'io.reactivex.rxjava2:rxjava:2.1.6'

    implementation project(":wlplatform")
    implementation project(":wlconnector")
    implementation project(":wlhardwarehub")
    implementation project(":wlserver")
    implementation project(":wlchannel")
    implementation project(":wlscreen")
}