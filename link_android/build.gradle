// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    apply from: 'versions.gradle'
    repositories {
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
        maven { url "https://wdnexus.autoai.com/content/repositories/snapshots/" }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        mavenCentral()
        google()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:7.4.2"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
        maven { url "https://wdnexus.autoai.com/content/repositories/snapshots/" }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        google()
        mavenCentral()
    }
}
apply from: "${project.rootDir}/config.gradle"

task clean(type: Delete) {
    delete rootProject.buildDir
}