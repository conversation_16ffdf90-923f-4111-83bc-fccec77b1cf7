rm *.aar

if [ "$1" = "release" ];then
    echo "release!!!"
    mv ../../../../WeLink-Auto/WeLinkServer/wlserver/build/outputs/aar/wlserver-release.aar wlserver.aar
    mv ../../../../welink.screen/WeLinkPCM/wlpcm/build/outputs/aar/wlpcm-release.aar wlpcm.aar
else
    echo "debug!!!"
    mv ../../../../WeLink-Auto/WeLinkServer/wlserver/build/outputs/aar/wlserver-debug.aar wlserver.aar
    mv ../../../../welink.screen/WeLinkPCM/wlpcm/build/outputs/aar/wlpcm-debug.aar wlpcm.aar
fi

cp ../../../../WeLink-Auto/WeLinkServer/wlserver/libs/wlconnector.aar wlconnector.aar
