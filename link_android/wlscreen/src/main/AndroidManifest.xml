<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.autoai.welink.screen" >
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
	<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
	<uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW"/>
	<uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
	<uses-permission android:name="android.permission.CHANGE_CONFIGURATION"/>
	<application>
		<service
			android:name="com.autoai.welink.screen.AutoClickService"
			android:enabled="true"
			android:exported="true"
			android:label="@string/accessible_title"
			android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
			<intent-filter>
				<action android:name="android.accessibilityservice.AccessibilityService" />
			</intent-filter>
			<!-- 配置服务服务配置文件路径-->
			<meta-data
				android:name="android.accessibilityservice"
				android:resource="@xml/accessible_service_config" />
		</service>
		<service
			android:name=".ScreenService"
			android:enabled="true"
			android:exported="true"
			android:foregroundServiceType="mediaProjection"/>
	</application>
</manifest>