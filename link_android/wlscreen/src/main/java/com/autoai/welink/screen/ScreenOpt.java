package com.autoai.welink.screen;

import static android.app.Activity.RESULT_OK;

import android.app.Activity;
import android.app.Notification;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.media.projection.MediaProjectionConfig;
import android.media.projection.MediaProjectionManager;
import android.net.Uri;
import android.os.Build;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;
import android.view.Surface;

public class ScreenOpt {
    static void log(String text){
        WLScreen.log(text);
    }
    Activity activity;
    private MediaProjectionManager mediaProjectionManager;
    boolean enableScreenHorizontal;

    public ScreenOpt(Activity act, boolean enableScreenHorizontal,Notification notification) {
        activity = act;
        mNotification=notification;
        this.enableScreenHorizontal=enableScreenHorizontal;
        if (enableScreenHorizontal){
            try {
                initScreenHorizontal();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 开始全局录屏
     * @param width 截屏区域宽
     * @param  height 截屏区域高
     * @param  density 截屏dpi
     */
    public void start(int width, int height, int density) {
        log("fullscreen start 1");
        ScreenService.start(width,height,density);
        runServiceCMD("start");
        mediaProjectionManager = (MediaProjectionManager) activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        Intent permissionIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= 34) {
            permissionIntent = mediaProjectionManager.createScreenCaptureIntent(MediaProjectionConfig.createConfigForDefaultDisplay());
        }else{
            permissionIntent = mediaProjectionManager.createScreenCaptureIntent();
        }
        activity.startActivityForResult(permissionIntent, WLScreen.SCREEN_CAPTURE_REQUEST_CODE);
        log("fullscreen start 2");
    }

    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        log("onActivityResult "+requestCode+","+resultCode);
        if(requestCode == WLScreen.SCREEN_CAPTURE_REQUEST_CODE) {
            if(resultCode == RESULT_OK) {
                Intent intent = new Intent(activity, ScreenService.class);
                intent.setAction("activityResult");
                intent.putExtra("resultCode", resultCode);
                intent.putExtra("data", data);
                log("onActivityResult Agree");
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ) {
                    log("startForegroundService");
                    activity.startForegroundService(intent);
                }else {
                    log("startService");
                    activity.startService(intent);
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 设置录屏输出目标
     * @param surface 录屏目标
     * @param width 截屏区域宽
     * @param  height 截屏区域高
     * @param  density 截屏dpi
     */
    public void setSurface(Surface surface,int width, int height, int density) {
        ScreenService.setSurface(surface,width,height,density);
        runServiceCMD("setSurface");
    }

    /**
     * 停止全局录屏
     */
    public void stop() {
        runServiceCMD("stop");
    }

    void runServiceCMD(String opt){
//        ScreenService.setNotification(prepareNotification());
        ScreenService.setNotification(mNotification);
        Intent intent = new Intent(activity, ScreenService.class);
        intent.setAction(opt);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ) {
            activity.startForegroundService(intent);
        }else {
            activity.startService(intent);
        }
    }

    //=========================================================
    //强制横竖屏功能
    private ServiceConnection mServiceConnection;
    ScreenService.MyBinder mBinder;
    ScreenService.Callback mCallback;

    void initScreenHorizontal() {
        mServiceConnection = new ServiceConnection(){
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                mBinder = (ScreenService.MyBinder)service;
                if(mCallback != null) {
                    mBinder.getService().addCallback(mCallback);
                }
                log("LocalService is connected");
            }

            @Override
            public void onServiceDisconnected(ComponentName name) {
                log("LocalService is disconnected");
            }
        };

        runServiceCMD("ScreenHorizontal");//只是启动服务,不需要执行什么动作
        Intent bindIntent = new Intent(activity, ScreenService.class);
        activity.bindService(bindIntent, mServiceConnection, Context.BIND_AUTO_CREATE);

//        requestPermissions();
    }

    public void release(Activity atc) {
        log("release mServiceConnection="+mServiceConnection);
        if (mServiceConnection!=null) {
            atc.unbindService(mServiceConnection);
        }
    }

    private ScreenService.MyBinder getBinder(){
        return mBinder;
    }

    public void isSwitchToHorizontal (boolean isSwitchToHorizontal) {
        if (!enableScreenHorizontal) {
            return;
        }
        if (isSwitchToHorizontal) {
            if(getBinder().getService().getStatus()){
                log("Service already running!");
                return;
            }
            Log.d("wlserver", "isSwitchToHorizontal: " +canDrawOverlayViews());
            if(canDrawOverlayViews()) {
                getBinder().getService().startHorizontalScreen();
            }
        }
        else {

//            if(!getBinder().getService().getStatus()){
//                Log.d("wlserver", "isSwitchToHorizontal22 : " +canDrawOverlayViews());
//                if(canDrawOverlayViews()) {
//                    getBinder().getService().startHorizontalScreen();
//                } else {
//                    return;
//                }
//            }

            getBinder().getService().stopHorizontalScreen();
        }
    }

    private void requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            requestPermissionsNew();
        }else{
            requestPermissionsOld();
        }
    }

    private void requestPermissionsOld(){
//        ActivityCompat.requestPermissions(activity, new String[] { "android.permission.SYSTEM_ALERT_WINDOW" }, 123);
//        ActivityCompat.requestPermissions(activity, new String[] { "android.permission.WRITE_SETTINGS" }, 456);
    }

    private void requestPermissionsNew(){
        requestAlertWindowPermission();
        requestWriteSettings();
    }

    private void requestAlertWindowPermission() {
        if(!Settings.System.canWrite(activity)) {
            Intent intent = new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION");
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            activity.startActivityForResult(intent, 1);
        }
    }

    private void requestWriteSettings() {
        if(!Settings.System.canWrite(activity)){
            Intent intent = new Intent("android.settings.action.MANAGE_WRITE_SETTINGS");
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            activity.startActivityForResult(intent, 2);
        }
    }

    private boolean canDrawOverlayViews() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            return true;
        }
        try {
            return Settings.canDrawOverlays(activity);
        } catch (NoSuchMethodError noSuchMethodError) {
            noSuchMethodError.printStackTrace();
            return true;
        }
    }

    Notification mNotification;
//    private Notification prepareNotification() {
//        if (mNotification ==null) {
//            PendingIntent pendingIntent = PendingIntent.getActivity(activity, 0, new Intent(activity, activity.getClass()), 0);
//            Notification.Builder builder = new Notification.Builder(activity).setContentIntent(pendingIntent).setSmallIcon(R.mipmap.ic_launcher).setTicker("started").setWhen(System.currentTimeMillis())
//                    .setContentTitle("welink前台服务").setContentText("welink正在运行(全录屏/强制横竖屏)");
//            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
//                NotificationChannel notificationChannel = new NotificationChannel("com.autoai.welink.screen", "welink_fullscreen", NotificationManager.IMPORTANCE_MIN);
//                NotificationManager manager = (NotificationManager) activity.getSystemService(NOTIFICATION_SERVICE);
//                manager.createNotificationChannel(notificationChannel);
//                builder.setChannelId("com.autoai.welink.screen");
//            }
//            mNotification = builder.build();
//        }
//        return mNotification;
//    }
}
