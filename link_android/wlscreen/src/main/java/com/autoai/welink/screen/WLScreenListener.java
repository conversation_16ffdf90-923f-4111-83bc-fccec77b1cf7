package com.autoai.welink.screen;

public interface WLScreenListener {
    /*
     * 手机前台运行的APP包名通知（仅在全录屏运行时提供通知,hid模式需要用户手动打开使用情况统计权限,非hid模式需打开辅助功能）
     * @param packageName 当前运行的APP包名
     */
    void currentAppPackageName(String packageName);

    /**
     * 屏幕角度通知（仅在全录屏运行时提供通知）
     * 折叠屏展开折叠通知（通过ScreenService获取宽高）
     * @param rotation 当前屏幕角度, 值定义为Surface.ROTATION_0等
     * @param width 当前屏幕宽度
     * @param height 当前屏幕高度
     */
    void onRotation(int rotation, int width, int height);

    /**
     * 断开互联后，台持有的录屏持有对象失效回调
     */
    void onStopScreen();
}
