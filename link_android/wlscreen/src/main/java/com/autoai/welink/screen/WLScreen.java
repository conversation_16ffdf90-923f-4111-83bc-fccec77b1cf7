package com.autoai.welink.screen;

import android.app.Activity;
import android.app.Notification;
import android.content.Intent;
import android.util.Log;
import android.view.MotionEvent;
import android.view.Surface;

public class WLScreen {
    static void log(String text) {
        AnFileLog.e("fatal_screen", text);
        //if(BuildConfig.DEBUG)Log.e("fatal_screen",text);
    }

    public static final int SCREEN_CAPTURE_REQUEST_CODE = 5065;
    ScreenOpt screenOpt = null;

    /**
     * 获取功能库版本号
     *
     * @return 返回功能库版本号字符串
     */
    public static String getVersion() {
        log("WLScreen- getVersion() " + BuildConfig.VERSION_NAME + ":" + BuildConfig.VERSION_CODE);
        return BuildConfig.VERSION_NAME + ":" + BuildConfig.VERSION_CODE;
    }

    /**
     * 辅助功能权限是否已开启
     *
     * @return 返回辅助功能权限是否开启
     */
    public static boolean isAccessibilitySettingsOn(Activity act) {
        return AutoClickService.isAccessibilitySettingsOn(act);
    }

    /**
     * 全局录屏对象构建
     *
     * @param act                    当前Activity
     * @param listener               回调对象,用于通知当前前台app的包名和横竖屏状态
     * @param enableScreenHorizontal 是否开启强制横竖屏功能(需要额外权限支持)
     * @param notification           全录屏服务需要关联的前台通知
     */
    public WLScreen(Activity act, WLScreenListener listener, boolean enableScreenHorizontal, Notification notification) {
        AnFileLog.init(act, "/Test/welinklog/wlscreen-log.txt");
        log("WLScreen- WLScreen() " + act + ",+ " + listener + ",enableScreenHorizontal=" + enableScreenHorizontal + "," + notification);
        log("WLScreen- version:" + BuildConfig.VERSION_NAME + ":" + BuildConfig.VERSION_CODE);
        screenOpt = new ScreenOpt(act, enableScreenHorizontal, notification);
        AutoClickService.setListener(listener);
        ScreenService.setListener(listener);
    }

    /**
     * 全局录屏对象释放
     *
     * @param atc 当前Activity
     */
    public void release(Activity atc) {
        log("WLScreen- release() " + atc + "," + screenOpt);
        if (null != screenOpt) {
            screenOpt.release(atc);
        }
    }

    /**
     * 开始全局录屏
     *
     * @param width   截屏区域宽
     * @param height  截屏区域高
     * @param density 截屏dpi
     */
    public void start(int width, int height, int density) {
        log("WLScreen- start() " + width + ",+ " + height + ",density=" + density);
        screenOpt.start(width, height, density);
    }

    /**
     * 弹出框返回结果 onActivityResult
     *
     * @return 只在是全录屏的权限结果, 且用户同意了全录屏后, 才会返回true
     */
    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        log("WLScreen- onActivityResult() " + requestCode + ", " + resultCode + ",data=" + data);
        return screenOpt.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 设置录屏输出目标
     *
     * @param surface 录屏目标
     * @param width   截屏区域宽
     * @param height  截屏区域高
     * @param density 截屏dpi
     */
    public void setSurface(Surface surface, int width, int height, int density) {
        log("WLScreen- setSurface() " + surface + "," + width + "," + height + ",density" + density);
        screenOpt.setSurface(surface, width, height, density);
    }

    /**
     * 停止全局录屏
     */
    public void stop() {
        log("WLScreen- stop() ");
        screenOpt.stop();
    }

    /**
     * 设置屏幕横屏, 如果初始化时指明不需要
     *
     * @param isSwitchToHorizontal 设置是否横屏，true 横屏，false 竖屏
     */
    public void setIsSwitchToHorizontal(boolean isSwitchToHorizontal) {
        log("WLScreen- setIsSwitchToHorizontal() " + isSwitchToHorizontal);
        Log.d("wlserver", "setIsSwitchToHorizontal: " + isSwitchToHorizontal);
        if (null != screenOpt) {
            screenOpt.isSwitchToHorizontal(isSwitchToHorizontal);
        }
    }

    /**
     * 模拟硬按键操作(仅非HID模式且辅助服务打开时可工作)
     *
     * @param action 可以设置类似AccessibilityService.GLOBAL_ACTION_BACK
     */
    static public void performAction(int action) {
        log("WLScreen- performAction() " + action);
        AutoClickService.performAction(action);
    }

    /**
     * 发送Touch事件,触发全局回控(仅非HID模式且辅助服务打开时可工作)
     *
     * @param motionEvent
     */
    public void touch(MotionEvent motionEvent) {
        log("WLScreen- touch() " + motionEvent);
        AutoClickService.touch(motionEvent);
    }

    /**
     * 设置log输出与否,配置项同时影响文件log和logcat输出
     * 其中文件log需要事先开启写文件权限且手机上存在Test目录,log文件名为wlscreen.txt
     *
     * @param enable true:输出文件log和logcat,false:不输出log
     */
    public static void setLogMode(boolean enable) {
        AnFileLog.enableLogCat(enable);
        AnFileLog.enableLogFile(enable);
    }

    /**
     * 设置是否启用HID模式
     *
     * @param enable true:启用HID模式,false:禁用HID模式(使用辅助服务来实现全局回控)
     */
    public static void setHidMode(boolean enable) {
        AutoClickService.enableHID = enable;
    }
}
