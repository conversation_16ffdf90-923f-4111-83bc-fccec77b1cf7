package com.autoai.welink.screen;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.accessibilityservice.GestureDescription;
import android.content.ComponentName;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.Path;
import android.graphics.Point;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.Display;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;

import java.lang.ref.WeakReference;

/**
 * @description 自动点击AccessibilityService版
 */
public class AutoClickService extends AccessibilityService {
    static void log(String text) {
        WLScreen.log(text);
    }

    private GestureDetector gestureDetector;
    private GestureDetector.SimpleOnGestureListener listener;
    static public boolean enableHID = false;
    static public boolean accessibilityRun = false;
    static WLScreenListener screenListener;
    static AutoClickService autoClickService;

    static public void setListener(WLScreenListener listener) {
        screenListener = listener;
    }

    private Point screenSize = null;

    public boolean isTouchInScreen(MotionEvent motionEvent) {
        if (motionEvent.getRawX() < 0) {
            log("!!!! getRawX=" + motionEvent.getRawX());
            return false;
        }
        if (motionEvent.getRawY() < 0) {
            log("!!!! getRawY=" + motionEvent.getRawY());
            return false;
        }
        if (motionEvent.getX() < 0) {
            log("!!!! getX=" + motionEvent.getX());
            return false;
        }
        if (motionEvent.getY() < 0) {
            log("!!!! getY=" + motionEvent.getY());
            return false;
        }
        //是否正数超过边界,需要考虑横竖屏的情况, 即使越界了,手势识别也不会异常,可以不加
        return true;
    }

    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        log("onServiceConnected 111");

        //Configure these here for compatibility with API 13 and below.
        AccessibilityServiceInfo config = new AccessibilityServiceInfo();
        config.eventTypes = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED;
        config.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC;

        if (Build.VERSION.SDK_INT >= 16)
        //Just in case this helps
        {
            config.flags = AccessibilityServiceInfo.FLAG_INCLUDE_NOT_IMPORTANT_VIEWS;
        }

        setServiceInfo(config);
        log("onServiceConnected 222");
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        if (null == screenListener) {
            return;
        }
//		log( "AccessibilityService "+this+" onAccessibilityEvent " + event.toString());
        if (event.getEventType() == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            if (event.getPackageName() != null && event.getClassName() != null) {
                ComponentName componentName = new ComponentName(event.getPackageName().toString(), event.getClassName().toString());
                ActivityInfo activityInfo = tryGetActivity(componentName);
                if (activityInfo != null) {
                    log("onAccessibilityEvent screenListener=" + screenListener + ",componentName=" + componentName + ",currentAPPPackageName=" + event.getPackageName() + ",activityInfo=" + activityInfo);
                    CharSequence cs = event.getPackageName();
                    if (cs != null) {
                        screenListener.currentAppPackageName(cs.toString());
                    }
                }
            }
        }
    }

    private ActivityInfo tryGetActivity(ComponentName componentName) {
        try {
            return getPackageManager().getActivityInfo(componentName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            return null;
        }
    }

    // To check if service is enabled
    static public boolean isAccessibilitySettingsOn(Context mContext) {
        int accessibilityEnabled = 0;
        final String service = mContext.getPackageName() + "/" + AutoClickService.class.getCanonicalName();
        try {
            accessibilityEnabled = Settings.Secure.getInt(mContext.getApplicationContext().getContentResolver(), android.provider.Settings.Secure.ACCESSIBILITY_ENABLED);
        } catch (Settings.SettingNotFoundException e) {

        }
        TextUtils.SimpleStringSplitter mStringColonSplitter = new TextUtils.SimpleStringSplitter(':');

        if (accessibilityEnabled == 1) {
            String settingValue = Settings.Secure.getString(mContext.getApplicationContext().getContentResolver(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
            if (settingValue != null) {
                mStringColonSplitter.setString(settingValue);
                while (mStringColonSplitter.hasNext()) {
                    String accessibilityService = mStringColonSplitter.next();
                    if (accessibilityService.equalsIgnoreCase(service)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    protected boolean onGesture(int gestureId) {
        return super.onGesture(gestureId);
    }

    @Override
    public void onInterrupt() {
    }

    /**
     * 点击指定位置
     * 注意7.0以上的手机才有此方法，请确保运行在7.0手机上
     */
    public void dispatchGestureClick(GestureDescription.StrokeDescription strokeDescription) {
        dispatchGesture(new GestureDescription.Builder().addStroke(strokeDescription).build(), null, null);
    }

    //=========================================================
    MovitonEventHandle movitonEventHandle = new MovitonEventHandle(AutoClickService.this);

    private static class MovitonEventHandle extends Handler {
        private WeakReference<AutoClickService> reference;
        private long lastErrorTime = 0;

        public MovitonEventHandle(AutoClickService test1) {
            reference = new WeakReference<>(test1);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            int what = msg.what;
            switch (what) {
                case 1:

                    if (reference.get() != null) {
                        GestureDescription.StrokeDescription strokeDescription = (GestureDescription.StrokeDescription) msg.obj;
                        //华为P20异常,在此处会耗时很久,最终超时不执行,而且频繁这样执行会造成系统服务异常,考虑如何减少异常的出现,先加个log看看是否能触发这里
                        long cur1 = System.currentTimeMillis();
                        log("!AutoClickSrv dispatchGestureClick1");
                        reference.get().dispatchGestureClick(strokeDescription);
                        log("!AutoClickSrv dispatchGestureClick2");
                        long cur2 = System.currentTimeMillis();
                        if (cur2 - cur1 > 200) {
                            lastErrorTime = cur2;
                            log("!!got P20 dispatchGestureClick error timeout =" + (cur2 - cur1));
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 发送Touch事件
     *
     * @param motionEvent
     */
    static public void touch(MotionEvent motionEvent) {
        log("!AutoClickSrv in autoClickService=" + autoClickService + ",enableHID=" + enableHID + "," + motionEvent.toString());
        if (enableHID) {
            return;
        }
        if (autoClickService != null) {
            if (!autoClickService.isTouchInScreen(motionEvent)) {
                return;
            }
            try {
                autoClickService.gestureDetector.onTouchEvent(motionEvent);
            } catch (Exception e) {
                log("gestureDetector.onTouchEvent(motionEvent) error:" + e.toString());
            }
        }
    }

    //模拟按键操作
    static public void performAction(int action) {
        log("performAction autoClickService=" + autoClickService + ",enableHID=" + enableHID + "," + action);
        if (enableHID) {
            return;
        }
        if (autoClickService == null) {
            return;
        }
        autoClickService.performGlobalAction(action);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        autoClickService = this;
        accessibilityRun = true;
        screenSize = new Point();
        Display display = ((WindowManager) getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
        display.getRealSize(screenSize);

        log("AutoClickService onCreate accessibilityRun=" + accessibilityRun + ",screenListener=" + screenListener);
        listener = new GestureDetector.SimpleOnGestureListener() {
            @Override
            /*
             *每按一下屏幕立即触发
             * */
            public boolean onDown(MotionEvent e) {
                log("!AutoClickService onDown " + e);
                if (e == null) {
                    return super.onDown(e);
                }
                Path path = new Path();
                path.moveTo(e.getRawX(), e.getRawY());
                GestureDescription.StrokeDescription sd = new GestureDescription.StrokeDescription(path, 0, 100);
                Message msg = new Message();
                msg.what = 1;
                msg.obj = sd;
                autoClickService.movitonEventHandle.sendMessage(msg);
                return super.onDown(e);
            }

            @Override
            /*
             *用户按下屏幕并且没有移动或松开。主要是提供给用户一个可视化的反馈，告诉用户他们的按下操作已经
             * 被捕捉到了。如果按下的速度很快只会调用onDown(),按下的速度稍慢一点会先调用onDown()再调用onShowPress().
             * */
            public void onShowPress(MotionEvent e) {
                log("AutoClickService onShowPress " + e);
            }

            @Override
            /*
             *一次单纯的轻击抬手动作时触发
             * */
            public boolean onSingleTapUp(MotionEvent e) {
                log("AutoClickService onSingleTapUp " + e);
                return super.onSingleTapUp(e);
            }

            @Override
            /*
             * 长按。在down操作之后，过一个特定的时间触发
             * */
            public void onLongPress(MotionEvent e) {
                log("!AutoClickService onLongPress " + e);
                if (e == null) {
                    return;
                }
                Path path = new Path();
                path.moveTo(e.getRawX(), e.getRawY());
                GestureDescription.StrokeDescription sd = new GestureDescription.StrokeDescription(path, 0, 500);
                Message msg = new Message();
                msg.what = 1;
                msg.obj = sd;
                autoClickService.movitonEventHandle.sendMessage(msg);
            }

            @Override
            /*
             *屏幕拖动事件，如果按下的时间过长，调用了onLongPress，再拖动屏幕不会触发onScroll。拖动屏幕会多次触发
             * @param e1 开始拖动的第一次按下down操作,也就是第一个ACTION_DOWN
             * @parem e2 触发当前onScroll方法的ACTION_MOVE
             * @param distanceX 当前的x坐标与最后一次触发scroll方法的x坐标的差值。
             * @param diastancY 当前的y坐标与最后一次触发scroll方法的y坐标的差值。
             * */
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                log("!AutoClickService onScroll " + e1 + "," + e2);
                if (e1 == null || e2 == null) {
                    return super.onScroll(e1, e2, distanceX, distanceY);
                }
                Path path = new Path();
                path.moveTo(e1.getRawX(), e1.getRawY());
                path.lineTo(e2.getRawX(), e2.getRawY());
                GestureDescription.StrokeDescription sd = new GestureDescription.StrokeDescription(path, 0, 500);
                Message msg = new Message();
                msg.what = 1;
                msg.obj = sd;
                autoClickService.movitonEventHandle.sendMessage(msg);
                return super.onScroll(e1, e2, distanceX, distanceY);
            }

            @Override
            /*
             * 按下屏幕，在屏幕上快速滑动后松开，由一个down,多个move,一个up触发
             * @param e1 开始快速滑动的第一次按下down操作,也就是第一个ACTION_DOWN
             * @parem e2 触发当前onFling方法的move操作,也就是最后一个ACTION_MOVE
             * @param velocityX：X轴上的移动速度，像素/秒
             * @parram velocityY：Y轴上的移动速度，像素/秒
             * */
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                log("AutoClickService onFling " + "," + e1 + "," + e2);
                return super.onFling(e1, e2, velocityX, velocityY);
            }
        };

        gestureDetector = new GestureDetector(this, listener);
    }

    @Override
    public void onDestroy() {
        accessibilityRun = false;
        screenListener = null;
        movitonEventHandle = null;
        autoClickService = null;
        log("AutoClickService onDestroy accessibilityRun=" + accessibilityRun);
        super.onDestroy();
    }

}
