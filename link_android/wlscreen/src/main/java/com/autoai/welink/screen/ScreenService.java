package com.autoai.welink.screen;

import static android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION;

import android.Manifest;
import android.app.Notification;
import android.app.Service;
import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Point;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.provider.Settings;
import android.util.Log;
import android.view.Display;
import android.view.Surface;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

//集成了全录屏前台服务和原本的强制横竖屏服务
public class ScreenService extends Service {
    static void log(String text) {
        WLScreen.log(text);
    }

    final int NOTIFICATION_ID = 0x33456;
    /**
     * 录屏manager
     */
    private MediaProjectionManager mediaProjectionManager;
    /**
     * 录屏对象
     */
    private MediaProjection mMediaProjection;
    /**
     * 用于录屏的徐display
     */
    private VirtualDisplay mVirtualDisplay;
    /**
     * 给到录屏的display 的实际 surface，它同时也是编码器的input surface
     */
    static Surface virtualSurface = null;
    /**
     * 给到 {@link #mVirtualDisplay}的实际参数
     */
    static int vdWidth, vdHeight, vdDensity;
    static ScreenService screenService;
    /**
     * 前台服务的 Notification
     */
    static Notification mNotification;

    static public void setNotification(Notification notification) {
        mNotification = notification;
    }

    static public void setSurface(Surface surface, int width, int height, int density) {
        log("ScreenSrv setSurface " + width + "," + height + "," + density + "," + surface);
        vdWidth = width;
        vdHeight = height;
        vdDensity = density;
        virtualSurface = surface;
    }

    static public void start(int width, int height, int density) {
        vdWidth = width;
        vdHeight = height;
        vdDensity = density;
        log("ScreenSrv start " + width + "," + height + "," + density);
    }

    @Override
    public IBinder onBind(Intent intent) {
        log("ScreenSrv onBind");
        return myBinder;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        log("ScreenSrv onCreate");
        screenService = this;
        mediaProjectionManager = (MediaProjectionManager) getApplicationContext().getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        invokeCallback(Callback.STOP);
//        checkHandle.sendEmptyMessageDelayed(1, 100);

        //-->注册横竖屏切换
        registOrientationChangeReceiver();

    }


    /**
     * 监听横竖屏切换
     */
    public class OrientationChangeReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(Intent.ACTION_CONFIGURATION_CHANGED)) {
                Configuration newConfig = context.getResources().getConfiguration();
                if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    // 横屏
                    Log.i("shecw1", "OrientationChangeReceiver::ORIENTATION_LANDSCAPE");
                } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
                    // 竖屏
                    Log.i("shecw1", "OrientationChangeReceiver::ORIENTATION_PORTRAIT");
                }
                //保留之前逻辑
                checkHorizontalScreen();
            }
        }
    }

    OrientationChangeReceiver orientationChangeReceiver = null;

    public void registOrientationChangeReceiver() {
        orientationChangeReceiver = new OrientationChangeReceiver();
        IntentFilter filter = new IntentFilter(Intent.ACTION_CONFIGURATION_CHANGED);
        registerReceiver(orientationChangeReceiver, filter);
    }

    public void unregistOrientationChangeReceiver() {
        unregisterReceiver(orientationChangeReceiver);
    }

    @Override
    public void onDestroy() {
        log("ScreenSrv onDestroy");
        needRunStartForeground = true;
        screenService = null;
        screenListener = null;
//        checkHandle = null;
        unregistOrientationChangeReceiver();
        stopHorizontalScreen();
        clearCallback();
        super.onDestroy();
    }

    boolean needRunStartForeground = true;

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (needRunStartForeground) {
            if (mNotification != null) {
                startForeground(NOTIFICATION_ID, mNotification);
                needRunStartForeground = false;
                log("startForeground ok!");
            } else {
                log("!!!!error,mNotification=null!");
            }
        }
        if (intent != null) {
            String opt = intent.getAction();
            log("ScreenSrv onStartCommand " + opt + "," + vdWidth + "," + vdHeight + "," + vdDensity);
            if ("activityResult".equals(opt)) {
                int resultCode = intent.getIntExtra("resultCode", -1);
                Intent data = intent.getParcelableExtra("data");
                if (mediaProjectionManager != null) {
                    mMediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data);
                    mMediaProjection.registerCallback(callback, null);
                }
                log("mediaProjectionManager=" + mediaProjectionManager + ",mMediaProjection=" + mMediaProjection);
            } else if ("setSurface".equals(opt)) {
                //--->核心逻辑
                log("ScreenSrv onStartCommand " + opt + "," + mVirtualDisplay + "virtualSurface = " + virtualSurface + ",mMediaProjection:" + mMediaProjection);
                Log.i("shecw1", "ScreenService::onStartCommand action->setSurface  start vdWidth = " + vdWidth + "vdHeight = " + vdHeight);

                if (mVirtualDisplay == null) {
                    try {
                        //--->创建用于录屏的mVirtualDisplay
                        mVirtualDisplay = mMediaProjection.createVirtualDisplay("screenrecordservice", vdWidth, vdHeight, vdDensity,
                                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR, virtualSurface, null, null);
                    } catch (Exception e) {
                        log("ScreenSrv onStartCommand error:" + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    mVirtualDisplay.setSurface(virtualSurface);
                    mVirtualDisplay.resize(vdWidth, vdHeight, vdDensity);
                }

                Log.i("shecw1", "ScreenService::onStartCommand action->setSurface  end ");
            } else if ("start".equals(opt)) {
            } else if ("stop".equals(opt)) {
                if (mVirtualDisplay != null) {
                    mVirtualDisplay.release();
                    mVirtualDisplay = null;
                }
                if (mMediaProjection != null) {
                    mMediaProjection.unregisterCallback(callback);
                    mMediaProjection.stop();
                    mMediaProjection = null;
                }
            }
        }
        return super.onStartCommand(intent, flags, startId);
    }

    MediaProjection.Callback callback = new MediaProjection.Callback() {
        @Override
        public void onStop() {
            log("ScreenSrv onStop---------");
            //MediaProjection 已失效
            if (screenListener != null) {
                screenListener.onStopScreen();
            }
        }
    };

    @Override
    public boolean onUnbind(Intent intent) {
        log("ScreenSrv onUnbind");
        return super.onUnbind(intent);
    }

    @Override
    public void onRebind(Intent intent) {
        log("ScreenSrv onRebind");
        super.onRebind(intent);
    }

    //=========================================================
    //强制横竖屏功能代码
    private boolean mRunningState = false;
    private int rotationallow = 0;
    private int rotationval = 0;
    private WindowManager wm;
    private LinearLayout orientationChanger;
    private WindowManager.LayoutParams orientationLayout;
    private MyBinder myBinder = new MyBinder();

    interface Callback {
        int STOP = 0;
        int START = 1;

        void onServiceStateChg(int state);
    }

    public class MyBinder extends Binder {
        public ScreenService getService() {
            return ScreenService.this;
        }
    }

    boolean isFirstTime = true;

    public void prepareOverlayWindow() {
        if (!isFirstTime) {
            return;
        }
        isFirstTime = false;

        /* 准备Layout */
        this.orientationChanger = new LinearLayout(this);
//        Button button = new Button(this);
//        button.setText("横屏test");
//        this.orientationChanger.addView(button);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            this.orientationLayout = new WindowManager.LayoutParams(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY, 24, 1);
        } else {
            this.orientationLayout = new WindowManager.LayoutParams(WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY, 24, 1);
        }
        this.wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
    }

    public void startHorizontalScreen() {
        log("startHorizontalScreen");
        Log.d("wlserver", "startHorizontalScreen: ");
//        if (checkSelfPermission(Manifest.permission.WRITE_SETTINGS) != PackageManager.PERMISSION_GRANTED) {
//            return;
//        }

        Log.d("wlserver", "startHorizontalScreen:222 ");
        prepareOverlayWindow();
//        if (mRunningState) return;

        /* 保存系统默认的参数，用于结束时回复系统设置 */
        this.rotationval = Settings.System.getInt(getContentResolver(), "user_rotation", 0);
        this.rotationallow = Settings.System.getInt(getContentResolver(), "accelerometer_rotation", 1);
//        log( "old:user_rotation:" + rotationval);
//        log( "old:accelerometer_rotation:" + rotationallow);

        mRunningState = true;
        /* 将创建的layout添加到窗口 */
        orientationLayout.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
        this.wm.addView(this.orientationChanger, this.orientationLayout);
        this.orientationChanger.setVisibility(View.VISIBLE);

        /* 设置系统参数user_rotation，accelerometer_rotation，变为横屏 */
        Settings.System.putInt(getContentResolver(), Settings.System.USER_ROTATION, Surface.ROTATION_90);
        Settings.System.putInt(getContentResolver(), Settings.System.ACCELEROMETER_ROTATION, 0);

        invokeCallback(Callback.START);
    }

    public void stopHorizontalScreen() {
        log("stopHorizontalScreen");
        Log.d("wlserver", "stopHorizontalScreen: " + mRunningState);
//        if (!mRunningState) return;

        mRunningState = false;

        /* 恢复系统参数user_rotation，accelerometer_rotation，变为竖屏 */
        Settings.System.putInt(getContentResolver(), Settings.System.USER_ROTATION, this.rotationval);
        Settings.System.putInt(getContentResolver(), Settings.System.ACCELEROMETER_ROTATION, this.rotationallow);
//        Settings.System.putInt(getContentResolver(), Settings.System.USER_ROTATION, 0);
//        Settings.System.putInt(getContentResolver(), Settings.System.ACCELEROMETER_ROTATION, 0);
        /* 删除layout */
        this.wm.removeView(this.orientationChanger);
        this.orientationChanger.setVisibility(View.GONE);

        invokeCallback(Callback.STOP);
    }

    /* Ob */
    private ArrayList<Callback> mCallbacks = new ArrayList<>();

    public void addCallback(Callback callback) {
        mCallbacks.add(callback);
    }

    public void removeCallback(Callback callback) {
        mCallbacks.remove(callback);
    }

    public void clearCallback() {
        mCallbacks.clear();
    }

    private void invokeCallback(int state) {
        for (Callback cb : mCallbacks) {
            cb.onServiceStateChg(state);
        }
    }

    public boolean getStatus() {
        return mRunningState;
    }


    //=========================================================
    //屏幕方向通知, 和包名通知
    static WLScreenListener screenListener;
    boolean isFirstTimeCheckRotation = true;
    boolean isFirstTimeCheckTopApp = true;
    int igetRotation = Surface.ROTATION_0;
    String topAppName = "";
    //    CheckHandle checkHandle = new CheckHandle(this);
    int mPhonePointX, mPhonePointY;

//    private static class CheckHandle extends Handler {
//        private WeakReference<ScreenService> reference;
//
//        public CheckHandle(ScreenService test1) {
//            reference = new WeakReference<ScreenService>(test1);
//        }
//
//        @Override
//        public void handleMessage(Message msg) {
//            super.handleMessage(msg);
//            int what = msg.what;
//            switch (what) {
//                case 1:
//                    if (reference.get() != null) {
//                        reference.get().checkHorizontalScreen();
//                        if (AutoClickService.enableHID && !AutoClickService.accessibilityRun) {//非HID模式 && 辅助服务不工作时,就依靠使用情况统计
//                            reference.get().checkTopApp();
//                        }
//                        sendEmptyMessageDelayed(1, 100);
//                    }
//                    break;
//                default:
//                    break;
//            }
//        }
//    }

    static public void setListener(WLScreenListener listener) {
        screenListener = listener;
    }

    static public WLScreenListener getListener() {
        return screenListener;
    }

    void checkHorizontalScreen() {

//		log( "checkHorizontalScreen screenListener="+screenListener+",screenIsHorizontal="+screenIsHorizontal);
        if (null != screenListener) {

            WindowManager wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
            Display display = wm.getDefaultDisplay();
            Point mPoint = new Point();
            display.getRealSize(mPoint);
            int phonePointX = Math.min(mPoint.x, mPoint.y);
            int phonePointY = Math.max(mPoint.x, mPoint.y);
            int rotation = getRotation();

            if (isFirstTimeCheckRotation) {
                igetRotation = rotation;
                screenListener.onRotation(igetRotation, phonePointX, phonePointY);
                log(" screenListener.onRotation isFirstTime " + igetRotation);
                isFirstTimeCheckRotation = false;
            }

            if (igetRotation != rotation || mPhonePointX != phonePointX || mPhonePointY != phonePointY) {
                Log.i("shecw1", "ScreenService::checkHorizontalScreen");

                log(" screenListener.onRotation igetRotation rotation=" + rotation + ",phonePointX=" + phonePointX + ",phonePointY=" + phonePointY);
                log(" screenListener.onRotation igetRotation igetRotation=" + igetRotation + ",mPhonePointX=" + mPhonePointX + ",mPhonePointY=" + mPhonePointY);

                igetRotation = rotation;
                mPhonePointX = phonePointX;
                mPhonePointY = phonePointY;
                log(" screenListener.onRotation igetRotation=" + igetRotation + ",mPhonePointX=" + mPhonePointX + ",mPhonePointY=" + mPhonePointY);
                screenListener.onRotation(igetRotation, mPhonePointX, mPhonePointY);
            }
        }
    }

    private int getRotation() {

        int angle = ((WindowManager) getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getRotation();
        return angle;
    }

    void checkTopApp() {
        if (null != screenListener) {
            if (isFirstTimeCheckTopApp) {
                topAppName = getTopActivityPackageName(this.getApplicationContext(), 1000 * 10, topAppName);
                screenListener.currentAppPackageName(topAppName);
                isFirstTimeCheckTopApp = false;
            }

            String top = getTopActivityPackageName(this.getApplicationContext(), 1000, topAppName);
            if (!topAppName.equals(top)) {
                topAppName = top;
                log("getTopActivityPackageName=" + topAppName);
                screenListener.currentAppPackageName(topAppName);
            }
        }
    }

    //使用情况访问权限
    static private UsageStats mRecent;

    static public String getTopActivityPackageName(Context context, int checktime, String lastTopPackageName) {
        long time = System.currentTimeMillis();
        UsageStatsManager usageStatsManager = (UsageStatsManager) context.getSystemService(Context.USAGE_STATS_SERVICE);
        List<UsageStats> queryUsageStats = usageStatsManager.queryUsageStats(UsageStatsManager.INTERVAL_BEST, time - checktime, time);
        if (queryUsageStats == null || queryUsageStats.isEmpty()) {
            return lastTopPackageName;
        }

        for (UsageStats usageStats : queryUsageStats) {
            if (mRecent == null || mRecent.getLastTimeUsed() < usageStats.getLastTimeUsed()) {
                mRecent = usageStats;
            }
        }
        lastTopPackageName = mRecent.getPackageName();
        return lastTopPackageName;
    }
}
