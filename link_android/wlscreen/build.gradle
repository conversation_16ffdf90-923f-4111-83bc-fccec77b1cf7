apply plugin: 'com.android.library'

//apply from: "${project.rootDir}/library.gradle"
//apply from: "${project.rootDir}/maven_push.gradle"

apply from: "../library.gradle"
apply from: "../versions.gradle"
apply from: "../maven_push.gradle"


android {
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    android {
        lintOptions {
            abortOnError false
        }
    }
}

dependencies {
//    implementation "com.autoai.link.threadpool:threadpool:${rootProject.myVersions.threadpool}"
//    implementation "com.autoai.link.baselog:baselog:${rootProject.myVersions.baselog}"

    implementation "com.autoai.link.threadpool:threadpool:${myVersions.threadpool}"
    implementation "com.autoai.link.baselog:baselog:${myVersions.baselog}"
}
