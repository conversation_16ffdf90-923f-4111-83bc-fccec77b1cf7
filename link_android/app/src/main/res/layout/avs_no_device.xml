<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="ResourceName">

    <ImageView
        android:id="@+id/state_no_device_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="200dp"
        android:background="@drawable/wushebei" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/state_no_device_img"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="40dp"
        android:gravity="center"
        android:text="@string/no_device"
        android:textColor="#BF0F1923"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/device_reconnect_button"
        android:layout_width="268dp"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="100dp"
        android:background="@drawable/avs_button_dark_bg"
        android:gravity="center"
        android:text="@string/reconnect"
        android:textColor="@android:color/white"
        android:textSize="18dp" />
</RelativeLayout>