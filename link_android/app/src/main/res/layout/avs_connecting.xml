<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="ResourceName">

    <com.autoai.avs.android.sdk.SpreadView
        android:id="@+id/main_state_img"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_marginTop="103dp"
        android:layout_centerHorizontal="true"
        app:spread_center_color="@android:color/white"
        app:spread_delay_milliseconds="60"
        app:spread_distance="5"
        app:spread_max_radius="70"
        app:spread_radius="70"
        app:spread_center_image="@drawable/touping"
        app:spread_spread_color="@color/colorSpread"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_state_img"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:text="@string/connecting"
        android:textColor="#BF0F1923"
        android:layout_marginTop="28dp"
        android:textSize="18dp" />
</RelativeLayout>