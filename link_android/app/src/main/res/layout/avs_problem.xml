<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:ignore="ResourceName">

    <TextView
        android:id="@+id/problem_content_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="30dp"
        android:gravity="center"
        android:text="@string/problem_help_question_1"
        android:textColor="#800F1923"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/problem_content_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/problem_content_1"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:gravity="start"
        android:text="@string/problem_help_answer_1"
        android:textColor="#FF0F1923"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/problem_content_3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/problem_content_2"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="@string/problem_help_question_2"
        android:textColor="#800F1923"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/problem_content_4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/problem_content_3"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:gravity="start"
        android:text="@string/problem_help_answer_2"
        android:textColor="#FF0F1923"
        android:textSize="16dp" />
</RelativeLayout>