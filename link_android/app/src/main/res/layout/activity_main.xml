<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="RtlHardcoded">

    <TextView
        android:id="@+id/main_title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="12dp"
        android:gravity="center_vertical"
        android:padding="16dp"
        android:text="@string/phone_link"
        android:textColor="#FF0F1923"
        android:textSize="18sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/problem_back_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:background="@android:color/transparent"
        android:src="@drawable/back"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/main_title_text"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_title_text" />


    <ImageButton
        android:id="@+id/problem_state_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="32dp"
        android:background="@android:color/transparent"
        android:src="@drawable/problem_black"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/main_title_text"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_title_text" />

    <FrameLayout
        android:id="@+id/main_content_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@+id/main_title_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_title_text" />

</androidx.constraintlayout.widget.ConstraintLayout>