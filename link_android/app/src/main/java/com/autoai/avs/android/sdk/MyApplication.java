package com.autoai.avs.android.sdk;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import com.autoai.avs.linksdk.WlLinkSdkLog;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        registerUncaughtExceptionHandler(this);
        WlLinkSdkLog.setIsLoggable(true);
    }

    public static void registerUncaughtExceptionHandler(Context context) {
        Thread.UncaughtExceptionHandler handler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler((t, exception) -> {
            Log.e("互连测试App", "应用崩溃", exception);
            String filePath = context.getApplicationContext().getExternalFilesDir(null).getAbsolutePath() + File.separator + "exception_" + System.currentTimeMillis() + ".txt";
            Log.w("互连测试App", "日志位置:" + filePath);
            FileWriter writer;
            try {
                writer = new FileWriter(filePath);
                writer.write("应用崩溃:" + Log.getStackTraceString(exception));
                writer.flush();
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            assert handler != null;
            handler.uncaughtException(t, exception);
        });
    }
}
