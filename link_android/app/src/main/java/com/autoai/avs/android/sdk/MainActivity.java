package com.autoai.avs.android.sdk;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

import com.autoai.avs.linksdk.LinkManager;
import com.autoai.avs.linksdk.listener.LinkState;
import com.autoai.avs.linksdk.util.AppUtils;
import com.autoai.link.threadpool.ThreadPoolUtil;

public class MainActivity extends Activity implements View.OnClickListener {
    private static final String TAG = "MainActivity";
    private static final int PERMISSION_REQUEST_CODE = 333;

    private FrameLayout mHolderView;
    private ImageButton mProblemBtn;
    private ImageButton mBackBtn;
    private TextView mTitle;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_main);
        initView();
        LinkManager.getInstance().addLinkStateChangeListener(this::linkStateChange);

        // Activity 被杀, 后台服务存活情况下, 或者切换语言等会出现此情况
        if (LinkManager.getInstance().getLinkState() == LinkState.STATE_CONNECTED) {
            linkStateChange(LinkState.STATE_CONNECTED);
        } else {
            linkStateChange(LinkState.STATE_CONNECTING);
            checkAndRequestPermission();
        }
//        UsbManager usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);
//        UsbAccessory[] list = usbManager.getAccessoryList();
//        if (list != null) {
//            UsbAccessory accessory = list[0];
//            Log.e(TAG, "UsbAccessory: " + accessory );
//        }
    }


    private void linkStateChange(int state) {
        ThreadPoolUtil.getInstance().runOnUiThread(() -> {
            Log.d(TAG, "LinkStateChange: " + state);
            switch (state) {
                case LinkState.STATE_IDLE:
                    showNoDeviceView();
                    break;
                case LinkState.STATE_SEARCHING:
                case LinkState.STATE_CONNECTING:
                    showConnectingView();
                    break;
                case LinkState.STATE_CONNECTED:
                    showConnectSuccessView();
                    break;
                case LinkState.STATE_DISCONNECT:
                    showDisconnectView();
                case LinkState.STATE_NO_DEVICES:
                    showNoDeviceView();
                    break;
                case LinkState.STATE_ERROR:
                    showConnectFailView();
                    break;
                default:
            }
        });
    }

    private void checkAndRequestPermission() {
        String[] permissions;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_SCAN,Manifest.permission.POST_NOTIFICATIONS};
        } else {
            permissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION};
        }
        boolean canPass = true;
        for (String permission : permissions) {
            if (ActivityCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                canPass = false;
                break;
            }
        }
        if (canPass) {
            tryStartLink();
        } else {
            ActivityCompat.requestPermissions(this, permissions, PERMISSION_REQUEST_CODE);
        }
    }

    private void tryStartLink() {
        Log.d(TAG, "WelinService绑定 WLPlatform tryStartLink: ");
        if (LinkManager.getInstance().getLinkState() != LinkState.STATE_CONNECTED) {
            LinkManager.getInstance().startLink(this);
        } else {
            Log.w(TAG, "WelinService绑定 WLPlatform startLink, has connected, skip!");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean canPass = true;
            for (String permission : permissions) {
                if (ActivityCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                    canPass = false;
                    break;
                }
            }
            if (canPass) {
                tryStartLink();
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        Log.d(TAG, "onNewIntent:: intent action:" + intent.getAction() + "Extra: " + AppUtils.bundleToString(intent.getExtras()));

        if (LinkManager.getInstance() != null) {
            setIntent(intent);
            tryStartLink();
        }
    }

    private void initView() {
        mHolderView = findViewById(R.id.main_content_container);
        mBackBtn = findViewById(R.id.problem_back_btn);
        mProblemBtn = findViewById(R.id.problem_state_btn);
        mTitle = findViewById(R.id.main_title_text);
        mProblemBtn.setOnClickListener(this);
        mBackBtn.setOnClickListener(this);
        findViewById(R.id.problem_back_btn).setOnClickListener(this);
        showConnectingView();
    }

    private void showConnectingView() {
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        View view = layoutInflater.inflate(R.layout.avs_connecting, mHolderView, false);
        problemButtonEnable(false);
        backButtonVisible(false);
        resumeTitleDescribe("手机投屏");
        mHolderView.removeAllViews();
        attachView(view);
    }

    private void problemButtonEnable(boolean clickable) {
        int resId = clickable ? R.drawable.problem_black : R.drawable.problem_grey;
        mProblemBtn.setTag(clickable);
        mProblemBtn.setVisibility(View.VISIBLE);
        mProblemBtn.setImageResource(resId);
    }

    private void backButtonVisible(boolean visible) {
        int resId = visible ? View.VISIBLE : View.GONE;
        mBackBtn.setVisibility(resId);
    }


    private void resumeTitleDescribe(int resId) {
        mTitle.setText(resId);
    }

    private void resumeTitleDescribe(String describe) {
        mTitle.setText(describe);
    }

    private void attachView(View view) {
        mHolderView.addView(view, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
    }

    private void detachView(View view) {
        mHolderView.removeView(view);
    }

    @Override
    public void onClick(View v) {
        if (LinkManager.getInstance() == null) {
            return;
        }

        int id = v.getId();
        if (id == R.id.problem_state_btn) {
            //帮助事件
            boolean clickable = (boolean) mProblemBtn.getTag();
            if (clickable) {
                showProblemView();
            }
        } else if (id == R.id.disconnect_button) {
            //断开连接事件
            LinkManager.getInstance().stopLink();
            showConnectFailView();
        } else if (id == R.id.reconnect_button || id == R.id.device_reconnect_button || id == R.id.fail_reconnect_button) {
            //重新连接事件
            tryStartLink();
            showConnectingView();
        } else if (id == R.id.problem_back_btn) {
            //帮助退出事件
            resumeTitleDescribe(R.string.main_title);
            problemButtonEnable(true);
            backButtonVisible(false);
            outOfStackView();
        }
    }

    private boolean outOfStackView() {
        int childCount = mHolderView.getChildCount();
        if (childCount-- > 1) {
            mHolderView.removeViewAt(childCount);
//            View showView = mHolderView.getChildAt(childCount - 1);
            return true;
        }
        return false;
    }

    private void showConnectSuccessView() {
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        View view = layoutInflater.inflate(R.layout.avs_success, mHolderView, false);
        backButtonVisible(false);
        problemButtonEnable(true);
        view.findViewById(R.id.disconnect_button).setOnClickListener(this);
        mHolderView.removeAllViews();
        resumeTitleDescribe(R.string.main_title);
        attachView(view);
    }

    private void showDisconnectView() {
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        View view = layoutInflater.inflate(R.layout.avs_disconnect, mHolderView, false);
        backButtonVisible(false);
        problemButtonEnable(true);
        view.findViewById(R.id.reconnect_button).setOnClickListener(this);
        mHolderView.removeAllViews();
        resumeTitleDescribe(R.string.main_title);
        attachView(view);
    }

    private void showNoDeviceView() {
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        View view = layoutInflater.inflate(R.layout.avs_no_device, mHolderView, false);
        backButtonVisible(false);
        problemButtonEnable(true);
        view.findViewById(R.id.device_reconnect_button).setOnClickListener(this);
        mHolderView.removeAllViews();
        resumeTitleDescribe(R.string.main_title);
        attachView(view);
    }

    private void showConnectFailView() {
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        View view = layoutInflater.inflate(R.layout.avs_fail, mHolderView, false);
        backButtonVisible(false);
        problemButtonEnable(true);
        view.findViewById(R.id.fail_reconnect_button).setOnClickListener(this);
        mHolderView.removeAllViews();
        resumeTitleDescribe(R.string.main_title);
        attachView(view);
    }

    private void showProblemView() {
        LayoutInflater layoutInflater = LayoutInflater.from(this);
        View view = layoutInflater.inflate(R.layout.avs_problem, mHolderView, false);
        backButtonVisible(true);
        mProblemBtn.setVisibility(View.GONE);
        resumeTitleDescribe(R.string.problem_help_title);
        attachView(view);
    }

    private long mLastTime;

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            if (outOfStackView()) {
                resumeTitleDescribe(R.string.main_title);
                problemButtonEnable(true);
                backButtonVisible(false);
                return true;
            }
            long currentTime = System.currentTimeMillis();
            if (currentTime - mLastTime <= 500) {
                finish();
            } else {
                mLastTime = System.currentTimeMillis();
                Toast.makeText(this, "再此点击退出应用程序", Toast.LENGTH_SHORT).show();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (LinkManager.getInstance() != null) {
            LinkManager.getInstance().onActivityResult(requestCode, resultCode, data);
        }
    }
}