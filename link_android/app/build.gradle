plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion project.COMPILE_SDK_VERSION

    defaultConfig {
        applicationId project.APPLICATION_ID
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        platform {
            storeFile file('../key/platform.jks')
            storePassword "autoai"
            keyAlias "agent"
            keyPassword "autoai"
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            signingConfig signingConfigs.platform
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation "com.autoai.link.threadpool:threadpool:${project.myVersions.threadpool}"
    implementation "com.autoai.link.baselog:baselog:${project.myVersions.baselog}"
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.1'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'

    implementation project(":linkSdk")
}