package com.autoai.welink.wireless;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.bluetooth.le.ScanFilter;
import android.content.Context;
import android.content.pm.PackageManager;
import android.net.wifi.WifiManager;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.wifigo.WifiDirectGo;

import java.util.List;

public class WLHardwareHub {

    private WifiDirectGo wifiDirectGo = null;

    /***
	 * 禁止包外构建WLHardwareHub对象
	 */
	protected WLHardwareHub() {
	}

	/***
	 * 获取功能库版本号
	 * @return 返回功能库版本号字符串
	 */
	public static String getVersion() {
		return BuildConfig.VERSION_NAME + "/" + BuildConfig.BUILD_TYPE;
	}


    /**
     * 建立硬件组，等待硬件心跳，注意：建立硬件组与启动蓝牙BLE唤醒不能同时进行
     * 相关硬件开发参照文档《硬件无线连接协议》
     * 硬件注册服务URL: http://IP:Port/signup?ver=1&package=com.autoai.welink&port=14558&device=Demo-Device-N00001
     *
     * @param context Application Context
     * @param networkName Wi-Fi Direct 热点名称
     * @param passphrase Wi-Fi Direct 热点密码
     * @param frequency 信道频率
     * 以下是中国国内Wi-Fi 5G推荐信道，其他信道是禁止或有DFS要求，DFS有要求的信道不建议单独指定
     * frequency:
     * 信道：频率
     * 36: 5180
     * 38: 5190
     * 40: 5200
     * 44: 5220
     * 46: 5230
     * 48: 5240
     * 149: 5745
     * 151: 5755
     * 153: 5765
     * 157: 5785
     * 159: 5795
     * 161: 5805
     * 165: 5825
     * @param rssi BLE最小信号强度
     * @param timeout BLE连接超时
     * @param bleDeviceFilterList BLE设备地址过滤，用于USB授权无线连接
     * @param listener 硬件消息回调，为null则取消等待WeLinkHardware的消息
     */
    public static WLHardwareHub createHardwareGroup(final Context context, final String networkName, final String passphrase, final Integer frequency, final Integer rssi, final Integer timeout, final List<ScanFilter> bleDeviceFilterList, @NonNull final WLHardwareHubListener listener) {
        AnFileLog.e("wlhardwarehub", "createHardwareGroup 1");

        //检查蓝牙是否打开、是否支持BLE、Wi-Fi是否打开、是否支持Wi-Fi Direct
        if (!checkPermission(context, listener)) {
            return null;
        }

        AnFileLog.e("wlhardwarehub", "createHardwareGroup 2");

        WLHardwareHub wlHardwareHub = new WLHardwareHub();

        wlHardwareHub.wifiDirectGo = WifiDirectGo.initGo(context, networkName, passphrase, frequency, listener, rssi, timeout, bleDeviceFilterList);

        if (wlHardwareHub.wifiDirectGo == null) {
            return null;
        }

        AnFileLog.e("wlhardwarehub", "createHardwareGroup 3");
        return wlHardwareHub;
    }



    /**
     * 刷新组信息
     * 用于加速车机连接失败后的再一次连接
     */
    public void refreshGroupInfo() {
        if (wifiDirectGo != null) {
            wifiDirectGo.refreshGroupInfo();
        }
    }



    private static boolean checkPermission(Context context, WLHardwareHubListener wlHardwareListener) {
        if (!context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE)) {
            if (wlHardwareListener != null) {
                wlHardwareListener.onError(1);
                return false;
            }
        }

        BluetoothManager bm = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        BluetoothAdapter ba = null;
        if (bm != null) {
            ba = bm.getAdapter();
        }
        if (ba == null || !ba.isEnabled()) {
            if (wlHardwareListener != null) {
                wlHardwareListener.onError(2);
                return false;
            }
        }

        WifiManager wm = (WifiManager)context.getSystemService(Context.WIFI_SERVICE);
        if (wm == null || !wm.isWifiEnabled()) {
            if (wlHardwareListener != null) {
                wlHardwareListener.onError(3);
                return false;
            }
        }

        if (ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            if (wlHardwareListener != null) {
                wlHardwareListener.onError(4);
                return false;
            }
        }

        return true;
    }


    /**
     * 销毁硬件组
     */
    public void destroyHardwareGroup() {
        AnFileLog.e("wlhardwarehub", "destroyHardwareGroup");
        if (wifiDirectGo != null) {
            wifiDirectGo.release();
            wifiDirectGo = null;
        }
    }

    public static void enableLogCat(boolean enable) {
        AnFileLog.enableLogCat(enable);
    }

    public static void enableLogFile(boolean enable) {
        AnFileLog.enableLogFile(enable);
    }
    public static void enableLogInit(Context context) {
        AnFileLog.init(context);
    }

    /**
     * 隐藏组信息
     */
    public void hideGroupInfo() {
        if (wifiDirectGo != null) {
            wifiDirectGo.hideGroupInfo();
        }
    }

    /**
     * 显示组信息
     */
    @Deprecated
    public void showGroupInfo() {
        if (wifiDirectGo != null) {
            wifiDirectGo.showGroupInfo();
        }
    }
}
