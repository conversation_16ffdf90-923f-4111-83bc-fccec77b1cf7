package com.autoai.welink.wireless;

public interface WLHardwareHubListener {
    /**
     * BLE扫描状态
     * @param status ble状态码
     *               0: Wi-Fi Direct GO广播，content: GO签名
     *               1: BLE设备连接中...
     *               2: BLE设备连接成功
     *               3: BLE服务连接中...
     *               4: BLE服务连接成功
     *               5: 读取版本中...
     *               6: 版本读取成功，content：版本号
     *               7: 写Wi-Fi Direct GO签名
     *               8: 签名写成功，content：签名
     *               9: Wi-Fi Direct GC连接，content: 网络密码
     *               -1: 设备连接失败
     *               -2: 服务连接失败
     *               -3: 版本读取失败
     *               -4: 签名写失败
     *               -5: 操作超时
     */
    void onStatusChanged(int status, String content);

    /**
     * 有匹配的车机接入并发送了心跳
     * @param packageName 包名
     * @param ip          匹配车机的ip
     * @param port        匹配车机的监听端口号
     * @param device      匹配车机的设备名
     */
    void onHeartbeat(String packageName, String ip, int port, String device);

    /**
     * 错误
     * @param reason 错误码
     *               0: 创建Wi-Fi Direct GO成功
     *               1: 设备不支持蓝牙BLE
     *               2: 没有打开蓝牙
     *               3: 没有打开Wi-Fi
     *               4: 需要请求定位权限(ACCESS_FINE_LOCATION)
     *               5: 服务发生异常
     *               6: 创建Wi-Fi Direct GO失败
     *
     * 收到除0以外的错误码，硬件组将无效，需要重新建立硬件组
     */
    void onError(int reason);
}
