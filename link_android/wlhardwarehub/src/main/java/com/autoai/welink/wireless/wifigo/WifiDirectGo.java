package com.autoai.welink.wireless.wifigo;

import android.annotation.SuppressLint;
import android.bluetooth.le.ScanFilter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.net.wifi.p2p.WifiP2pConfig;
import android.net.wifi.p2p.WifiP2pGroup;
import android.net.wifi.p2p.WifiP2pInfo;
import android.net.wifi.p2p.WifiP2pManager;
import android.net.wifi.p2p.nsd.WifiP2pDnsSdServiceInfo;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.ArrayMap;
import android.util.Log;

import androidx.core.content.ContextCompat;

import com.autoai.welink.wireless.WLHardwareHubListener;
import com.autoai.welink.wireless.ble.BLEConnector;
import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.utiliy.elonen.NanoHTTPD;

import java.net.ServerSocket;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

public class WifiDirectGo extends BroadcastReceiver {
    private String TAG = "wifi-go";

    /**
     * wifi-p2p go 名称
     */
    private final static String instanceName = "welink";

    /**
     * wifi-p2p go 内容
     */
    private final static String serviceType = "hardware-register";

    private final static String WIFI_GO_FLAG = instanceName + "." + serviceType;
    private final static String URL_PATH = "/signup";
    private final static String URL_PARAM_VER = "ver";
    private final static String CURRENT_VER = "1";
    private final static String URL_PARAM_PORT = "port";
    private final static String URL_PARAM_DEVICE = "device";
    private final static String URL_PARAM_PACKAGE = "package";
    private final static int TRY_GO_COUNT = 5;
    /**
     * 上下文：目前传入为后台service
     */
    private Context context;

    /**
     *  系统wifi-p2p manager
     */
    private WifiP2pManager manager = null;
    /**
     * 实际通信信道api
     */
    private WifiP2pManager.Channel channel = null;
    /**
     * 无感互联模块对外通知callback
     */
    private WLHardwareHubListener listener;


    /**
     * 系统wifi-p2p go的信息
     */
    private WifiP2pDnsSdServiceInfo info = null;

    /**
     * go 的自定义字段，包含于{@link #info}
     */
    private final Map<String, String> mapDnsSdInfo = new HashMap<>();
    /**
     * 心跳包server,监听信道是否连接可用
     */
    private RunNanoHTTPD httpServer = null;
    /**
     * 创建的 wifi-direct  group owner 名字
     */
    private String networkName = null;

    /**
     * 加入  wifi-direct  go 的签名。目前每次初始化生产随机数
     */
    private String signature = null;

    /**
     * wifi-p2p go热点接入的 wifi密码..
     */
    private String passphrase = null;
    private int frequency = 0;
    /**
     * 到{@link #createGroup()}
     * 方法执行开始 :isCreating = true
     * go 创建结果回调 :isCreating = false
     */
    private boolean isCreating = false;

    /**
     * 创建 go 失败重试次数
     */
    private int tryGO = 0;

    /**
     * ble 连接握手模组
     */
    private BLEConnector bleConnector = null;

    /**
     * 蓝牙ble 强度
     */
    Integer rssi;
    Integer timeout;
    List<ScanFilter> bleDeviceFilterList;
    Map<String, Long> connectedDevices = new ArrayMap<>();
    Map<String, String> connectedDevicesIP = new ArrayMap<>();
    final Object connectedDevicesIPLock = new Object();
    Thread connectedDevicesCheckThread = null;

    /**
     * 当前是否处于连接状态
     * true 是互联状态，不在启动ble扫描，群组创建失败也不在重试   false，正常状态，ble扫描，群组创建失败时重试
     */
    private boolean isConnected = false;

    /**
     * 以下是中国国内Wi-Fi 5G推荐信道，其他信道是禁止或有DFS要求，DFS有要求的信道不建议单独指定
     * frequency:
     * 信道：频率
     * 36: 5180
     * 38: 5190
     * 40: 5200
     * 44: 5220
     * 46: 5230
     * 48: 5240
     * 149: 5745
     * 151: 5755
     * 153: 5765
     * 157: 5785
     * 159: 5795
     * 161: 5805
     * 165: 5825
     */
    protected WifiDirectGo(final Context context, final String networkName, final String passphrase, WLHardwareHubListener listener, final Integer rssi, final Integer frequency, final Integer timeout, final List<ScanFilter> bleDeviceFilterList) throws Exception {
        //--> 初始化 wifi-direct go 和 报了相关参数
        this.context = context;
        this.networkName = networkName;
        this.passphrase = passphrase;
        this.listener = listener;
        this.signature = getRandomString();
        this.rssi = rssi;
        this.timeout = timeout;
        this.bleDeviceFilterList = bleDeviceFilterList;
        isConnected = false;


        ServerSocket serverSocket = new ServerSocket(0);
        int http_port = serverSocket.getLocalPort();
        serverSocket.close();
        //--> wifi-direct 与车机连接成功后，心跳包server，用于监听信道层是否联通
        httpServer = new RunNanoHTTPD(http_port, listener);
        httpServer.start(NanoHTTPD.SOCKET_READ_TIMEOUT, false);

        mapDnsSdInfo.put("http_port", http_port + "");
        mapDnsSdInfo.put("server", WIFI_GO_FLAG);
        mapDnsSdInfo.put("signature", signature);

        this.frequency = frequency;

        AnFileLog.e("wifi-go", "http_port:" + http_port
                +"\n,signature:" + signature
                +"\n,WIFI_GO_FLAG:" + WIFI_GO_FLAG
                +"\n,networkName:" + networkName
                +"\n,passphrase:" + passphrase
                +"\n,timeout:" + timeout
                +"\n,rssi:" + rssi
                +"\n,frequency:" + frequency


        );
        if (listener != null) {
            listener.onStatusChanged(0, signature);
        }

        //--> 系统wifi-p2p manager
        manager = (WifiP2pManager) context.getSystemService(Context.WIFI_P2P_SERVICE);
        channel = manager.initialize(context, context.getMainLooper(), null);

        //--> 注册wifi-p2p状态监听
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION);
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION);
        this.context.registerReceiver(this, intentFilter);

        //--> 复归重试次数
        tryGO = 0;
//        createGroup();

        manager.requestConnectionInfo(channel, new WifiP2pManager.ConnectionInfoListener() {

            @Override
            public void onConnectionInfoAvailable(WifiP2pInfo wifiP2pInfo) {
                AnFileLog.e(TAG, "wifiP2pInfo: " + wifiP2pInfo);
                if(wifiP2pInfo == null){
                    createGroup();
                    return;
                }
                if (wifiP2pInfo.groupFormed) {
                    AnFileLog.e(TAG, "群组仍然存在");
//                    removeGroup();
                    //todo 杀掉进程再次进入app 移除服务  当前逻辑是检测到之前服务未释放先移除服务再创建
                    //当前看返回值每次移除都回调成功，继续跟中
                    info = WifiP2pDnsSdServiceInfo.newInstance(instanceName, serviceType, mapDnsSdInfo);
                    deinitGoP2pDnsSdService();
                    manager.removeGroup(channel, new WifiP2pManager.ActionListener() {
                        @Override
                        public void onSuccess() {
                            AnFileLog.e(TAG, "onSuccess 群组已释放22");
                            createGroup();
                        }

                        @Override
                        public void onFailure(int reason) {
                            AnFileLog.e(TAG, "群组释放失败");
                            createGroup();
                        }
                    });

                }else{
                    AnFileLog.e(TAG, "群组已释放1");
                    createGroup();
                }
            }
        });

    }

    public static WifiDirectGo initGo(Context context, final String networkName, final String passphrase, final Integer frequency, WLHardwareHubListener listener, final Integer rssi, final Integer timeout, final List<ScanFilter> bleDeviceFilterList) {
        WifiDirectGo go;

        try {
            go = new WifiDirectGo(context, networkName, passphrase, listener, rssi, frequency, timeout, bleDeviceFilterList);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        //-->fixme:@shecw 检查这里逻辑
//        go.connectedDevicesCheckThread = new Thread() {
//            @Override
//            public void run() {
//                while (!isInterrupted()) {
//                    List<String> clients = new ArrayList<>();
//                    for (String key : go.connectedDevices.keySet()) {
//                        Long time = go.connectedDevices.get(key);
//                        if (time != null && (System.currentTimeMillis() - time) <= 3000) {
//                            AnFileLog.e("onHeartbeat", "key: " + key);
//                            clients.add(key);
//                        } else {
//                            synchronized (go.connectedDevicesIPLock) {
//                                go.connectedDevicesIP.remove(key);
//                            }
//                        }
//                    }
//
//                    if (go.bleConnector != null) {
//                        if (clients.size() > 0) {
//                            AnFileLog.e("onHeartbeat", "filter: " + clients.get(0));
//                        }
//                        go.bleConnector.resetFilter(clients);
//                    }
//
//                    try {
//                        sleep(1000);
//                    } catch (InterruptedException e) {
//                        return;
//                    }
//                }
//            }
//        };
//        go.connectedDevicesCheckThread.start();

        return go;
    }

    public void release() {
        AnFileLog.e(TAG,"release----");
        if (connectedDevicesCheckThread != null) {
            connectedDevicesCheckThread.interrupt();
            connectedDevicesCheckThread = null;
        }

        if (bleConnector != null) {
            bleConnector.stop();
            bleConnector = null;
        }
        bleDeviceFilterList = null;

        removeGroup();

        try {
            context.unregisterReceiver(this);
        } catch (Exception ignored) {
        }

        channel = null;
        manager = null;

        mapDnsSdInfo.clear();

        if (httpServer != null) {
            httpServer.stop();
            httpServer = null;
        }

        signature = null;
        listener = null;
        passphrase = null;
        networkName = null;
        context = null;
    }

    private boolean enable5745 = true;
    private boolean enable5785 = true;
    private boolean enable5825 = true;

    /**
     * 创建go
     */
    @SuppressLint("MissingPermission")
    private void createGroup() {
        AnFileLog.e(TAG,"isConnected:"+isConnected+",isCreating:"+isCreating);
        if (context == null ||isConnected || ContextCompat.checkSelfPermission(context, android.Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            AnFileLog.e("wifi-go", "WifiP2pManager:createGroup，context == null isConnected:"+isConnected);
            return;
        }

        isCreating = true;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            //--> Android 10及其以上，支持5g，有线使用5g选择信道

            if (frequency == 0) {
                frequency = 5745;

                Map<Integer, Integer> FF = new ArrayMap<>();
                WifiManager wm = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
                if (wm != null && wm.isWifiEnabled()) {
                    //--> 获取周边所有可接入热点频段
                    List<ScanResult> results = wm.getScanResults();
                    for (ScanResult r : results) {
                        Log.i("shecw1","r = " + r);
                        Integer count = FF.get(r.frequency);
                        if (count == null) {
                            count = 0;
                        }
                        FF.put(r.frequency, ++count);
                    }

                    //--> 5745 热点数量
                    Integer count5745 = FF.get(5745);
                    if (count5745 == null)
                        count5745 = 0;

                    //--> 5785 热点数量
                    Integer count5785 = FF.get(5785);
                    if (count5785 == null)
                        count5785 = 0;

                    //--> 5825 热点数量
                    Integer count5825 = FF.get(5825);
                    if (count5825 == null)
                        count5825 = 0;

                    //处理某些手机不支持特定信道的问题（如：小米8 Android10不支持5825信道）-->
                    if (!enable5745) {
                        count5745 = Integer.MAX_VALUE;
                    }
                    if (!enable5785) {
                        count5785 = Integer.MAX_VALUE;
                    }
                    if (!enable5825) {
                        count5825 = Integer.MAX_VALUE;
                    }

                    //--> 有线使用频段最少得，尽可能避免同频段干扰
                    if (count5745 <= count5785 && count5745 <= count5825) {
                        frequency = 5745;
                    } else if (count5785 <= count5745 && count5785 <= count5825) {
                        frequency = 5785;
                    } else {
                        frequency = 5825;
                    }

                    //--> 如果都失败了，那么使用系统分配频段
                    if (!enable5745 && !enable5785 && !enable5825) {
                        frequency = 0;
                    }
                    AnFileLog.e("ble-scan", "5745: " + count5745);
                    AnFileLog.e("ble-scan", "5785: " + count5785);
                    AnFileLog.e("ble-scan", "5825: " + count5825);
                }
            }

            AnFileLog.e("ble-scan", "frequency: " + frequency);

            manager.createGroup(channel,
                new WifiP2pConfig.Builder()
                    .enablePersistentMode(true)
                    .setGroupOperatingFrequency(frequency)
                    .setNetworkName("DIRECT-" + networkName)
                    .setPassphrase(passphrase)
                    .build(),
                new WifiP2pManager.ActionListener() {
                    /**
                     * go 创建成功
                     */
                    @Override
                    public void onSuccess() {
                        isCreating = false;
                        if (manager != null) {
                            //--> 初始化dns service信息
                            initGoP2pDnsSdService();
                        }
                    }

                    @Override
                    public void onFailure(int reason) {
                        isCreating = false;
                        //--> 标记不可用频段
                        if (frequency == 5745) {
                            enable5745 = false;
                        } else if (frequency == 5785) {
                            enable5785 = false;
                        } else if (frequency == 5825) {
                            enable5825 = false;
                        }
                        frequency = 0;
                        //--> retry
                        if (tryGO++ < TRY_GO_COUNT) {
                           AnFileLog.e("wifi-go", "WifiP2pManager:createGroup失败，onFailure回调 重试次数 : " + tryGO + "reason--> " + reason);
                            if (manager == null || context == null|| isConnected) {
                                AnFileLog.e("wifi-go", "WifiP2pManager:createGroup失败，onFailure回调 重试的过程中被销毁");
                                return;
                            }
                            manager.removeGroup(channel, null);
                            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    createGroup();
                                }
                            }, tryGO * 500);
                        } else {
                            if (reason == WifiP2pManager.ERROR) {
                                OnError(6);
                            } else {
                                OnError(5);
                            }
                        }
                    }
                });
        } else {
            //--> Android 10以下，不支持信道的选择
            manager.createGroup(channel, new WifiP2pManager.ActionListener() {
                @Override
                public void onSuccess() {
                    isCreating = false;
                    if (manager != null) {
                        //--> 初始化当前创建 go 的 dns 服务
                        initGoP2pDnsSdService();
                    }
                }

                @Override
                public void onFailure(int reason) {
                    isCreating = false;
                    if (tryGO++ < TRY_GO_COUNT) {
                        //--> 错误重试
                        AnFileLog.e("wifi-go", "WifiP2pManager:createGroup失败，onFailure回调 重试次数 : " + tryGO + "reason--> " + reason+",isConnected:"+isConnected);
                        if (manager == null || context == null || isConnected) {
                            AnFileLog.e("wifi-go", "WifiP2pManager:createGroup失败，onFailure回调 重试的过程中被销毁");
                            return;
                        }

                        //--> retry
                        manager.removeGroup(channel, null);
                        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                createGroup();
                            }
                        }, tryGO * 500);
                    } else {
                        if (reason == WifiP2pManager.ERROR) {
                            OnError(6);
                        } else {
                            OnError(5);
                        }
                    }
                }
            });
        }
    }



    @SuppressLint("MissingPermission")
    private void initGoP2pDnsSdService() {
        if (context == null || ContextCompat.checkSelfPermission(context, android.Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            AnFileLog.e("wifi-go", "WifiP2pManager:initGoP2pDnsSdService，context == null");
            return;
        }

        //--> 创建go 的 info
        info = WifiP2pDnsSdServiceInfo.newInstance(instanceName, serviceType, mapDnsSdInfo);

        if (info != null) {
            //--> 将dns info与go channel绑定
            manager.addLocalService(channel, info, new WifiP2pManager.ActionListener() {
                /**
                 * 本地服务添加成功
                 */
                @Override
                public void onSuccess() {
                    AnFileLog.e(TAG, "WifiP2pManager:initGoP2pDnsSdService：：onSuccess");
                    OnError(0);

                    if (bleConnector != null) {
                        bleConnector.stop();
                    }

                    if (context == null) {
                        AnFileLog.e(TAG, "WifiP2pManager:initGoP2pDnsSdService，context == null");
                        return;
                    }

                    AnFileLog.e("ble-scan", "go: " + this+",isConnected:"+isConnected);
                    if(!isConnected) {
                        // --> 开启蓝牙ble 识别
                        bleConnector = BLEConnector.start(context, getSignature(), rssi, timeout, bleDeviceFilterList, listener);
                    }
                }

                /**
                 * 本地服务添加失败
                 */
                @Override
                public void onFailure(int reason) {
                    AnFileLog.e(TAG, "WifiP2pManager:initGoP2pDnsSdService：：onFailure --> reason = " + reason);
                    OnError(5);
                }
            });
        }
    }

    /**
     * 互联成功是停止扫描
     */
    public void hideGroupInfo() {
        if (manager == null) {
            return;
        }
        isConnected = true;
        AnFileLog.e("wifi-go","hideGroupInfo-->isConnected:"+isConnected);
        pauseHardwareScan();
        deinitGoP2pDnsSdService();
    }

    /**
     * fixme：暂时无处使用，后续删除
     */
    @Deprecated
    public void showGroupInfo() {
        if (manager == null) {
            return;
        }

        initGoP2pDnsSdService();
    }

    /**
     * fixme：暂时无处使用，后续删除
     */
    public void refreshGroupInfo() {
        if (manager == null) {
            return;
        }
        refreshGoP2pDnsSdService();
    }

    public void pauseHardwareScan() {
        if (bleConnector != null) {
            bleConnector.stop();
            bleConnector = null;
        }
    }

    public void resumeHardwareScan() {
        if (bleConnector == null) {
            bleConnector = BLEConnector.start(context, getSignature(), rssi, timeout, bleDeviceFilterList, listener);
        }
    }

    public void removeGroup() {
        if (manager == null) {
            return;
        }
        AnFileLog.e(TAG,"removeGroup----");
        deinitGoP2pDnsSdService();
        manager.removeGroup(channel, null);
    }

    public boolean isGroup() {
        return info != null || isCreating;
    }

    /**
     * 暂未使用
     *  fixme：暂时无处使用，后续删除
     */
    @SuppressLint("MissingPermission")
    @Deprecated
    private void refreshGoP2pDnsSdService() {
        if (context == null || ContextCompat.checkSelfPermission(context, android.Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            AnFileLog.e("wifi-go", "WifiP2pManager:refreshGoP2pDnsSdService，context == null");
            return;
        }

        deinitGoP2pDnsSdService();

        mapDnsSdInfo.remove("signature");
        signature = getRandomString();
        mapDnsSdInfo.put("signature", signature);
        info = WifiP2pDnsSdServiceInfo.newInstance(instanceName, serviceType, mapDnsSdInfo);

        if (info != null) {
            manager.addLocalService(channel, info, new WifiP2pManager.ActionListener() {
                @Override
                public void onSuccess() {
                    OnError(0);
                    bleConnector.updateSignature(signature);
                }

                @Override
                public void onFailure(int reason) {
                    OnError(5);
                }
            });
        }
    }

    private void deinitGoP2pDnsSdService() {
        if (info != null) {
            manager.removeLocalService(channel, info, null);
            info = null;
        }
    }

    private static String getRandomString() {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < 6; i++) {
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }

        return sb.toString();
    }

    public String getSignature() {
        return signature;
    }

    public void OnError(final int reason) {
        AnFileLog.e("onHeartbeat", "error: " + reason);
        if (reason != 0) {
            removeGroup();
        }

        if (listener != null) {
            listener.onError(reason);
        }
    }
    @Override
    public void onReceive(Context context, Intent intent) {
        if (this.context == null) {
            AnFileLog.e("wifi-go", "WifiDirectGo:onReceive context == null!!!");
            return;
        }

        String action = intent.getAction();
        AnFileLog.e("wifi-go", "WifiDirectGo:onReceive action =="+action);

        if (WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION.equals(action)) {
            //--> WIFI_P2P 可用状态发生变化
            int state = intent.getIntExtra(WifiP2pManager.EXTRA_WIFI_STATE, -1);
            //wifi 可用状态变化
            AnFileLog.e("wifi-go", " WIFI_P2P_STATE_CHANGED_ACTION state:"+state);
            switch (state) {
                //--> wifi p2p 可用
                case WifiP2pManager.WIFI_P2P_STATE_ENABLED:
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            if (!isGroup()) {
                                tryGO = 0;
                                //-->创建go
                                createGroup();
                            }
                        }
                    });
                    break;
                //--> wifi p2p 禁用
                case WifiP2pManager.WIFI_P2P_STATE_DISABLED:
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            if (isGroup()) {
                                OnError(5);
                            }
                        }
                    });
                    break;
            }
        } else if (WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION.equals(action)) {
            //--> 有连接状态的变化
            //fixme @shecw 后续这里可以细化
            WifiP2pGroup wifiP2pGroup = intent.getParcelableExtra(WifiP2pManager.EXTRA_WIFI_P2P_GROUP);
            if (wifiP2pGroup != null && wifiP2pGroup.getClientList().size() > 0) {
                AnFileLog.e("wifi-go", " wifiP2pGroup.getClientList() == "+wifiP2pGroup.getClientList().size());
                if (listener != null) {
                    listener.onStatusChanged(9, wifiP2pGroup.getPassphrase());
                }
            }
        }
    }
    class RunNanoHTTPD extends NanoHTTPD {
        private final WLHardwareHubListener listener;

        public RunNanoHTTPD(int port, WLHardwareHubListener listener) {
            super(port);
            this.listener = listener;
        }

        String getParam(Map<String, List<String>> params, String name) {
            if (params == null) {
                return null;
            }

            List<String> list = params.get(name);
            if (list != null) {
                return list.get(0);
            }

            return null;
        }

        @Override
        public Response serve(IHTTPSession session) {
            AnFileLog.e("onHeartbeat", "WifiDirectGo::serve --> receive: " + session.getQueryParameterString());
            String url = session.getUri();
            if (url.indexOf(URL_PATH) + URL_PATH.length() != url.length()) {
                return newFixedLengthResponse("wrong path");
            }

            AnFileLog.e("onHeartbeat", "parameter");
            Map<String, List<String>> params = session.getParameters();
            if (params == null) {
                return newFixedLengthResponse("missing parameter");
            }

            AnFileLog.e("onHeartbeat", "version");
            String ver = getParam(params, URL_PARAM_VER);
            if (ver == null) {
                return newFixedLengthResponse("missing parameter: " + URL_PARAM_VER);
            }

            AnFileLog.e("onHeartbeat", "device");
            String device = getParam(params, URL_PARAM_DEVICE);

            if (device != null) {
                if (device.equals("SAIC_BLE")) {
                    //兼容AP32，连接AP32后不再连接A7Plus车机，反之亦然
                    device = "WeLink";
                }

                AnFileLog.e("onHeartbeat", "device-put: " + device);
                if (!connectedDevicesIP.containsKey(device)) {
                    synchronized (connectedDevicesIPLock) {
                        connectedDevicesIP.put(device, session.getRemoteIpAddress());
                    }
                } else if (!Objects.equals(connectedDevicesIP.get(device), session.getRemoteIpAddress())) {
                    return newFixedLengthResponse(Response.Status.INTERNAL_ERROR, NanoHTTPD.MIME_PLAINTEXT, "repeat device");
                }
                connectedDevices.put(device, System.currentTimeMillis());
            }

            AnFileLog.e("onHeartbeat", "port");
            String remotePort = getParam(params, URL_PARAM_PORT);
            if (remotePort == null) {
                return newFixedLengthResponse("missing parameter: " + URL_PARAM_PORT);
            }

            AnFileLog.e("onHeartbeat", "package");
            String packageName = getParam(params, URL_PARAM_PACKAGE);
            if (packageName == null) {
                return newFixedLengthResponse("missing parameter: " + URL_PARAM_PACKAGE);
            }

            AnFileLog.e("onHeartbeat", "current version: " + ver);
            if (!CURRENT_VER.equals(ver)) {
                return newFixedLengthResponse("ver error");
            }

            AnFileLog.e("onHeartbeat", "remote port: " + remotePort);
            int port;
            try {
                port = Integer.parseInt(remotePort);
            } catch (Exception e) {
                return newFixedLengthResponse("port error");
            }

            String remoteIP = session.getRemoteIpAddress();

            AnFileLog.e("onHeartbeat", "callback");
            if (listener != null) {
                listener.onHeartbeat(packageName, remoteIP, port, device);
            }

            AnFileLog.e("onHeartbeat", "response");
            return newFixedLengthResponse("Hello WeLink");
        }
    }
}
