package com.autoai.welink.wireless.ble;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.os.ParcelUuid;

import androidx.annotation.NonNull;

import com.autoai.welink.wireless.WLAPConfigListener;
import com.autoai.welink.wireless.WLAPConnectListener;
import com.autoai.welink.wireless.WLHardwareHubListener;
import com.autoai.welink.wireless.utiliy.AnFileLog;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;

public class BLEConnector extends BroadcastReceiver {

    /**
     * 扫描到蓝牙状态回调
     *
     * @param context The Context in which the receiver is running.
     * @param intent  The Intent being received.
     */
    @SuppressLint("MissingPermission")
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();


        if (BluetoothAdapter.ACTION_STATE_CHANGED.equals(action)) {
            int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, -1);
            switch (state) {
                //--> 蓝牙开启
                case BluetoothAdapter.STATE_ON:
                    AnFileLog.e("ble-scan", "BLEConnector::BluetoothState STATE_ON 蓝牙开启");
                    /*if (isClose) {
                        isClose = false;*/
                        new Handler(Looper.getMainLooper()).post(() -> {
                            if (isScan) {
                                AnFileLog.e("ble-scan", "startScan - ACTION_STATE_CHANGED");
                             /*   List<ScanFilter> list = new ArrayList<>();
                                ScanFilter scanFilterName = new ScanFilter.Builder().setServiceUuid(new ParcelUuid(new UUID(0xf56d4814aac0c56aL, 0xfc87cd4d6980d247L))).build();
                                list.add(scanFilterName);
                                scanFilterName = new ScanFilter.Builder().setDeviceName("SAIC_BLE").build();
                                list.add(scanFilterName);
                                bluetoothLeScanner.startScan(list, scanSettings, scanCallback);*/
                                startBle();
                            }
                        });
//                    }
                    break;
                //--> 蓝牙关闭
                case BluetoothAdapter.STATE_OFF:
                    AnFileLog.e("ble-scan", "BLEConnector::BluetoothState STATE_OFF 蓝牙关闭");
//                    isClose = true;
                    new Handler(Looper.getMainLooper()).post(() -> {
                        if (isScan) {
                            AnFileLog.e("ble-scan", "stopScan - ACTION_STATE_CHANGED");
                            //--> fixme:补充显示调用关掉扫描    如果蓝牙没打开 调用关闭扫描接口会崩溃
//                            bluetoothLeScanner.stopScan(scanCallback);
                           if(listener != null) {
                               listener.onError(2);
                           }
                        }
                    });
                    break;
            }
        }
    }

    static final long switch_state_interval = 100; //连接服务的准备时间

    /**
     * 上下文，WeLinkService
     */
    Context context;

    /**
     * ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓蓝牙ble api组 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
     */
    BluetoothManager bluetoothManager;
    BluetoothAdapter bluetoothAdapter;
    /**
     * ble 设备扫描
     */
    BluetoothLeScanner bluetoothLeScanner;
    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
    /**
     * 车机端ble Advertising表示
     */
    private final ParcelUuid huBltAdvertisingUuid = new ParcelUuid(new UUID(0xf56d4814aac0c56aL, 0xfc87cd4d6980d247L));

    String signature;
    Integer timeout = 5;

    /**
     * she tips:暂时为null
     */
    @Deprecated
    List<ScanFilter> bleDeviceFilterList;

    /**
     * ble是否处于scan中心设备（车机）
     */
    boolean isScan = false;
    boolean isConnecting = false;
    final Object isConnectingLock = new Object();
    boolean isClose = false;

    /**
     * 用于ble数据通信gatt api
     */
    BluetoothGatt bluetoothGatt = null;

    /**
     * welink_version 获取版本号信息
     */
    BluetoothGattCharacteristic welink_version = null;
    /**
     * welink_type 获取hu版本类型
     */
    BluetoothGattCharacteristic welink_type = null;

    /**
     * 用于将状态回调给外部
     */
    WLHardwareHubListener listener;
    /**
     * 用于将获取的 ap 名称和密码回调给外部
     */
    WLAPConfigListener wlapConfigListener;

    final Object listenerLock = new Object();

    //    List<String> connectedDevices = new ArrayList<>();
    final Object connectedDevicesLock = new Object();
    HashSet<BluetoothGatt> gattHashSet = new HashSet<>();
    Timer timer = new Timer();
    final Object timerLock = new Object();
    TimerTask timerTask;


    /**
     * ble scan 扫描信号阀值
     */
    Integer rssi = -70;
    final Object rssiLock = new Object();
//    long heartbeatTimeout;


    /**
     * 蓝牙ble广播san 方式
     */
    ScanSettings scanSettings = new ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE)//高频扫描
            .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
            .build();

    /**
     * 扫描结果 scanCallback
     * she tips:系统会在主线程执行回调
     */
    ScanCallback scanCallback = new ScanCallback() {
        /**
         * 应用了单次扫描方式，如果扫描次数首先可以尝试批量扫描方式
         */
        @Override
        public void onScanResult(int callbackType, final ScanResult result) {
            super.onScanResult(callbackType, result);

            AnFileLog.e("ble-scan", "BLEConnector::onScanResult device discovered callback - 1 ScanResult : " + result.getDevice());
            //--> start ↓↓↓↓↓目前此处为 bleDeviceFilterList = null，历史遗留逻辑，暂时保留，后续考虑移除
            if (BLEConnector.this.bleDeviceFilterList != null) {
                final String deviceAddress = result.getDevice().getAddress();
                boolean bFind = false;
                for (ScanFilter filter : BLEConnector.this.bleDeviceFilterList) {
                    if (filter.getDeviceAddress() != null &&
                            deviceAddress.equals(filter.getDeviceAddress())) {
                        bFind = true;
                        break;
                    }
                }
                if (!bFind) {
                    AnFileLog.e("ble-scan", "device discovered callback - 2");
                    return;
                }
            }
            //--> end ↑↑↑↑↑↑↑

            AnFileLog.e("ble-scan", "BleConnector::onScanResult::device discovered, connecting:" + isConnecting
                    + ", scanning: " + isScan
                    + ", gatt: " + (bluetoothGatt != null)
                    + ", getRssi:" + result.getRssi());

            //--> step1 处理一系列flag，不符合return掉
            //--> 连接中，不重复进行连接
            synchronized (isConnectingLock) {
                if (isConnecting || !isScan || bluetoothGatt != null) return;
                AnFileLog.e("ble-scan", "BLEConnector::onScanResult callback: " + this);
                isConnecting = true;
            }

            //--> 签名null 无法进行身份识别
            if (signature == null) {
                AnFileLog.e("ble-scan", "BLEConnector::onScanResult signature is null");
                isConnecting = false;
                return;
            }
            //--> 蓝牙设备信号强度太弱，舍弃
            if (result.getRssi() < rssi) {
                AnFileLog.e("ble-scan", "BLEConnector::onScanResult rssi is low, device:" + result.getRssi() + ", filter:" + rssi);
                isConnecting = false;
                return;
            }
            //--》step2 状态回调
            AnFileLog.e("ble-scan", "BLEConnector::onScanResult device connecting");
            synchronized (listenerLock) {
                if (listener != null) {
                    listener.onStatusChanged(1, "" + result.getRssi());
                }
            }

            //--》step3 连接到蓝牙ble服务（车机端）；蓝牙Gatt协议
            //--》蓝牙ble握手流程
            //  * 调用顺序流：
            //   --> onConnectionStateChange 连接成功
            //   --> gatt.discoverServices() 寻找gatt服务
            //   --> onServicesDiscovered 找到服务
            //   --> gatt.readCharacteristic(welink_version) 从车机ble Service读取数据
            //   --> onCharacteristicRead
            //   --> gatt.writeCharacteristic(welink_type);
            //   --> onCharacteristicWrite
            // she tips: 改回调Framework会在子线程执行
            final BluetoothGattCallback callback = new BluetoothGattCallback() {

                private Object object = new Object();
                @SuppressLint("MissingPermission")
                @Override
                public void onConnectionStateChange(final BluetoothGatt gatt, int status, int newState) {
                    super.onConnectionStateChange(gatt, status, newState);

                    AnFileLog.e("ble-scan", "BLEConnector::onScanResult::onConnectionStateChange this = " + object);
                    //-->容错处理，只处理当前业务bluetoothGatt，滤掉其他
                    if (gatt != bluetoothGatt) {
                        if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                            AnFileLog.e("ble-scan", "BLEConnector::onConnectionStateChange ble-disconnect-0: " + gatt);
//                            gattList.remove(gatt);
                            gatt.close();
                        }
                        return;
                    }

                    //--> gatt 握手成功
                    if (status == BluetoothGatt.GATT_SUCCESS && newState == BluetoothProfile.STATE_CONNECTED) {
                        //--> 停掉5s超时
                        stop_timeout();
                        AnFileLog.e("ble-scan", "BLEConnector::onConnectionStateChange device connected: " + gatt);
                        synchronized (listenerLock) {
                            if (listener != null) {
                                listener.onStatusChanged(2, gatt.getDevice().getAddress());
                            }
                        }

                        //--> 发现BLE服务
                        Runnable runnable = () -> {
                            synchronized (rssiLock) {
                                if (context != null && signature != null) {
                                    AnFileLog.e("ble-scan", "BLEConnector::onConnectionStateChange service connecting");
                                    synchronized (listenerLock) {
                                        if (listener != null) {
                                            listener.onStatusChanged(3, null);
                                        }
                                    }

                                    gatt.discoverServices();
                                }
                            }
                        };
                        new Handler(Looper.getMainLooper()).postDelayed(runnable, switch_state_interval);
                        //-->开启超时
                        start_timeout(timeout * 1000 + switch_state_interval);

                        return;
                    }

                    AnFileLog.e("ble-scan", "BLEConnector::onConnectionStateChange device connect failed");
                    stop_timeout();

                    new Handler(Looper.getMainLooper()).postDelayed(BLEConnector.this::disconnect_gatt, switch_state_interval);

                    synchronized (listenerLock) {
                        if (listener != null && bluetoothGatt == gatt) {
                            listener.onStatusChanged(-1, null);
                        }
                    }
                }

                /**
                 * 发现
                 * @param gatt GATT client invoked {@link BluetoothGatt#discoverServices}
                 * @param status {@link BluetoothGatt#GATT_SUCCESS} if the remote device has been explored
                 * successfully.
                 */
                @SuppressLint("MissingPermission")
                @Override
                public void onServicesDiscovered(final BluetoothGatt gatt, int status) {
                    super.onServicesDiscovered(gatt, status);
                    AnFileLog.e("ble-scan", "BLEConnector::onScanResult::onServicesDiscovered this = " + object);

                    if (gatt != bluetoothGatt) {
                        AnFileLog.e("ble-scan", "BLEConnector::onServicesDiscovered ble-disconnect-2: " + gatt);
                        return;
                    }

                    if (status == BluetoothGatt.GATT_SUCCESS) {
                       //申请修改发送数据长度 最多512字节 ，默认23字节  需要蓝牙BLUETOOTH_CONNECT权限
                        gatt.requestMtu(100);
                        //-->fixme 全局静态uuid 提取
                        //-->获取车机端welink用于握手的ble gatt service
                        BluetoothGattService welink_service = gatt.getService(new UUID(0xf56d4814aac0c56aL, 0xfc87cd4d6980d247L));
                        if (welink_service != null) {
                            //--> 获取character welink_version；welink_type
                            welink_version = welink_service.getCharacteristic(new UUID(0xcc1dffbf73ff17adL, 0xaf07e625edd8279fL));
                            welink_type = welink_service.getCharacteristic(new UUID(0x7bdfd2e5d7b5c787L, 0x96dfd286a1f14b41L));
                            BluetoothGattCharacteristic welink_ap = welink_service.getCharacteristic(new UUID(0x7a2cd5815fd76c3dL, 0xef20b2885c75c510L));
                            if (welink_version != null && welink_type != null && welink_ap != null) {
                                //--> 服务及其Characteristic 获取成功,停止超时计时
                                stop_timeout();

                                AnFileLog.e("ble-scan", "BLEConnector::onServicesDiscovered service connected");
                                synchronized (listenerLock) {
                                    if (listener != null) {
                                        listener.onStatusChanged(4, null);
                                    }
                                }

                                //-->读取版本号
                                @SuppressLint("MissingPermission") Runnable runnable = () -> {
                                    synchronized (rssiLock) {
                                        if (context != null && signature != null) {
                                            AnFileLog.e("ble-scan", "BLEConnector::onServicesDiscovered version reading");
                                            synchronized (listenerLock) {
                                                if (listener != null) {
                                                    listener.onStatusChanged(5, null);
                                                }
                                            }

//                                            gatt.readCharacteristic(welink_version);
                                            gatt.readCharacteristic(welink_ap);
                                        }
                                    }
                                };
                                new Handler(Looper.getMainLooper()).postDelayed(runnable, switch_state_interval);
                                //-->开启超时计时
                                start_timeout(timeout * 1000 + switch_state_interval);

                                return;
                            }
                        }
                    }

                    AnFileLog.e("ble-scan", "BLEConnector::onServicesDiscovered service connect failed");
                    stop_timeout();

                    new Handler(Looper.getMainLooper()).postDelayed(BLEConnector.this::disconnect_gatt, switch_state_interval);

                    synchronized (listenerLock) {
                        if (listener != null) {
                            listener.onStatusChanged(-2, null);
                        }
                    }
                }

                @SuppressLint("MissingPermission")
                @Override
                public void onCharacteristicRead(final BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
                    super.onCharacteristicRead(gatt, characteristic, status);
                    AnFileLog.e("ble-scan", "BLEConnector::onScanResult::onCharacteristicRead this = " + object);
                    if (gatt != bluetoothGatt) {
                        if (status != BluetoothGatt.GATT_SUCCESS) {
                            AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicRead ble-disconnect-3: " + gatt);
                            gatt.close();
                        } //else {
//                            String version = characteristic.getStringValue(0);
//                            if (version != null) {
//                                String[] v = version.split(",");
//                                if (v.length >= 4 && v[0].equals("v") && v[1].equals("2")) {
//                                    synchronized (connectedDevicesLock) {
//                                        if (!connectedDevices.contains(v[3]) && (System.currentTimeMillis() - heartbeatTimeout) > timeout * 1000) {
//                                            AnFileLog.e("ble-scan", "ble-disconnect-1: " + gatt);
//                                            gatt.close();
//                                        }
//                                    }
//                                }
//                            }
//                        }

                        return;
                    }

                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        String version = characteristic.getStringValue(0);
                        if (version != null) {
                            //--> 获取到版本相关字段,停止超时计时
                            stop_timeout();


                            String[] v = version.split(",");
                            AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicRead version: " + wlapConfigListener+",v.length="+v.length+",v[1]="+v[1]+",v[2]="+v[2]);
                            if (v.length >= 3 && v[0].equals("w") ) {
                                 if(wlapConfigListener != null){
                                     wlapConfigListener.onNetWorkConfig(v[1],v[2]);
                                 }
                            }
                            //--> fixme:意义不明的容错，后续回头check
//                            if (v.length >= 4 && v[0].equals("v") && v[1].equals("2")) {
//                                AnFileLog.e("ble-scan", "repeating-wifi-check: " + v[3]);
//                                synchronized (connectedDevicesLock) {
//                                    if (connectedDevices.contains(v[3])) {
//                                        AnFileLog.e("ble-scan", "repeating-wifi");
//                                        Runnable runnable = new Runnable() {
//                                            final BLEConnector connector = BLEConnector.this;
//                                            final BluetoothGattCharacteristic version = welink_version;
//
//                                            @Override
//                                            public void run() {
//                                                synchronized (connectedDevicesLock) {
//                                                    if (connector.signature == null || connector != BLEConnector.this) {
//                                                        //解决某些手机重复kill、start app后，对象被复用导致的问题
//                                                        AnFileLog.e("ble-scan", "ble-disconnect-x: " + gatt);
//                                                        gatt.close();
//                                                        return;
//                                                    }
//                                                }
//
//                                                AnFileLog.e("ble-scan", "ble-heartbeat" + gatt);
//
//                                                gattList.add(gatt);
//                                                if (!gatt.readCharacteristic(version)) {
//                                                    AnFileLog.e("ble-scan", "ble-disconnect-2: " + gatt);
//                                                    gattList.remove(gatt);
//                                                    gatt.close();
//                                                    return;
//                                                }
//
//                                                new Handler(Looper.getMainLooper()).postDelayed(this, 1000);
//                                            }
//                                        };
//                                        new Handler(Looper.getMainLooper()).postDelayed(runnable, 1000);
//
//                                        bluetoothGatt = null;
//                                        disconnect_gatt();
//                                        return;
//                                    }
//                                }
//                            } else if (v.length >= 2 && v[0].equals("v") && v[1].equals("1")) {
//                                if (connectedDevices.contains("WeLink")) {
//                                    bluetoothGatt = null;
//                                    disconnect_gatt();
//                                    return;
//                                }
//                            }

                            AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicRead version: " + version);
                            synchronized (listenerLock) {
                                if (listener != null) {
                                    listener.onStatusChanged(6, version);
                                }
                            }

                            Runnable runnable = () -> {
                                synchronized (rssiLock) {
                                    if (context != null && signature != null) {
                                        AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicRead signature writing");
                                        synchronized (listenerLock) {
                                            if (listener != null) {
                                                listener.onStatusChanged(7, null);
                                            }
                                        }
                                        if(wlapConfigListener != null && v.length >=3){
                                            //走车机热点连接
                                            welink_type.setValue("a,"+v[1]+","+v[2] + ",50"+"," + signature);
                                        }else{
                                            welink_type.setValue("o," + signature + ",50");
                                        }
                                        gatt.writeCharacteristic(welink_type);
                                    }
                                }
                            };
                            new Handler(Looper.getMainLooper()).postDelayed(runnable, switch_state_interval);
                            start_timeout(timeout * 1000 + switch_state_interval);
                            return;
                        }
                    }

                    AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicRead version read failed");
                    stop_timeout();

                    new Handler(Looper.getMainLooper()).postDelayed(BLEConnector.this::disconnect_gatt, switch_state_interval);

                    synchronized (listenerLock) {
                        if (listener != null) {
                            listener.onStatusChanged(-3, null);
                        }
                    }
                }

                @Override
                public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
                    super.onCharacteristicWrite(gatt, characteristic, status);
                    AnFileLog.e("ble-scan", "BLEConnector::onScanResult::onCharacteristicWrite this = " + object);

                    if (gatt != bluetoothGatt) {
                        return;
                    }

                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        stop_timeout();

                        AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicWrite signature: " + signature);
                        synchronized (listenerLock) {
                            if (listener != null) {
                                listener.onStatusChanged(8, signature);
                            }
                        }
                        //--> fixme: 心跳逻辑
                        Runnable runnable = new Runnable() {
                            final BLEConnector connector = BLEConnector.this;
                            final BluetoothGattCharacteristic welinkType = welink_type;

                            @SuppressLint("MissingPermission")
                            @Override
                            public void run() {
                                synchronized (connectedDevicesLock) {
                                    if (connector.signature == null || connector != BLEConnector.this) {
                                        //解决某些手机重复kill、start app后，对象被复用导致的问题
                                        AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicWrite ble-disconnect-x: " + gatt);
                                        gatt.close();
                                        return;
                                    }
                                }

                                AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicWrite ble-heartbeat" + gatt+",welink_type:"+welink_type.getStringValue(0));
                                //welinkType 已写入值，无需重新设置
                               // welinkType.setValue("o," + signature + ",50");
                                if (!gatt.writeCharacteristic(welinkType)) {
                                    AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicWrite ble-disconnect-2: " + gatt);
                                    gattHashSet.remove(gatt);
                                    gatt.close();
                                    return;
                                }
                                //--> 心跳包
                                new Handler(Looper.getMainLooper()).postDelayed(this, 1000);
                            }
                        };

                        if(!gattHashSet.contains(gatt)){
                            //--> hash set 保证不重复
                            gattHashSet.add(gatt);
                            new Handler(Looper.getMainLooper()).postDelayed(runnable, 1000);
                        }


                        //--> 完成身份识别，断开连接
                        bluetoothGatt = null;
//                        disconnect_gatt();
                    } else {
                        AnFileLog.e("ble-scan", "BLEConnector::onCharacteristicWrite signature write failed");
                        stop_timeout();

                        new Handler(Looper.getMainLooper()).postDelayed(BLEConnector.this::disconnect_gatt, switch_state_interval);

                        synchronized (listenerLock) {
                            if (listener != null) {
                                listener.onStatusChanged(-4, signature);
                            }
                        }
                    }
                }
            };

            //--> 与中心设备通过gatt协议连接
            @SuppressLint("MissingPermission") Runnable runnable = () -> {
                bluetoothGatt = result.getDevice().connectGatt(context, false, callback);
                AnFileLog.e("ble-scan", "BleConnector::onScanResult -> device connecting bluetoothGatt = " + bluetoothGatt.toString());
            };
            new Handler(Looper.getMainLooper()).postDelayed(runnable, switch_state_interval);
            //--》step4 添加超时定时器
            start_timeout(timeout * 1000);
        }
    };

    /**
     * 扫描车机端蓝牙设备
     *
     * @param context
     * @param signature           用于传输通道连接的wifi direct group owner的签名signature
     * @param rssi                扫描设备信号强度
     * @param timeout             超时
     * @param bleDeviceFilterList 蓝牙设备过滤器
     */
    protected BLEConnector(final Context context, final String signature, final Integer rssi, final Integer timeout, final List<ScanFilter> bleDeviceFilterList) {
        if (rssi < 0) {
            this.rssi = rssi;
        }
        //--> 参数赋值
        this.context = context;
        this.signature = signature;

        if (timeout > 0) {
            this.timeout = timeout;
        }
        //-->she tips:暂时未使用
        this.bleDeviceFilterList = bleDeviceFilterList;
        //--> ble 相关api 引用
        bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        if (bluetoothManager == null) {
            AnFileLog.e("ble-scan", "BLEConnector::<init>bluetoothManager is null");
            return;
        }
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        if (bluetoothAdapter == null){
            AnFileLog.e("ble-scan", "BLEConnector::<init>bluetoothAdapter is null");
            return;
        }
        int state = bluetoothAdapter.getState();
        if(BluetoothAdapter.STATE_ON != state){
            AnFileLog.e("ble-scan", "BLEConnector:: BluetoothState <init>蓝牙未开启" );
        }
        //-->监听蓝牙状态
        IntentFilter bleStatusFilter = new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED);
        context.registerReceiver(this, bleStatusFilter);
        AnFileLog.e("ble-scan", "BLEConnector::<init> 注册蓝牙广播");
        isScan = true;
        startBle();
    }

    /**
     * 开启蓝牙扫描
     *
     * @param context
     * @param signature
     * @param rssi
     * @param timeout
     * @param bleDeviceFilterList
     * @param listener
     * @return
     */
    static public BLEConnector start(Context context, final String signature, final Integer rssi, final Integer timeout, final List<ScanFilter> bleDeviceFilterList, @NonNull final WLHardwareHubListener listener) {
        BLEConnector bleConnector = new BLEConnector(context, signature, rssi, timeout, bleDeviceFilterList);
        bleConnector.listener = listener;
        return bleConnector;
    }
    static public BLEConnector start(Context context, final String signature, final Integer rssi, final Integer timeout, final List<ScanFilter> bleDeviceFilterList, @NonNull final WLHardwareHubListener listener, WLAPConfigListener wlapConfigListener) {
        BLEConnector bleConnector = new BLEConnector(context, signature, rssi, timeout, bleDeviceFilterList);
        bleConnector.listener = listener;
        bleConnector.wlapConfigListener = wlapConfigListener;
        return bleConnector;
    }
    /**
     * 开始蓝牙扫描
     */
    @SuppressLint("MissingPermission")
    private void startBle() {
        //--> ble 扫描设备
        bluetoothLeScanner = bluetoothAdapter.getBluetoothLeScanner();
        if (bluetoothLeScanner == null) {
            AnFileLog.e("ble-scan", "BLEConnector::<init> bluetoothLeScanner is null");
            if(listener != null) {
                listener.onError(2);
            }
            return;
        }

        AnFileLog.e("ble-scan", "BLEConnector::::<init> 完成赋值初始化 " + this);
        //蓝牙广播扫描过滤器条件（条件之间|或的关系）
        List<ScanFilter> list = new ArrayList<>();

        //-->条件1 uuid，这里与车机保持一致
        ScanFilter scanFilterName = new ScanFilter.Builder().setServiceUuid(huBltAdvertisingUuid).build();
        list.add(scanFilterName);
        //-->条件2 device name
        //she tips:暂未使用
        scanFilterName = new ScanFilter.Builder().setDeviceName("SAIC_BLE").build();
        list.add(scanFilterName);

        //-->开启扫描 结果通过scanCallback返回
        bluetoothLeScanner.startScan(list, scanSettings, scanCallback);
        AnFileLog.e("ble-scan", "BLEConnector::<init> 开启ble 扫描 startScan" + this);
    }
    public void updateSignature(final String signature) {
        this.signature = signature;
    }


    /**
     * she tips:暂时废弃
     *
     * @param connectedDevices
     */
    @Deprecated
    public void resetFilter(final List<String> connectedDevices) {
//        synchronized (connectedDevicesLock) {
//            this.connectedDevices.clear();
//            this.connectedDevices.addAll(connectedDevices);
//        }
    }

    @SuppressLint("MissingPermission")
    public void stop() {
        AnFileLog.printStackTraceString("shecw5", "BLEConnector::stop()");
        try {
            context.unregisterReceiver(this);
        } catch (Exception e) {
            AnFileLog.e("ble-scan", "stopScan - e:" + e.getMessage());
        }
        synchronized (rssiLock) {
            Iterator<BluetoothGatt> iterator = gattHashSet.iterator();
            while (iterator.hasNext()) {
                BluetoothGatt gatt = iterator.next();
                gatt.close();
                iterator.remove(); // 安全移除元素
            }

            disconnect_gatt();
            isScan = false;
            AnFileLog.e("ble-scan", "BLEConnector::stopScan() - stop");
            try {
                if(bluetoothLeScanner != null) {
                    bluetoothLeScanner.stopScan(scanCallback);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            signature = null;
            context = null;
        }
        listener = null;
        wlapConfigListener = null;
    }

    void start_timeout(final long timeout) {
        synchronized (timerLock) {
            timerTask = new TimerTask() {
                @Override
                public void run() {
                    AnFileLog.e("ble-scan", "timeout");

                    synchronized (listenerLock) {
                        if (listener != null) {
                            listener.onStatusChanged(-5, null);
                        }
                    }
                    disconnect_gatt();
                }
            };

            timer.schedule(timerTask, timeout);
        }
    }

    void stop_timeout() {
        synchronized (timerLock) {
            if (timerTask != null) {
                timerTask.cancel();
                timerTask = null;
            }
        }
    }

    @SuppressLint("MissingPermission")
    void disconnect_gatt() {
        AnFileLog.e("ble-scan", "disconnect_gatt-------");
        isConnecting = false;
        if (bluetoothGatt != null) {
            bluetoothGatt.close();
            bluetoothGatt = null;
        }
        welink_type = null;
        welink_version = null;
    }
}
