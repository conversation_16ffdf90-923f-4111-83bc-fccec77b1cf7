package com.autoai.welink.wireless.utiliy;

import java.util.Random;

/**
 * <AUTHOR> zhanggc
 * @version : 1.0
 * @description : java类作用描述
 * @date : 2025/6/18 15:03
 */
public class Util {
    public static String getRandomString() {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < 6; i++) {
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }

        return sb.toString();
    }
}
