package com.autoai.welink.wireless.wifiap;

import android.bluetooth.le.ScanFilter;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.net.wifi.WifiNetworkSpecifier;
import android.text.TextUtils;

import com.autoai.welink.wireless.WLAPConfigListener;
import com.autoai.welink.wireless.WLAPConnectListener;
import com.autoai.welink.wireless.WLHardwareHubListener;
import com.autoai.welink.wireless.ble.BLEConnector;
import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.utiliy.Util;

import java.util.List;

/**
 * <AUTHOR> zhanggc
 * @version : 1.0
 * @description : 通过名称和密码 连接车机热点
 * @date : 2025/6/17 9:47
 */
public class WifiConnectHU {
    private static final String TAG = "WifiConnect";
    private String signature;
    private Context context;
    /**
     * ble 连接握手模组
     */
    private BLEConnector bleConnector = null;
    /**
     * 是否处于连接网络中
     */
    private boolean isConnecting;
    private ConnectivityManager connectivityManager;
    private ConnectivityManager.NetworkCallback networkCallback;

    /**
     * 蓝牙ble 强度
     */
    Integer rssi;
    Integer timeout;
    /**
     * 蓝牙扫描过滤条件
     */
    List<ScanFilter> bleDeviceFilterList;
    WLAPConnectListener listener;

    private WifiConnectHU(final Context context, String netWorkName,String passphrase,WLAPConnectListener listener, final Integer rssi, final Integer timeout, final List<ScanFilter> bleDeviceFilterList) {
        this.context = context;
        this.listener = listener;
        this.rssi = rssi;
        this.timeout = timeout;
        this.bleDeviceFilterList = bleDeviceFilterList;
        isConnecting = false;
        signature = Util.getRandomString();
        connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        AnFileLog.e(TAG, "WifiConnectHU:init --> networkName: " + netWorkName + ",password:" + passphrase);
        if(!TextUtils.isEmpty(netWorkName) && !TextUtils.isEmpty(passphrase)){
            //后台互联 直接走网络连接逻辑
            requestApConnect(netWorkName, passphrase);
        }else {
            startBle();
        }
    }

    public void startBle() {
        AnFileLog.e(TAG, "WifiConnectHU:startBle----");
       stopBle();
        bleConnector = BLEConnector.start(context, getSignature(), rssi, timeout, bleDeviceFilterList, null, new WLAPConfigListener() {
            @Override
            public void onNetWorkConfig(String netWorkName, String passphrase) {
                AnFileLog.e(TAG, "WifiConnectHU:networkName: " + netWorkName + ",password:" + passphrase + ",isConnecting=" + isConnecting);
                if (!isConnecting) {
                    requestApConnect(netWorkName, passphrase);
                }
            }
        });
    }

    public static WifiConnectHU init(final Context context, String netWorkName,String passphrase, WLAPConnectListener listener, final Integer rssi, final Integer timeout, final List<ScanFilter> bleDeviceFilterList) {
        return new WifiConnectHU(context,netWorkName,passphrase, listener, rssi, timeout, bleDeviceFilterList);
    }

    private void requestApConnect(String networkName, String password) {
        if (TextUtils.isEmpty(networkName) || TextUtils.isEmpty(password)) {
            return;
        }
        if (connectivityManager == null) {
            AnFileLog.e(TAG, "WifiConnectHU:connectivityManager is null");
            return;
        }
        isConnecting = true;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            WifiNetworkSpecifier.Builder builder = new WifiNetworkSpecifier.Builder();

            WifiNetworkSpecifier wifiNetworkSpecifier = builder.setSsid(networkName)
                    .setWpa2Passphrase(password)
                    .build();

            NetworkRequest networkRequest = new NetworkRequest.Builder()
                    .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                    .removeCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    .addCapability(NetworkCapabilities.NET_CAPABILITY_TRUSTED)
                    .setNetworkSpecifier(wifiNetworkSpecifier)
                    .build();
            if (networkCallback == null) {
//                AnFileLog.e(TAG, "networkCallback is  null");
//                connectivityManager.unregisterNetworkCallback(networkCallback);

                //网络连接回调
                networkCallback = new ConnectivityManager.NetworkCallback() {
                    @Override
                    public void onAvailable(Network network) {
                        // 绑定到热点网络
//                cm.bindProcessToNetwork(network);
                        AnFileLog.e(TAG, "WifiConnectHU:onAvailable Connected to network: " + network);
//                    isConnecting = false;
                        if (listener != null) {
                            listener.onConnectionSuccess( networkName,  password);
                        }
                    }

                    @Override
                    public void onUnavailable() {
                        super.onUnavailable();
                        //加入热点失败重新开启蓝牙扫描
                        startBle();
                        isConnecting = false;
                        AnFileLog.e(TAG, "WifiConnectHU:onUnavailable: onFile");
                    }

                    @Override
                    public void onLost(Network network) {
                        AnFileLog.e(TAG, "WifiConnectHU:Lost connection to network: " + network);
                        isConnecting = false;
                    }
                };
            }
            connectivityManager.requestNetwork(networkRequest, networkCallback);

        } else {
            AnFileLog.e(TAG, "WifiConnectHU:android 版本 小于 29 ，不支持ap 热点连接");
        }
    }


    public String getSignature() {
        return signature;
    }

    /**
     * 停止蓝牙扫描连接车机设备，
     */
    public void stopBle() {
        if (bleConnector != null) {
            AnFileLog.e(TAG, "WifiConnectHU:stopBle----");
            bleConnector.stop();
            bleConnector = null;
        }
    }

    public void release() {
        stopBle();
        AnFileLog.e(TAG, "release----");
        if (connectivityManager != null && networkCallback != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            }catch (Exception e){
                AnFileLog.e(TAG, "release---Error-e:"+e.getMessage());
            }
            networkCallback = null;
            connectivityManager = null;
        }
        context = null;
    }
}
