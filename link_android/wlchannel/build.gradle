apply plugin: 'com.android.library'

//apply from: "${project.rootDir}/library.gradle"
//apply from: "${project.rootDir}/maven_push.gradle"

apply from: "../library.gradle"
apply from: "../versions.gradle"
apply from: "../maven_push.gradle"

android {
    defaultConfig {
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a"
        }
    }


    buildTypes {
        release {
            externalNativeBuild {
                cmake {
                    cFlags "-s"
                    cppFlags "-s"
                    arguments "-DCMAKE_BUILD_TYPE=Release"
                }
            }
        }

        debug {
            packagingOptions {
                doNotStrip "*/armeabi-v7a/*.so"
                doNotStrip "*/arm64-v8a/*.so"
            }
        }
    }
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
        }
    }
}

dependencies {
    implementation 'androidx.annotation:annotation:1.2.0'
}

