/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_autoai_welink_channel_WLChannel */

#ifndef _Included_com_autoai_welink_channel_WLChannel
#define _Included_com_autoai_welink_channel_WLChannel
#ifdef __cplusplus
extern "C" {
#endif
#undef com_autoai_welink_channel_WLChannel_H264_TIMEOUT
#define com_autoai_welink_channel_WLChannel_H264_TIMEOUT 1000LL
/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    init_weLink
 * Signature: (Ljava/lang/String;Lcom/autoai/welink/channel/WLChannel/WeLinkCB;)V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_init_1weLink
  (JNIEnv *, jclass, jstring, jstring, jobject);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    config_weLink
 * Signature: (IIII)V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_config_1weLink
  (JNIEnv *, jclass, jint, jint, jint, jint);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    deinit_weLink
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_deinit_1weLink
  (JNIEnv *, jclass);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    reset_weLink
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_reset_1weLink
  (JNIEnv *, jclass);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    send_pcm
 * Signature: ([B)V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1pcm
  (JNIEnv *, jclass, jbyteArray);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    send_h264
 * Signature: ([B)V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1h264
  (JNIEnv *, jclass, jbyteArray);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    send_message
 * Signature: ([B)V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1message
  (JNIEnv *, jclass, jbyteArray);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    send_car_data
 * Signature: ([B)V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1car_1data
  (JNIEnv *, jclass, jbyteArray);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    get_version
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_autoai_welink_channel_WLChannel_get_1version
  (JNIEnv *, jclass);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    start_record
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_start_1record
  (JNIEnv *, jclass);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    stop_record
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_stop_1record
  (JNIEnv *, jclass);

/*
 * Class:     com_autoai_welink_channel_WLChannel
 * Method:    enable_log
 * Signature: (ZLjava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_enable_1log
  (JNIEnv *, jclass, jboolean, jstring,jint level);

#ifdef __cplusplus
}
#endif
#endif
