#ifndef WELINK_SDK_API_H
#define WELINK_SDK_API_H

#ifdef __cplusplus
extern "C" {
#endif

#define WL_ERR_NONE		0			/**< success to process  */
#define WL_ERR_FAILED	-1			/**< fail to process  */
#define WL_ERR_PARAM	-2			/**< parameter error  */

#define WL_IMEI_LEN				64		/**< imei length */
#define WL_BT_ADDR_LEN			20		/**< bluetooth address length */
#define WL_GUID_LEN				64		/**< guid length */
#define WL_EQUIPMENT_LEN		64		/**< equipment identifier length */
#define WL_HU_VERSION_LEN		16		/**< head unit version length */
#define WL_PLATFORM_LEN			16		/**< head unit platform length */
#define WL_MAX_LEN				256		/**< parameter max length */

/**
 * @brief Define the sdk status.
 */
typedef enum {
	SDK_STATUS_READY,					/**< sdk ready state */
	SDK_STATUS_CONNECTING,				/**< connecting status */
	SDK_STATUS_CONNECTED,				/**< connected status */
	SDK_STATUS_CONNECT_ERR,				/**< conncet error status */
	SDK_STATUS_MAX_NUM					/**< the number of status */
}welinkSdkStatus;

/**
 * @brief Define the sdk status error type.
 */
typedef enum {
	SDK_STATUS_ERR_CONNECT_FAILED,				/**< Connect error */
	SDK_STATUS_ERR_HEART_TIMER,					/**< Heart Timer error */
	SDK_STATUS_ERR_SOCKET,						/**< Socket error */
}welinkSdkStatusError;

/**
 * @brief Define the log type.
 */
typedef enum {
	SDK_LOG_TYPE_ERROR,				/**< error level log */
	SDK_LOG_TYPE_WARNING,			/**< warning and above level log */
	SDK_LOG_TYPE_INFORMATION,		/**< information and above level log */
	SDK_LOG_TYPE_DEBUG,				/**< debug and above level log */
	SDK_LOG_TYPE_PROTOCOL,			/**< protocol and above level log */
	SDK_LOG_TYPE_MEMORY,			/**< memory and above level log */
	SDK_LOG_TYPE_LOCK,				/**< lock and above level log */
	SDK_LOG_TYPE_VERBOSE,			/**< verbose and above level log */
	SDK_LOG_TYPE_NONE				/**< donot output log */
}welinkSdkLogType;

/**
  * @brief Define the message channel callback data type.
  */
typedef enum {
	SDK_MSG_CB_DATA_TYPE_JSON,					/**< json data for app */
	SDK_MSG_CB_DATA_TYPE_AUTHORIZATION_DATA		/**< authorization file data */
}welinkSdkMessageChannelCallbackDataType;

/**
  * @brief Define the cardata channel callback data type.
  */
typedef enum {
	SDK_CARDATA_CB_DATA_TYPE_BODY		/**< hu car data */
}welinkSdkCardataChannelCallbackDataType;

/**
  * @brief Define the audio channel callback data type.
  */
typedef enum {
	SDK_AUDIO_CB_DATA_TYPE_PCM		/**< hu audio data */
}welinkSdkAudioChannelCallbackDataType;

/**
  * @brief Define the VR channel callback data type.
  */
typedef enum {
	SDK_VR_CB_DATA_TYPE_RECORD,			/**< hu record data */
	SDK_VR_CB_DATA_TYPE_PROTOCOL		/**< protocol data */
}welinkSdkVRChannelCallbackDataType;

/**
 * @brief Define the sdk message type.
 */
typedef enum {
	SDK_MSG_TYPE_START_SEND_H264,			/**< start send h264 data */
	SDK_MSG_TYPE_STOP_SEND_H264,			/**< stop send h264 data */
	SDK_MSG_TYPE_SINGLE_TOUCH_EVENT,		/**< single touch event */
	SDK_MSG_TYPE_MULTI_TOUCH_EVENT2,		/**< multi touch event(motionevent2) */
	SDK_MSG_TYPE_MULTI_TOUCH_EVENT3, 		/**< multi touch event(motionevent3) */
	SDK_MSG_TYPE_HARD_KEY,					/**< hard key information */
	SDK_MSG_TYPE_FPS_CONFIG,				/**< fps config information */
	SDK_MSG_TYPE_SETTINGAVAIL,				/**< video fps config option */
	SDK_MSG_TYPE_CREATE_CHANNEL,			/**< create independent channel */
	SDK_MSG_TYPE_HU_VERTICAL_FULL			/**< hu vertucal full screen display */
}welinkSdkMessageType;

/**
 * @brief Define the sdk message informaiton.
 */
typedef struct {
	welinkSdkMessageType msgType;			/**< message type */
	int	para1;								/**< message parameter */
	int para2;								/**< message parameter */
	char data[WL_MAX_LEN];					/**< message data */
}welinkSdkMessageInfo;

/**
 * @brief Define the sdk commond type.
 */
typedef enum {
	SDK_CMD_TYPE_FOREGROUND,				/**< switch to foreground */
	SDK_CMD_TYPE_BACKGROUND,				/**< switch to background */
	SDK_CMD_TYPE_START_RECORD,				/**< start record */
	SDK_CMD_TYPE_STOP_RECORD,				/**< stop record */
	SDK_CMD_TYPE_DISCONNECT,				/**< disconnect connection with hu */
	SDK_CMD_TYPE_RESET						/**< reset sdk */
}welinkSdkCommondType;

/**
 * @brief Define the sdk commond.
 */
typedef struct {
	welinkSdkCommondType type;				/**< commond type */		
	int para1;								/**< parameter */
	int para2;								/**< parameter */
	char para3[WL_MAX_LEN];					/**< parameter */
}welinkSdkCommond;

/**
 * @brief Define the touch event type.
 */
typedef enum {
	SDK_TOUCH_EVENT_TYPE_PRESS,				/**< press event */
	SDK_TOUCH_EVENT_TYPE_RELEASE,			/**< release event */
	SDK_TOUCH_EVENT_TYPE_MOVE,				/**< move event */
	SDK_TOUCH_EVENT_TYPE_COMM				/**< common event */
}welinkSdkTouchEventType;

/**
 * @brief Define the multi touch event struct.
 */
typedef struct {
	welinkSdkTouchEventType type;			/**< touch event type */		
	int x;									/**< horizontal coordinate */
	int y;									/**< vertical coordinate */
	int id;									/**< point id, start from 0 */
	int pressure;							/**< touch pressure */
}welinkSdkMultiTouchEvent;

/**
 * @brief Define the single touch event struct.
 */
typedef struct {
	welinkSdkTouchEventType type;			/**< touch event type */		
	int x;									/**< horizontal coordinate */
	int y;									/**< vertical coordinate */
}welinkSdkSingleTouchEvent;

/**
 * @brief Define the message channel data type.
 */
typedef enum {
	SDK_MSG_DATA_TYPE_JSON,					/**< protocol json data */
	SDK_MSG_DATA_TYPE_QRCODE,				/**< qrcode data */
	SDK_MSG_DATA_TYPE_WECHAT_PORTRAIT		/**< wechat protrait data */
}welinkSdkMessageDataType;

/**
 * @brief Define the vr channel data type.
 */
typedef enum {
	SDK_VR_DATA_TYPE_APK,					/**< apk data */
	SDK_VR_DATA_TYPE_PROTOCOL,				/**< protocol json data */
	SDK_VR_DATA_TYPE_UPGRADE				/**< system upgrade data */
}welinkSdkVRDataType;

/**
 * @brief Define the audio channel data type.
 */
typedef enum {
	SDK_AUDIO_DATA_TYPE_PCM					/**< pcm data */
}welinkSdkAudioDataType;

/**
 * @brief Define the video channel data type.
 */
typedef enum {
	SDK_VIDEO_DATA_TYPE_IMAGE,				/**< image data */
	SDK_VIDEO_DATA_TYPE_STREAM,				/**< video data */
	SDK_VIDEO_DATA_TYPE_COMMOND,			/**< commond */
	SDK_VIDEO_DATA_TYPE_NC,					/**< heartbeat NC */
	SDK_VIDEO_DATA_TYPE_NP					/**< heartbeat NP */
}welinkSdkVideoDataType;

/**
   * @brief The callback function of message channel.
   *
   * @param enType: Callback data type defined in welinkSdkMessageChannelCallbackDataType.
   * @param pData: Message channel data from hu.
   * @param iLength: The length of data.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncMessageData)(welinkSdkMessageChannelCallbackDataType enType, void *pData, int iLength);

/**
   * @brief The callback function of cardata channel.
   *
   * @param enType: Callback data type defined in welinkSdkCardataChannelCallbackDataType.
   * @param pData: Cardata channel data from hu.
   * @param iLength: The length of data.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncCarData)(welinkSdkCardataChannelCallbackDataType enType, void *pData, int iLength);

/**
   * @brief The callback function of audio channel.
   *
   * @param enType: Callback data type defined in welinkSdkAudioChannelCallbackDataType.
   * @param pData: Audio channel data from hu.
   * @param iLength: The length of data.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncAudioData)(welinkSdkAudioChannelCallbackDataType enType, void *pData, int iLength);

/**
   * @brief The callback function of VR channel.
   *
   * @param enType: Callback data type defined in welinkSdkVRChannelCallbackDataType.
   * @param pData: VR channel data from hu.
   * @param iLength: The length of data.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncVRData)(welinkSdkVRChannelCallbackDataType enType, void *pData, int iLength);

/**
   * @brief The callback function of video channel.
   *
   * @param pData: video channel data from hu.
   * @param iLength: The length of data.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncVideoData)(void *pData, int iLength);

/**
  * 1秒内，发送视频流数据len之和
 */
typedef void (*pFuncVideoDataTotalLen)(int totalLen, int successedTotalLen);

/**
   * @brief The callback function of sdk status change.
   *
   * @param iStatus: The sdk status defined in welinkSdkStatus.
   * @param iPara1: Parameter1.
   * @param iPara2: Parameter2.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncSdkStatus)(int iStatus, int iPara1, char *iPara2);

/**
   * @brief The callback function of sdk message.
   *
   * @param pMsg: Message.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncSdkMessage)(welinkSdkMessageInfo *pMsg);

/**
   * @brief The callback function of sdk resolution.
   *
   * @param iWidth: Head unit width.
   * @param iHeight: Head unit height.
   * 
   * @return void.
   *  
   */
typedef void (*pFuncSdkHuResolution)(int iWidth, int iHeight);

/**
  * @brief Define the callback function of channels.
  */
typedef struct {
	pFuncMessageData msgData;
	pFuncCarData carData;
	pFuncAudioData audioData;
	pFuncVRData vrData;
	pFuncVideoData videoData;
    pFuncVideoDataTotalLen videoDataTotalLen;
}welinkSdkChannelCallback;

/**
  * @brief Define the callback function of sdk.
  */
typedef struct {
	pFuncSdkStatus callbackSdkStatus;
	pFuncSdkMessage callbackSdkMessage;
	pFuncSdkHuResolution callbackSdkHuResolution;
}welinkSdkCallback;

/**
  * @brief Define the sdk information.
  */
typedef struct {
	int densityDpi;									/**< dpi information */
	bool isSupportVerticalFull;						/**< vertical display in full screen option, false not supported , true supported */
	int huBtAutoConnect;							/**< bluetooth auto connect option, 1 auto connect, 0 not auto connent */
	int huSupportRecord;							/**< record option, 1 supported, 0 not supported */
	int huSupportCall;								/**< hu phone call option, 1 supported, 0 not supported */
	int recordDefault;								/**< default record option, 0 hu record, 1 phone record, 2 bt record */
	int linkMethod;									/**< link method option, 1 usb connect, 2 wifi connect, 3 AOA connect */
	char huPlatform[WL_PLATFORM_LEN];				/**< hu platform */
	char equipmentId[WL_EQUIPMENT_LEN];				/**< equipment identifier */
	char imei[WL_IMEI_LEN];							/**< imei */
	char btAddress[WL_BT_ADDR_LEN];					/**< bluetooth address */
	char guid[WL_GUID_LEN];							/**< guid number */
	char huAppVersion[WL_HU_VERSION_LEN];			/**< hu app version */
	char huSdkVersion[WL_HU_VERSION_LEN];			/**< hu sdk version */
}
welinkSdkInfo;

/**
  * @brief Define the sdk config information.
  */
typedef struct {
	bool isSupportTouchChannel;					/**< touch channel option, true support, false not support */
	int codecMode;								/**< codec mode option, 1 jpeg, 2 video */
	int platformType;							/**< phone platform option, 0 android, 1 ios */
	int systemVersion;							/**< phone system version */
	int logType;								/**< log level type */
	const char *logPath;						/**< path for log file */
	const char *appVersion;						/**< phone app version */
	welinkSdkChannelCallback channelCallback;	/**< channel callback struct */
	welinkSdkCallback sdkCallback;				/**< sdk callback struct */
}
welinkSdkConfig;

/**
  * @brief Define the phone and frame resolution.
  */
typedef struct {
	int phoneWidth;								/**< phone width */
	int phoneHeight;							/**< phone height */
	int frameWidth;								/**< video frame width */
	int frameHeight;							/**< video frame height */
}welinkSdkResolution;

/**
   * @brief Init sdk.
   *
   * @param pConfig: The config informations to initialize sdk.
   * 
   * @return WL_ERR_NONE if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSdkInit(welinkSdkConfig *pConfig);

/**
   * @brief Deinit sdk.
   *
   * @return WL_ERR_NONE if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSdkDeinit(void);

/**
   * @brief Set phone and frame resolution.
   *
   * @param pResolution: The phone and frame resolution.
   * 
   * @return WL_ERR_NONE if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSetResolution(welinkSdkResolution *pResolution);

/**
   * @brief Get sdk informations.
   *
   * @param config: The config informations to initialize sdk.
   * 
   * @return sdk informations if successfully processed, otherwise NULL.
   *  
   */
welinkSdkInfo* welinkGetSdkInfo(void);

/**
   * @brief Get sdk version.
   *
   * @param void.
   * 
   * @return sdk version string.
   * 
   */
const char* welinkGetSdkVersion(void);

/**
   * @brief Send commonds to sdk.
   *
   * @param pCmd: The commonds to control sdk.
   * 
   * @return WL_ERR_NONE if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSendCommond(welinkSdkCommond *pCmd);

/**
   * @brief Send data to sdk message channel.
   *
   * @param enType: Message data type.
   * @param pData: Message data package.
   * @param iLength: The length of message data.
   * 
   * @return The length of data if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSendMessageData(welinkSdkMessageDataType enType, const char *pData, int iLength);

/**
   * @brief Send data to sdk VR channel.
   *
   * @param enType: VR data type.
   * @param pData: VR data package.
   * @param iLength: The length of VR data.
   * 
   * @return The length of data if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSendVRData(welinkSdkVRDataType enType, const char *pData, int iLength);

/**
   * @brief Send data to sdk audio channel.
   *
   * @param enType: Audio data type.
   * @param pData: Audio data package.
   * @param iLength: The length of audio data.
   * 
   * @return The length of data if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSendAudioData(welinkSdkAudioDataType enType, const char *pData, int iLength);

/**
   * @brief Send data to sdk video channel.
   *
   * @param enType: Video data type.
   * @param pData: Video data package.
   * @param iLength: The length of video data.
   * 
   * @return The length of data if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSendVideoData(welinkSdkVideoDataType enType, const char *pData, int iLength);

/**
   * @brief Send data to sdk cardata channel.
   *
   * @param pData: Cardata package.
   * @param iLength: The length of car data.
   * 
   * @return The length of data if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkSendCarData(const char *pData, int iLength);

/**
   * @brief Reset sdk log type and path.
   *
   * @param iType: log type.
   * @param pPath: The path of log storage.
   * 
   * @return WL_ERR_NONE if successfully processed, otherwise WL_ERR_FAILED, WL_ERR_PARAM.
   *  
   */
int welinkResetLogTypeAndPath(int iType, const char *pPath);
#ifdef __cplusplus
}
#endif

#endif /* WELINK_SDK_API_H */
