#include <jni.h>
#include <string>
#include <android/log.h>
#include "./com_autoai_welink_channel_WLChannel.h"
#include "./WeLinkSdkApi/include/WeLinkSdkApi.h"

#define TAG    "wlchannel"

#ifdef NDEBUG
#define LOGD(...)
#else
#define LOGD(...)  __android_log_print(ANDROID_LOG_DEBUG,TAG,__VA_ARGS__)
#endif

struct WeLinkHuInfo {
    const char *vehicleType;
    int huFPS;
    int huSupportMic;
    int huSupportBtPhone;
    const char *huBtMacAddress;
    const char *huPlatform;
    const char *huAppVersion;
};

static JavaVM* g_jvm = nullptr;
static jobject g_cb = nullptr;
static int g_huFPS = 30;

static bool getJniEnv(JavaVM *vm, JNIEnv **env);
static void onReady(JNIEnv *env);
static void onConnecting(JNIEnv *env, int huWidth, int huHeight);
static void onConnected(JNIEnv *env, WeLinkHuInfo *pInfo);
static void onDisconnected(JNIEnv *env);
static void onStartSendH264(JNIEnv *env);
static void onStopSendH264(JNIEnv *env);
static void onMotionEvent(JNIEnv *env, const void *data);
static void onHardKey(JNIEnv *env, const void *data, int len);
static void onMessage(JNIEnv *env, const void *data, int len);
static void onCarData(JNIEnv *env, const void *data, int len);
static void onMicData(JNIEnv *env, const void *data, int len);
static void onAudioData(JNIEnv *env, const void *data, int len);

static void funcSdkStatus(int iStatus, int iPara1, char *iPara2);
static void funcSdkMessage(welinkSdkMessageInfo *pMsg);
static void funcSdkHuResolution(int iWidth, int iHeight);

static void funcMessageData(welinkSdkMessageChannelCallbackDataType enType, void *pData, int iLength);
static void funcCarData(welinkSdkCardataChannelCallbackDataType enType, void *pData, int iLength);
static void funcMicData(welinkSdkVRChannelCallbackDataType enType, void *pData, int iLength);
static void funcAudioData(welinkSdkAudioChannelCallbackDataType enType, void *pData, int iLength);

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_init_1weLink
        (JNIEnv *env, jclass, jstring version, jstring path, jobject cb) {
    LOGD("jni-fun: init_welink");
    env->GetJavaVM(&g_jvm);
    g_cb = env->NewGlobalRef(cb);

    const char *logPath = "";
    if (path != nullptr) {
        logPath = env->GetStringUTFChars(path, nullptr);
    }

    welinkSdkChannelCallback channelCallback;
    memset(&channelCallback,0,sizeof(welinkSdkChannelCallback));
    channelCallback.msgData = funcMessageData;
    channelCallback.carData = funcCarData;
    channelCallback.vrData = funcMicData;
    channelCallback.audioData = funcAudioData;
    channelCallback.videoData = nullptr;

    welinkSdkCallback sdkCallback;
    sdkCallback.callbackSdkStatus = funcSdkStatus;
    sdkCallback.callbackSdkMessage = funcSdkMessage;
    sdkCallback.callbackSdkHuResolution = funcSdkHuResolution;

    const char *ver = "";
    if (version != nullptr) {
        ver = env->GetStringUTFChars(version, nullptr);
    }

    welinkSdkConfig config;
    config.appVersion = ver;
    config.isSupportTouchChannel = true;
    config.codecMode = 2;
    config.platformType = 0;
    config.systemVersion = 23;

#ifdef NDEBUG
    config.logType = SDK_LOG_TYPE_NONE;
#else
    config.logType = SDK_LOG_TYPE_VERBOSE;
#endif


    config.logPath = logPath;
    config.channelCallback = channelCallback;
    config.sdkCallback = sdkCallback;

    LOGD("log path: %s", config.logPath);
    welinkSdkInit(&config);

    if (version != nullptr && ver != nullptr) {
        env->ReleaseStringUTFChars(version, ver);
    }
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_config_1weLink
        (JNIEnv *, jclass, jint screenWidth, jint screenHeight, jint videoWidth, jint videoHeight) {
    LOGD("jni-fun: config_welink");
    welinkSdkResolution resolution;
    resolution.phoneWidth = screenWidth;
    resolution.phoneHeight = screenHeight;
    resolution.frameWidth = videoWidth;
    resolution.frameHeight = videoHeight;

    welinkSetResolution(&resolution);
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_deinit_1weLink
        (JNIEnv *env, jclass) {
    LOGD("jni-fun: deinit_welink");
    welinkSdkDeinit();

    env->DeleteGlobalRef(g_cb);
    g_cb = nullptr;
    g_jvm = nullptr;
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_reset_1weLink
        (JNIEnv *env, jclass) {
    LOGD("jni-fun: reset_welink");
    welinkSdkCommond command;
    command.type = SDK_CMD_TYPE_RESET;
    welinkSendCommond(&command);
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1pcm
        (JNIEnv *env, jclass, jbyteArray data) {
    LOGD("jni-fun: send_pcm");
    jbyte *bytes = env->GetByteArrayElements(data, nullptr);
    int len = env->GetArrayLength(data);

    welinkSendAudioData(SDK_AUDIO_DATA_TYPE_PCM, (char *)bytes, len);

    env->ReleaseByteArrayElements(data, bytes, 0);
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1h264
        (JNIEnv *env, jclass, jbyteArray data) {
    if (data != nullptr) {
        jbyte *bytes = env->GetByteArrayElements(data, nullptr);
        int len = env->GetArrayLength(data);
        LOGD("jni-fun: send_h264 - %d", len);

        welinkSendVideoData(SDK_VIDEO_DATA_TYPE_STREAM, (char *) bytes, len);

        env->ReleaseByteArrayElements(data, bytes, 0);
    } else {
        LOGD("jni-fun: send_heartbeat");
        welinkSendVideoData(SDK_VIDEO_DATA_TYPE_NC, "screen not change", strlen("screen not change") + 1);
    }
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1message
        (JNIEnv *env, jclass, jbyteArray data) {
    LOGD("jni-fun: send_message");
    jbyte *bytes = env->GetByteArrayElements(data, nullptr);
    int len = env->GetArrayLength(data);

    welinkSendMessageData(SDK_MSG_DATA_TYPE_JSON, (char *)bytes, len);

    env->ReleaseByteArrayElements(data, bytes, 0);
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_send_1car_1data
        (JNIEnv *env, jclass, jbyteArray data) {
    LOGD("jni-fun: send_car_data");
    jbyte *bytes = env->GetByteArrayElements(data, nullptr);
    int len = env->GetArrayLength(data);

    welinkSendCarData((char *)bytes, len);

    env->ReleaseByteArrayElements(data, bytes, 0);
}

extern "C" JNIEXPORT jstring JNICALL Java_com_autoai_welink_channel_WLChannel_get_1version
        (JNIEnv *env, jclass) {
    LOGD("jni-fun: get_version");
    return env->NewStringUTF(welinkGetSdkVersion());
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_start_1record
        (JNIEnv *, jclass) {
    LOGD("jni-fun: start_record");
    welinkSdkCommond sdkCommond;
    sdkCommond.type = SDK_CMD_TYPE_START_RECORD;
    welinkSendCommond(&sdkCommond);
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_stop_1record
        (JNIEnv *, jclass) {
    LOGD("jni-fun: stop_record");
    welinkSdkCommond sdkCommond;
    sdkCommond.type = SDK_CMD_TYPE_STOP_RECORD;
    welinkSendCommond(&sdkCommond);
}

extern "C" JNIEXPORT void JNICALL Java_com_autoai_welink_channel_WLChannel_enable_1log
        (JNIEnv *env, jclass, jboolean enable, jstring path, jint level) {
    if (enable) {
        const char *logPath = "";
        if (path != nullptr) {
            logPath = env->GetStringUTFChars(path, nullptr);
        }

        welinkResetLogTypeAndPath(level, logPath);

        if (path != nullptr && logPath != nullptr) {
            env->ReleaseStringUTFChars(path, logPath);
        }
    } else {
        welinkResetLogTypeAndPath(SDK_LOG_TYPE_NONE, NULL);
    }
}

static bool getJniEnv(JavaVM *vm, JNIEnv **env) {
    bool did_attach_thread = false;
    *env = nullptr;
    // Check if the current thread is attached to the VM
    auto get_env_result = vm->GetEnv((void**)env, JNI_VERSION_1_6);
    if (get_env_result == JNI_EDETACHED) {
        if (vm->AttachCurrentThread(env, NULL) == JNI_OK) {
            did_attach_thread = true;
        } else {
            // Failed to attach thread. Throw an exception if you want to.
        }
    } else if (get_env_result == JNI_EVERSION) {
        // Unsupported JNI version. Throw an exception if you want to.
    }
    return did_attach_thread;
}

static void funcSdkStatus(int iStatus, int iPara1, char *iPara2) {
    LOGD("jni-cb: funcSdkStatus");
    if (g_jvm == nullptr) {
        return;
    }

    JNIEnv *env = nullptr;
    bool did_attach = getJniEnv(g_jvm, &env);
    if (!did_attach) {
        return;
    }

    switch (iStatus) {
        case SDK_STATUS_READY:
            LOGD("SDK_STATUS_READY");
            onReady(env);
            break;
        case SDK_STATUS_CONNECTING:
            LOGD("SDK_STATUS_CONNECTING");
            break;
        case SDK_STATUS_CONNECTED: {
            LOGD("SDK_STATUS_CONNECTED");
            welinkSdkInfo *pSdkInfo = welinkGetSdkInfo();
            WeLinkHuInfo huInfo;

            huInfo.huFPS = g_huFPS;
            huInfo.vehicleType = pSdkInfo->equipmentId;
            huInfo.huBtMacAddress = pSdkInfo->btAddress;
            huInfo.huSupportBtPhone = pSdkInfo->huSupportCall;
            huInfo.huSupportMic = pSdkInfo->huSupportRecord;
            huInfo.huPlatform = pSdkInfo->huPlatform;
            huInfo.huAppVersion = pSdkInfo->huAppVersion;

            onConnected(env, &huInfo);
        }
            break;
        case SDK_STATUS_CONNECT_ERR:
            LOGD("SDK_STATUS_CONNECT_ERR");
            onDisconnected(env);
            break;
        default:
            break;
    }

    if (did_attach) {
        g_jvm->DetachCurrentThread();
    }
}

static void funcSdkMessage(welinkSdkMessageInfo *pMsg) {
    LOGD("jni-cb: funcSdkMessage");
    if (g_jvm == nullptr) {
        return;
    }

    JNIEnv *env = nullptr;
    bool did_attach = getJniEnv(g_jvm, &env);
    if (!did_attach) {
        return;
    }

    switch (pMsg->msgType) {
        case SDK_MSG_TYPE_START_SEND_H264:
            LOGD("SDK_MSG_TYPE_START_SEND_H264");
            onStartSendH264(env);
            break;
        case SDK_MSG_TYPE_STOP_SEND_H264:
            LOGD("SDK_MSG_TYPE_STOP_SEND_H264");
            onStopSendH264(env);
            break;
        case SDK_MSG_TYPE_SINGLE_TOUCH_EVENT:
            LOGD("SDK_MSG_TYPE_SINGLE_TOUCH_EVENT");
            onMotionEvent(env, pMsg->data);
            break;
        case SDK_MSG_TYPE_MULTI_TOUCH_EVENT2:
            LOGD("SDK_MSG_TYPE_MULTI_TOUCH_EVENT2");
            onMotionEvent(env, pMsg->data);
            break;
        case SDK_MSG_TYPE_MULTI_TOUCH_EVENT3:
            LOGD("SDK_MSG_TYPE_MULTI_TOUCH_EVENT3");
            onMotionEvent(env, pMsg->data);
            break;
        case SDK_MSG_TYPE_HARD_KEY:
            LOGD("SDK_MSG_TYPE_HARD_KEY");
            onHardKey(env, pMsg->data, pMsg->para1);
            break;
        case SDK_MSG_TYPE_FPS_CONFIG:
            LOGD("SDK_MSG_TYPE_FPS_CONFIG");
            g_huFPS = pMsg->para1;
            break;
        default:
            break;
    }

    if (did_attach) {
        g_jvm->DetachCurrentThread();
    }
}

static void funcSdkHuResolution(int iWidth, int iHeight) {
    LOGD("jni-cb: SdkHuResolution");
    if (g_jvm == nullptr) {
        return;
    }

    JNIEnv *env = nullptr;
    bool did_attach = getJniEnv(g_jvm, &env);
    if (!did_attach) {
        return;
    }

    onConnecting(env, iWidth, iHeight);

    if (did_attach) {
        g_jvm->DetachCurrentThread();
    }
}

static void funcMessageData(welinkSdkMessageChannelCallbackDataType enType, void *pData, int iLength) {
    LOGD("jni-cb: funcMessageData");
    if (g_jvm == nullptr || pData == nullptr) {
        return;
    }

    JNIEnv *env = nullptr;
    bool did_attach = getJniEnv(g_jvm, &env);
    if (!did_attach) {
        return;
    }

    if (enType == SDK_MSG_CB_DATA_TYPE_JSON) {
        onMessage(env, pData, iLength);
    }

    if (did_attach) {
        g_jvm->DetachCurrentThread();
    }
}

static void funcCarData(welinkSdkCardataChannelCallbackDataType enType, void *pData, int iLength) {
    LOGD("jni-cb: funcCarData");
    if (g_jvm == nullptr || pData == nullptr) {
        return;
    }

    JNIEnv *env = nullptr;
    bool did_attach = getJniEnv(g_jvm, &env);
    if (!did_attach) {
        return;
    }

    onCarData(env, pData, iLength);

    if (did_attach) {
        g_jvm->DetachCurrentThread();
    }
}

static void funcMicData(welinkSdkVRChannelCallbackDataType enType, void *pData, int iLength) {
    LOGD("jni-cb: funcMicData");
    if (g_jvm == nullptr || pData == nullptr) {
        return;
    }

    JNIEnv *env = nullptr;
    bool did_attach = getJniEnv(g_jvm, &env);
    if (!did_attach) {
        return;
    }

    onMicData(env, pData, iLength);

    if (did_attach) {
        g_jvm->DetachCurrentThread();
    }
}

static void funcAudioData(welinkSdkAudioChannelCallbackDataType enType, void *pData, int iLength) {
    LOGD("jni-cb: funcAudioData");
    if (g_jvm == nullptr || pData == nullptr) {
        return;
    }

    JNIEnv *env = nullptr;
    bool did_attach = getJniEnv(g_jvm, &env);
    if (!did_attach) {
        return;
    }

    onAudioData(env, pData, iLength);

    if (did_attach) {
        g_jvm->DetachCurrentThread();
    }
}

static void onReady(JNIEnv *env) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onReadyId = env->GetMethodID(cbClazz, "onReady", "()V");
    env->CallVoidMethod(g_cb, onReadyId);
}

static void onConnecting(JNIEnv *env, int huWidth, int huHeight) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onConnectingId = env->GetMethodID(cbClazz, "onConnecting", "(II)V");
    env->CallVoidMethod(g_cb, onConnectingId, huWidth, huHeight);
}

static void onConnected(JNIEnv *env, WeLinkHuInfo *pInfo) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onConnectedId = env->GetMethodID(cbClazz, "onConnected",
                                               "(Ljava/lang/String;IIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
    env->CallVoidMethod(g_cb, onConnectedId,
                        env->NewStringUTF(pInfo->vehicleType),
                        pInfo->huFPS,
                        pInfo->huSupportMic,
                        pInfo->huSupportBtPhone,
                        env->NewStringUTF(pInfo->huBtMacAddress),
                        env->NewStringUTF(pInfo->huPlatform),
                        env->NewStringUTF(pInfo->huAppVersion));
}

static void onDisconnected(JNIEnv *env) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onDisconnectedId = env->GetMethodID(cbClazz, "onDisconnected", "()V");
    env->CallVoidMethod(g_cb, onDisconnectedId);
}

static void onStartSendH264(JNIEnv *env) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onStartSendH264Id = env->GetMethodID(cbClazz, "onStartSendH264", "()V");
    env->CallVoidMethod(g_cb, onStartSendH264Id);
}

static void onStopSendH264(JNIEnv *env) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onStopSendH264Id = env->GetMethodID(cbClazz, "onStopSendH264", "()V");
    env->CallVoidMethod(g_cb, onStopSendH264Id);
}

static void onMotionEvent(JNIEnv *env, const void *data) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onMotionEventId = env->GetMethodID(cbClazz, "onMotionEvent",
                                                  "([B)V");
    int len = strlen((const char *)data);
    jbyteArray byteData = env->NewByteArray(len);
    env->SetByteArrayRegion(byteData, 0, len, static_cast<const jbyte *>(data));
    env->CallVoidMethod(g_cb, onMotionEventId, byteData);
    env->DeleteLocalRef(byteData);
}

static void onHardKey(JNIEnv *env, const void *data, int len) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onHardKeyId = env->GetMethodID(cbClazz, "onHardKey", "([B)V");

    jbyteArray byteData = env->NewByteArray(len);
    env->SetByteArrayRegion(byteData, 0, len, static_cast<const jbyte *>(data));
    env->CallVoidMethod(g_cb, onHardKeyId, byteData);
    env->DeleteLocalRef(byteData);
}

static void onMessage(JNIEnv *env, const void *data, int len) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onMessageId = env->GetMethodID(cbClazz, "onMessage", "([B)V");

    jbyteArray byteData = env->NewByteArray(len);
    env->SetByteArrayRegion(byteData, 0, len, static_cast<const jbyte *>(data));
    env->CallVoidMethod(g_cb, onMessageId, byteData);
    env->DeleteLocalRef(byteData);
}

static void onCarData(JNIEnv *env, const void *data, int len) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onCarDataId = env->GetMethodID(cbClazz, "onCarData", "([B)V");

    jbyteArray byteData = env->NewByteArray(len);
    env->SetByteArrayRegion(byteData, 0, len, static_cast<const jbyte *>(data));
    env->CallVoidMethod(g_cb, onCarDataId, byteData);
    env->DeleteLocalRef(byteData);
}

static void onMicData(JNIEnv *env, const void *data, int len) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onMicDataId = env->GetMethodID(cbClazz, "onMicData", "([B)V");

    jbyteArray byteData = env->NewByteArray(len);
    env->SetByteArrayRegion(byteData, 0, len, static_cast<const jbyte *>(data));
    env->CallVoidMethod(g_cb, onMicDataId, byteData);
    env->DeleteLocalRef(byteData);
}

static void onAudioData(JNIEnv *env, const void *data, int len) {
    if (g_cb == nullptr) return;

    jclass cbClazz = env->GetObjectClass(g_cb);
    jmethodID onAudioDataId = env->GetMethodID(cbClazz, "onAudioData", "([B)V");

    jbyteArray byteData = env->NewByteArray(len);
    env->SetByteArrayRegion(byteData, 0, len, static_cast<const jbyte *>(data));
    env->CallVoidMethod(g_cb, onAudioDataId, byteData);
    env->DeleteLocalRef(byteData);
}