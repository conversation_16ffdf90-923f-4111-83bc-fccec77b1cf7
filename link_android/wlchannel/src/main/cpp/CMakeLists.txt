cmake_minimum_required(VERSION 3.4.1)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/WeLinkSdkApi/include)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/WeLinkSdkApi/${ANDROID_ABI})

add_library( # Sets the name of the library.
             wlchannel

             # Sets the library as a shared library.
             SHARED

             # Provides a relative path to your source file(s).
             wlchannel.cpp )

# Searches for a specified prebuilt library and stores the path as a
# variable. Because CMake includes system libraries in the search path by
# default, you only need to specify the name of the public NDK library
# you want to add. CMake verifies that the library exists before
# completing its build.

find_library( # Sets the name of the path variable.
              log-lib

              # Specifies the name of the NDK library that
              # you want CMake to locate.
              log )

# Specifies libraries C<PERSON>ake should link to your target library. You
# can link multiple libraries, such as libraries you define in this
# build script, prebuilt third-party libraries, or system libraries.

target_link_libraries( # Specifies the target library.
                       wlchannel
                       "-Wl,-z,max-page-size=16384"
                       # Links the target library to the log library
                       # included in the NDK.
                       ${log-lib}
                        welinksdk
                        wljason )
