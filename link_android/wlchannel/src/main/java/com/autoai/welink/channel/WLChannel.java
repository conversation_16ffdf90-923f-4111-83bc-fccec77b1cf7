package com.autoai.welink.channel;

import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;

import com.autoai.welink.channel.aoa.WLAOA;
import com.autoai.welink.channel.utiliy.AnFileLog;
import com.autoai.welink.channel.utiliy.CommandSource;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.CopyOnWriteArrayList;

public class WLChannel {
    /**
     * 获取单例
     */
    public static WLChannel getInstance(Context context) {
        if (singleInstance == null) {
            singleInstance = new WLChannel(context);
        }

        return singleInstance;
    }

    private WLChannel(Context context) {
        this.context = context.getApplicationContext();
    }

    /**
     * 初始化互联库，并进入无线互联等待状态
     *
     * @param listener 互联状态回调
     */
    public void init(@NonNull final WLChannelListener listener) {
        AnFileLog.init(context);
        AnFileLog.e("link-point", "init");

        mainHandler = new Handler(Looper.getMainLooper());
        preConnectTasks.clear();

        this.listener = listener;
        weLinkCB = new WeLinkCB() {
            @Override
            public void onReady() {
                mainHandler.post(() -> {
                    AnFileLog.e("link-point", "sdk-ready");
                    isWifiReady = true;
                    if (backupIntent != null) {
                        connect(backupIntent);
                    }
                });
            }

            @Override
            public void onConnecting(final int huWidth, final int huHeight) {
                AnFileLog.e("link-point", "hu-ready");
                isWifiReady = false;
                WLChannelListener.Config config = listener.onConnecting(huWidth, huHeight);
                config_weLink(config.screenWidth, config.screenHeight, config.videoWidth, config.videoHeight);
            }

            @Override
            public void onConnected(final String vehicleType, final int huFPS, final int huSupportRecord, final int huSupportBtPhone, final String huBtMacAddress, final String huPlatform, String vehicleVersion) {
                mainHandler.post(() -> {
                    AnFileLog.e("link-point", "connected");
                    isConnected = true;
                    isAOA2WeLinkConnected = isAOA2HUConnected;
                    listener.onConnected(isAOA2WeLinkConnected, vehicleType, huFPS, huSupportRecord == 1, huSupportBtPhone == 1, huBtMacAddress, huPlatform, vehicleVersion);
                    mainHandler.postDelayed(heartbeatRunnable, H264_TIMEOUT);

                    // 如果连接前的缓存任务数据不为空, 在这里进行执行.
                    if (!preConnectTasks.isEmpty()) {
                        AnFileLog.e("link-point", "preConnectTasks not empty, start execute");
                        for (Runnable task : preConnectTasks) {
                            task.run();
                        }
                        AnFileLog.e("link-point", "preConnectTasks execute completed, clear preConnectTasks");
                        preConnectTasks.clear();
                    }
                });
            }

            @Override
            public void onDisconnected() {
                mainHandler.post(() -> {
                    if (!isWifiReady) {
                        mainHandler.removeCallbacks(heartbeatRunnable);
                        AnFileLog.e("link-point", "disconnected");
                        reset_weLink(false);
                        isAOA2HUConnecting = false;
                        listener.onDisconnected();
                        preConnectTasks.clear();
                    }
                });
            }

            @Override
            public void onStartSendH264() {
                mainHandler.post(() -> {
                    if (isConnected) {
                        listener.onStartSendH264();
                    }
                });
            }

            @Override
            public void onStopSendH264() {
                mainHandler.post(() -> {
                    if (isConnected) {
                        listener.onStopSendH264();
                    }
                });
            }

            @Override
            public void onMotionEvent(byte[] data) {
                mainHandler.post(() -> {
                    if (isConnected) {
                        listener.onMotionEvent(data);
                    }
                });
            }

            @Override
            public void onHardKey(byte[] data) {
                mainHandler.post(() -> {
                    if (isConnected) {
                        listener.onHardKey(data);
                    }
                });
            }

            @Override
            public void onMessage(byte[] data) {
                mainHandler.post(() -> {
                    if (isConnected) {
                        listener.onMessage(data);
                    } else {
                        preConnectTasks.add(() -> listener.onMessage(data));
                        AnFileLog.e("link-point", "onMessage but have not connected, add to preConnectTasks: " + preConnectTasks.size());
                    }
                });
            }

            @Override
            public void onCarData(byte[] data) {
                mainHandler.post(() -> {
                    if (isConnected) {
                        listener.onCarData(data);
                    } else {
                        preConnectTasks.add(() -> listener.onMessage(data));
                        AnFileLog.e("link-point", "onCarData but have not connected, add to preConnectTasks: " + preConnectTasks.size());
                    }
                });
            }

            @Override
            public void onMicData(byte[] data) {
                mainHandler.post(() -> {
                    if (isConnected) {
                        listener.onMicData(data);
                    }
                });
            }

            @Override
            public void onAudioData(byte[] data) {
                mainHandler.post(() -> {
                    if (isConnected) {
                        switch (new CommandSource(data).commandID()) {
                            case CommandSource.COMMAND_TO_PHONE_PCM_START:
                                listener.onAudioPlayStart();
                                break;
                            case CommandSource.COMMAND_TO_PHONE_PCM_ENDS:
                                listener.onAudioPlayEnds();
                                break;
                            default:
                                break;
                        }
                    }
                });
            }
        };

        String logPath = getNativeLogPath(true);
        init_weLink(BuildConfig.VERSION_NAME + "/" + BuildConfig.BUILD_TYPE, logPath, weLinkCB);
    }

    /**
     * 获取版本号
     *
     * @return 返回版本号字符串
     */
    public static String getVersion() {
        return BuildConfig.VERSION_NAME + "/" + BuildConfig.BUILD_TYPE +
                "$" + get_version();
    }

    /**
     * AOA互联
     *
     * @param intent AOA Intent
     */
    public synchronized void connect(@NonNull final Intent intent) {
        if (isConnected || isAOA2HUConnecting) {
            AnFileLog.e("link-point", "connect request cancel: isConnected = " + isConnected + " isAOA2HUConnecting = " + isAOA2HUConnecting);
            return;
        }

        UsbAccessory usbAccessory = (UsbAccessory) intent.getParcelableExtra(UsbManager.EXTRA_ACCESSORY);
        if (currentUsbAccessory != null
                && currentUsbAccessory.hashCode() == usbAccessory.hashCode()) {
            AnFileLog.e("link-point", "usb accessory rrrrrrrrr");
            return;
        }

        reset_weLink(true);

        if (!isWifiReady || isAOA2HUConnecting) {
            AnFileLog.e("link-point", "connect-prepare");
            backupIntent = intent;
        } else {
            firstSendH264 = true;

            AnFileLog.e("link-point", "connect-start");
            backupIntent = null;
            currentUsbAccessory = usbAccessory;
            isWifiReady = false;
            isAOA2HUConnecting = true;

            wlaoa = new WLAOA(context);
            if (!wlaoa.connect(intent, new WLAOA.StatusCB() {
                @Override
                public void onConnected() {
                    isAOA2HUConnected = true;
                }

                @Override
                public void onFailed() {
                    mainHandler.post(() -> {
                        if (isAOA2HUConnecting && !isWifiReady) {
                            mainHandler.removeCallbacks(heartbeatRunnable);
                            AnFileLog.e("link-point", "failed");
                            reset_weLink(false);

                            if (listener != null) {
                                listener.onDisconnected();
                            }

                            isAOA2HUConnecting = false;
                        }
                    });
                }

                @Override
                public void onReadSize(int size) {
                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onReadSize(size);
                        }
                    });
                }

                @Override
                public void onWriteSize(int size) {
                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onWriteSize(size);
                        }
                    });
                }
            })) {
                mainHandler.removeCallbacks(heartbeatRunnable);
                AnFileLog.e("link-point", "usb accessory invalid");
                reset_weLink(false);

                if (listener != null) {
                    listener.onDisconnected();
                }

                isAOA2HUConnecting = false;
            }
        }
    }

    /**
     * 重置互联
     * 断开互联并重新等待Wi-Fi互联
     */
    public void reset() {
        reset_weLink(true);
    }

    private void reset_weLink(boolean isWifiReady) {
        AnFileLog.e("link-point", "reset");
        isConnected = false;
        isAOA2HUConnected = false;
        isAOA2WeLinkConnected = false;

        mainHandler.removeCallbacks(heartbeatRunnable);

        if (wlaoa != null) {
            wlaoa.disconnect();
            wlaoa = null;
            currentUsbAccessory = null;
            backupIntent = null;
            isAOA2HUConnecting = false;
        }

        if (!isWifiReady) {
            AnFileLog.e("link-point", "reset_weLink");
            reset_weLink();
        }
    }

    /**
     * 释放资源
     */
    public void deinit() {
        AnFileLog.e("link-point", "deinit");
        reset_weLink(true);
        context = null;
        listener = null;
        wlaoa = null;
        weLinkCB = null;
        deinit_weLink();

        singleInstance = null;
    }

    /**
     * 发送H264数据
     *
     * @param data H264数据
     */
    public void sendH264(byte[] data) {
        if (isSendExitMessage) return;
        if (!isConnected) return;

        if (firstSendH264) {
            firstSendH264 = false;
            AnFileLog.e("link-point", "h264-first-send");
        }

        mainHandler.removeCallbacks(heartbeatRunnable);

        AnFileLog.e("wlchannel", "send_h264");
        send_h264(data);
        mainHandler.postDelayed(heartbeatRunnable, H264_TIMEOUT);
    }

    /**
     * 发送PCM数据
     *
     * @param data PCM数据
     */
    public void sendPCM(byte[] data) {
        if (isSendExitMessage) return;
        if (!isConnected) return;

        send_pcm(data);
    }

    /**
     * 发送消息
     *
     * @param data 消息数据
     */
    public void sendMessage(byte[] data) {
        if (isSendExitMessage) return;
        if (!isConnected) return;

        send_message(data);
    }

    /**
     * 发送车机数据
     *
     * @param data 车机数据
     */
    public void sendCarData(byte[] data) {
        if (isSendExitMessage) return;
        if (!isConnected) return;

        send_car_data(data);
    }

    /**
     * 发送退出消息
     */
    public void sendExitMessage() {
        isSendExitMessage = true;

        String tbtString = null;
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android|ios|ce");
            JSONObject command = new JSONObject();
            command.put("method", "onExitWelink");
            JSONObject extData = new JSONObject();
            extData.put("SettingExitWelink", false);
            command.put("extData", extData);
            jsonObject.put("command", command);
            tbtString = jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (tbtString != null) {
            sendMessage(tbtString.getBytes());
        }

        isSendExitMessage = false;
    }

    /**
     * 开始车机录音
     */
    public void startRecord() {
        if (!isConnected) return;

        start_record();
    }

    /**
     * 结束车机录音
     */
    public void stopRecord() {
        if (!isConnected) return;

        stop_record();
    }

    public void enableLogCat(boolean enable,int logLevel) {
        AnFileLog.enableLogCat(enable);
        enable_log(enable, null,logLevel);
    }


    private String getNativeLogPath(boolean enable) {
        File file = context.getExternalFilesDir(null);
        String logPath = null;
        if (enable && file != null) {
            Date now = new Date(); // 获取当前时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = dateFormat.format(now); // 格式化日期
            logPath = file.getAbsolutePath() + FLAG_DIR +"/"+formattedDate+"_"+WELINKSDK_LOG_FILE;
        }
        return logPath;
    }

    public void enableLogFile(boolean enable, int logLevel) {
        AnFileLog.enableLogFile(enable);

        File file = context.getExternalFilesDir(null);
        String logPath = getNativeLogPath(enable);
        enable_log(enable, logPath,logLevel);
    }

    private static WLChannel singleInstance = null;
    private Context context;
    private WLChannelListener listener;
    private static final long H264_TIMEOUT = 600;
    private WLAOA wlaoa = null;
    private WeLinkCB weLinkCB;
    private static final String FLAG_DIR = "/Test/welinklog";

    private boolean isConnected = false; //WeLink连接成功
    private boolean isWifiReady = false; //Wi-Fi互联SDK处于准备状态
    private boolean isAOA2HUConnecting = false; //AOA开始连接车机
    private boolean isAOA2HUConnected = false; //AOA与车机连接成功
    private boolean isAOA2WeLinkConnected = false; //AOA与WeLink连接成功
    private volatile boolean isSendExitMessage = false;

    /**
     * 对于某些情况下, 车机发送的消息和数据在连接成功状态之前回来, 这是做个缓存, 等待连接成功之后, 再一次性发出去
     */
    private final CopyOnWriteArrayList<Runnable> preConnectTasks = new CopyOnWriteArrayList<>();

    private Handler mainHandler;

    private UsbAccessory currentUsbAccessory = null;
    private Intent backupIntent = null;
    private final Runnable heartbeatRunnable = new Runnable() {
        @Override
        public void run() {
            AnFileLog.e("wlchannel", "heartbeat-1");
            if (!isConnected) return;

            AnFileLog.e("wlchannel", "heartbeat-2");
            send_h264(null);
            mainHandler.postDelayed(heartbeatRunnable, H264_TIMEOUT);
        }
    };
    private boolean firstSendH264 = true;

    protected interface WeLinkCB {
        void onReady();

        void onConnecting(int huWidth, int huHeight);

        void onConnected(String vehicleType, int huFPS, int huSupportRecord, int huSupportBtPhone, String huBtMacAddress, String huPlatform, String vehicleVersion);

        void onDisconnected();

        void onStartSendH264();

        void onStopSendH264();

        void onMotionEvent(byte[] data);

        void onHardKey(byte[] data);

        void onMessage(byte[] data);

        void onCarData(byte[] data);

        void onMicData(byte[] data);

        void onAudioData(byte[] data);
    }

    private static final String WELINKSDK_LOG_FILE = "sdk-log.txt";

    private native static void init_weLink(String version,String logPath, WeLinkCB cb);

    private native static void config_weLink(int screenWidth, int screenHeight, int videoWidth, int videoHeight);

    private native static void deinit_weLink();

    private native static void reset_weLink();

    private native static void send_pcm(byte[] data);

    private native static void send_h264(byte[] data); //data=null,则发送心跳

    private native static void send_message(byte[] data);

    private native static void send_car_data(byte[] data);

    private native static String get_version();

    private native static void start_record();

    private native static void stop_record();

    private native static void enable_log(boolean enable, String logPath,int logLevel);

    ////// shared object //////
    static {
        System.loadLibrary("wlchannel");
    }
}
