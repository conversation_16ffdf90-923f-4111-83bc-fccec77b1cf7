package com.autoai.welink.channel.aoa;

import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;
import android.os.ParcelFileDescriptor;

import androidx.annotation.NonNull;

import com.autoai.welink.channel.aoa.link.AOA;
import com.autoai.welink.channel.aoa.link.Wifi2AoaBuffer;
import com.autoai.welink.channel.aoa.link.Aoa2WifiBuffer;
import com.autoai.welink.channel.aoa.link.WIFI;
import com.autoai.welink.channel.aoa.link_v1.Wifi2AoaBuffer_v1;
import com.autoai.welink.channel.aoa.link_v1.Aoa2WifiBuffer_v1;
import com.autoai.welink.channel.aoa.link_v1.AOA_v1;
import com.autoai.welink.channel.aoa.link_v1.WIFI_v1;
import com.autoai.welink.channel.utiliy.AnFileLog;

import java.io.FileDescriptor;
import java.io.IOException;

public class WLAOA {
    private UsbManager usbManager;
    ParcelFileDescriptor fileDescriptor = null;
    private AOA aoa = null;
    private WIFI wifi = null;
    private Wifi2AoaBuffer wifi2AoaBuffer = null;
    private Aoa2WifiBuffer aoa2WifiBuffer = null;
    private StatusCB extStatusCB = null;
    private final Object extStatusCBLock = new Object();
    private final StatusCB statusCB = new StatusCB() {
        @Override
        public void onConnected() {
            synchronized (extStatusCBLock) {
                if (extStatusCB != null) {
                    extStatusCB.onConnected();
                }
            }
        }

        @Override
        public void onFailed() {
            synchronized (extStatusCBLock) {
                if (extStatusCB != null) {
                    extStatusCB.onFailed();
                }
            }
        }

        @Override
        public void onReadSize(int size) {
            synchronized (extStatusCBLock) {
                if (extStatusCB != null) {
                    extStatusCB.onReadSize(size);
                }
            }
        }

        @Override
        public void onWriteSize(int size) {
            synchronized (extStatusCBLock) {
                if (extStatusCB != null) {
                    extStatusCB.onWriteSize(size);
                }
            }
        }
    };

    public interface StatusCB {
        void onConnected();
        void onFailed();
        void onReadSize(int size);
        void onWriteSize(int size);
    }

    public WLAOA(@NonNull Context context) {
        usbManager = (UsbManager)context.getSystemService(Context.USB_SERVICE);
    }

    /**
     * 连接AOA与Wi-Fi，并在两者之间转发数据
     * 如果已经通过Wi-Fi互联成功，则不需要再调用connect
     * 连接错误由Wi-Fi互联SDK负责，WLAOA不负责连接失败
     * @param intent 通过ParcelableExtra(UsbManager.EXTRA_ACCESSORY)指定UsbAccessory对象,
     *               通过IntExtra("welink-version")指定welink协议版本，默认版本是: 1
     *
     */
    public boolean connect(@NonNull final Intent intent, final StatusCB cb) {
        AnFileLog.e("link-point", "check-aoa-1");
        if (intent.getParcelableExtra(UsbManager.EXTRA_ACCESSORY) == null) {
            AnFileLog.e("link-point", "UsbAccessory is null");
            return false;
        }

        AnFileLog.e("link-point", "check-aoa-2");
        ParcelFileDescriptor fileDescriptor;
        try {
            fileDescriptor = usbManager.openAccessory((UsbAccessory) intent.getParcelableExtra(UsbManager.EXTRA_ACCESSORY));
        } catch (Exception e) {
            fileDescriptor = null;
        }

        if (fileDescriptor == null) {
            AnFileLog.e("link-point", "ParcelFileDescriptor is null");
            return false;
        }

        AnFileLog.e("link-point", "check-aoa-3");
        final FileDescriptor fd = fileDescriptor.getFileDescriptor();
        if (fd == null) {
            AnFileLog.e("link-point", "FileDescriptor is null");
            try {
                fileDescriptor.close();
            } catch (IOException e) {
            }

            return false;
        }

        disconnect();

        this.fileDescriptor = fileDescriptor;

        if (intent.getIntExtra("welink-version", 1) == 1) {
            aoa = new AOA_v1();
            wifi = new WIFI_v1();
            wifi2AoaBuffer = new Wifi2AoaBuffer_v1(WIFI_v1.W2A_MAX_COUNT);
            aoa2WifiBuffer = new Aoa2WifiBuffer_v1(AOA_v1.A2W_MAX_COUNT);
        }

        assert(aoa != null && wifi != null && wifi2AoaBuffer != null && aoa2WifiBuffer != null);

        extStatusCB = cb;

        new Thread(new Runnable() {
            @Override
            public void run() {
                if (start_proxy(fd, statusCB)) {
                    if (statusCB != null) {
                        statusCB.onConnected();
                    }
                } else {
                    AnFileLog.e("link-point", "start_proxy error");
                    if (statusCB != null) {
                        statusCB.onFailed();
                    }
                    disconnect();
                }
            }
        }).start();

        return true;
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        synchronized (extStatusCBLock) {
            extStatusCB = null;
        }

        if (aoa != null) {
            aoa.disconnect();
            aoa = null;
        }

        if (wifi != null) {
            wifi.disconnect();
            wifi = null;
        }

        try {
            if (fileDescriptor != null) {
                fileDescriptor.close();
                fileDescriptor = null;
            }
        } catch (IOException e) {
        }
    }

    private boolean start_proxy(FileDescriptor fileDescriptor, final StatusCB statusCB) {
        boolean bWifiConnected = wifi.connect(aoa2WifiBuffer, wifi2AoaBuffer, new WIFI.ConnectStatus() {
            @Override
            public void onConnectFailed() {
                if (statusCB != null) {
                    AnFileLog.e("link-point", "wifi-failed");
                    statusCB.onFailed();
                }
                disconnect();
            }
        });

        if (!bWifiConnected) {
            return false;
        }

        aoa.connect(fileDescriptor, aoa2WifiBuffer, wifi2AoaBuffer, new AOA.ConnectStatus() {
            @Override
            public void onConnectFailed() {
                if (statusCB != null) {
                    AnFileLog.e("link-point", "aoa-failed");
                    statusCB.onFailed();
                }
                disconnect();
            }

            @Override
            public void onReadSize(int size) {
                if (statusCB != null) {
                    statusCB.onReadSize(size);
                }
            }

            @Override
            public void onWriteSize(int size) {
                if (statusCB != null) {
                    statusCB.onWriteSize(size);
                }
            }
        });

        return true;
    }
}
