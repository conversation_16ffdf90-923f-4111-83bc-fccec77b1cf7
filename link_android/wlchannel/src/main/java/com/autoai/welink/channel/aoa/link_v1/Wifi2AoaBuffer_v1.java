package com.autoai.welink.channel.aoa.link_v1;

import androidx.annotation.NonNull;

import com.autoai.welink.channel.aoa.link.Wifi2AoaBuffer;
import com.autoai.welink.channel.utiliy.AnFileLog;
import com.autoai.welink.channel.utiliy.CommandSource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.LinkedBlockingQueue;

public class Wifi2AoaBuffer_v1 implements Wifi2AoaBuffer {
    public static final int PORT_DATA = 6821;
    public static final int PORT_MSG = 6805;
    public static final int PORT_VIDEO = 6802;
    public static final int PORT_HU = 6831;
    public static final int PORT_AUDIO = 6833;

    private HashMap<Integer, byte[]> bufferMap = new HashMap<>();
    private HashMap<Integer, Integer> bufferSizeMap = new HashMap<>();
    private HashMap<Integer, byte[]> headerMap = new HashMap<>();
    private LinkedBlockingQueue<byte[]> dataQueue;

    private byte[] buffer_zero = new byte[0];

    public Wifi2AoaBuffer_v1(int size) {
        bufferMap.put(PORT_DATA, buffer_zero);
        bufferMap.put(PORT_MSG, buffer_zero);
        bufferMap.put(PORT_VIDEO, buffer_zero);
        bufferMap.put(PORT_HU, buffer_zero);
        bufferMap.put(PORT_AUDIO, buffer_zero);

        bufferSizeMap.put(PORT_DATA, 0);
        bufferSizeMap.put(PORT_MSG, 0);
        bufferSizeMap.put(PORT_VIDEO, 0);
        bufferSizeMap.put(PORT_HU, 0);
        bufferSizeMap.put(PORT_AUDIO, 0);

        headerMap.put(PORT_DATA, buffer_zero);
        headerMap.put(PORT_MSG, buffer_zero);
        headerMap.put(PORT_VIDEO, buffer_zero);
        headerMap.put(PORT_HU, buffer_zero);
        headerMap.put(PORT_AUDIO, buffer_zero);

        dataQueue = new LinkedBlockingQueue<>(size);
    }

    @Override
    public boolean write(int port, @NonNull byte[] data) {
        byte[]  buffer = bufferMap.get(port);
        Integer size = bufferSizeMap.get(port);
        byte[] header = headerMap.get(port);

        if (buffer == null || size == null || header == null) {
            AnFileLog.e("wifi->aoa", "port error: " + port);
            return false;
        }

        if (header.length > 0) {
            AnFileLog.e("wifi->aoa", "data-fix(" + port + "): " + data.length);
            byte[] temp = new byte[header.length + data.length];
            System.arraycopy(header, 0, temp, 0, header.length);
            System.arraycopy(data, 0, temp, header.length, data.length);
            data = temp;
            headerMap.put(port, buffer_zero);
        }

        AnFileLog.e("wifi->aoa", "wifi receive data - " + port + " : " + data.length);

        if (size == 0) {
            if (data.length >= 8) {
                AnFileLog.e("wifi->aoa", "header - " + port + " : " +
                        Integer.toHexString(data[0]) + " " +
                        Integer.toHexString(data[1]) + " " +
                        Integer.toHexString(data[2]) + " " +
                        Integer.toHexString(data[3]) + " " +
                        Integer.toHexString(data[4]) + " " +
                        Integer.toHexString(data[5]) + " " +
                        Integer.toHexString(data[6]) + " " +
                        Integer.toHexString(data[7]));

                if (data[0] == 'W' && data[1] == 'L') {
                    int len = CommandSource.toInt(data, 4) + 8;
                    buffer = new byte[len];
                    AnFileLog.e("wifi->aoa", "parser - " + port + " : " + len);
                }
            } else {
                AnFileLog.e("wifi->aoa", "data-reserve(" + port + "): " + data.length);
                headerMap.put(port, data);
                return true;
            }
        }

        if (buffer.length == 0) {
            AnFileLog.e("wifi->aoa", "data-exception(" + port + "): " + data.length);
            return false;
        }

        int length = Math.min(buffer.length - size, data.length);
        System.arraycopy(data, 0, buffer, size, length);
        size += length;

        bufferMap.put(port, buffer);
        bufferSizeMap.put(port, size);

        if (size == buffer.length) {
            try {
                bufferMap.put(port, buffer_zero);
                bufferSizeMap.put(port, 0);
                dataQueue.put(buffer);
                AnFileLog.e("wifi->aoa", "cache-size: " + count());
            } catch (InterruptedException e) {
                return false;
            }
        }

        if (data.length - length > 0) {
            byte[] multi_data = new byte[data.length - length];
            AnFileLog.e("wifi->aoa", "multi - " + port + " : " + multi_data.length);
            System.arraycopy(data, length, multi_data, 0, multi_data.length);
            write(port, multi_data);
        }

        return true;
    }

    @Override
    public byte[] read() {
        try {
            return dataQueue.take();
        } catch (InterruptedException e) {
            return null;
        }
    }

    @Override
    public void clear() {
        bufferMap.put(PORT_DATA, buffer_zero);
        bufferMap.put(PORT_MSG, buffer_zero);
        bufferMap.put(PORT_VIDEO, buffer_zero);
        bufferMap.put(PORT_HU, buffer_zero);
        bufferMap.put(PORT_AUDIO, buffer_zero);

        bufferSizeMap.put(PORT_DATA, 0);
        bufferSizeMap.put(PORT_MSG, 0);
        bufferSizeMap.put(PORT_VIDEO, 0);
        bufferSizeMap.put(PORT_HU, 0);
        bufferSizeMap.put(PORT_AUDIO, 0);

        dataQueue.clear();
    }

    @Override
    public int count() {
        return dataQueue.size();
    }
}
