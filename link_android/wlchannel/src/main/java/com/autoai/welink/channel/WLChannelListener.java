package com.autoai.welink.channel;

public interface WLChannelListener {
    class Config {
        public Config(int screenWidth, int screenHeight, int videoWidth, int videoHeight) {
            this.screenWidth = screenWidth;
            this.screenHeight = screenHeight;
            this.videoWidth = videoWidth;
            this.videoHeight = videoHeight;
        }

        /**
         * 屏幕分辨率宽度，仅用于MotionEvent1协议的回控坐标转换
         */
        public int screenWidth;
        /**
         * 屏幕分辨率高度，仅用于MotionEvent1协议的回控坐标转换
         */
        public int screenHeight;
        /**
         * 视频帧分辨率宽度
         */
        public int videoWidth;
        /**
         * 视频帧分辨率高度
         */
        public int videoHeight;
    }

    /**
     * 正在连接车机
     * @param huWidth 车机屏幕宽度
     * @param huHeight 车机屏幕高度
     * @return 分辨率配置
     */
    Config onConnecting(int huWidth, int huHeight);

    /**
     * 已连接车机
     * @param isAOA 是否是AOA互联
     * @param vehicleType 车机类型
     * @param huFPS 车机支持的FPS
     * @param huSupportRecord 是否支持录音
     * @param huSupportBtPhone 是否支持蓝牙电话
     * @param huBtMacAddress 车机蓝牙MAC地址
     * @param huPlatform 车机系统
     */
    void onConnected(boolean isAOA, String vehicleType, int huFPS, boolean huSupportRecord, boolean huSupportBtPhone, String huBtMacAddress, String huPlatform, String vehicleVersion);

    /**
     * 连接断开
     */
    void onDisconnected();

    /**
     * 开始发送H264数据
     */
    void onStartSendH264();

    /**
     * 停止发送H264数据
     */
    void onStopSendH264();

    /**
     * 接收到MotionEvent
     * @param data MotionEvent1数据
     */
    void onMotionEvent(byte[] data);

    /**
     * 接收到硬按键
     * @param data 硬按键数据
     */
    void onHardKey(byte[] data);

    /**
     * 接收到消息
     * @param data 消息数据
     */
    void onMessage(byte[] data);

    /**
     * 接收到车机数据
     * @param data 车机数据
     */
    void onCarData(byte[] data);

    /**
     * 接收到车机Mic数据
     * @param data Mic数据
     */
    void onMicData(byte[] data);

    /**
     * 车机开始播放声音
     */
    void onAudioPlayStart();

    /**
     * 车机结束播放声音
     */
    void onAudioPlayEnds();

    /**
     * 从车机接收数据字节数
     * @param size 字节数
     */
    void onReadSize(int size);

    /**
     * 向车机发送数据字节数
     * @param size 字节数
     */
    void onWriteSize(int size);
}
