package com.autoai.welink.channel.aoa.link_v1;

import androidx.annotation.NonNull;

import com.autoai.welink.channel.aoa.link.Aoa2WifiBuffer;
import com.autoai.welink.channel.aoa.link.WIFI;
import com.autoai.welink.channel.aoa.link.Wifi2AoaBuffer;
import com.autoai.welink.channel.utiliy.AnFileLog;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.HashMap;

import static java.lang.Thread.sleep;

public class WIFI_v1 implements WIFI {
    private static final String LOOPBACK_IP = "127.0.0.1";
    private static final int PORT_DATA = 6821;
    private static final int PORT_MSG = 6805;
    private static final int PORT_VIDEO = 6802;
    private static final int PORT_HU = 6831;
    private static final int PORT_AUDIO = 6833;

    private static final int READ_SIZE = 1280 * 1024;
    public static final int W2A_MAX_COUNT = 20 * 3 * 2;

    private HashMap<Integer, Socket> socketMap = new HashMap<>();
    private HashMap<Integer, InputStream> inputStreamMap = new HashMap<>();
    private HashMap<Integer, OutputStream> outputStreamMap = new HashMap<>();
    private volatile ConnectStatus connectStatus;
    private final Object receiveDataCBLock = new Object();
    private int maxCacheCount = 0;
    private final Object maxCacheCountLock = new Object();

    public boolean connect(@NonNull final Aoa2WifiBuffer a2w, @NonNull final Wifi2AoaBuffer w2a, @NonNull ConnectStatus cb) {
        disconnect();

        int i = 0;
        do {
            try {
                socketMap.put(PORT_DATA, new Socket(LOOPBACK_IP, PORT_DATA));
                socketMap.put(PORT_MSG, new Socket(LOOPBACK_IP, PORT_MSG));
                socketMap.put(PORT_VIDEO, new Socket(LOOPBACK_IP, PORT_VIDEO));
                socketMap.put(PORT_HU, new Socket(LOOPBACK_IP, PORT_HU));
                socketMap.put(PORT_AUDIO, new Socket(LOOPBACK_IP, PORT_AUDIO));
                break;
            } catch (IOException e) {
                e.printStackTrace();
            }

            AnFileLog.e("link-point", "socket connect error");
            
            Socket data = socketMap.get(PORT_DATA);
            Socket msg = socketMap.get(PORT_MSG);
            Socket video = socketMap.get(PORT_VIDEO);
            Socket hu = socketMap.get(PORT_HU);
            Socket audio = socketMap.get(PORT_AUDIO);

            try {
                if (data != null) {
                    data.close();
                    socketMap.put(PORT_DATA, null);
                }
                if (msg != null) {
                    msg.close();
                    socketMap.put(PORT_MSG, null);
                }
                if (video != null) {
                    video.close();
                    socketMap.put(PORT_VIDEO, null);
                }
                if (hu != null) {
                    hu.close();
                    socketMap.put(PORT_HU, null);
                }
                if (audio != null) {
                    audio.close();
                    socketMap.put(PORT_AUDIO, null);
                }
            } catch (IOException e1) {
            }

            if (i++ >= 3) {
                disconnect();
                return false;
            }

            try {
                sleep(100);
            } catch (InterruptedException e) {
                return false;
            }
        } while (true);

        Socket data = socketMap.get(PORT_DATA);
        Socket msg = socketMap.get(PORT_MSG);
        Socket video = socketMap.get(PORT_VIDEO);
        Socket hu = socketMap.get(PORT_HU);
        Socket audio = socketMap.get(PORT_AUDIO);

        if (data == null ||
        msg == null ||
        video == null ||
        hu == null ||
        audio == null) {
            return false;
        }

        if (!data.isConnected() ||
        !msg.isConnected() ||
        !video.isConnected() ||
        !hu.isConnected() ||
        !audio.isConnected()) {
            disconnect();
            return false;
        }

        try {
            inputStreamMap.put(PORT_DATA, data.getInputStream());
            inputStreamMap.put(PORT_MSG, msg.getInputStream());
            inputStreamMap.put(PORT_VIDEO, video.getInputStream());
            inputStreamMap.put(PORT_HU, hu.getInputStream());
            inputStreamMap.put(PORT_AUDIO, audio.getInputStream());
        } catch (IOException e) {
            disconnect();
            return false;
        }

        try {
            outputStreamMap.put(PORT_DATA, data.getOutputStream());
            outputStreamMap.put(PORT_MSG, msg.getOutputStream());
            outputStreamMap.put(PORT_VIDEO, video.getOutputStream());
            outputStreamMap.put(PORT_HU, hu.getOutputStream());
            outputStreamMap.put(PORT_AUDIO, audio.getOutputStream());
        } catch (IOException e) {
            disconnect();
            return false;
        }

        connectStatus = cb;

        start_thread(PORT_DATA, a2w, w2a);
        start_thread(PORT_MSG, a2w, w2a);
        start_thread(PORT_VIDEO, a2w, w2a);
        start_thread(PORT_HU, a2w, w2a);
        start_thread(PORT_AUDIO, a2w, w2a);

        return true;
    }

    public void disconnect() {
        synchronized (receiveDataCBLock) {
            connectStatus = null;
        }

        try {
            InputStream isd = inputStreamMap.get(PORT_DATA);
            InputStream ism = inputStreamMap.get(PORT_MSG);
            InputStream isv = inputStreamMap.get(PORT_VIDEO);
            InputStream ish = inputStreamMap.get(PORT_HU);
            InputStream isa = inputStreamMap.get(PORT_AUDIO);

            if (isd != null) {
                isd.close();
                inputStreamMap.put(PORT_DATA, null);
            }
            if (ism != null) {
                ism.close();
                inputStreamMap.put(PORT_MSG, null);
            }
            if (isv != null) {
                isv.close();
                inputStreamMap.put(PORT_VIDEO, null);
            }
            if (ish != null) {
                ish.close();
                inputStreamMap.put(PORT_HU, null);
            }
            if (isa != null) {
                isa.close();
                inputStreamMap.put(PORT_AUDIO, null);
            }

            OutputStream osd = outputStreamMap.get(PORT_DATA);
            OutputStream osm = outputStreamMap.get(PORT_MSG);
            OutputStream osv = outputStreamMap.get(PORT_VIDEO);
            OutputStream osh = outputStreamMap.get(PORT_HU);
            OutputStream osa = outputStreamMap.get(PORT_AUDIO);

            if (osd != null) {
                osd.close();
                outputStreamMap.put(PORT_DATA, null);
            }
            if (osm != null) {
                osm.close();
                outputStreamMap.put(PORT_MSG, null);
            }
            if (osv != null) {
                osv.close();
                outputStreamMap.put(PORT_VIDEO, null);
            }
            if (osh != null) {
                osh.close();
                outputStreamMap.put(PORT_HU, null);
            }
            if (osa != null) {
                osa.close();
                outputStreamMap.put(PORT_AUDIO, null);
            }

            Socket data = socketMap.get(PORT_DATA);
            Socket msg = socketMap.get(PORT_MSG);
            Socket video = socketMap.get(PORT_VIDEO);
            Socket hu = socketMap.get(PORT_HU);
            Socket audio = socketMap.get(PORT_AUDIO);

            if (data != null) {
                data.close();
                socketMap.put(PORT_DATA, null);
            }
            if (msg != null) {
                msg.close();
                socketMap.put(PORT_MSG, null);
            }
            if (video != null) {
                video.close();
                socketMap.put(PORT_VIDEO, null);
            }
            if (hu != null) {
                hu.close();
                socketMap.put(PORT_HU, null);
            }
            if (audio != null) {
                audio.close();
                socketMap.put(PORT_AUDIO, null);
            }
        } catch (IOException e) {
        }
    }

    private void start_thread(final int port, final Aoa2WifiBuffer a2w, final Wifi2AoaBuffer w2a) {
        new Thread(new Runnable() {
            private byte[] readData = new byte[READ_SIZE];

            @Override
            public void run() {
                while (connectStatus != null) {
                    try {
                        int size = 0;
                        try {
                            InputStream is = inputStreamMap.get(port);
                            if (is != null) {
                                size = is.read(readData);
                            }
                        } catch (Exception e) {
                            AnFileLog.e("link-point", "wifi-read-expection(" + port + "): " + e.getMessage());
                            throw new IOException();
                        }
                        if (size == 0) {
                            AnFileLog.e("link-point", "wifi-read-size(" + port + ") == 0");
                            continue;
                        }
                        if (size == -1) {
                            AnFileLog.e("link-point", "wifi-read-size(" + port + ") == -1");
                            throw new IOException();
                        }

                        byte[] data = new byte[size];
                        System.arraycopy(readData, 0, data, 0, size);

                        if (!w2a.write(port, data)) {
                            AnFileLog.e("link-point", "w2a-write-failed");
                            throw new IOException();
                        }

                        synchronized (maxCacheCountLock) {
                            int count = w2a.count();
                            if (count > maxCacheCount) {
                                maxCacheCount = count;
                            }
                            if (count >= W2A_MAX_COUNT) {
                                AnFileLog.e("link-point", "w2a-max");
                                throw new IOException();
                            }
                        }
                    } catch (IOException e) {
                        AnFileLog.e("wifi-aoa", "wifi-read exception: " + e.getMessage());

                        synchronized (receiveDataCBLock) {
                            if (connectStatus != null) {
                                connectStatus.onConnectFailed();
                            }
                        }
                        return;
                    }
                }
            }
        }).start();

        new Thread((new Runnable() {
            @Override
            public void run() {
                OutputStream os = outputStreamMap.get(port);

                if (os == null) {
                    AnFileLog.e("wifi-aoa", "outputStreamMap invalid: " + port);
                    synchronized (receiveDataCBLock) {
                        if (connectStatus != null) {
                            connectStatus.onConnectFailed();
                        }
                    }

                    return;
                }

                while (connectStatus != null) {
                    AnFileLog.e("aoa->wifi", "wifi-sender wait...");
                    byte[] data = a2w.read(port);

                    if (data != null) {
                        try {
                            AnFileLog.e("aoa->wifi", "wifi send data - " + port + " : " + data.length);

                            if (data.length > 0) {
                                AnFileLog.e("aoa->wifi", "outputStream.write");
                                os.write(data);
                            }

                            if (a2w.count(port) == 0) {
                                AnFileLog.e("aoa->wifi", "outputStream.flush");
                                os.flush();
                            }

                            AnFileLog.e("aoa->wifi", "wifi send data - " + port + " : end");
                        } catch (Exception e) {
                            AnFileLog.e("aoa->wifi", "wifi-sender exception: " + e.getMessage());

                            synchronized (receiveDataCBLock) {
                                if (connectStatus != null) {
                                    connectStatus.onConnectFailed();
                                }
                            }

                            break;
                        }
                    }
                }
            }
        })).start();
    }
}
