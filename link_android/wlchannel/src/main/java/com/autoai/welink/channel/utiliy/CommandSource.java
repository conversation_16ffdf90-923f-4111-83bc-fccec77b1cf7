package com.autoai.welink.channel.utiliy;

public class CommandSource {
    public static final int COMMAND_TO_PHONE_PCM_START = 40010;
    public static final int COMMAND_TO_PHONE_PCM_ENDS = 40020;

    public CommandSource(byte[] data) {
        int[] intArr = getCommandSource(data);
        if(intArr == null){
            return;
        }

        mCommandID = intArr[1];
    }

    public int commandID() {
        return mCommandID;
    }

    private int mCommandID = -1;

    public static int byteToInt(byte b) {
        //Java 总是把 byte 当做有符处理；我们可以通过将其和 0xFF 进行二进制与得到它的无符值
        return b & 0xFF;
    }

    public static int byte2ArToInt(byte[] b) {
        return (b[0] & 0xFF) |
                (b[1] & 0xFF) << 8;
    }

//    public static byte[] to4bytes(int intValue) {
//        byte[] bytes = {
//                (byte) ((intValue & 0xff000000) >> 24),
//                (byte) ((intValue & 0x00ff0000) >> 16),
//                (byte) ((intValue & 0x0000ff00) >> 8),
//                (byte) (intValue & 0x000000ff)
//        };
//        return bytes;
//    }

    public static int toInt(byte[] bytes, int offset) {
        return ((bytes[offset] & 0xff) << 24) + ((bytes[offset + 1] & 0xff) << 16)
                + ((bytes[offset + 2] & 0xff) << 8) + (bytes[offset + 3] & 0xff);
    }

    private static final int PCM_MARK = 0x55AA;

    private boolean isMarkRight(byte[] datas) {
        try {
            byte[] markTempType = new byte[2];
            markTempType[0] = datas[0];
            markTempType[1] = datas[1];
            int _markValue = byte2ArToInt(markTempType);
            if (PCM_MARK != _markValue) {
                AnFileLog.e("wlchannel", "onAudioData:识别标识符不一致！");
                return false;
            }
        } catch (Exception e) {
        }
        return true;
    }

    private int[] getCommandSource(byte[] datas) {
        int[] intArr = new int[2];
        try {
            if (datas == null || datas.length <= 0) {
                return null;
            }
            if (!isMarkRight(datas)) {
                return null;
            }
            byte sourceByte = datas[4];
            intArr[0] = byteToInt(sourceByte);

            byte[] tempType = new byte[2];
            tempType[0] = datas[2];
            tempType[1] = datas[3];
            intArr[1] = byte2ArToInt(tempType);

        } catch (Exception e) {
        }
        return intArr;
    }
}



