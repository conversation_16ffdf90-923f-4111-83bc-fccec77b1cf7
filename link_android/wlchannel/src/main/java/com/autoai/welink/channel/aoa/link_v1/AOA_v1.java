package com.autoai.welink.channel.aoa.link_v1;

import androidx.annotation.NonNull;

import com.autoai.welink.channel.aoa.link.AOA;
import com.autoai.welink.channel.aoa.link.Aoa2WifiBuffer;
import com.autoai.welink.channel.aoa.link.Wifi2AoaBuffer;
import com.autoai.welink.channel.utiliy.AnFileLog;

import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class AOA_v1 implements AOA {
    private static final int READ_SIZE = 1280 * 1024;
    public static final int A2W_MAX_COUNT = 5 * 3 * 2;

    private FileInputStream inputStream = null;
    private FileOutputStream outputStream = null;
    private volatile ConnectStatus connectStatus;
    private final Object connectStatusLock = new Object();
    private int maxCacheCount = 0;

    private boolean firstRead = true;

    public void connect(@NonNull FileDescriptor fd, @NonNull final Aoa2WifiBuffer a2w, @NonNull final Wifi2AoaBuffer w2a, @NonNull ConnectStatus cb) {
        disconnect();

        outputStream = new FileOutputStream(fd);
        inputStream = new FileInputStream(fd);

        connectStatus = cb;

        new Thread(new Runnable() {
            private byte[] readData = new byte[READ_SIZE];

            @Override
            public void run() {
                AnFileLog.e("link-point", "aoa-ready");
                while (connectStatus != null) {
                    try {
                        int size;
                        try {
                            size = inputStream.read(readData);
                        } catch (Exception e) {
                            AnFileLog.e("link-point", "aoa-read-expection: " + e.getMessage());
                            throw new IOException();
                        }
                        if (size == 0) {
                            AnFileLog.e("link-point", "aoa-read-size == 0");
                            continue;
                        }
                        if (size == -1) {
                            AnFileLog.e("link-point", "aoa-read-size == -1");
                            throw new IOException();
                        }

                        synchronized (connectStatusLock) {
                            if (connectStatus != null) {
                                connectStatus.onReadSize(size);
                            }
                        }

                        if (firstRead) {
                            firstRead = false;
                            AnFileLog.e("link-point", "aoa-first-read");
                        }

                        byte[] data = new byte[size];
                        System.arraycopy(readData, 0, data, 0, size);

                        if (!a2w.write(data)) {
                            AnFileLog.e("link-point", "a2w-write-failed");
                            throw new IOException();
                        }

                        int count = a2w.count();
                        if (count > maxCacheCount) {
                            maxCacheCount = count;
                        }
                        if (count >= A2W_MAX_COUNT) {
                            AnFileLog.e("link-point", "a2w-max");
                            throw new IOException();
                        }
                    } catch (IOException e) {
                        AnFileLog.e("aoa->wifi", "aoa-read exception: " + e.getMessage());

                        synchronized (connectStatusLock) {
                            if (connectStatus != null) {
                                connectStatus.onConnectFailed();
                            }
                        }
                        return;
                    }
                }
            }
        }).start();

        new Thread(new Runnable() {
            @Override
            public void run() {
                while (connectStatus != null) {
                    AnFileLog.e("wifi->aoa", "aoa-sender wait...");
                    byte[] data = w2a.read();

                    if (data != null) {
                        try {
                            AnFileLog.e("wifi->aoa", "aoa send data: " + data.length);

                            if (data.length > 0) {
                                AnFileLog.e("wifi->aoa", "outputStream.write");
                                outputStream.write(data);
                            }

                            if (w2a.count() == 0) {
                                AnFileLog.e("wifi->aoa", "outputStream.flush");
                                outputStream.flush();
                            }

                            synchronized (connectStatusLock) {
                                if (connectStatus != null) {
                                    connectStatus.onWriteSize(data.length);
                                }
                            }

                            AnFileLog.e("wifi->aoa", "aoa send data: end");
                        } catch (Exception e) {
                            AnFileLog.e("wifi->aoa", "aoa-sender exception: " + e.getMessage());

                            synchronized (connectStatusLock) {
                                if (connectStatus != null) {
                                    connectStatus.onConnectFailed();
                                }
                            }

                            break;
                        }
                    }
                }
            }
        }).start();
    }

    public void disconnect() {
        synchronized (connectStatusLock) {
            connectStatus = null;
        }

        try {
            if (inputStream != null) {
                inputStream.close();
                inputStream = null;
            }

            if (outputStream != null) {
                outputStream.close();
                outputStream = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
