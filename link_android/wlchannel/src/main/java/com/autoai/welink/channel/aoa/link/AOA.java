package com.autoai.welink.channel.aoa.link;

import androidx.annotation.NonNull;

import java.io.FileDescriptor;

public interface AOA {
    interface ConnectStatus {
        void onConnectFailed();
        void onReadSize(int size);
        void onWriteSize(int size);
    }

    void connect(@NonNull FileDescriptor fd, @NonNull Aoa2WifiBuffer a2w, @NonNull Wifi2AoaBuffer w2a, @NonNull ConnectStatus cb);
    void disconnect();
}
