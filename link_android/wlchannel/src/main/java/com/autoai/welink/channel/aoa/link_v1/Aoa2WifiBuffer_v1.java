package com.autoai.welink.channel.aoa.link_v1;

import androidx.annotation.NonNull;

import com.autoai.welink.channel.aoa.link.Aoa2WifiBuffer;
import com.autoai.welink.channel.utiliy.AnFileLog;
import com.autoai.welink.channel.utiliy.CommandSource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.LinkedBlockingQueue;

public class Aoa2WifiBuffer_v1 implements Aoa2WifiBuffer {
    private static final int PORT_DATA = Wifi2AoaBuffer_v1.PORT_DATA;
    private static final int PORT_MSG = Wifi2AoaBuffer_v1.PORT_MSG;
    private static final int PORT_VIDEO = Wifi2AoaBuffer_v1.PORT_VIDEO;
    private static final int PORT_HU = Wifi2AoaBuffer_v1.PORT_HU;
    private static final int PORT_AUDIO = Wifi2AoaBuffer_v1.PORT_AUDIO;

    private HashMap<Integer, LinkedBlockingQueue<byte[]>> dataQueueMap = new HashMap<>();
    int port = -1; //用于保存待续数据的端口号
    byte[] buffer = null;
    int buffer_offset = 0;
    byte[] header = null;

    public Aoa2WifiBuffer_v1(int size) {
        dataQueueMap.put(PORT_DATA, new LinkedBlockingQueue<byte[]>(size));
        dataQueueMap.put(PORT_MSG, new LinkedBlockingQueue<byte[]>(size));
        dataQueueMap.put(PORT_VIDEO, new LinkedBlockingQueue<byte[]>(size));
        dataQueueMap.put(PORT_HU, new LinkedBlockingQueue<byte[]>(size));
        dataQueueMap.put(PORT_AUDIO, new LinkedBlockingQueue<byte[]>(size));
    }

    @Override
    public boolean write(@NonNull byte[] data) {
        if (header != null) {
            AnFileLog.e("aoa->wifi", "data-fix" + data.length);
            byte[] temp = new byte[header.length + data.length];
            System.arraycopy(header, 0, temp, 0, header.length);
            System.arraycopy(data, 0, temp, header.length, data.length);
            data = temp;
            header = null;
        }

        int len = data.length;

        AnFileLog.e("aoa->wifi", "aoa receive data: " + data.length);

        if (data.length >= 8) {
            if ((data[0] == 'W') && (data[1] == 'L')) {
                switch (data[2]) {
                    case 8:
                        port = PORT_VIDEO;
                        break;
                    case 9:
                    case 12:
                        port = PORT_MSG;
                        break;
                    case 13:
                        port = PORT_DATA;
                        break;
                    case 18:
                        port = PORT_AUDIO;
                        break;
                    case 20:
                        port = PORT_HU;
                        break;
                    default:
                        port = 0;
                        break;
                }
                AnFileLog.e("aoa->wifi", "header - " + port + " : " +
                        Integer.toHexString(data[0]) + " " +
                        Integer.toHexString(data[1]) + " " +
                        Integer.toHexString(data[2]) + " " +
                        Integer.toHexString(data[3]) + " " +
                        Integer.toHexString(data[4]) + " " +
                        Integer.toHexString(data[5]) + " " +
                        Integer.toHexString(data[6]) + " " +
                        Integer.toHexString(data[7]));

                len = CommandSource.toInt(data, 4) + 8;
                AnFileLog.e("aoa->wifi", "parser - " + port + " : " + len);

                if (len == 0) {
                    AnFileLog.e("aoa->wifi", "data-exception(" + port + "): " + data.length);
                    return true;
                }
            }
        }

        if (port < 0) {
            if (data.length >= 8) {
                AnFileLog.e("aoa->wifi", "data-exception: " + data.length);
                return false;
            } else {
                AnFileLog.e("aoa->wifi", "data-reserve" + data.length);
                header = data;
                return true;
            }
        }

        if (buffer == null) {
            buffer = new byte[len];
            buffer_offset = 0;
        }

        int length = Math.min(buffer.length - buffer_offset, data.length);
        System.arraycopy(data, 0, buffer, buffer_offset, length);
        buffer_offset += length;

        if (buffer.length == buffer_offset) {
            if (port > 0) {
                LinkedBlockingQueue<byte[]> dataQueue = dataQueueMap.get(port);
                if (dataQueue == null) {
                    return false;
                }

                try {
                    dataQueue.put(buffer);
                    AnFileLog.e("aoa->wifi", "cache-size - " + port + " : " + count(port));
                    AnFileLog.e("aoa->wifi", "cache-size-total: " + count());
                    buffer = null;
                    buffer_offset = 0;
                    port = -1;
                } catch (InterruptedException e) {
                    return false;
                }
            } else {
                buffer = null;
                buffer_offset = 0;
                port = -1;
            }

            if (data.length - length > 0) {
                byte[] multi_data = new byte[data.length - length];
                AnFileLog.e("aoa->wifi", "multi-data: " + multi_data.length);
                System.arraycopy(data, length, multi_data, 0, multi_data.length);
                write(multi_data);
            }
        }

        return true;
    }

    @Override
    public byte[] read(int port) {
        LinkedBlockingQueue<byte[]> dataQueue = dataQueueMap.get(port);
        if (dataQueue == null) {
            return null;
        }

        try {
            return dataQueue.take();
        } catch (InterruptedException e) {
            return null;
        }
    }

    @Override
    public void clear() {
        LinkedBlockingQueue<byte[]> queue;

        queue = dataQueueMap.get(PORT_DATA);
        if (queue != null) {
            queue.clear();
        }
        queue = dataQueueMap.get(PORT_MSG);
        if (queue != null) {
            queue.clear();
        }
        queue = dataQueueMap.get(PORT_VIDEO);
        if (queue != null) {
            queue.clear();
        }
        queue = dataQueueMap.get(PORT_HU);
        if (queue != null) {
            queue.clear();
        }
        queue = dataQueueMap.get(PORT_AUDIO);
        if (queue != null) {
            queue.clear();
        }

        port = 0;
    }

    @Override
    public int count(int port) {
        LinkedBlockingQueue<byte[]> queue = dataQueueMap.get(port);
        if (queue != null) {
            return queue.size();
        }

        return 0;
    }

    @Override
    public int count() {
        int size = 0;

        LinkedBlockingQueue<byte[]> queue = dataQueueMap.get(PORT_DATA);
        if (queue != null) {
            size += queue.size();
        }
        dataQueueMap.get(PORT_MSG);
        if (queue != null) {
            size += queue.size();
        }
        dataQueueMap.get(PORT_VIDEO);
        if (queue != null) {
            size += queue.size();
        }
        dataQueueMap.get(PORT_HU);
        if (queue != null) {
            size += queue.size();
        }
        dataQueueMap.get(PORT_AUDIO);
        if (queue != null) {
            size += queue.size();
        }

        return size;
    }
}
