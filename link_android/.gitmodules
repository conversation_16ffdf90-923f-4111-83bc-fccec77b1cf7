; [submodule "deepSdk/WeLinkServer"]
; 	path = deepSdk/WeLinkServer
; 	url = ssh://********************:10222/WeLink-Auto/WeLinkServer.git
; [submodule "deepSdk/WeLinkConnector"]
; 	path = deepSdk/WeLinkConnector
; 	url = ssh://********************:10222/WeLink-Auto/WeLinkConnector.git
; [submodule "deepSdk/WeLinkChannel"]
; 	path = deepSdk/WeLinkChannel
; 	url = ssh://********************:10222/WeLink-Auto/WeLinkChannel.git
; [submodule "deepSdk/WeLinkHardwareHub"]
; 	path = deepSdk/WeLinkHardwareHub
; 	url = ssh://********************:10222/WeLink-Auto/WeLinkHardwareHub.git
; [submodule "deepSdk/WeLinkScreen"]
; 	path = deepSdk/WeLinkScreen
; 	url = ssh://********************:10222/WeLink-Platform/WeLinkScreen.git
