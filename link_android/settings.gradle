rootProject.name = "LinkAndroid"
include ':app'
include ':linkSdk'

include ':wlplatform'

include ':wlconnector'

include ':wlchannel'

include ':wlhardwarehub'

include ':wlserver'

include ':wlscreen'

//include ':local_aarlib:ffmpeg_media_retriever_core'
//project(':local_aarlib:ffmpeg_media_retriever_core').projectDir = file("./link_android/local_aarlib/ffmpeg_media_retriever_core")
//
//include ':local_aarlib:ffmpeg_media_retriever_core_native'
//project(':local_aarlib:ffmpeg_media_retriever_core_native').projectDir = file("./link_android/local_aarlib/ffmpeg_media_retriever_core_native")
//
//include ':local_aarlib:ffmpeg_media_retriever_native_v7'
//project(':local_aarlib:ffmpeg_media_retriever_native_v7').projectDir = file("./link_android/local_aarlib/ffmpeg_media_retriever_native_v7")
