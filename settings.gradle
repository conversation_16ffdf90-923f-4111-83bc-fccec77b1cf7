rootProject.name = "haiwai"
include ':app'
include ':basemodule'

/**
 * 如果已经将link_android仓库源码配置好，且启用USE_SDK_SOURCE = true，那么使用本地互联lib源码
 */
if (file("./link_android").exists() && USE_SDK_SOURCE == "true") {
    println("setting.gradle：---> 使用本地互联lib源码 作为module")

    include ':wlplatform'
    project(':wlplatform').projectDir = file("./link_android/wlplatform")

    include ':wlconnector'
    project(':wlconnector').projectDir = file("./link_android/wlconnector")

    include ':wlhardwarehub'
    project(':wlhardwarehub').projectDir = file("./link_android/wlhardwarehub")

    include ':wlserver'
    project(':wlserver').projectDir = file("./link_android/wlserver")

    include ':wlchannel'
    project(':wlchannel').projectDir = file("./link_android/wlchannel")

    include ':wlscreen'
    project(':wlscreen').projectDir = file("./link_android/wlscreen")

    //---> she tips:因为这个库，没有aliyun代理备份，需要去github获取，容易get不到仓库，所以做成本地依赖
//    include ':local_aarlib:ffmpeg_media_retriever_core'
//    project(':local_aarlib:ffmpeg_media_retriever_core').projectDir = file("./link_android/local_aarlib/ffmpeg_media_retriever_core")
//
//    //---> she tips:因为这个库，没有aliyun代理备份，需要去github获取，容易get不到仓库，所以做成本地依赖
//    include ':local_aarlib:ffmpeg_media_retriever_core_native'
//    project(':local_aarlib:ffmpeg_media_retriever_core_native').projectDir = file("./link_android/local_aarlib/ffmpeg_media_retriever_core_native")
//
//    //---> she tips:因为这个库，没有aliyun代理备份，需要去github获取，容易get不到仓库，所以做成本地依赖
//    include ':local_aarlib:ffmpeg_media_retriever_native_v7'
//    project(':local_aarlib:ffmpeg_media_retriever_native_v7').projectDir = file("./link_android/local_aarlib/ffmpeg_media_retriever_native_v7")


} else {
    //---> she tips:因为这个库，没有aliyun代理备份，需要去github获取，容易get不到仓库，所以做成本地依赖
//    include ':local_aarlib:ffmpeg_media_retriever_core'
//    project(':local_aarlib:ffmpeg_media_retriever_core').projectDir = file("./local_aarlib/ffmpeg_media_retriever_core")
//
//    //---> she tips:因为这个库，没有aliyun代理备份，需要去github获取，容易get不到仓库，所以做成本地依赖
//    include ':local_aarlib:ffmpeg_media_retriever_core_native'
//    project(':local_aarlib:ffmpeg_media_retriever_core_native').projectDir = file("./local_aarlib/ffmpeg_media_retriever_core_native")
//
//    //---> she tips:因为这个库，没有aliyun代理备份，需要去github获取，容易get不到仓库，所以做成本地依赖
//    include ':local_aarlib:ffmpeg_media_retriever_native_v7'
//    project(':local_aarlib:ffmpeg_media_retriever_native_v7').projectDir = file("./local_aarlib/ffmpeg_media_retriever_native_v7")

}

