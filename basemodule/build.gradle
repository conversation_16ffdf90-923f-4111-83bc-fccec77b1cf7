//
apply plugin: 'com.android.library'
//
apply from: "../gradleshell/dependencies.gradle"
apply plugin: 'org.jetbrains.kotlin.android'
//
android {
    compileSdkVersion rootProject.ext.android.compileSdk

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdk
        targetSdkVersion rootProject.ext.android.targetSdk
        //
        versionName rootProject.ext.android.versionName
        versionCode rootProject.ext.android.versionCode
        //
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
        ndk {
            abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "boolean", "IS_THIRDAPP", "false"
            //Instrumentation代码覆盖率测试报告开关
            //testCoverageEnabled true
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "boolean", "IS_THIRDAPP", "false"
            //Instrumentation代码覆盖率测试报告开关
            //testCoverageEnabled true
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    sourceSets {
        main {
            jniLibs.srcDirs 'src/main/jniLibs'
            assets.srcDirs 'assets/'
        }
    }
}

dependencies {
//    api fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
//    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLSCREEN_HID
    implementation 'com.autoai.welink.logiclib.skincore:skinframework:0.1.5'
    implementation 'com.autoai.link.baselog:baselog:0.0.11'
    implementation "com.autoai.link.threadpool:threadpool:0.0.6"
//    implementation COMMON_DEPEN.DESIGN
    implementation COMMON_DEPEN.GUAVA_ANDROID
    implementation COMMON_DEPEN.COMMONS_IO
    implementation COMMON_DEPEN.MAPBAR_ANDROID_APPFRAMEWORK
    implementation COMMON_DEPEN.MAPBAR_ANDROID_GUID
    implementation COMMON_DEPEN.MAPBAR_ANDROID_MIRROR
//    implementation COMMON_DEPEN.WELINK_NETHTTPS_MOBSTAT
    implementation COMMON_DEPEN.MAPBAR_ANDROID_ONE_APPFRAMEWORK
    implementation COMMON_DEPEN.JESSYAN_AUTOSIZE
    implementation 'androidx.core:core-ktx:1.6.0'
    // 状态栏透明
    implementation 'com.geyifeng.immersionbar:immersionbar:3.2.2'

}
