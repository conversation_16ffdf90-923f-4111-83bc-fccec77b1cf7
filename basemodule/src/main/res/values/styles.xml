<resources xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">

    <style name="textAppearanceXLargeM">
        <item name="android:textSize" tools:ignore="SpUsage">19dp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="textAppearanceXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">20dp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="ximatextAppearanceXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">16dp</item>
        <item name="android:textColor">#ffffff</item>
    </style>

    <style name="textAppearanceXBig">
        <item name="android:textSize" tools:ignore="SpUsage">36dp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="navi_ffffff_textAppearanceXXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">19dp</item>
        <item name="android:textColor">#ffffff</item>
    </style>

    <style name="textAppearanceXXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">22dp</item>
        <item name="android:textStyle">normal</item>
    </style>
    <!--PermissonActivity所有需要透明的style-->
    <style name="translucent" parent="android:Theme.Light.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>

    <style name="Translucent_NoTitle" parent="android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="iosDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item><!--取消默认Dialog的windowFrame框-->
        <item name="android:windowNoTitle">true</item><!--设置无标题Dialog-->
        <item name="android:backgroundDimEnabled">true</item><!--是否四周变暗-->
        <item name="android:windowIsFloating">true</item><!-- 是否悬浮在activity上 -->
        <item name="android:windowContentOverlay">@null</item><!-- 取消默认ContentOverlay背景 -->
        <item name="android:windowBackground">@android:color/transparent
        </item><!--取消window默认背景 不然四角会有黑影-->
    </style>
</resources>