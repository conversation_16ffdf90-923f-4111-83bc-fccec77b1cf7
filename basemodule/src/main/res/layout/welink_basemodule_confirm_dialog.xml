<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dialog_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/welink_pop_top_dailog_bj"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="30dp"
        android:autoLink="web"
        android:textColor="#ffffff"
        android:textSize="16dp"
        android:visibility="gone"
        tools:ignore="SpUsage" />

    <ImageView
        android:id="@+id/confirm_dialog_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="40dip"
        android:background="@drawable/welink_popdialog_icon_success"
        android:visibility="visible"
        tools:ignore="ContentDescription" />


    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1.0"
        android:autoLink="web"
        android:gravity="center"
        android:lineSpacingExtra="7dp"
        android:textColor="#ffffff"
        android:textSize="16dp"
        tools:ignore="SpUsage" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#696a72" />

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/btn_cancel"
            style="@style/textAppearanceXLarge"
            android:layout_width="0dip"
            android:layout_height="48dp"
            android:layout_weight="1.0"
            android:gravity="center"
            android:minHeight="@dimen/button_min_height"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text=""
            android:textColor="#ffff" />

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="#696a72" />

        <TextView
            android:id="@+id/btn_ok"
            style="@style/textAppearanceXLarge"
            android:layout_width="0dip"
            android:layout_height="48dp"
            android:layout_weight="1.0"
            android:gravity="center"
            android:minHeight="@dimen/button_min_height"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:textColor="#ffffff" />
    </LinearLayout>
</LinearLayout>