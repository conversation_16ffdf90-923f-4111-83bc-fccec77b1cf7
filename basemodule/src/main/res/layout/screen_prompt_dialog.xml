<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:background="@drawable/disclaimers_bg"
    android:orientation="vertical"
    android:paddingBottom="24dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="19dp"
        android:text="镜像已开启"
        android:textColor="#000"
        android:textSize="14sp" />


    <TextView
        android:id="@+id/tv_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="12dp"
        android:text="welink手机互联已建立，您可以返回手机桌面并开始使用手机镜像功能。"
        android:textAlignment="center"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:textColor="#000"
        android:textSize="14sp" />



    <LinearLayout
        android:layout_marginTop="28dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:text="确定"
            android:layout_marginLeft="57dp"
            android:layout_marginRight="57dp"
            android:padding="10dp"
            android:background="@drawable/btn_click_n"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />
    </LinearLayout>
</LinearLayout>