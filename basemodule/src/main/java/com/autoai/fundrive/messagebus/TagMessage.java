package com.autoai.fundrive.messagebus;

import java.util.Objects;

final class TagMessage {

   private Object event;
   private String tag;

    TagMessage(Object event, String tag) {
        this.event = event;
        this.tag = tag;
    }

    public Object getEvent() {
        return event;
    }

    public void setEvent(Object event) {
        this.event = event;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof TagMessage) {
            TagMessage tagMessage = (TagMessage) obj;
            return Objects.equals(tagMessage.event.getClass(), event.getClass())
                    && Objects.equals(tagMessage.tag, tag);
        }
        return false;
    }

    boolean isSameType(final Class<?> eventType, final String tag) {
        return Objects.equals(event.getClass(), eventType)
                && Objects.equals(this.tag, tag);
    }

    @Override
    public String toString() {
        return "event: " + event + ", tag: " + tag;
    }
}
