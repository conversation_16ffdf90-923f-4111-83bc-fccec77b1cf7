package com.autoai.fundrive.basemodule.widget;

import com.autoai.fundrive.basemodule.activity.AppActivity;

public class ToastImpl {
    private final AOAToast mAOAToast;
    private final int mRootViewId;

    public ToastImpl(AppActivity aAppActivity, int aRootViewId) {
        this.mRootViewId = aRootViewId;
        mAOAToast = new AOAToast(aAppActivity);
    }

    public void showAlert(int i, int aRootViewId) {
        int rootViewId = aRootViewId;
        if (rootViewId == -1) {
            rootViewId = mRootViewId;
        }
        mAOAToast.makeText(i, AOAToast.LENGTH_SHORT, rootViewId);
        mAOAToast.show();
    }

    public void showAlert(String s, int aRootViewId) {
        int rootViewId = aRootViewId;
        if (rootViewId == -1) {
            rootViewId = mRootViewId;
        }
        mAOAToast.makeText(s, AOAToast.LENGTH_SHORT, rootViewId);
        mAOAToast.show();
    }
}
