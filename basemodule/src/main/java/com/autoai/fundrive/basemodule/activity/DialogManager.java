package com.autoai.fundrive.basemodule.activity;

import com.autoai.fundrive.basemodule.widget.PopDialogGroup;
import com.autoai.fundrive.basemodule.widget.ToastImpl;
import com.mapbar.android.control.ViewBaseManager;
import com.mapbar.android.model.PageObject;
import com.autoai.fundrive.basemodule.BaseModuleManager;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;

public abstract class DialogManager extends BaseDialogManager implements ActivityService {

    private PageManager mPageManager = null;
    private ToastImpl mToastImpl;
    private PopDialogGroup mDialogGroup;

    public DialogManager(ViewBaseManager aViewBaseManager) {
        super(aViewBaseManager);
        mToastImpl = new ToastImpl(this, -1);
        mDialogGroup = new PopDialogGroup(this);
    }

    @Override
    public void loadModuleService() {

    }

    @Override
    public AppActivity getActivity() {
        return this;
    }

    @Override
    public PageObject createPage(int index) {
        if (mPageManager == null && getContext() != null) {
            mPageManager = getPageManager(this);
        }
        if (mPageManager != null) {
            return mPageManager.createPage(index);
        } else {
            return null;
        }

    }

    public abstract PageManager getPageManager(AppActivity aAppActivity);

    public abstract PageObject getMainPage();

    public abstract int getMainViewLayout();

    @Override
    public void showAlert(int i) {
        mToastImpl.showAlert(i, getRootViewId());
    }

    @Override
    public void showAlert(String s) {
        mToastImpl.showAlert(s, getRootViewId());
    }

    @Override
    public void showAlert(int i, int aRootViewId) {
        mToastImpl.showAlert(i, aRootViewId);
    }

    @Override
    public void showAlert(String s, int aRootViewId) {
        mToastImpl.showAlert(s, aRootViewId);
    }

    @Override
    public void closeDialog(BasePopDialog aDialog, int flagId) {
        aDialog.close(flagId);
    }

    @Override
    public void showDialog(BasePopDialog aDialog, int flagId) {
        aDialog.setAppActivity(this);
        aDialog.setDialogGroup(mDialogGroup);
        aDialog.show(flagId);
    }

    /**
     * 添加销毁生命周期方法
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        closeDialog(0);

        this.getModuleManager().unloadModuleService(BaseModuleManager.TYPE_DIALOG);
        mDialogGroup = null;
        mToastImpl = null;
        mPageManager = null;
    }

    @Override
    public void closeDialog(int flagId) {
        mDialogGroup.dismiss();
    }
}
