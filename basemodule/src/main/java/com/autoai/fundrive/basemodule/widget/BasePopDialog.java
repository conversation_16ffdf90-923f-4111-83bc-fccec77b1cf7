package com.autoai.fundrive.basemodule.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Point;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.commontool.CommonUtil;
import com.mapbar.android.model.OnDialogListener;
import com.autoai.fundrive.basemodule.R;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class BasePopDialog implements PopDialogGroup.PopDialogGroupListener {

    protected PopDialogGroup dialogGroup;
    protected Map<Integer, DialogIdToHome> dialogHomeArr = new HashMap<>();
    protected DialogIdToHome currHome;
    /**
     * 对应屏幕的AppActivity
     */
    protected AppActivity mAppActivity;
    private TextView btnCancel;
    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            currHome.timeCount--;
            if (currHome.timeCount <= 0) {
                handler.removeCallbacksAndMessages(0);
                closeDialog(msg.what);
            } else {
                if (currHome.dialogOkResourceId != 0) {
                    currHome.dialogOkStr = mAppActivity.getContext().getResources().getString(currHome.dialogOkResourceId);
                }
                String str = currHome.dialogOkStr + "(" + currHome.timeCount + "s" + ")";
                btnCancel.setText(str);
                handler.sendEmptyMessageDelayed(1, 1000);
            }
        }
    };

    public BasePopDialog() {

    }

    public void setDialogGroup(PopDialogGroup aDialogGroup) {
        dialogGroup = aDialogGroup;
    }

    @Override
    public void initDialog(int dialogId, int flagId, PopDialogGroup.DialogType dialogType, AOADialog popDialog) {
        switch (dialogId) {
            case 1:
                showTwoButtonDialog(dialogId, flagId, dialogType, popDialog);
                break;
            case 2:
                showOneButtonDialog(dialogId, flagId, dialogType, popDialog);
                break;
            default:
                break;
        }
    }

    @SuppressLint("InflateParams")
    private void showTwoButtonDialog(int dialogId, int flagId, PopDialogGroup.DialogType dialogType, AOADialog popDialog) {
        currHome = dialogHomeArr.get(flagId);
        if (currHome == null) {
            return;
        }
        LayoutInflater inflater = LayoutInflater.from(mAppActivity.getContext());
        View view;
        if (currHome.layoutId > 0) {
            // 得到加载view
            view = inflater.inflate(currHome.layoutId, null);
        } else {
            // 得到加载view
            view = inflater.inflate(R.layout.welink_basemodule_confirm_dialog, null);
            TextView tvTitle = view.findViewById(R.id.tv_title);
            if (currHome.dialogTitleResourceId != 0) {
                tvTitle.setVisibility(View.VISIBLE);
                tvTitle.setText(currHome.dialogTitleResourceId);
            } else if (!TextUtils.isEmpty(currHome.dialogTitleStr)) {
                tvTitle.setVisibility(View.VISIBLE);
                tvTitle.setText(currHome.dialogTitleStr);
            } else {
                tvTitle.setVisibility(View.GONE);
            }
        }
        setDialogSize(view, dialogType, dialogId, flagId);
        popDialog.setContentView(view);

        ImageView icon = view.findViewById(R.id.confirm_dialog_icon);
        if (currHome.backgroundResourceId > 0) {
            icon.setVisibility(View.VISIBLE);
            icon.setBackgroundResource(currHome.backgroundResourceId);
        } else {
            icon.setVisibility(View.GONE);
        }

        TextView tvDesc = view.findViewById(R.id.tv_desc);
        if (currHome.dialogContentResourceId != 0) {
            tvDesc.setText(currHome.dialogContentResourceId);
        } else {
            tvDesc.setText(currHome.dialogContentStr);
        }

        TextView btnCancel = view.findViewById(R.id.btn_cancel);
        if (currHome.dialogCancelResourceId != 0) {
            btnCancel.setText(currHome.dialogCancelResourceId);
        } else {
            btnCancel.setText(currHome.dialogCancelStr);
        }
        btnCancel.setOnClickListener(v -> {
            if (currHome.mListener != null) {
                currHome.mListener.onCancel();
            }
            closeDialog(flagId);
        });

        TextView btn_ok = view.findViewById(R.id.btn_ok);
        if (currHome.dialogOkResourceId != 0) {
            btn_ok.setText(currHome.dialogOkResourceId);
        } else {
            btn_ok.setText(currHome.dialogOkStr);
        }
        btn_ok.setOnClickListener(v -> {
            if (currHome.mListener != null) {
                currHome.mListener.onOk();
            }
            closeDialog(flagId);
        });

        popDialog.setOnCancelListener(() -> {
            if (currHome.mListener != null) {
                currHome.mListener.onCancel();
            }
            closeDialog(flagId);
        });
        popDialog.setCanceledOnTouchOutside(currHome.isCanceledOnTouchOutside);
        popDialog.setCancelable(false);
        popDialog.show();
    }

    @SuppressLint("InflateParams")
    public void showOneButtonDialog(int dialogId, int flagId, PopDialogGroup.DialogType dialogType, AOADialog popDialog) {
        try {
            currHome = dialogHomeArr.get(flagId);
            if (currHome == null) {
                return;
            }

            handler.removeCallbacksAndMessages(0);
            LayoutInflater inflater = LayoutInflater.from(mAppActivity.getContext());
            View view;
            if (currHome.layoutId > 0) {
                view = inflater.inflate(currHome.layoutId, null);
            } else {
                view = inflater.inflate(R.layout.welink_basemodule_onebutton_dialog, null);
            }
            setDialogSize(view, dialogType, dialogId, flagId);
            popDialog.setContentView(view);
            ImageView tvTitle = view
                    .findViewById(R.id.one_tv_title);
            if (currHome.backgroundResourceId > 0) {
                tvTitle.setVisibility(View.VISIBLE);
                tvTitle.setBackgroundResource(currHome.backgroundResourceId);
            } else {
                tvTitle.setVisibility(View.GONE);
            }
            TextView tvDesc = view.findViewById(R.id.one_tv_desc);
            if (currHome.dialogContentResourceId != 0) {
                tvDesc.setText(currHome.dialogContentResourceId);
            } else {
                tvDesc.setText(currHome.dialogContentStr);
            }
            btnCancel = view.findViewById(R.id.one_btn_cancel);
            btnCancel.setOnClickListener(v -> {
                handler.removeCallbacksAndMessages(0);
                if (currHome.mListener != null) {
                    currHome.mListener.onCancel();
                }
                closeDialog(flagId);
            });
            popDialog.setOnCancelListener(() -> {
                handler.removeCallbacksAndMessages(0);
                if (currHome.mListener != null) {
                    currHome.mListener.onCancel();
                }
                closeDialog(flagId);
            });

            popDialog.setCanceledOnTouchOutside(false);
            popDialog.setCancelable(true);
            popDialog.show();
            // 没有3s消失的功能
            if (!currHome.isShowTime) {
                if (currHome.dialogOkResourceId != 0) {
                    btnCancel.setText(currHome.dialogOkResourceId);
                } else {
                    btnCancel.setText(currHome.dialogOkStr);
                }
            } else {//用倒计时需要设置倒计时时间，默认是3s
                if (currHome.timeCount < 3) {
                    currHome.timeCount = 3;
                }
                if (currHome.dialogOkResourceId != 0) {
                    currHome.dialogOkStr = mAppActivity.getContext().getResources().getString(currHome.dialogOkResourceId);
                }
                String cancelStr = currHome.dialogOkStr + String.format("(%ss)", currHome.timeCount);
                btnCancel.setText(cancelStr);
                handler.sendEmptyMessageDelayed(flagId, 1000);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public int getRootViewId() {
        //return mAppActivity.getRootViewId();
        return R.id.flyt_dialog;
    }

    private void setDialogSize(View view, PopDialogGroup.DialogType dialogType, int dialogId, int flagId) {
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(1, 1);
        setDialogSize(params, dialogType, dialogId, flagId);
        params.gravity = Gravity.CENTER;
        view.setLayoutParams(params);
    }

    @Override
    public void setDialogSize(ViewGroup.LayoutParams aParams, PopDialogGroup.DialogType dialogType, int dialogId, int flagId) {
        WindowManager m = (WindowManager) mAppActivity.getContext().getSystemService(Context.WINDOW_SERVICE);
        // 获取屏幕宽、高用
        Display d = m.getDefaultDisplay();
        Point point = new Point();
        d.getSize(point);
        int w, h;
        switch (dialogType) {
            case EBLUETOOTH:
                w = point.x;
                h = (int) (point.y * 0.9);
                break;
            case ENAVIGATION:
                w = (int) (point.x * 0.77);
                h = (int) (point.y * 0.8);
                break;
            case EONEBUTTON:
                w = (int) (point.x * 0.45);
                h = (int) (point.y * 0.5);
                break;
            case EACTIONS:
            case VR:
                w = point.x;
                h = point.y;
                break;
            case ENONE:
                if (point.x > point.y) {
                    w = (int) (point.x * 0.7);
                    h = (int) (point.y * 0.75);
                } else {
                    w = (int) (point.x * 0.45);
                    h = (int) (point.y * 0.45);
                }
                break;
            case ENET:
                w = (int) (point.x * 0.9);
                h = CommonUtil.dip2px(mAppActivity.getContext(), 196);
                break;
            case EKNOW:
            case EWARN:
                w = (int) (point.x * 0.45);
                h = (int) (point.y * 0.45);
                break;
            default:
                if (point.x > point.y) {
                    w = (int) (point.x * 0.85);
                    h = (int) (point.y * 0.85);
                } else {
                    w = (int) (point.y * 0.85);
                    h = (int) (point.x * 0.85);
                }
                break;
        }
        if (w > h) {
            aParams.width = w;
            aParams.height = h;
        } else {
            aParams.width = h;
            aParams.height = w;
        }
    }

    /**
     * 创建AOADialog时绑定对应的AppActivity
     */
    @Override
    public void setAppActivity(AppActivity appActivity) {
        mAppActivity = appActivity;
    }

    public AppActivity getAppActivity() {
        return mAppActivity;
    }

    public static class DialogIdToHome {
        public int layoutId;
        /**
         * 对话框分类ID
         */
        public int dialogId;
        /**
         * 对话框标识ID
         */
        public int flagId;
        public int backgroundResourceId;
        public int dialogTitleResourceId;
        public String dialogTitleStr;
        public int dialogContentResourceId;
        public String dialogContentStr;
        public int dialogCancelResourceId;
        public String dialogCancelStr;
        public int dialogOkResourceId;
        public String dialogOkStr;
        public OnDialogListener mListener;
        public boolean isCanceledOnTouchOutside = true;
        public boolean isShowTime;
        public int timeCount;
        public PopDialogGroup.DialogType dialogType;
    }

    protected void closeDialog(int flagId) {
        dialogGroup.dismiss(flagId);
    }

    public void show(int flagId) {

    }

    public void close(int flagId) {
        closeDialog(flagId);
    }
}
