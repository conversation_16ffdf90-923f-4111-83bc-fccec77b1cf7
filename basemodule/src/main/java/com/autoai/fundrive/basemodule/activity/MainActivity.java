package com.autoai.fundrive.basemodule.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.BluetoothHidDevice;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.view.View;
import android.view.ViewGroup;

import com.autoai.fundrive.commontool.DevelopModel;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.OnWiFiConnectListener;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.fundrive.platform.sendmsg.SendMsgToCarHelper;
import com.autoai.fundrive.receiver.NetworkReceiver;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.autoai.welink.platform.WLPlatform;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.listener.LinkConnectListener;
import com.autoai.fundrive.platform.listener.LinkPlatformListener;
import com.autoai.welink.screen.WLScreen;
import com.gyf.immersionbar.ImmersionBar;
import com.mapbar.android.control.ViewBaseManager;
import com.mapbar.android.model.PageObject;
import com.autoai.fundrive.basemodule.BaseModuleManager;
import com.autoai.fundrive.basemodule.BuildConfig;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.messagebus.MessageCenter;
import com.autoai.fundrive.messagebus.bean.ParamSet;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

import me.jessyan.autosize.internal.CancelAdapt;

public abstract class MainActivity extends Activity implements ActivityService, CancelAdapt {

    private BaseModuleActivity mBaseModuleActivity = null;
    /**
     * 适配返回键
     * 车机端DialogManager需要暴露给子类，
     */
    protected volatile DialogManager mDialogManager = null;
    /**
     * 适配返回键
     * 需要把互联状态暴漏给子类，用于判断当前时一车机端为准还是手机端为准
     */
    protected boolean isConnectCar = false;
    protected boolean isAOA = true;
    private BroadcastReceiver mBroadcastReceiver = null;
    private BroadcastReceiver mBluetoothReceiver = null;
    private boolean hasTargetDeviceDisconnected = false;

    private PageManager mPageManager = null;
    private IHUAndMUChangePages mHUMUChangePages;
    /**
     * 监听网络状态
     */
    private NetworkReceiver wifiReceiver;
    public OnWiFiConnectListener wiFiBleLinkTopManger;

    private final LinkConnectListener connectListener = new LinkConnectListener() {
        @Override
        public boolean onConnected(Dialog dialog) {
            LogManager.d("onConnected start ----------->");
            if (dialog == null) {
                LogManager.d("dialog is null, onConnected start is not support display ----------->");
                return false;
            }
            initDialogContext(dialog.getContext());
            broadcastConnectStatus(true);
            if (mDialogManager == null) {
                mDialogManager = getDialogManager(mBaseModuleActivity.getViewBaseManager());
                mDialogManager.setScreenIndex(mBaseModuleActivity.getViewBaseManager().addViewManager(mDialogManager));
                mDialogManager.initModuleManager();
            }
            runOnUiThread(() -> {
                if (mHUMUChangePages != null) {
                    mHUMUChangePages.showPageOnHU(mDialogManager, dialog, mBaseModuleActivity);
                    mHUMUChangePages.showPageOnMU(mBaseModuleActivity, getUnuseView());
                }
            });

            return true;
        }

        @Override
        public void onDisconnected() {
            LogManager.d("onDisconnected ----------->");
            disconnect();
            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.isConnectCar(false);
            }
        }
        @Override
        public void onConnectError(int type) {
            LogManager.d("onError type:" + type);
            onDisconnected();
            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.isConnectCar(false);
            }
        }
    };
    private final LinkPlatformListener platformListener = new LinkPlatformListener() {

        @Override
        public void onLinkDisconnected() {
            LogManager.d("onLinkDisconnected ------------->");
            MainActivity.this.isAOA = true;
            connectionStatus(400);
        }

        @Override
        public void onBleLinkError(int error) {
            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.OnError(error);
            }
        }

        @Override
        public void onBleLinkStatus(int status) {
            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.OnConnectListener(status);
            }
        }

        @Override
        public void onBleLinkScanTimeout() {
            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.onBleLinkScanTimeout();
            }
        }

        @Override
        public void onBleLinkJoinTimeout() {
            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.onBleLinkJoinTimeout();
            }
        }

        @Override
        public void onConnecting() {
            connectionStatus(100);
        }

        @Override
        public void onLinkConnected(String vehicleType, boolean isAOA) {
            LogManager.d("onLinkConnected -------------> ==" + isAOA);
            LogManager.d("onLinkConnected -------------> =11=" + Thread.currentThread().getName());
            MainActivity.this.isAOA = isAOA;
            
            // 跳过 WLConnectManager，直接调用录屏适配器
            LogManager.i("【NEW_FLOW】Skip WLConnectManager, direct call connectedAdapter()");
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.connectedAdapter();

            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.isAOA(isAOA);
            }
            if (wiFiBleLinkTopManger != null) {
                wiFiBleLinkTopManger.isConnectCar(true);
            }
            connectionStatus(200);
//            if (!isAOA){
//                //开始校准
//                WLProtocolManager.getInstance().sendSureCalibration();
//            }
            sendDeviceInfoToCar();
            // 互联成功后，检查是否有匹配的HID设备
            collectAndSendAllHIDDeviceInfo();
            // 重置蓝牙断开标记，准备新的连接检测
            hasTargetDeviceDisconnected = false;
           //互联成功已有投屏权限 直接回到桌面
            if(platformManager.isScreen()){
                goHome(mBaseModuleActivity.getContext());
            };
        }

    };

    /**
     * 预留给各个业务launcher在车机互联成功做车机端环境的初始化操作，类似车机端Application中的一些初始化实现
     * 当前用到的：换肤、多语言适配
     */
    protected void initDialogContext(Context context) {

    }

    protected void disconnect() {
        /*runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (successdialog!=null){
                    successdialog.dismiss();
                }
                errordialog = new ScreenCastingPromptDialog(MainActivity.this);
                errordialog.setConfirmListener(view -> {
                    errordialog.dismiss();
                });
                errordialog.setTextListener("投屏失败","投屏失败，请尝试重新操作。","知道了");
                errordialog.show();
            }
        });*/
        broadcastConnectStatus(false);
        runOnUiThread(() -> {
            if (mHUMUChangePages != null) {
                mHUMUChangePages.clearMuPage(mBaseModuleActivity);
                mHUMUChangePages.clearHUPage(mDialogManager, mBaseModuleActivity);
            }
            if (mDialogManager != null) {
                mDialogManager.onDestroy();
                mDialogManager = null;
            }
        });
    }

    private void broadcastConnectStatus(boolean status) {
        isConnectCar = status;
        //
        Map<String, Boolean> param = new HashMap<>(4);
        param.put("isConnectCar", status);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().postSticky(paramSet, Configs.COMMON_BROADCAST);
    }

    private void startLink(Intent intent) {
        if (wiFiBleLinkTopManger != null) {
            wiFiBleLinkTopManger.hideTopTip();
        }
        ThreadPoolUtil.getInstance().getDefaultExecutor().execute(new Runnable() {
            @Override
            public void run() {
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                if (intent.hasExtra("UsbAccessory")) {
                    UsbAccessory usbAccessory = intent.getParcelableExtra("UsbAccessory");
                    Intent usbIntent = new Intent();
                    usbIntent.setAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
                    usbIntent.putExtra("accessory", usbAccessory);
                    platformManager.startLink(usbIntent);
                } else {
                    platformManager.startLink(intent);
                }
            }
        });
    }

    public void goHome(Context context) {

        /*if(!isAOA){
            boolean isOn = WLScreen.isAccessibilitySettingsOn(this);
            LogManager.d("isAccessibilitySettingsOn -------------->"+isOn);
            if(!isOn) {
                Intent intent = new Intent("android.settings.ACCESSIBILITY_SETTINGS");
                startActivity(intent);
            }else{
                Intent homeIntent = new Intent(Intent.ACTION_MAIN);
                homeIntent.addCategory(Intent.CATEGORY_HOME);
                homeIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(homeIntent);
            }
        }else{*/
        Intent homeIntent = new Intent(Intent.ACTION_MAIN);
        homeIntent.addCategory(Intent.CATEGORY_HOME);
        homeIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(homeIntent);
//        }
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        mBaseModuleActivity = new BaseModuleActivity(this);
        mBaseModuleActivity.setScreenIndex(0);
        mBaseModuleActivity.setRootViewId(getRootViewId());
        mPageManager = getPageManager(mBaseModuleActivity);

        BaseModuleManager moduleManager = getModuleManager();
        moduleManager.setScreenIndex(mBaseModuleActivity.getScreenIndex());
        mBaseModuleActivity.setModuleManager(moduleManager);
        mBaseModuleActivity.setAnimatorResId(getAnimatorResId());
        mBaseModuleActivity.init();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        LogManager.d("onNewIntent");
        setIntent(intent);
        if (!BuildConfig.IS_THIRDAPP) {
            if (getIntent() != null && UsbManager.ACTION_USB_ACCESSORY_ATTACHED.equals(getIntent().getAction())) {
                startLink(getIntent());
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
       /* getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);*/
        //沉浸式状态栏
        setContentView(getMainViewLayout());
//        XPermissionManager.getInstance(this).setActivity(this);

        initPageView();
        //沉浸式状态栏透明黑字
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init();
//        requestMustPermissions();
        mBaseModuleActivity.loadService();
//   屏蔽 改为同意弹框后开始     initPlatform();
        //监听网络变话
        registerWifi();
        //监听HID设备连接
        monitorHIDDeviceConnection();
    }

    private void registerWifi() {
        // 在Activity或其他组件中注册BroadcastReceiver
        IntentFilter intentFilter = new IntentFilter();
//        intentFilter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
        intentFilter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
//        intentFilter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION);

        wifiReceiver = new NetworkReceiver();
        registerReceiver(wifiReceiver, intentFilter);

    }

    public void initPlatform() {
        LogManager.d("initPlatform -------------->");
        //非第三方app
        if (!BuildConfig.IS_THIRDAPP) {
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            //C日志等级区分
            int logLevel = DevelopModel.getInstance().getLogLevel(this);
            LogManager.d("logLevel ---->" + logLevel);
            switch(logLevel){
             case 1:
                 platformManager.init(this, platformListener,7);
                 break;
             case 2:
                 platformManager.init(this, platformListener,1);
                 break;
             case 3:
                 platformManager.init(this, platformListener,3);
                 break;
            case -1:
                 platformManager.init(this, platformListener,8);
                 break;
                 default:
                 platformManager.init(this, platformListener,7);
                 LogManager.d("logLevel ---->" + logLevel);
            }
            platformManager.initConnect(this, connectListener);
            if (getIntent() != null && getIntent().getAction() != null && getIntent().getAction().equals(UsbManager.ACTION_USB_ACCESSORY_ATTACHED)) {
                startLink(getIntent());
            }
        } else {
            //第三方app - 保持原有逻辑，使用WLPlatformManager
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.initConnect(this, connectListener);
        }
    }

//    /**
//     * grantResults对应于申请的结果，这里的数组对应于申请时的第二个权限字符串数组。
//     * 如果你同时申请两个权限，那么grantResults的length就为2，分别记录你两个权限的申请结果
//     */
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        XPermissionManager.getInstance(this).onRequestPermissionsResult(requestCode, permissions, grantResults, this);
//    }

    @Override
    protected void onResume() {
        super.onResume();

//        // 用于用户跳转到设置页面后再次做权限检查
//        if (XPermissionManager.getInstance(this).needRequestMustPermission()) {
//            requestMustPermissions();
//        }
        Map<String, Boolean> param = new HashMap<>(4);
        param.put("isAppLifeForeground", true);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, Configs.COMMON_BROADCAST);
    }

    @Override
    protected void onPause() {
        super.onPause();
        Map<String, Boolean> param = new HashMap<>(4);
        param.put("isAppLifeForeground", false);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, Configs.COMMON_BROADCAST);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LogManager.d("onDestroy---->");
        if (wiFiBleLinkTopManger != null) {
            wiFiBleLinkTopManger.onDestory();
        }
        if (wifiReceiver != null) {
            unregisterReceiver(wifiReceiver);
        }
        if (mBluetoothReceiver != null) {
            unregisterReceiver(mBluetoothReceiver);
            mBluetoothReceiver = null;
        }
        if (!BuildConfig.IS_THIRDAPP) {
            if (mBroadcastReceiver != null) {
                unregisterReceiver(mBroadcastReceiver);
                mBroadcastReceiver = null;
            }
        }
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.stopLink();


        if (mDialogManager != null) {
            mDialogManager.onDestroy();
            mDialogManager = null;
        }

        mPageManager = null;
        mHUMUChangePages = null;
        mBaseModuleActivity.onDestroy();
        mBaseModuleActivity = null;
    }

    @Override
    public void loadModuleService() {
        BaseModuleManager moduleManager = getModuleManager();
        String[] preArray = moduleManager.preInitModule();
        if (preArray != null && preArray.length > 0) {
            for (String s : preArray) {
                initModule(s);
            }
        }
    }

    protected void initModule(String aModuleName) {
        BaseModuleManager moduleManager = getModuleManager();
        if (moduleManager != null) {
            moduleManager.loadModuleService(BaseModuleManager.TYPE_ACTIVITY, aModuleName);
        }
    }

    @Override
    public AppActivity getActivity() {
        LogManager.d(mBaseModuleActivity.toString());
        return mBaseModuleActivity;
    }

    @Override
    public PageObject createPage(int index) {
        LogManager.d("createPage");
        return mPageManager.createPage(index);
    }

    public abstract PageObject getMainPage();

    public abstract PageManager getPageManager(AppActivity aAppActivity);

    public abstract DialogManager getDialogManager(ViewBaseManager aViewBaseManager);

    public abstract BaseModuleManager getModuleManager();

    public abstract int getRootViewId();
    public abstract void connectionStatus(int status);

    public abstract int getAnimatorResId();

    public abstract int getMainViewLayout();

    public abstract View getUnuseView();

    private void initPageView() {
        ViewGroup view = this.findViewById(getAnimatorResId());
        PageObject mainPage = getMainPage();
        view.addView(mainPage.getView());
        mBaseModuleActivity.getViewInterface().pushPage(mainPage, -1, null);
        mBaseModuleActivity.addMessageManager(mBaseModuleActivity);

        mHUMUChangePages = new ClearHUPages();

    }

//    /**
//     * 申请必要权限
//     */
//    private void requestMustPermissions() {
//        // 防止onResume中多次调用
//        String[] permissions = getMustRequestPermissions();
//        XPermissionManager.getInstance(this).requestPermissions(XPermissionManager.PERMISSION_MUST_REQ, permissions,
//                new XPermissionManager.OnPermissionListener() {
//                    @Override
//                    public void onPermissionGranted() {
//                        mBaseModuleActivity.loadService();
//                        initPlatform();
//                    }
//
//                    @Override
//                    public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied, String permissionNames) {
//                        // 被拒绝 重置申请
//                        int[] index = {1, 1};
//                        PageChangeUtils.showDialog("app", index, 1);
//                    }
//
//                    @Override
//                    public void onShowTips(int requestCode, boolean needShowTips) {
//                        if (needShowTips) {
//                            int[] index = {2, 2};
//                            PageChangeUtils.showDialog("app", index, 2);
//                        } else {
//                            PageChangeUtils.closeDialog(-1, 2);
//                        }
//                    }
//                });
//        XPermissionManager.getInstance(this).setNeedRequestMustPermission(false);
//    }
//
//    /**
//     * 获取必要权限数组
//     */
//    protected abstract String[] getMustRequestPermissions();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.activityResult(requestCode, resultCode, data);
        if (requestCode != WLScreen.SCREEN_CAPTURE_REQUEST_CODE) {
            return;
        }
        if (resultCode == Activity.RESULT_OK) {
           /* if (errordialog!=null){
                errordialog.dismiss();
            }
            successdialog = new ScreenCastingPromptDialog(MainActivity.this);
            successdialog.setConfirmListener(view -> {
                successdialog.dismiss();
            });
            successdialog.setTextListener("镜像已开启","welink手机互联已建立，您可以返回手机桌面并开始使用手机镜像功能。","知道了");
            successdialog.show();
*/
            goHome(mBaseModuleActivity.getContext());
        } else {
            SendMsgToCarHelper.sendActionCarHome("cancel_cast");
        }
    }


    /**
     * 发送设备信息到车机端
     * 根据本机的蓝牙name去判断车机的蓝牙是否被占用
     * 同时发送屏幕超时设置给车机
     */
    private void sendDeviceInfoToCar() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        @SuppressLint("MissingPermission") String BluetoothName = bluetoothAdapter.getName();
        LogManager.d("bluetooth address:" + BluetoothName);

        String model = Build.MODEL;
        String deviceName = Build.DEVICE;
        String deviceBrand = Build.BRAND;

        // 读取屏幕超时设置
        int screenTimeout = getScreenTimeout();

        // 发送包含屏幕超时信息的设备信息
        WLProtocolManager.getInstance().sendDeviceInfo(model, deviceName, deviceBrand, BluetoothName, screenTimeout);
    }

    /**
     * 获取系统屏幕超时设置
     *
     * @return 屏幕超时时间(毫秒)，获取失败时返回默认值30秒
     */
    private int getScreenTimeout() {
        try {
            int timeout = Settings.System.getInt(getContentResolver(), Settings.System.SCREEN_OFF_TIMEOUT, 30000);
            LogManager.i("getScreenTimeout: 成功读取屏幕超时设置 timeout=" + timeout + "ms (" + (timeout/1000) + "秒)");
            return timeout;
        } catch (Exception e) {
            LogManager.e("getScreenTimeout: 读取屏幕超时设置失败 " + e.getMessage());
            // 返回默认值30秒
            return 30000;
        }
    }

    /**
     * 获取蓝牙地址
     */
    private String getBluetoothMacAddress() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        String bluetoothMacAddress = "";
        try {
            Field mServiceField = bluetoothAdapter.getClass().getDeclaredField("mService");
            mServiceField.setAccessible(true);

            Object btManagerService = mServiceField.get(bluetoothAdapter);

            if (btManagerService != null) {
                bluetoothMacAddress = (String) btManagerService.getClass().getMethod("getAddress").invoke(btManagerService);
            }
        } catch (NoSuchFieldException | NoSuchMethodException | IllegalAccessException | InvocationTargetException ignore) {

        }
        return bluetoothMacAddress;
    }
    
    /**
     * 监听HID Profile设备连接情况并发送连接设备信息到车机
     */
    @SuppressLint("MissingPermission")
    private void monitorHIDDeviceConnection() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter == null) {
            LogManager.d("Device does not support Bluetooth");
            return;
        }
        
       // 注册蓝牙适配器连接状态变化的广播接收器
       IntentFilter filter = new IntentFilter();
       // 监听蓝牙适配器连接状态变化
       filter.addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED);
       // 监听蓝牙适配器开关状态变化
       filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);

       mBluetoothReceiver = new BroadcastReceiver() {
           @Override
           public void onReceive(Context context, Intent intent) {
               String action = intent.getAction();

               LogManager.i("sendHIDPairingStatus Bluetooth device state changed: " + action);
               // 监听蓝牙适配器连接状态变化
               if (BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED.equals(action)) {
                   int state = intent.getIntExtra(BluetoothAdapter.EXTRA_CONNECTION_STATE, BluetoothAdapter.STATE_DISCONNECTED);
                   BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);

                   if (device != null) {
                       String deviceAddress = device.getAddress();
                       String deviceName = device.getName();

                       LogManager.i("sendHIDPairingStatus Bluetooth device state changed: " + deviceName + ", address: " + deviceAddress + ", state: " + state);

                       WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                       // 获取目标设备的MAC地址
                       String huMacAddress = platformManager.getHuBtMacAddress();

                       // 判断当前设备是否是目标设备
                       if (huMacAddress != null && huMacAddress.equals(deviceAddress)) {
                           if (state == BluetoothAdapter.STATE_CONNECTED) {
                               // 目标设备已连接，发送配对成功消息
                               WLProtocolManager.getInstance().sendSpecificHIDDeviceInfo(deviceName, deviceAddress, true);
                               hasTargetDeviceDisconnected = false; // 重置断开标记
                               LogManager.i("Target device connected: " + deviceName + ", address: " + deviceAddress);
                           } else if (state == BluetoothAdapter.STATE_DISCONNECTED) {
                               // 目标设备已断开，发送配对断开消息
                               WLProtocolManager.getInstance().sendSpecificHIDDeviceInfo(deviceName, deviceAddress, false);
                               hasTargetDeviceDisconnected = true; // 标记已发送断开通知
                               LogManager.i("Target device disconnected: " + deviceName + ", address: " + deviceAddress);
                           }
                       }
                   }
               }
               // 监听蓝牙适配器开关状态变化
               else if (BluetoothAdapter.ACTION_STATE_CHANGED.equals(action)) {
                   int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, -1);
                   LogManager.i("sendHIDPairingStatus Bluetooth adapter state changed: " + state);
                   
                   // 蓝牙关闭时，检查是否有已连接的车机设备并发送断开通知
                   if (state == BluetoothAdapter.STATE_OFF) {
                       WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                       String huMacAddress = platformManager.getHuBtMacAddress();
                       
                       // 如果互联状态下有目标车机设备，且还未发送过断开通知，则发送断开通知
                       if (huMacAddress != null && isConnectCar && !hasTargetDeviceDisconnected) {
                           WLProtocolManager.getInstance().sendSpecificHIDDeviceInfo(null, huMacAddress, false);
                           hasTargetDeviceDisconnected = true; // 标记已发送断开通知
                           LogManager.i("Bluetooth adapter turned off, sending disconnect notification for device: " + huMacAddress);
                       }
                   }
               }
           }
       };

        
        registerReceiver(mBluetoothReceiver, filter);
    }
    
    /**
     * 检查是否存在特定HID设备并发送状态到车机
     */
    @SuppressLint("MissingPermission")
    private void collectAndSendAllHIDDeviceInfo() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter == null) {
            LogManager.d("Device does not support Bluetooth");
            return;
        }

        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        // 获取目标设备的MAC地址
        String targetMacAddress = platformManager.getHuBtMacAddress();

        if (targetMacAddress == null) {
            LogManager.d("HID No target MAC address defined");
            return;
        }

        LogManager.i("HID  collectAndSendAllHIDDeviceInfo");
        // * HID Host 是系统接口，拿不到。所以只能使用 A2DP来实现
        // 使用HID设备配置文件获取所有连接的HID设备
        bluetoothAdapter.getProfileProxy(this, new BluetoothProfile.ServiceListener() {
            @Override
            public void onServiceConnected(int profile, BluetoothProfile proxy) {
                if (profile == BluetoothProfile.A2DP) {
                    // 获取所有已连接的HID设备
                    List<BluetoothDevice> devices = proxy.getConnectedDevices();
                    LogManager.i("HID Found " + devices.size() + " connected HID devices");
                    
                    // 查找是否有MAC地址匹配的设备
                    boolean foundTargetDevice = false;
                    String deviceName = null;
                    
                    // 检查每个设备
                    for (BluetoothDevice device : devices) {
                        if (device != null) {
                            String address = device.getAddress();
                            
                            LogManager.d("HID HID device: " + device.getName() + ", address: " + address);
                            
                            // 检查MAC地址是否匹配
                            if (targetMacAddress.equals(address)) {
                                foundTargetDevice = true;
                                deviceName = device.getName();
                                break;
                            }
                        }
                    }
                    
                    // 根据是否找到目标设备发送消息
                    if (foundTargetDevice) {
                        // 找到目标设备，发送连接状态
                        WLProtocolManager.getInstance().sendSpecificHIDDeviceInfo(deviceName, targetMacAddress, true);
                        LogManager.i("HID Found target HID device: " + deviceName + ", address: " + targetMacAddress);
                    } else {
                        // 未找到目标设备，发送断开状态
                        WLProtocolManager.getInstance().sendSpecificHIDDeviceInfo(null, targetMacAddress, false);
                        LogManager.i("HID Target HID device not connected, address: " + targetMacAddress);
                    }
                    
                    // 关闭代理连接
                    bluetoothAdapter.closeProfileProxy(profile, proxy);
                }
            }

            @Override
            public void onServiceDisconnected(int profile) {
                LogManager.d("Bluetooth profile service disconnected: " + profile);
            }
        }, BluetoothProfile.A2DP);
    }
    

}
