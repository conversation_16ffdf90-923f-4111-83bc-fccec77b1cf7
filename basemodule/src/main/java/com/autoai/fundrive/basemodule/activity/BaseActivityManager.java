package com.autoai.fundrive.basemodule.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.mapbar.android.control.ViewBaseManager;
import com.autoai.fundrive.basemodule.BuildConfig;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.MessageManager;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.messagebus.MessageCenter;

import java.util.List;

/**
 * 处理与页面切换框架之间的交互
 * 处理打开第三方APP
 */
public abstract class BaseActivityManager extends AppActivity {
    private Activity mActivity;
    private int mScreenIndex = 1;
    private MessageManager messageManager = null;
    private ViewBaseManager mViewBaseManager = null;

    public BaseActivityManager(@NonNull Activity aActivity) {
        mActivity = aActivity;
    }

    public void initViewBaseManager() {
        mViewBaseManager = new ViewBaseManager(this, 2);
        messageManager = new MessageManager(mViewBaseManager.getViewArray());
        messageManager.register();
    }

    public ViewBaseManager getViewBaseManager() {
        return mViewBaseManager;
    }

    public void addMessageManager(AppActivity aAppActivity) {
        if (messageManager != null) {
            messageManager.addAppActivity(aAppActivity);
        }
    }

    public void removeMessageManger(AppActivity aAppActivity) {
        if (messageManager != null) {
            messageManager.removeAppActivity(aAppActivity);
        }
    }

    @Override
    public View findView(int id) {
        return mActivity.findViewById(id);
    }

    @Override
    public void setScreenIndex(int i) {
        this.mScreenIndex = i;
    }

    @Override
    public int getScreenIndex() {
        return mScreenIndex;
    }

    @Override
    public Context getContext() {
        return mActivity;
    }

    /**
     * 注销所有已经注册到消息总线上的消息
     */
    public void onDestroy() {
        messageManager.unregister();
        messageManager.removeAll();
        messageManager = null;
        mActivity = null;
        MessageCenter.getDefault().unregisterAll();
        mViewBaseManager.onDestroy();
        mViewBaseManager = null;
    }

    @Override
    public void performThirdApp(String aAppPackageName, String aClassName, int aAppType) {
        if (!BuildConfig.IS_THIRDAPP) {
            if (!isAppAvailable(aAppPackageName)) {
                LogManager.d( "未安装" + aAppPackageName + "APP");
                return;
            }
            LogManager.d( "已经安装第三方APP");
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            String connectStr = platformManager.getConnectKey(aAppPackageName, aAppType);
            LogManager.d( "连接字符串：" + connectStr);
//                if(connectStr != null) {
            Intent intent = new Intent();
            Bundle bundle = new Bundle();
            bundle.putString(Configs.THIRD_APP_CONSTANT, connectStr);
            intent.setClassName(aAppPackageName, aClassName);
            intent.putExtras(bundle);
            mActivity.startActivity(intent);

            platformManager.requestConnect(aAppPackageName, connectStr);
//                }
        } else {
            if (!isAppAvailable("com.fvwcrs.android.launcher")) {
                LogManager.d( "未安装com.fvwcrs.android.launcher APP");
                return;
            }
            LogManager.d( "已经安装第三方APP");
            Intent intent = new Intent();
            intent.putExtra("aAppPackageName", aAppPackageName);
            intent.putExtra("aAppAvtivityName", aClassName);
            intent.putExtra("aAppType", aAppType);
            intent.setClassName("com.fvwcrs.android.launcher", "com.autoai.welink.logiclib.activity.ModuleActivity");
            mActivity.startActivity(intent);
        }
    }

    /**
     * 判断是否存在该应用
     *
     * @param appName APP的包名
     * @return 如果存在该应用，则返回true；否则返回false
     */
    private boolean isAppAvailable(String appName) {
        final PackageManager packageManager = mActivity.getPackageManager();
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);
        for (int i = 0; i < pinfo.size(); i++) {
            String pn = pinfo.get(i).packageName;
            if (pn.equalsIgnoreCase(appName)) {
                return true;
            }
        }
        return false;
    }
}
