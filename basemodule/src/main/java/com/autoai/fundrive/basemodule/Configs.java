package com.autoai.fundrive.basemodule;

import com.autoai.fundrive.basemodule.page.PageChangeUtils;

/**
 * <AUTHOR>
 */
public class Configs {

    private Configs() {
        super();
    }

    /**
     * 通用的通知标志 用于需要通知所有模块的情况
     */
    public static final String COMMON_BROADCAST = "CommonBroadcast";

    /**
     * 表示两端
     * 车机端和手机端同步操作，比如调用
     * {@link PageChangeUtils}
     * 的时候
     */
    public static final int SCREEN_ALL = -1;
    /**
     * 表示手机端
     */
    public static final int SCREEN_PHONE = 0;
    /**
     * 表示车机端
     */
    public static final int SCREEN_CAR = 1;

    public static final int VIEW_PAGE_FLAG = 1;

    public static final int PAGE_MAIN = 1;
    public static final int PAGE_SUCCESS = 10090;
    public static final int PAGE_AUXILIARY = 10089;
    public static final int PAGE_MIRRORING_FAIL = 10088;
    public static final int PAGE_MIRRORING_SUC = 10087;

    public static final String THIRD_APP_CONSTANT = "com.autoai.welink.CONNECTION";
}
