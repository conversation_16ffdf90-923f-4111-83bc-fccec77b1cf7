package com.autoai.fundrive.basemodule.page;

import android.view.animation.Animation;

import com.mapbar.android.model.FilterObj;
import com.autoai.fundrive.basemodule.bean.MessageParam;
import com.autoai.fundrive.basemodule.bean.PageParam;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;
import com.autoai.fundrive.messagebus.MessageCenter;
import com.autoai.fundrive.messagebus.bean.ParamSet;

import java.util.HashMap;
import java.util.Map;

public class PageChangeUtils {
    private static final PageParam s = new PageParam();
    private static final MessageParam m = new MessageParam();

    public static void showPage(int aScreenIndex, int flag, String moduleName, int pageId, Object object) {
        showPage(aScreenIndex, flag, moduleName, pageId, object, false, null, null);
    }

    public static void showPage(int aScreenIndex, int flag, String moduleName, int pageId, Object object, boolean isClearPage, Animation in, Animation out) {
        s.reset();
        s.setUpdateScreenIndex(aScreenIndex);
        s.setFlag(flag);
        s.setToModuleName(moduleName);
        s.setToModuleId(pageId);
        s.setParamData(object);
        s.setClearPage(isClearPage);
        s.setFromAnimation(in);
        s.setToAnimation(out);
        MessageCenter.getDefault().post(s, "showPage");
    }

    public static void showJumpFirstPage(int aScreenIndex, int var1, String var2, int var3, FilterObj var4, Animation var5, Animation var6) {
        s.reset();
        s.setUpdateScreenIndex(aScreenIndex);
        s.setFlag(var1);
        s.setToModuleName(var2);
        s.setToModuleId(var3);
        s.setParamData(var4);
        s.setFromAnimation(var5);
        s.setToAnimation(var6);
        MessageCenter.getDefault().post(s, "showJumpFirstPage");
    }

    public static void showJumpPrevious(int var1, String var2, int var3, FilterObj var4, Animation var5, Animation var6) {
        showJumpPrevious(0, var1, var2, var3, var4, var5, var6);
    }

    public static void showJumpPrevious(int aScreenIndex, int var1, String var2, int var3, FilterObj var4, Animation var5, Animation var6) {
        s.reset();
        s.setUpdateScreenIndex(aScreenIndex);
        s.setFlag(var1);
        s.setToModuleName(var2);
        s.setToModuleId(var3);
        s.setParamData(var4);
        s.setFromAnimation(var5);
        s.setToAnimation(var6);
        MessageCenter.getDefault().post(s, "showJumpPrevious");
    }

    public static void showJumpPrevious(int var1, String var2, int var3, FilterObj var4) {
        showJumpPrevious(0, var1, var2, var3, var4, null, null);
    }

    public static void showJumpPrevious(int aScreenIndex, int var1, String var2, int var3, FilterObj var4) {
        showJumpPrevious(aScreenIndex, var1, var2, var3, var4, null, null);
    }

    public static void showJumpPrevious(int aScreenIndex, int var1, int var3, FilterObj var4) {
        showJumpPrevious(aScreenIndex, var1, "app", var3, var4, null, null);
    }

    public static void showJumpPrevious(int var1, int var3, FilterObj var4) {
        showJumpPrevious(var1, "app", var3, var4, null, null);
    }

    public static void sendToPage(int aScreenIndex, int flag, int pageId, Object object) {
        sendToPage(aScreenIndex, flag, pageId, object, false);
    }

    public static void sendToPage(int flag, int pageId, Object object) {
        sendToPage(flag, pageId, object, false);
    }

    public static void sendToPage(int aScreenIndex, int flag, String moduleName, int pageId, Object object) {
        sendToPage(aScreenIndex, flag, moduleName, pageId, object, false, null, null);
    }

    public static void sendToPage(int flag, String moduleName, int pageId, Object object) {
        sendToPage(flag, moduleName, pageId, object, false, null, null);
    }

    public static void sendToPage(int aScreenIndex, int flag, int pageId, Object object, boolean isClearPage) {
        sendToPage(aScreenIndex, flag, "app", pageId, object, isClearPage, null, null);
    }

    public static void sendToPage(int flag, int pageId, Object object, boolean isClearPage) {
        sendToPage(flag, "app", pageId, object, isClearPage, null, null);
    }

    public static void sendToPage(int aScreenIndex, int flag, String moduleName, int pageId, Object object, boolean isClearPage, Animation in, Animation out) {
        s.reset();
        s.setUpdateScreenIndex(aScreenIndex);
        s.setFlag(flag);
        s.setToModuleName(moduleName);
        s.setToModuleId(pageId);
        s.setParamData(object);
        s.setClearPage(isClearPage);
        s.setFromAnimation(in);
        s.setToAnimation(out);
        MessageCenter.getDefault().post(s, "sendToPage");
    }

    public static void sendToPage(int flag, String moduleName, int pageId, Object object, boolean isClearPage, Animation in, Animation out) {
        sendToPage(0, flag, "app", pageId, object, isClearPage, null, null);
    }

    public static void showPrevious(int aScreenIndex) {
        showPrevious(aScreenIndex, null);
    }

    public static void showPrevious() {
        showPrevious(0);
    }

    public static void showPrevious(FilterObj var1) {
        showPrevious(0, var1);
    }

    public static void showPrevious(int aScreenIndex, FilterObj var1) {
        s.reset();
        s.setUpdateScreenIndex(aScreenIndex);
        s.setParamData(var1);
        MessageCenter.getDefault().post(s, "showPrevious");
    }

    public static void showAlert(int aScreenIndex, int i) {
        m.reset();
        m.setmUpdateScreenIndex(aScreenIndex);
        m.setmStringId(i);
        MessageCenter.getDefault().post(m, "showAlert");
    }

    public static void showAlert(int aScreenIndex, String s) {
        m.reset();
        m.setmUpdateScreenIndex(aScreenIndex);
        m.setmString(s);
        MessageCenter.getDefault().post(m, "showAlert");
    }

    public static void showAlert(int aScreenIndex, int i, int aRootViewId) {
        m.reset();
        m.setmUpdateScreenIndex(aScreenIndex);
        m.setmStringId(i);
        m.setmRootViewId(aRootViewId);
        MessageCenter.getDefault().post(m, "showAlert");
    }

    public static void showAlert(int aScreenIndex, String s, int aRootViewId) {
        m.reset();
        m.setmUpdateScreenIndex(aScreenIndex);
        m.setmString(s);
        m.setmRootViewId(aRootViewId);
        MessageCenter.getDefault().post(m, "showAlert");
    }

    /**
     * 显示弹出对话框，适合使用在指定单个设备上进行显示，不适合所有设备显示
     *
     * @param aScreenIndex 指定显示设备ID
     */
    public static void showDialog(int aScreenIndex, BasePopDialog aDialog, int flagId) {
        m.reset();
        m.setmUpdateScreenIndex(aScreenIndex);
        m.setmObject(aDialog);
        m.setmFlag(flagId);
        MessageCenter.getDefault().post(m, "showDialog");
    }

    /**
     * 同时在多个设备上显示弹出对话框
     *
     * @param aModuleName 对话框所属模块名称
     * @param aDialogID   在各个屏幕上显示对话框ID，按照屏幕顺序存放相应的对话框ID，当为-1时，表示该屏幕不显示
     */
    public static void showDialog(String aModuleName, int[] aDialogID, int flagId) {
        m.reset();
        m.setmUpdateScreenIndex(-1);
        m.setmString(aModuleName);
        m.setmObject(aDialogID);
        m.setmFlag(flagId);
        MessageCenter.getDefault().post(m, "showDialog");
    }

    /**
     * 关闭弹出对话框，适合关闭在指定单个设备上进行显示的对话框，不适合所有设备显示的对话框
     */
    public static void closeDialog(int aScreenIndex, BasePopDialog aDialog, int flagId) {
        m.reset();
        m.setmUpdateScreenIndex(aScreenIndex);
        m.setmObject(aDialog);
        m.setmFlag(flagId);
        MessageCenter.getDefault().post(m, "closeDialog");
    }

    /**
     * 关闭弹出对话框，适合关闭在指定单个设备上进行显示的对话框
     */
    public static void closeDialog(int aScreenIndex, int flagId) {
        m.reset();
        m.setmUpdateScreenIndex(aScreenIndex);
        m.setmObject(null);
        m.setmFlag(flagId);
        MessageCenter.getDefault().post(m, "closeDialog");
    }

    public static void performThirdApp(String aAppPackageName, String aClassName, int aAppType) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("aAppPackageName", aAppPackageName);
        param.put("aClassName", aClassName);
        param.put("aAppType", aAppType);
        ParamSet<Object> p = new ParamSet<>(param);
        MessageCenter.getDefault().post(p, "performThirdApp");
    }

}
