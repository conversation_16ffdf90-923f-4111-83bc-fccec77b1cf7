package com.autoai.fundrive.basemodule.activity;

import android.app.Dialog;
import android.view.View;
import android.view.ViewGroup;

import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.mapbar.android.model.PageObject;
import com.mapbar.android.model.ViewInterface;

public class ClearHUPages implements IHUAndMUChangePages {
    private View mMUAnimatorView = null;
    private ViewGroup mMUParentView = null;
    private View mMUShowPage = null;

    @Override
    public void showPageOnHU(DialogManager aDialogManager, Dialog aDialog, BaseModuleActivity aBaseModuleActivity) {
        aDialogManager.setDialog(aDialog);
        aBaseModuleActivity.addMessageManager(aDialogManager);
        //mDialogManager.initSkin();
        aDialog.setContentView(aDialogManager.getMainViewLayout());
        aDialogManager.onCreate();
        ViewGroup view = aDialog.findViewById(aDialogManager.getAnimatorResId());

        //固定设置为首页
        PageObject curPage = aDialogManager.getMainPage();
        view.addView(curPage.getView());
        ViewInterface mViewInterface = aBaseModuleActivity.getViewInterface(aDialogManager.getScreenIndex());
        mViewInterface.pushPage(curPage, -1, null);

        String showModuleName = aBaseModuleActivity.getViewInterface(0).getCurrentModuleName();
        int showPos = aBaseModuleActivity.getViewInterface(0).getCurrentPagePosition();
        //mViewInterface.showPage(-1,showModuleName,showPos,null);
        PageChangeUtils.showPage(1, -1, showModuleName, showPos, null);
    }

    @Override
    public void showPageOnMU(BaseModuleActivity aBaseModuleActivity, View aShowView) {
        //PageChangeUtils.showPage(0,0,99998,null);
        mMUShowPage = aShowView;
        if (mMUShowPage != null) {
            mMUAnimatorView = aBaseModuleActivity.findView(aBaseModuleActivity.getRootViewId());
            mMUParentView = (ViewGroup) mMUAnimatorView.getParent();
            //mMUParentView.removeView(mMUAnimatorView);
            mMUParentView.addView(mMUShowPage, -1);
        }
    }

    @Override
    public void clearHUPage(DialogManager aDialogManager, BaseModuleActivity aBaseModuleActivity) {
        aBaseModuleActivity.removeMessageManger(aDialogManager);
        ViewInterface[] viewArray = aBaseModuleActivity.getViewBaseManager().getViewArray();
        if(aDialogManager == null || viewArray == null){
            return;
        }
        if (aDialogManager.getScreenIndex() < viewArray.length) {
            ViewInterface view = viewArray[aDialogManager.getScreenIndex()];
            view.onRelease();
            viewArray[aDialogManager.getScreenIndex()] = null;
        }
    }

    @Override
    public void clearMuPage(BaseModuleActivity aBaseModuleActivity) {
        if (mMUShowPage != null && mMUParentView != null) {
            mMUParentView.removeView(mMUShowPage);
            //mMUParentView.addView(mMUAnimatorView);
            mMUParentView = null;
            mMUAnimatorView = null;
            mMUShowPage = null;
        }

    }
}
