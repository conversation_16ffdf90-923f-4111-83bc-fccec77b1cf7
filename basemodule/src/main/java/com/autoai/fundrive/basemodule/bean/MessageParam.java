package com.autoai.fundrive.basemodule.bean;

public class MessageParam {
    private int mUpdateScreenIndex = -1;
    private int mStringId;
    private String mString;
    private int mRootViewId;
    private Object mObject;
    private int mFlag;

    public int getmUpdateScreenIndex() {
        return mUpdateScreenIndex;
    }

    public void setmUpdateScreenIndex(int mUpdateScreenIndex) {
        this.mUpdateScreenIndex = mUpdateScreenIndex;
    }

    public int getmStringId() {
        return mStringId;
    }

    public void setmStringId(int mStringId) {
        this.mStringId = mStringId;
    }

    public String getmString() {
        return mString;
    }

    public void setmString(String mString) {
        this.mString = mString;
    }

    public int getmRootViewId() {
        return mRootViewId;
    }

    public void setmRootViewId(int mRootViewId) {
        this.mRootViewId = mRootViewId;
    }

    public Object getmObject() {
        return mObject;
    }

    public void setmObject(Object mObject) {
        this.mObject = mObject;
    }

    public int getmFlag() {
        return mFlag;
    }

    public void setmFlag(int mFlag) {
        this.mFlag = mFlag;
    }

    public void reset() {
        mUpdateScreenIndex = -1;
        mStringId = -1;
        mRootViewId = -1;
        mObject = null;
        mFlag = -1;
    }
}
