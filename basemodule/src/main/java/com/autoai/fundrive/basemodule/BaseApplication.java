package com.autoai.fundrive.basemodule;

import android.app.Application;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.autoai.fundrive.commontool.LogManager;

import java.lang.reflect.Field;

/**
 * 获取全局gradle配置信息中的一些配置项参数
 *
 * <AUTHOR>
 */
public class BaseApplication {
    /**
     * 模块定义类class name
     */
    private static String MODULE_MANAGER_CLASS_NAME = "";
    private static String packageVersionName = "packageVersionName";

    private static Application mApplication;

    /**
     * @param buildConfigClass   获取当前运行Application的BuildConfig类
     * @param moduleManagerClass 获取业务模块配置类
     */
    public static void init(@NonNull Application application,
                            @NonNull Class<?> buildConfigClass,
                            @NonNull Class<?> moduleManagerClass) {
        packageVersionName = getFiledValue(buildConfigClass, "packageVersionName", packageVersionName);
        //
        MODULE_MANAGER_CLASS_NAME = moduleManagerClass.getName();
        mApplication = application;
//        // 屏幕适配
//         AutoDensityUtil.setCustomDensity(this);
//        //皮肤功能初始化
//         SkinManager.initActivity(this);
    }


    /**
     * 模块定义类class name
     */
    @Keep
    public static String getModuleManagerClassName() {
        return MODULE_MANAGER_CLASS_NAME;
    }

    @Nullable
    private static <T> T getFiledValue(Class<?> clazz, String fieldName, @NonNull T defaultValue) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            defaultValue = (T) field.get(null);
        } catch (Exception e) {
            LogManager.e("getFiledValue: ", e);
        }
        return defaultValue;
    }

    @Keep
    @NonNull
    public static Application getBaseApplication() {
        return mApplication;
    }

    /**
     * 获取版本信息
     */
    @Keep
    @NonNull
    public static String getPackageVersionName() {
        return packageVersionName;
    }

}
