package com.autoai.fundrive.basemodule;

import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.bean.MessageParam;
import com.autoai.fundrive.basemodule.bean.PageParam;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.mapbar.android.model.FilterObj;
import com.mapbar.android.model.PageObject;
import com.mapbar.android.model.ViewInterface;
import com.autoai.fundrive.messagebus.MessageCenter;
import com.autoai.fundrive.messagebus.bean.ParamSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MessageManager {
    private final ViewInterface[] mAif;
    private final List<AppActivity> mActivity = new ArrayList<>(4);

    public MessageManager(ViewInterface[] aif) {
        this.mAif = aif;
    }

    public void addAppActivity(AppActivity aAppActivity) {
        if (!mActivity.contains(aAppActivity)) {
            mActivity.add(aAppActivity);
        }
    }

    public void removeAppActivity(AppActivity aAppActivity) {
        mActivity.remove(aAppActivity);
    }

    public void removeAll() {
        mActivity.clear();
    }

    public void unregister() {
        MessageCenter.getDefault().unregister(this);
    }

    public void register() {
        registerSendToPage();
        registerShowPrevious();
        registerShowJumpPrevious();
        registerShowJumpFirstPage();
        registerShowPage();
        registerShowAlert();
        registerShowDialog();
        registerCloseDialog();
        registerPerformThirdApp();
    }

    private void registerSendToPage() {
        MessageCenter.getDefault().subscribe(this, "sendToPage", new MessageCenter.Callback<PageParam>() {
            @Override
            public void onEvent(PageParam s) {
                int screenIndex = s.getUpdateScreenIndex();
                if (screenIndex >= 0 && screenIndex < mAif.length) {
                    if (mAif[screenIndex] != null) {
                        mAif[screenIndex].sendToPage(s.getToModuleName(), s.getToModuleId(), s.getFlag(), s.getParamData());
                    }
                } else {
                    for (ViewInterface viewInterface : mAif) {
                        if (viewInterface != null) {
                            viewInterface.sendToPage(s.getToModuleName(), s.getToModuleId(), s.getFlag(), s.getParamData());
                        }
                    }
                }
            }
        });

    }

    private void registerShowPrevious() {
        MessageCenter.getDefault().subscribe(this, "showPrevious", new MessageCenter.Callback<PageParam>() {
            @Override
            public void onEvent(PageParam s) {
                //下标为0时，是手机上显示页面，取得手机上当前显示的前一页
                ArrayList<PageObject> allPages = mAif[0].getPages();
                PageObject prePage = null;
                int preFlag = 0;
                String moduleName = null;
                int viewId = -1;
                if (allPages.size() > 1) {
                    prePage = allPages.get(allPages.size() - 2);
                    preFlag = prePage.getPosition();
                    moduleName = prePage.getModuleName();
                    viewId = prePage.getPage().getMyViewPosition();
                }

                int screenIndex = s.getUpdateScreenIndex();
                if (screenIndex >= 0 && screenIndex < mAif.length) {
                    if (mAif[screenIndex] != null) {
                        ArrayList<PageObject> tempPages = mAif[screenIndex].getPages();
                        if (tempPages.size() > 1) {
                            //历史记录内有前一页
                            LogManager.d("showPrevious1 历史记录内有前一页");
                            mAif[screenIndex].showPrevious(s.getFlag(), (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                        } else if (prePage != null) {
                            //历史记录内无前一页，按照手机上的前一页显示
                            LogManager.d("showPrevious1 历史记录内无前一页，按照手机上的前一页显示");
                            mAif[s.getUpdateScreenIndex()].showJumpFirstPage(preFlag, moduleName, viewId, (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                        } else {
                            LogManager.d("showPrevious1 手机端也没有历史页面了，先不做处理");
                            //手机端也没有历史页面了，先不做处理
                        }
                    }
                } else {
                    for (ViewInterface viewInterface : mAif) {
                        if (viewInterface != null) {
                            ArrayList<PageObject> tempPages = viewInterface.getPages();
                            if (tempPages.size() > 1) {
                                //历史记录内有前一页
                                LogManager.d("showPrevious2 历史记录内有前一页");
                                viewInterface.showPrevious(s.getFlag(), (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                            } else if (prePage != null) {
                                //历史记录内无前一页，按照手机上的前一页显示
                                LogManager.d("showPrevious2 历史记录内无前一页，按照手机上的前一页显示");
                                viewInterface.showJumpFirstPage(preFlag, moduleName, viewId, (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                            } else {
                                //手机端也没有历史页面了，先不做处理
                                LogManager.d("preFlag:" + preFlag + ",moduleName:" + moduleName + ",viewId:" + viewId);
                                LogManager.d("showPrevious2 手机端也没有历史页面了，先不做处理");
                            }
                        }
                    }
                }
            }
        });
    }

    private void registerShowJumpPrevious() {
        MessageCenter.getDefault().subscribe(this, "showJumpPrevious", new MessageCenter.Callback<PageParam>() {
            @Override
            public void onEvent(PageParam s) {
                if (s.getUpdateScreenIndex() >= 0 && s.getUpdateScreenIndex() < mAif.length) {
                    if (mAif[s.getUpdateScreenIndex()] != null) {
                        mAif[s.getUpdateScreenIndex()].showJumpPrevious(s.getFlag(), s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                    }
                } else {
                    for (ViewInterface viewInterface : mAif) {
                        if (viewInterface != null) {
                            viewInterface.showJumpPrevious(s.getFlag(), s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                        }
                    }
                }
            }
        });

    }

    private void registerShowJumpFirstPage() {
        MessageCenter.getDefault().subscribe(this, "showJumpFirstPage", new MessageCenter.Callback<PageParam>() {
            @Override
            public void onEvent(PageParam s) {
                int screenIndex = s.getUpdateScreenIndex();
                if (screenIndex >= 0 && screenIndex < mAif.length) {
                    if (mAif[screenIndex] != null) {
                        mAif[screenIndex].showJumpFirstPage(s.getFlag(), s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                    }
                } else {
                    for (ViewInterface viewInterface : mAif) {
                        if (viewInterface != null) {
                            viewInterface.showJumpFirstPage(s.getFlag(), s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData(), s.getFromAnimation(), s.getToAnimation());
                        }
                    }
                }
            }
        });
    }

    private void registerShowPage() {
        MessageCenter.getDefault().subscribe(this, "showPage", new MessageCenter.Callback<PageParam>() {
            @Override
            public void onEvent(PageParam s) {
                int screenIndex = s.getUpdateScreenIndex();
                if (screenIndex >= 0 && s.getUpdateScreenIndex() < mAif.length) {
                    LogManager.d("showPage 1");
                    ViewInterface viewInterface = mAif[screenIndex];
                    if (viewInterface != null) {
                        if (s.getFlag() != -1) {
                            LogManager.d("showPage 2" + viewInterface);
                            viewInterface.showPage(s.getFlag(), s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData(), s.isClearPage(), s.getFromAnimation(), s.getToAnimation());
                        } else if (s.getParamData() != null) {
                            LogManager.d("showPage 3" + viewInterface);
                            viewInterface.showPage(s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData());
                        } else {
                            LogManager.d("showPage 4" + viewInterface);
                            viewInterface.showPage(s.getToModuleName(), s.getToModuleId());
                        }
                    }
                } else {
                    for (ViewInterface viewInterface : mAif) {
                        if (viewInterface != null) {
                            if (s.getFlag() != -1) {
                                LogManager.d("showPage 5" + viewInterface);
                                viewInterface.showPage(s.getFlag(), s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData(), s.isClearPage(), s.getFromAnimation(), s.getToAnimation());
                            } else if (s.getParamData() != null) {
                                LogManager.d("showPage 6" + viewInterface);
                                viewInterface.showPage(s.getToModuleName(), s.getToModuleId(), (FilterObj) s.getParamData());
                            } else {
                                LogManager.d("showPage 7" + viewInterface);
                                viewInterface.showPage(s.getToModuleName(), s.getToModuleId());
                            }
                        }
                    }
                }
            }
        });
    }

    private void registerShowAlert() {
        MessageCenter.getDefault().subscribe(this, "showAlert", new MessageCenter.Callback<MessageParam>() {
            @Override
            public void onEvent(MessageParam s) {
                if (s.getmUpdateScreenIndex() >= 0 && s.getmUpdateScreenIndex() < mActivity.size()) {
                    for (AppActivity appActivity : mActivity) {
                        if (appActivity.getScreenIndex() == s.getmUpdateScreenIndex()) {
                            showAlert(s, appActivity);
                            break;
                        }
                    }
                } else {
                    for (AppActivity appActivity : mActivity) {
                        if (appActivity != null) {
                            showAlert(s, appActivity);
                        }
                    }
                }
            }
        });
    }

    private void registerShowDialog() {
        MessageCenter.getDefault().subscribe(this, "showDialog", new MessageCenter.Callback<MessageParam>() {
            @Override
            public void onEvent(MessageParam s) {
                if (s.getmUpdateScreenIndex() >= 0 && s.getmUpdateScreenIndex() < mActivity.size()) {
                    for (AppActivity appActivity : mActivity) {
                        if (appActivity != null && appActivity.getScreenIndex() == s.getmUpdateScreenIndex()) {
                            showDialog((BasePopDialog) s.getmObject(), s.getmFlag(), appActivity);
                            break;
                        }
                    }
                } else if (s.getmUpdateScreenIndex() == -1) {
                    AppActivity appActivity = null;
                    for (int index = 0; index < mActivity.size(); index++) {
                        appActivity = mActivity.get(index);
                        if (appActivity != null && s.getmObject() instanceof BasePopDialog) {
                            showDialog((BasePopDialog) s.getmObject(), s.getmFlag(), appActivity);
                        } else if (appActivity != null && s.getmObject() instanceof int[]) {
                            int[] dialogId = (int[]) s.getmObject();
                            if (index < dialogId.length && dialogId[index] != -1) {
                                BasePopDialog dialog = appActivity.createPopDialog(s.getmString(), dialogId[index]);
                                showDialog(dialog, s.getmFlag(), appActivity);
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 在主线程刷新dialog UI
     */
    private void showDialog(BasePopDialog dialog, int flag, AppActivity appActivity) {
        if (dialog != null) {
            LogManager.d("showDialog flag:"+flag+",dialog:"+dialog);
            ThreadPoolUtil.getInstance().runOnUiThread(() -> appActivity.showDialog(dialog, flag));
        }
    }

    /**
     * 在主线程刷新Alert UI
     */
    private void showAlert(MessageParam messageParam, AppActivity appActivity) {
        ThreadPoolUtil.getInstance().runOnUiThread(() -> {
            if (messageParam.getmString() != null) {
                if (messageParam.getmRootViewId() != -1) {
                    appActivity.showAlert(messageParam.getmString(), messageParam.getmRootViewId());
                } else {
                    appActivity.showAlert(messageParam.getmString());
                }
            } else {
                if (messageParam.getmRootViewId() != -1) {
                    appActivity.showAlert(messageParam.getmStringId(), messageParam.getmRootViewId());
                } else {
                    appActivity.showAlert(messageParam.getmStringId());
                }
            }
        });
    }

    private void registerCloseDialog() {
        MessageCenter.getDefault().subscribe(this, "closeDialog", new MessageCenter.Callback<MessageParam>() {
            @Override
            public void onEvent(MessageParam s) {
                if (s.getmUpdateScreenIndex() >= 0 && s.getmUpdateScreenIndex() < mActivity.size()) {
                    for (AppActivity appActivity : mActivity) {
                        if (appActivity != null && appActivity.getScreenIndex() == s.getmUpdateScreenIndex()) {
                            closeDialog(appActivity, s.getmObject(), s.getmFlag());
                            break;
                        }
                    }
                } else {
                    for (AppActivity appActivity : mActivity) {
                        if (appActivity != null) {
                            closeDialog(appActivity, s.getmObject(), s.getmFlag());
                        }
                    }
                }
            }
        });
    }

    private void closeDialog(AppActivity appActivity, Object aDialog, int flagId) {
        ThreadPoolUtil.getInstance().runOnUiThread(() -> {
            if (aDialog != null) {
                appActivity.closeDialog((BasePopDialog) aDialog, flagId);
            } else {
                appActivity.closeDialog(flagId);
            }
        });
    }

    private void registerPerformThirdApp() {
        MessageCenter.getDefault().subscribe(this, "performThirdApp", new MessageCenter.Callback<ParamSet>() {
            @Override
            public void onEvent(ParamSet s) {
                Map<String, Object> paramList = s.getmParamList();
                String appPackageName = (String) paramList.get("aAppPackageName");
                String className = (String) paramList.get("aClassName");
                int aAppType = (int) paramList.get("aAppType");
                for (AppActivity appActivity : mActivity) {
                    if (appActivity != null) {
                        appActivity.performThirdApp(appPackageName, className, aAppType);
                    }
                }
            }
        });
    }
}
