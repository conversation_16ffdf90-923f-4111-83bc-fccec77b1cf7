package com.autoai.fundrive.basemodule.widget;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.R;


public class ScreenCastingPromptDialog extends Dialog {
    private final Context context;
    private TextView mTitle;
    private TextView mText,mConfirm;

    public ScreenCastingPromptDialog(@NonNull Context context) {
        super(context, R.style.iosDialog);
        this.context = context;
        setCancelable(false);
        initView();
    }

    private void initView() {
        View inflate = LayoutInflater.from(context).inflate(R.layout.screen_prompt_dialog, null);
        mTitle = inflate.findViewById(R.id.tv_title);
        mText = inflate.findViewById(R.id.tv_text);
        mConfirm = inflate.findViewById(R.id.confirm);
        super.setContentView(inflate);
    }
    //
    public void setConfirmListener(View.OnClickListener listener) {
        mConfirm.setOnClickListener(listener);
    }
    /*
    * TODO
    *   设置内容
    *   @param title 标题
    *   @param text 内容
    *   @param confirm 按钮文字
    * */
    public void setTextListener(String title, String text,String confirm) {
        mTitle.setText(title);
        mText.setText(text);
        mConfirm.setText(confirm);
    }

}
