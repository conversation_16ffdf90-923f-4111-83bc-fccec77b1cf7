package com.autoai.fundrive.basemodule.activity;

import android.app.Dialog;
import android.content.Context;
import android.view.KeyEvent;
import android.view.View;

import com.autoai.fundrive.basemodule.widget.BasePopDialog;
import com.mapbar.android.control.ViewBaseManager;
import com.mapbar.android.model.PageObject;
import com.mapbar.android.model.ViewInterface;
import com.autoai.fundrive.basemodule.BaseModuleManager;

public abstract class BaseDialogManager extends AppActivity {
    private Dialog mDialog = null;
    private BaseModuleManager mModuleManager;
    private int mScreenIndex = 1;
    private final ViewBaseManager mViewBaseManager;
    private OnKeyListener mListener = null;

    public BaseDialogManager(ViewBaseManager aViewBaseManager) {
        this.mViewBaseManager = aViewBaseManager;
    }

    public void setDialog(Dialog aDialog) {
        this.mDialog = aDialog;
        if (mDialog != null) {
            mDialog.setOnKeyListener((dialog, keyCode, event) -> {
                boolean returnValue = false;
                if(mListener != null) {
                    returnValue = mListener.onKeyDown(keyCode, event);
                }
                return returnValue;
            });
        }
    }

    public void setOnKeyListener(OnKeyListener aListener) {
        mListener = aListener;
    }

    public void initSkin() {
    }

    public void onCreate() {
    }

    public void initModuleManager() {
        mModuleManager = getModuleManager();
    }

    @Override
    public PageObject createPage(String aModuleName, int index) {
        PageObject page = null;
        if (mModuleManager != null) {
            page = mModuleManager.createPage(aModuleName, index);
        }
        return page;
    }

    @Override
    public BasePopDialog createPopDialog(String aModuleName, int index) {
        BasePopDialog popDialog = null;
        if (mModuleManager != null) {
            try {
                popDialog = mModuleManager.createPopDialog(aModuleName, index);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return popDialog;
    }

    public abstract BaseModuleManager getModuleManager();


    @Override
    public View findView(int id) {
        if (mDialog != null) {
            return mDialog.findViewById(id);
        } else {
            return null;
        }
    }

    @Override
    public void setScreenIndex(int i) {
        this.mScreenIndex = i;
    }

    @Override
    public int getScreenIndex() {
        return mScreenIndex;
    }

    @Override
    public Context getContext() {
        if (mDialog != null) {
            return mDialog.getContext();
        } else {
            return null;
        }
    }

    @Override
    public ViewInterface getViewInterface() {
        if (mViewBaseManager == null) {
            return null;
        } else {
            return mViewBaseManager.getViewInterface(mScreenIndex);
        }
    }

    @Override
    public void performThirdApp(String aAppPackageName, String aClaseName, int aAppType) {
        //Dialog上该方法为空实现
    }

    public interface OnKeyListener {
        boolean onKeyDown(int keyCode, KeyEvent event);
    }

    public void onDestroy() {
        mDialog = null;
        mModuleManager = null;
        mViewBaseManager.onRelease(getScreenIndex());
    }
}
