package com.autoai.fundrive.basemodule.activity;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.widget.PopDialogGroup;
import com.autoai.fundrive.basemodule.widget.ToastImpl;
import com.autoai.fundrive.commontool.LogManager;
import com.mapbar.android.model.PageObject;
import com.mapbar.android.model.ViewInterface;
import com.autoai.fundrive.basemodule.BaseModuleManager;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;

/**
 * 各个业务模块的初始化
 * 实现各个业务模块内的页面创建
 * 自定义Toast及Dialog管理
 */
public class BaseModuleActivity extends BaseActivityManager {
    private final int mScreenIndex = 0;
    private int mAnimatorResId = 0;
    private final ToastImpl mToastImpl;
    private int mRootViewId = -1;
    private final PopDialogGroup mDialogGroup;
    private BaseModuleManager mModuleManager;

    public BaseModuleActivity(@NonNull Activity aActivity) {
        super(aActivity);
        mToastImpl = new ToastImpl(this, -1);
        mDialogGroup = new PopDialogGroup(this);
    }

    public void setModuleManager(BaseModuleManager aModuleManager) {
        mModuleManager = aModuleManager;
    }

    public void init() {
        if (getContext() != null) {
            initViewBaseManager();
            initModuleManager();
        }
    }

    public void initModuleManager() {
        mModuleManager = getModuleManager();
        if (mModuleManager != null) {
            //先加载服务，后加载各个业务模块，防止业务模块依赖服务
            mModuleManager.initServiceMap(getContext());
        }
    }

    public void loadService() {
        mModuleManager = getModuleManager();
        if (mModuleManager != null) {
            mModuleManager.loadModuleService(BaseModuleManager.TYPE_ACTIVITY, "app");
        }
    }

    @Override
    public PageObject createPage(String aModuleName, int index) {
        LogManager.d("createPage");
        PageObject page = null;
        if (mModuleManager != null) {
            page = mModuleManager.createPage(aModuleName, index);
        }
        return page;
    }

    public BaseModuleManager getModuleManager() {
        return mModuleManager;
    }

    @Override
    public int getAnimatorResId() {
        return mAnimatorResId;
    }

    @Override
    public int getRootViewId() {
        return mRootViewId;
    }

    public void setRootViewId(int aRootViewId) {
        mRootViewId = aRootViewId;
    }

    public void setAnimatorResId(int aAnimatorResId) {
        mAnimatorResId = aAnimatorResId;
    }

    @Override
    public ViewInterface getViewInterface() {
        if (getViewBaseManager() == null) {
            return null;
        } else {
            return getViewBaseManager().getViewInterface(mScreenIndex);
        }
    }

    public ViewInterface getViewInterface(int aScreenIndex) {
        if (getViewBaseManager() == null) {
            return null;
        } else {
            return getViewBaseManager().getViewInterface(aScreenIndex);
        }
    }

    public boolean isCurrentShowPage(String moduleName, int position) {
        boolean returnValue = false;
        PageObject page = getViewBaseManager().getViewInterface(mScreenIndex).getCurrentPageObj();
        if (page != null && page.getModuleName().equals(moduleName)
                && position == page.getPage().getMyViewPosition()) {
            returnValue = true;
        }
        return returnValue;
    }

    @Override
    public void showAlert(int i) {
        mToastImpl.showAlert(i, mRootViewId);
    }

    @Override
    public void showAlert(String s) {
        mToastImpl.showAlert(s, mRootViewId);
    }

    @Override
    public void showAlert(int i, int aRootViewId) {
        mToastImpl.showAlert(i, aRootViewId);
    }

    @Override
    public void showAlert(String s, int aRootViewId) {
        mToastImpl.showAlert(s, aRootViewId);
    }

    @Override
    public void closeDialog(BasePopDialog aDialog, int flagId) {
        aDialog.close(flagId);
    }

    @Override
    public void closeDialog(int flagId) {
        mDialogGroup.dismiss(flagId);
    }

    @Override
    public BasePopDialog createPopDialog(String aModuleName, int index) {
        BasePopDialog popDialog = null;
        if (mModuleManager != null) {
            try {
                popDialog = mModuleManager.createPopDialog(aModuleName, index);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return popDialog;
    }

    @Override
    public void showDialog(BasePopDialog aDialog, int flagId) {
        aDialog.setAppActivity(this);
        aDialog.setDialogGroup(mDialogGroup);
        aDialog.show(flagId);
    }
}
