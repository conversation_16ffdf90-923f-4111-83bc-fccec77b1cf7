package com.autoai.fundrive.basemodule.broadcast;

import android.content.Intent;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 广播适配
 * 主要维护广播事件和监听对应关系
 *
 * <AUTHOR>
 */
class BroadcastMapping {

    private final Map<String[], Set<IBroadcastNotification>> mActionMapping;

    public BroadcastMapping() {
        mActionMapping = Collections.synchronizedMap(new HashMap<>());
    }

    private void availableMapping() {
        if (mActionMapping == null) {
            throw new RuntimeException("BroadcastMapping is null，start BroadcastService and init");
        }
    }

    public void addAction(IBroadcastNotification listener, String... newAction) {
        if (listener == null) {
            return;
        }
        if (newAction == null || newAction.length == 0) {
            return;
        }
        availableMapping();
        //排查重复添加
        for (Map.Entry<String[], Set<IBroadcastNotification>> entry : this.mActionMapping.entrySet()) {
            String[] registerAction = entry.getKey();
            if (Arrays.equals(registerAction, newAction)) {
                Set<IBroadcastNotification> broadcastNotifications = mActionMapping.get(registerAction);
                if (broadcastNotifications == null) {
                    broadcastNotifications = new HashSet<>();
                }
                broadcastNotifications.add(listener);
//                LogManager.d("add exist action [" + Arrays.toString(registerAction) + "] IBroadcastNotification=" + listener);
                return;
            }
        }
        //没有直接加
        Set<IBroadcastNotification> broadcastNotifications = new HashSet<>();
        broadcastNotifications.add(listener);
//        LogManager.d("add new action [" + Arrays.toString(newAction) + "] IBroadcastNotification=" + listener);
        mActionMapping.put(newAction, broadcastNotifications);
    }

    public void removeListener(IBroadcastNotification listener) {
        if (listener == null) {
            return;
        }
        availableMapping();
        //遍历对外接口匹配删除，不删除广播动作
        for (Map.Entry<String[], Set<IBroadcastNotification>> entry : this.mActionMapping.entrySet()) {
            Set<IBroadcastNotification> broadcastNotifications = entry.getValue();
            if (broadcastNotifications != null) {
                Iterator<IBroadcastNotification> iterator = broadcastNotifications.iterator();
                while (iterator.hasNext()) {
                    IBroadcastNotification broadcastNotification = iterator.next();
                    if (broadcastNotification == listener) {
                        iterator.remove();
//                        LogManager.d("removeListener =" + listener);
                        break;
                    }
                }
            }

        }
    }

    public void removeListener(IBroadcastNotification listener, String... removeAction) {
        if (listener == null) {
            return;
        }
        if (removeAction == null || removeAction.length == 0) {
            return;
        }
        availableMapping();
        //匹配删除
        for (Map.Entry<String[], Set<IBroadcastNotification>> entry : this.mActionMapping.entrySet()) {
            String[] existAction = entry.getKey();
            if (Arrays.equals(existAction, removeAction)) {
                Set<IBroadcastNotification> broadcastNotifications = entry.getValue();
                if (broadcastNotifications != null) {
                    Iterator<IBroadcastNotification> iterator = broadcastNotifications.iterator();
                    while (iterator.hasNext()) {
                        IBroadcastNotification broadcastNotification = iterator.next();
                        if (broadcastNotification == listener) {
                            iterator.remove();
//                            LogManager.d("removeListener action [" + Arrays.toString(removeAction) + "] IBroadcastNotification=" + listener);
                            return;
                        }
                    }
                }
            }
        }
    }

    public void removeAction(String... removeAction) {
        if (removeAction == null || removeAction.length == 0) {
            return;
        }
        //广播事件匹配删除
        for (Iterator<String[]> iterator = mActionMapping.keySet().iterator(); iterator.hasNext(); ) {
            String[] existAction = iterator.next();
            if (Arrays.equals(removeAction, existAction)) {
                iterator.remove();
//                LogManager.d("removeListener action " + Arrays.toString(removeAction));
            }
        }
    }

    public List<IBroadcastNotification> getListenerByIntentAction(Intent intent) {
        availableMapping();
        if (intent == null) {
            return null;
        }
        String broadcastAction = intent.getAction();
        if (TextUtils.isEmpty(broadcastAction)) {
            return null;
        }
        List<IBroadcastNotification> listener = new ArrayList<>();
        //查找对应Intent的广播事件监听
        for (Map.Entry<String[], Set<IBroadcastNotification>> entry : this.mActionMapping.entrySet()) {
            String[] key = entry.getKey();
            for (String registAction : key) {
                if (TextUtils.equals(broadcastAction, registAction)) {
                    Set<IBroadcastNotification> broadcastNotifications = entry.getValue();
                    listener.addAll(broadcastNotifications);
//                    LogManager.d("getListenerByIntentAction set [" + broadcastAction + "]" + Arrays.toString(key));
                    break;
                }
            }
        }
        return listener;
    }

    public void clear() {
        if (mActionMapping != null) {
            mActionMapping.clear();
        }
//        LogManager.d("BroadcastMapping clear");
    }

    public boolean isEmpty() {
        return mActionMapping.isEmpty();
    }
}
