package com.autoai.fundrive.commontool;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

/**
 * Created by Administrator on 2017/5/18.
 */

public class SharePreferenceUtil {

    /**
     * 驾驶行为分析 开发者模式测试数据  是否开启
     **/
    public static final String IS_DEBUG = "isDebug";


    private static SharePreferenceUtil instance;
    private static SharedPreferences sp;

    private Context mContext;

    private SharePreferenceUtil(Context context) {
        mContext = context;
        sp = mContext.getSharedPreferences("sp_data", Context.MODE_PRIVATE);
    }

    public static SharePreferenceUtil getInstance(Context context) {
        if (instance == null) {
            instance = new SharePreferenceUtil(context.getApplicationContext());
        }
        return instance;
    }

    /**
     * 保存debug模式
     **/
    public void setIsDebug(boolean open) {
        try {
            sp.edit().putBoolean(IS_DEBUG, open).commit();
        } catch (Throwable throwable) {
            Log.d("SharePreferenceUtil", "setDrivingAnalyzeOpenDevelopMode:" + throwable.getMessage());
        }

    }

    /**
     * 获取是否是debug模式
     **/
    public boolean getIsDebug() {
        return sp.getBoolean(IS_DEBUG, false);
    }

    private static final String SP_NAME = "future_rich";

    public static void saveBoolean(Context context, String key, boolean value) {
        if (sp == null)
            sp = context.getSharedPreferences(SP_NAME, 0);
        SharedPreferences.Editor edit = sp.edit();
        edit.putBoolean(key, value);
        edit.commit();
    }

    public static boolean getBoolean(Context context, String key, boolean value) {
        if (sp == null)
            sp = context.getSharedPreferences(SP_NAME, 0);
        return sp.getBoolean(key, value);
    }

    public static void saveString(Context context, String key, String value) {
        if (sp == null) sp = context.getSharedPreferences(SP_NAME, 0);
        SharedPreferences.Editor edit = sp.edit();
        edit.putString(key, value);
        edit.commit();
    }

    public static String getString(Context context, String key, String value) {
        if (sp == null) sp = context.getSharedPreferences(SP_NAME, 0);
        return sp.getString(key, value);
    }
}
