package com.autoai.fundrive.commontool;

import android.view.View;
import android.widget.TextView;

/**
 * <AUTHOR> zhanggc
 * @version : 1.0
 * @description : wifi 无感互联状态返回
 * @date : 2021/1/8 15:38
 */
public interface OnWiFiConnectListener {
    /**
     * HardwareGroup状态
     *
     * @param status 状态码
     *               0: Wi-Fi Direct GO广播，content: GO签名
     *               1: BLE设备连接中...
     *               2: BLE设备连接成功
     *               3: BLE服务连接中...
     *               4: BLE服务连接成功
     *               5: 读取版本中...
     *               6: 版本读取成功，content：版本号
     *               7: 写Wi-Fi Direct GO签名
     *               8: 签名写成功，content：签名
     *               9: Wi-Fi Direct GC连接，content: 网络密码
     *               1）ble扫描过程：对应状态0~8，8完成后进入wifi配对过程
     *               2）wifi配对过程：对应状态8~9，9完成后进入互联过程
     *               3）互联过程：对应状态9~正常投屏
     *               10: 未找到对应包名的硬件心跳---（为第三方硬件预留，目前不存在）
     *               -1: 设备连接失败
     *               -2: 服务连接失败
     *               -3: 版本读取失败
     *               -4: 签名写失败
     *               -5: 操作超时（-1~-5均代表ble扫描失败，失败后会自动重试ble，app上层不用做额外处理）
     */
    void OnConnectListener(int status);
    /**
     * 错误
     *
     * @param code 错误码
     *               1: 设备不支持蓝牙BLE---（蓝牙4.0以下的芯片不能支持，不在适配范围内，ios应无此问题）
     *               2: 没有打开蓝牙
     *               3: 没有打开Wi-Fi
     *               4: 需要请求定位权限(ACCESS_FINE_LOCATION)
     *               5: 服务发生异常---（代码异常，sdk会将状态发给app，原则上不会造成app崩溃，只是无法通过wifi互联，此情况概率极小，上层暂不处理）
     *               6: 创建Wi-Fi Direct GO失败---（部分手机芯片太旧或协议有问题，且出错后重试也无法互联，目前不确定具体有哪些手机型号，在手机顶部给出提示）
     */

    void OnError(int code);
    /**
     * 无感互联一直没有扫描到车机超时
     */
    void onBleLinkScanTimeout();

    /**
     * 无感互联扫描到车机但一直没有互联上超时
     */
    void onBleLinkJoinTimeout();

    /**
     * 连接车机状态
     * @param isConnectCar true 连接成功  false 断开
     */
    void isConnectCar(boolean isConnectCar);

    /**
     *
     * @param isAoa true usb的、连接 false 其他方式连接
     */
    void isAOA(boolean isAoa);
    void setTvTopView(TextView view);
    void hideTopTip();
    void onDestory();
}
