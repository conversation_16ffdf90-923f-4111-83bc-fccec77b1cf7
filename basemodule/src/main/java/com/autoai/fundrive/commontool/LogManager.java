package com.autoai.fundrive.commontool;

import android.content.Context;
import android.util.Log;

import com.autoai.link.baselog.WeLinkLog;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 多资源模块日志管理
 *
 * <AUTHOR>
 */
public class LogManager {
    private static final String TAG = "HIAWAI_LOG";
    private static final String LOG_NAME = LogManager.class.getSimpleName() + ".java";
    //
    private static String filePath;
    private static File logFile;
    private static RandomAccessFile writer;
    private static SimpleDateFormat fmt;
    private static final WeLinkLog LOG = new WeLinkLog() {
        protected String configTag() {
            return "WL_LOG_Screen";
        }

        protected String configLogName() {
            return LogManager.class.getSimpleName() + ".java";
        }
    };

    private LogManager() {
    }

    public static void init(Context context) {
        try {
            File file = context.getExternalFilesDir(null);
            if(file != null) {
                filePath =file.getAbsolutePath() + File.separator + "Test" + File.separator + "welinklog" + File.separator + "haiwai_log" + ".txt";
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public static boolean isIsLoggable() {
        return LOG.isFileLoggable();
    }

    public static void setIsLoggable(boolean isLoggable) {
        LOG.setIsLoggable(isLoggable);
    }

    public static boolean isIsFileLoggable() {
        return LOG.isFileLoggable();
    }

    public static void setIsFileLoggable(boolean isFileLoggable) {
        LOG.setFileLoggable(isFileLoggable);
    }

    public static void v(String msg) {
        LOG.v(msg);
        if (LOG.isFileLoggable()) {
            write(msg);
        }
    }

    public static void v(String msg, Throwable t) {
        LOG.v(msg, t);
        if (LOG.isFileLoggable()) {
            msg = msg + "\n" + android.util.Log.getStackTraceString(t);
            write(msg);
        }
    }

    public static void d(String msg) {
        LOG.d(msg);
        if (LOG.isFileLoggable()) {
            write(msg);
        }
    }

    public static void d(String msg, Throwable t) {
        LOG.d(msg, t);
        if (LOG.isFileLoggable()) {
            msg = msg + "\n" + android.util.Log.getStackTraceString(t);
            write(msg);
        }
    }

    public static void i(String msg) {
        LOG.i(msg);
        if (LOG.isFileLoggable()) {
            write(msg);
        }
    }

    public static void i(String msg, Throwable t) {
        LOG.i(msg, t);
        if (LOG.isFileLoggable()) {
            msg = msg + "\n" + android.util.Log.getStackTraceString(t);
            write(msg);
        }
    }

    public static void w(String msg) {
        LOG.w(msg);
        if (LOG.isFileLoggable()) {
            write(msg);
        }
    }

    public static void w(String msg, Throwable t) {
        LOG.w(msg, t);
        if (LOG.isFileLoggable()) {
            msg = msg + "\n" + android.util.Log.getStackTraceString(t);
            write(msg);
        }
    }

    public static void e(String msg) {
        LOG.e(msg);
        if (LOG.isFileLoggable()) {
            write(msg);
        }
    }

    public static void e(String msg, Throwable t) {
        LOG.e(msg, t);
        if (LOG.isFileLoggable()) {
            msg = msg + "\n" + android.util.Log.getStackTraceString(t);
            write(msg);
        }
    }

    public static void registerUncaughtExceptionHandler(Context context) {
        Thread.UncaughtExceptionHandler handler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler((t, exception) -> {
            e("应用崩溃", exception);
            FileWriter writer;
            try {
                String filePath = context.getApplicationContext().getExternalFilesDir(null).getAbsolutePath() + File.separator + "exception_" + System.currentTimeMillis() + ".txt";
                w("日志位置：" + filePath);
                writer = new FileWriter(filePath);
                writer.write("应用崩溃:" + Log.getStackTraceString(exception));
                writer.flush();
                writer.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            assert handler != null;
            handler.uncaughtException(t, exception);
        });
    }

    private static void write(String logStr) {
        try {
            if (writer == null) {
                logFile = new File(filePath);
                if (!logFile.exists()) {
                    File dir = logFile.getParentFile();
                    if (dir != null) {
                        dir.mkdirs();
                    }

                    logFile.createNewFile();
                }

                writer = new RandomAccessFile(logFile, "rwd");
            }

            if (fmt == null) {
                fmt = new SimpleDateFormat("MMdd HH:mm:ss.SSS", Locale.getDefault());
            }
            logStr = fmt.format(new Date()) + " " + logStr + "\n";
            byte[] buffer = logStr.getBytes();
            writer.seek(logFile.length());
            writer.write(buffer);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void printStackTraceString(String tag, String msg) {
        String stackTraceString =
                Log.getStackTraceString(new Throwable(msg));
        Log.e(
                tag,
                " \n" +
                        "  调用栈： ==》 " + stackTraceString
        );

    }

    public static void onDestroy() {
        if (writer != null) {
            try {
                writer.close();
                writer = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
