package com.autoai.fundrive.commontool;

import android.text.TextUtils;

import java.security.SecureRandom;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串处理
 */
public class StringUtil {
    private static final String FLOAT_PATTERN = "[0-9]+";

	/**
	 * 获取字符串中的前几位数字
	 *
	 * @param str 要检测的字符串
	 * @return 如果有数字则返回Integer，如果没有数字则返回null
	 */
	public static Float getFloatFromString(String str) {
		Pattern pattern = Pattern.compile(FLOAT_PATTERN);
		Matcher matcher = pattern.matcher(str);
		if (matcher.find()) {
			String number = matcher.group();
			if (!TextUtils.isEmpty(number)) {
				return Float.valueOf(number);
			}
		}
		return null;
	}

	public static String getRandomString(int length) {
		String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
		Random random = new Random();
		StringBuilder sb = new StringBuilder();
		sb.append("-");
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(62);
			sb.append(str.charAt(number));
		}

		return sb.toString();
	}
}
