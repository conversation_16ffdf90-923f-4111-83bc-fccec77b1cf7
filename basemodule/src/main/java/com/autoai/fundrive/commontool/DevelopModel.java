package com.autoai.fundrive.commontool;

import android.content.Context;

import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.google.gson.Gson;

public class DevelopModel {
    public static volatile DevelopModel mDevelopModel = null;

    public static DevelopModel getInstance() {
        if (mDevelopModel == null) {
            synchronized (DevelopModel.class) {
                if (mDevelopModel == null) {
                    mDevelopModel = new DevelopModel();
                }
            }
        }
        return mDevelopModel;
    }

    DevelopModel() {
    }

    public static class SwitchData {
        private boolean authWindow;
        private boolean driveSafetyVideoPlay;
        private boolean driveSafetySaveVideo;
        private boolean driveSafetyOffline;
        private boolean logEnabled;
        private int logLevel;

        private int fps;

        public boolean isAuthWindow() {
            return authWindow;
        }

        public void setAuthWindow(boolean authWindow) {
            this.authWindow = authWindow;
        }

        public boolean isDriveSafetyVideoPlay() {
            return driveSafetyVideoPlay;
        }

        public void setDriveSafetyVideoPlay(boolean driveSafetyVideoPlay) {
            this.driveSafetyVideoPlay = driveSafetyVideoPlay;
        }

        public boolean isDriveSafetySaveVideo() {
            return driveSafetySaveVideo;
        }

        public void setDriveSafetySaveVideo(boolean driveSafetySaveVideo) {
            this.driveSafetySaveVideo = driveSafetySaveVideo;
        }

        public boolean isDriveSafetyOffline() {
            return driveSafetyOffline;
        }

        public void setDriveSafetyOffline(boolean driveSafetyOffline) {
            this.driveSafetyOffline = driveSafetyOffline;
        }

        public boolean isLogEnabled() {
            return logEnabled;
        }

        public void setLogEnabled(boolean logEnabled) {
            this.logEnabled = logEnabled;
        }

        public int getLogLevel() {
            return logLevel;
        }

        public void setLogLevel(int logLevel) {
            this.logLevel = logLevel;
        }

        public int getFps() {
            return fps;
        }

        public void setFps(int fps) {
            this.fps = fps;
        }
    }

    public static final String SHARED_KEY_SWITCHES = "key_switches";

    private AuthWindowListener mAuthWindowListener;

    private LogListener mLogListener;
    private FpsListener mFpsListener;

    private DriveSafetyListener mDriveSafetyListener;

    /**
     * 获取数据
     */
    public SwitchData getData(Context context) {
        String json = SharePreferenceUtil.getInstance(context).getString(context, SHARED_KEY_SWITCHES, "");
        SwitchData switchData;
        if (json != null && !json.isEmpty()) {
            switchData = new Gson().fromJson(json, SwitchData.class);
        } else {
            // 第一次默认数据
            switchData = new SwitchData();
            switchData.setAuthWindow(false);
            switchData.setDriveSafetyVideoPlay(false);
            switchData.setDriveSafetySaveVideo(false);
            switchData.setDriveSafetyOffline(true);
            switchData.setLogEnabled(true);
            //2只打印ERROR级别日志 1级别全日志
            switchData.setLogLevel(1);
            switchData.setFps(30);
        }
        return switchData;
    }

    /**
     * 存入数据
     */
    public void saveData(Context context, SwitchData switchData) {
        SharePreferenceUtil.getInstance(context).saveString(context, SHARED_KEY_SWITCHES,
                new Gson().toJson(switchData));
    }

    public void switchAuthWindow(Context context, boolean enabled) {
        SwitchData data = getData(context);
        data.setAuthWindow(enabled);
        saveData(context, data);
        if (mAuthWindowListener != null) {
            mAuthWindowListener.onAuthWindowSwitch(enabled);
        }
    }

    public void switchLogLevel(Context context, boolean enabled, int level) {
        SwitchData data = getData(context);
        data.setLogEnabled(enabled);
        data.setLogLevel(level);
        saveData(context, data);
        if (mLogListener != null) {
            mLogListener.onLogSwitch(enabled, level);
        }
    }
    public void switchFps(Context context, int fps) {
        SwitchData data = getData(context);
        data.setFps(fps);
        saveData(context, data);
        if (mLogListener != null) {
            mFpsListener.onFpsSwitch(fps);
        }
    }
    public void switchDriveSafetyVideoPlay(Context context, boolean enabled) {
        SwitchData data = getData(context);
        data.setDriveSafetyVideoPlay(enabled);
        saveData(context,data);
        if (mDriveSafetyListener != null) {
            mDriveSafetyListener.onDriveSafetyOnlineGPTSwitch(data.driveSafetyVideoPlay);
        }
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.setDriveSafetyVideoPlay(data.driveSafetyVideoPlay);
    }

    public void switchDriveSafetySaveVideo(Context context, boolean enabled) {
        SwitchData data = getData(context);
        data.setDriveSafetySaveVideo(enabled);
        saveData(context,data);
        if (mDriveSafetyListener != null) {
            mDriveSafetyListener.onDriveSafetyOnlineOpencvSwitch(data.driveSafetySaveVideo);
        }
        // todo 需WLPlatformManager中加setDriveSafetyOnlineOpencvSwitch方法
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.setDriveSafetySaveVideo(data.driveSafetySaveVideo);
    }

    public void switchDriveSafetyOffline(Context context,boolean enabled) {
        SwitchData data = getData(context);
        data.setDriveSafetyOffline(enabled);
        saveData(context,data);
        if (mDriveSafetyListener != null) {
            mDriveSafetyListener.onDriveSafetyOfflineSwitch(data.driveSafetyOffline);
        }
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.setDriveSafetyOfflineSwitch(data.driveSafetyOffline);
    }

    /**
     * 获取鉴权弹窗开关状态
     */
    public Boolean getAuthWindowStatus(Context context) {
        return getData(context).isAuthWindow();
    }

    /**
     * 获取行车安全走行规制在线GPT开关状态
     */
    public Boolean getDriveSafetyVideoPlay(Context context) {
        return getData(context).isDriveSafetyVideoPlay();
    }

    /**
     * 获取行车安全走行规制在线opencv开关状态
     */
    public Boolean getDriveSafetySaveVideo(Context context) {
        return getData(context).isDriveSafetySaveVideo();
    }

    /**
     * 获取行车安全走行规制离线开关状态
     */
    public Boolean getDriveSafetyOfflineStatus(Context context) {
        return getData(context).isDriveSafetyOffline();
    }

    public void syncDeviceSafetyStatus(Context context){
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.setDriveSafetyVideoPlay(getDriveSafetyVideoPlay(context));
        platformManager.setDriveSafetySaveVideo(getDriveSafetySaveVideo(context));
        platformManager.setDriveSafetyOfflineSwitch(getDriveSafetyOfflineStatus(context));
    }

    /**
     * 获取log开关状态
     */
    public Boolean getLogStatus(Context context) {
        return getData(context).isLogEnabled();
    }

    /**
     * 获取log等级
     */
    public int getLogLevel(Context context) {
        return getData(context).getLogLevel();
    }

    public int getFps(Context context) {
        return getData(context).getFps();
    }

    public void setAuthWindowListener(AuthWindowListener authWindowListener) {
        this.mAuthWindowListener = authWindowListener;
    }

    public void setLogListener(LogListener logListener) {
        this.mLogListener = logListener;
    }

    public void setDriveSafetyListener(DriveSafetyListener driveSafetyListener) {
        this.mDriveSafetyListener = driveSafetyListener;
    }
    public void setFPSListener(FpsListener fpsListener) {
        this.mFpsListener = fpsListener;
    }

    /**
     * 鉴权弹窗开关监听
     */
    public interface AuthWindowListener {
        /**
         * 当鉴权弹窗开关状态改变时触发
         *
         * @param enabled 开关状态，true:打开，false:关闭
         */
        void onAuthWindowSwitch(boolean enabled);
    }

    /**
     * log开关监听
     */
    public interface LogListener {
        /**
         * 当log开关状态改变时触发
         *
         * @param enabled 开关状态
         * @param level   log级别
         */
        void onLogSwitch(boolean enabled, int level);
    }
    /**
     * log开关监听
     */
    public interface FpsListener {
        /**
         * 当fps 值更改
         *
         *
         */
        void onFpsSwitch(int fps);
    }
    /**
     * 行车安全走行规制开关监听
     */
    public interface DriveSafetyListener {
        /**
         * 当行车安全走行规制开关状态改变时触发
         *
         * @param onlineEnabled 在线GPT识别
         */
        void onDriveSafetyOnlineGPTSwitch(boolean onlineEnabled);

        /**
         * 当行车安全走行规制开关状态改变时触发
         *
         * @param onlineEnabled 在线opencv识别
         */
        void onDriveSafetyOnlineOpencvSwitch(boolean onlineEnabled);

        /**
         * 当行车安全开关走行规制状态改变时触发
         *
         * @param offlineEnabled 离线状态
         */
        void onDriveSafetyOfflineSwitch(boolean offlineEnabled);
    }

}
