package com.autoai.fundrive.hidscreen;

import android.app.Activity;
import android.app.Application;
import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Display;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.sendmsg.SendMsgToCarHelper;
import com.autoai.welink.screen.WLScreen;
import com.autoai.welink.screen.WLScreenListener;
import com.autoai.fundrive.messagebus.MessageCenter;
import com.autoai.fundrive.messagebus.bean.ParamSet;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class ScreenControl {

    private WLScreen mWLScreen;
    private Activity mContext;
    private Notification mNotification;
    private LimitScreenListener mLimitScreenListener;

    private int huScreenWidth;
    private int huScreenHeight;
    private int densityDpi;

    private int mScreenWidth;
    private int mScreenHeight;
    private int phonePointX = -1;
    private int phonePointY = -1;

    private int mDensityDpi;

    private int mOffsetX;
    private int mOffsetY;

    private float base = 1;

    private int mHidAngle;
    //触屏事件区域定义
    private final int[] mRect = new int[4];
    private int[] pHome = new int[4];
    private int[] pBack = new int[4];
    private int[] hHome = new int[4];

    private volatile boolean isStartScreen = false;
    private boolean isLinkFront = false;

    private Handler mHandler;

    /**
     * 断开互联时 占位录屏surface
     * 防止出现surface 被销毁导致 录屏停止
     */
    private SurfaceTexture placeholderSurfaceTexture;
    private final Application.ActivityLifecycleCallbacks activityLifecycleCallbacks = new Application.ActivityLifecycleCallbacks() {

        @Override
        public void onActivityStarted(@NonNull Activity activity) {
            LogManager.i("Activity ---- onActivityStarted --->activity:" + activity.getClass().getSimpleName() + ",isStartScreen:" + isStartScreen);
            isLinkFront = true;
            if (isStartScreen) {
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onLinkStarted();
                }
            }
        }

        @Override
        public void onActivityStopped(Activity activity) {
            LogManager.i("Activity ---- onActivityStopped ------------>activity:" + activity.getClass().getSimpleName() + ",isStartScreen:" + isStartScreen);
            isLinkFront = false;
            if (isStartScreen) {
//                calculateScreen();
//                setScreenSurface();

//                mRect[0] = mOffsetX;
//                mRect[1] = mOffsetY;
//                mRect[2] = mOffsetX + mScreenWidth;
//                mRect[3] = mOffsetY + mScreenHeight;
//
//                //要求退出app发送屏幕尺寸
//                updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);



                LogManager.i("ScreenControl ---- onActivityStopped ------------>onLinkStopped");
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onLinkStopped();
                }
            }
        }

        @Override
        public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {

        }

        @Override
        public void onActivityResumed(@NonNull Activity activity) {

        }

        @Override
        public void onActivityPaused(@NonNull Activity activity) {

        }

        @Override
        public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

        }

        @Override
        public void onActivityDestroyed(@NonNull Activity activity) {

        }
    };

    public void setLimitScreenListener(LimitScreenListener listener) {
        mLimitScreenListener = listener;
    }

    public void registerActivityListener(Application mApplication) {
        mApplication.registerActivityLifecycleCallbacks(activityLifecycleCallbacks);
    }

    public void unregisterActivityListener(Application mApplication) {
        mApplication.unregisterActivityLifecycleCallbacks(activityLifecycleCallbacks);
    }

    public void link(Activity context, Notification notification, int huScreenWidth, int huScreenHeight, int densityDpi) {
        LogManager.i("ScreenControl ---- link ------------>");
        mContext = context;
        mNotification = notification;
        this.huScreenWidth = huScreenWidth;
        this.huScreenHeight = huScreenHeight;
        this.densityDpi = densityDpi;

        LogManager.i("LimitScreenListener ---- onLink ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onLink();
//            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
//            if (platformManager.mAOA){
//            }
        }
    }

    public void unLink() {
        LogManager.i("ScreenControl ---- unLink ------------>");
        stopScreen();
     /*   if (mWLScreen != null) {
            mWLScreen.release(mContext);
            mWLScreen = null;
        }*/
        LogManager.i("LimitScreenListener ---- onUnLink ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onUnLink();
        }
    }

    public void onTouch(MotionEvent motionEvent) {
        LogManager.i("ScreenControl ---- onTouch ------------>");
        if (mWLScreen == null || motionEvent.getPointerCount() > 1) {
            return;
        }
        int screenLeft = mOffsetX;
        int screenTop = mOffsetY;
        motionEvent.offsetLocation(-screenLeft, -screenTop);
        float x = motionEvent.getX();
        float y = motionEvent.getY();
        x = x / base;
        y = y / base;
        motionEvent.setLocation(x, y);
        MotionEvent me = MotionEvent.obtain(motionEvent.getDownTime(), motionEvent.getEventTime(), motionEvent.getAction(),
                motionEvent.getX(), motionEvent.getY(), motionEvent.getPressure(), motionEvent.getSize(), motionEvent.getMetaState(),
                motionEvent.getXPrecision(), motionEvent.getYPrecision(), motionEvent.getDeviceId(),
                motionEvent.getEdgeFlags());
        me.setSource(motionEvent.getSource());
        mWLScreen.touch(me);
//        InputManagerTest.injectInputEvent(me);
    }

    public void startScreen() {
        LogManager.i("ScreenControl ---- startScreen ------------>");
        if (mWLScreen == null) {
            mWLScreen = new WLScreen(mContext, new WLScreenListener() {

                @Override
                public void currentAppPackageName(String packageName) {
                    LogManager.i("ScreenControl ---- currentAppPackageName ------------>" + packageName);

                }

                @Override
                public void onStopScreen() {
                    LogManager.i("ScreenControl ---- onStopScreen mHandler=" + mHandler);
                    //移除定时
                    isStartScreen = false;
                    if (mHandler != null) {
                        mHandler.removeCallbacks(delayedTask);
                        mHandler.post(delayedTask);
                    }
                }

                @Override
                public void onRotation(int rotation, int width, int height) {
                    LogManager.i("ScreenControl ---- onRotation ------------>rotation:" + rotation + ",isLinkFront:" + isLinkFront);

                    switch (rotation) {
                        case Surface.ROTATION_0:
                            mHidAngle = 0;
                            break;
                        case Surface.ROTATION_90:
                            mHidAngle = 90;
                            break;
                        case Surface.ROTATION_180:
                            mHidAngle = 180;
                            break;
                        case Surface.ROTATION_270:
                            mHidAngle = 270;
                            break;
                    }

                    phonePointX = -1;
                    phonePointY = -1;
                    //--->计算屏幕宽高
                    calculateScreen(width, height);
                    LogManager.i("ScreenControl ---- onRotation ------------>mOffsetX=" + mOffsetX + ", mOffsetY=" + mOffsetY
                            + ",mScreenWidth=" + mScreenWidth + ",mScreenHeight=" + mScreenHeight
                            + ",mHidAngle=" + mHidAngle + ",isLinkFront=" + isLinkFront);

                    if (!isStartScreen) {
                        return;
                    }

                    // 屏幕旋转只通知，不再重置编码器
//                    setScreenSurface();

                    //实际录屏内容区域
                    mRect[0] = mOffsetX;
                    mRect[1] = mOffsetY;
                    mRect[2] = mScreenWidth;
                    mRect[3] = mScreenHeight;
                    updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);
                }
            }, false, mNotification);
        }
        calculateScreen();
        updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);
        LogManager.i("ScreenControl ---- startScreen ------------>mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi + ",isStartScreen=" + isStartScreen);
        if (isStartScreen) {
            //已成功录屏发送 消息
//            SendMsgToCarHelper.sendActionCarHome("reauthorize_record_screen", "alread_record_screen");
            if (mHandler != null) {
                mHandler.removeCallbacks(delayedTask);
            }
            //互联成功 准备好录屏 surface
            setScreenSurface();
            if (placeholderSurfaceTexture != null) {
                placeholderSurfaceTexture.release();
                placeholderSurfaceTexture = null;
            }
            //回调 能接收锁屏消息
            if (mLimitScreenListener != null) {
                mLimitScreenListener.onStartScreen();
            }
        } else {
            //启动录屏消息
            SendMsgToCarHelper.sendActionCarHome("reauthorize_record_screen", "start_record_screen");
            //-->she tips:录屏size使用hu车机侧的size
            mWLScreen.start(huScreenWidth, huScreenHeight, mDensityDpi);
        }
//        mWLScreen.start(mScreenWidth, mScreenHeight, mDensityDpi);
        LogManager.i("LimitScreenListener ---- onRequestFullScreenLimit ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onRequestFullScreenLimit();
        }
    }

    public void stopScreen() {
        LogManager.i("ScreenControl ---- stopScreen ------------>" + Thread.currentThread().getName());
//        updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);
        if (mWLScreen != null) {
            SurfaceTexture surfaceTexture = new SurfaceTexture(0);
            placeholderSurfaceTexture = surfaceTexture;
            Surface surface = new Surface(surfaceTexture);
            //录屏surface 占位 ，防止因为surface 释放导致录屏终止   也可替换为null
            mWLScreen.setSurface(surface, mScreenWidth, mScreenHeight, mDensityDpi);
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            //关闭编码器
            platformManager.getDisEnableExternal();
            platformManager.setExternalCastScreen(false);
//            isStartScreen = false;
//          如果处于录屏中， 暂时不停止录屏  延迟停止录屏时间
            if (mHandler == null) {
                mHandler = new Handler(Looper.getMainLooper());
            }
            if (isStartScreen) {
                LogManager.i("LimitScreenListener ---- mHandler ------------>");
                mHandler.postDelayed(delayedTask, 1000 * 60 * 5);
            } else {
                mHandler.post(delayedTask);
            }
            LogManager.i("LimitScreenListener ---- onStopScreen ------------>");
            if (mLimitScreenListener != null) {
                mLimitScreenListener.onStopScreen();
            }
        }
    }

    private Runnable delayedTask = new Runnable() {
        @Override
        public void run() {
            mHandler = null;
            //停止录屏
            isStartScreen = false;
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            LogManager.i("ScreenControl ---- stopScreen2 ------------>" + platformManager);
            platformManager.setScreenStatus(isStartScreen);
            if (mWLScreen != null) {
                mWLScreen.stop();
                mWLScreen.release(mContext);
                mWLScreen = null;
            }
        }
    };

    public void pauseScreen() {
        LogManager.i("ScreenControl ---- pauseScreen ------------>");
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.getDisEnableExternal();
        platformManager.setExternalCastScreen(false);

        isStartScreen = false;

        mRect[0] = 0;
        mRect[1] = 0;
        mRect[2] = 0;
        mRect[3] = 0;
        updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);

        Map<String, Boolean> param = new HashMap<>(4);
        param.put("dialog_masking", false);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, "dialog_masking");

        LogManager.i("LimitScreenListener ---- onPauseScreen ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onPauseScreen();
        }
    }

    public void resumeScreen() {
        LogManager.i("ScreenControl ---- resumeScreen ------------>");
        calculateScreen();
        setScreenSurface();

        isStartScreen = true;

        mRect[0] = mOffsetX;
        mRect[1] = mOffsetY;
        mRect[2] = mScreenWidth;
        mRect[3] = mScreenHeight;
        updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);

        Map<String, Boolean> param = new HashMap<>(4);
        param.put("dialog_masking", true);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, "dialog_masking");

        LogManager.i("LimitScreenListener ---- onResumeScreen ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onResumeScreen();
        }
    }

    public void updateHid(int[] pHome, int[] pBack, int[] hHome) {
        LogManager.i("ScreenControl ---- updateHid ------------>");
        mRect[0] = mOffsetX;
        mRect[1] = mOffsetY;
        mRect[2] = mScreenWidth;
        mRect[3] = mScreenHeight;
        this.pHome = pHome;
        this.pBack = pBack;
        this.hHome = hHome;

        //要求退出app 不发送屏幕尺寸
        updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);
    }

    public void activityResult(int requestCode, int resultCode, Intent data) {
        LogManager.i("ScreenControl ---- activityResult ------------>requestCode:" + requestCode + ",resultCode:" + resultCode);
        if (mWLScreen == null) {
            return;
        }

        if (requestCode == WLScreen.SCREEN_CAPTURE_REQUEST_CODE) {
            mWLScreen.onActivityResult(requestCode, resultCode, data);
            if (resultCode == Activity.RESULT_OK) {
                isStartScreen = true;
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                platformManager.setScreenStatus(isStartScreen);

                calculateScreen();
                setScreenSurface();
                mRect[0] = mOffsetX;
                mRect[1] = mOffsetY;
                mRect[2] = mScreenWidth;
                mRect[3] = mScreenHeight;

                updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);
                LogManager.i("LimitScreenListener ---- onStartScreen ------------>");
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onStartScreen();
                }
            } else {
                LogManager.i("ScreenControl ---- activityResult ------------>onStopScreen");
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onStopScreen();
                }
            }
        }
    }


    /**
     * 根据实际宽高，初始化核心功能
     * 1.编码器
     * 2.投屏
     */
    private void setScreenSurface() {
        LogManager.printStackTraceString("shecw1", "ScreenControl::setScreenSurface");
        LogManager.i("ScreenControl ---- setScreenSurface ------------>");
        if (mWLScreen == null) {
            return;
        }
        Rect rect = new Rect(0, 0, huScreenWidth, huScreenHeight);

        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        //--->依据范围获取投屏显示容器
        Surface mSurface = platformManager.getEnableExternal(rect);
        //--->she tips:使用hu主机侧的size 重新设置surface给录屏
        mWLScreen.setSurface(mSurface, huScreenWidth, huScreenHeight, mDensityDpi);
        LogManager.i("ScreenControl ---- setScreenSurface ------------>mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi);
        platformManager.setExternalCastScreen(true);
    }

    /**
     * 计算投屏的宽高，涉及到
     * 1.录屏参数
     * 2.编码器分辨率参数
     *
     * @param width
     * @param height
     */
    private void calculateScreen(int width, int height) {
        LogManager.printStackTraceString("shecw1", "ScreenControl::calculateScreen2 width = " + width + ";height = " + height);
        LogManager.i("ScreenControl ---- calculateScreen ------------>phonePointX=" + phonePointX + ", phonePointY=" + phonePointY + ", width=" + width + ", height=" + height);
        if (phonePointX == -1 || phonePointY == -1) {
            // 折叠屏mContext（Activity）获取的宽高是连接时的宽高，ScreenService能获取展开折叠后最新宽高
            phonePointX = width;
            phonePointY = height;
            LogManager.i("ScreenControl - calculateScreen ->phonePointX=" + phonePointX + ",phonePointY=" + phonePointY);
        }
        //--->根据角度，给screen赋值
        if (mHidAngle == 90 || mHidAngle == 270) {
            mScreenWidth = phonePointY;
            mScreenHeight = phonePointX;
        } else {
            mScreenWidth = phonePointX;
            mScreenHeight = phonePointY;
        }

        //---> 根据车机屏幕的宽和实际设备的宽huScreenWidth，计算宽的缩放比
        float wBase = (float) (huScreenWidth * 1.0 / mScreenWidth);
        //---> 根据车机屏幕的宽和实际设备的高huScreenHeight，计算比率高的缩放比
        float hBase = (float) (huScreenHeight * 1.0 / mScreenHeight);
        //---> 以缩放比最小的边进行换算
        base = Math.min(wBase, hBase);

        //--->计算要投屏的宽高
        mScreenWidth = (int) (mScreenWidth * base);
        mScreenHeight = (int) (mScreenHeight * base);

        mDensityDpi = densityDpi;

        Log.i("shecw1", "ScreenControl::calculateScreen2 mScreenWidth = " + mScreenWidth + ";mScreenHeight = " + mScreenHeight
                + ";phonePointX = " + phonePointX + ";phonePointY = " + phonePointY
                + ";huScreenWidth = " + huScreenWidth + ";huScreenHeight = " + huScreenHeight
                + ";base = " + base);

    }

    private void calculateScreen() {
        LogManager.printStackTraceString("shecw1", "ScreenControl::calculateScreen1");
        LogManager.i("ScreenControl ---- calculateScreen ------------>");
        if (phonePointX == -1 || phonePointY == -1) {
            WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
            Display display = wm.getDefaultDisplay();
            Point mPoint = new Point();
            display.getRealSize(mPoint);
            phonePointX = mPoint.x;
            phonePointY = mPoint.y;
        }
        //
        if (mHidAngle == 90 || mHidAngle == 270) {
            mScreenWidth = phonePointY;
            mScreenHeight = phonePointX;
        } else {
            mScreenWidth = phonePointX;
            mScreenHeight = phonePointY;
        }


        float wBase = (float) (huScreenWidth * 1.0 / mScreenWidth);
        float hBase = (float) (huScreenHeight * 1.0 / mScreenHeight);

        base = Math.min(wBase, hBase);

        mScreenWidth = (int) (mScreenWidth * base);
        mScreenHeight = (int) (mScreenHeight * base);

        mOffsetX = (huScreenWidth - mScreenWidth) / 2;
        mOffsetY = (huScreenHeight - mScreenHeight) / 2;
        mDensityDpi = densityDpi;
        Log.i("shecw1", "ScreenControl::calculateScreen1 mScreenWidth = " + mScreenWidth + ";mScreenHeight = " + mScreenHeight
                + ";phonePointX = " + phonePointX + ";phonePointY = " + phonePointY
                + ";huScreenWidth = " + huScreenWidth + ";huScreenHeight = " + huScreenHeight
                + ";base = " + base);

    }

    private void updateStates(boolean isEnabled, int angle, int[] mRect, int[] pHome, int[] pBack, int[] hHome) {
        LogManager.i("ScreenControl ---- updateStates ------------>");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android");

            JSONObject command = new JSONObject();
            command.put("method", "hidTouch");

            JSONObject extData = new JSONObject();
            extData.put("enabled", isEnabled);
            extData.put("angle", angle);
            extData.put("x", mRect[0]);
            extData.put("y", mRect[1]);
            extData.put("w", mRect[2]);
            extData.put("h", mRect[3]);
            command.put("extData", extData);

            JSONObject phoneHome = new JSONObject();
            phoneHome.put("x", pHome[0]);
            phoneHome.put("y", pHome[1]);
            phoneHome.put("w", pHome[2]);
            phoneHome.put("h", pHome[3]);
            command.put("phoneHome", phoneHome);

            JSONObject phoneBack = new JSONObject();
            phoneBack.put("x", pBack[0]);
            phoneBack.put("y", pBack[1]);
            phoneBack.put("w", pBack[2]);
            phoneBack.put("h", pBack[3]);
            command.put("phoneBack", phoneBack);

            JSONObject phoneSize = new JSONObject();
            phoneSize.put("x", 0);
            phoneSize.put("y", 0);
            phoneSize.put("w", phonePointX);
            phoneSize.put("h", phonePointY);
            command.put("phoneSize", phoneSize);

            JSONObject huHome = new JSONObject();
            huHome.put("x", hHome[0]);
            huHome.put("y", hHome[1]);
            huHome.put("w", hHome[2]);
            huHome.put("h", hHome[3]);
            command.put("huHome", huHome);

            jsonObject.put("command", command);

        } catch (JSONException e) {
            LogManager.e("updateStates exception:" + e.getMessage());
        }
        String hidMessage = jsonObject.toString();
        LogManager.i("ScreenControl ---- updateStates ------------>hidMessage：" + hidMessage);
        WLProtocolManager.getInstance().sendCommondMessage(hidMessage);
        WLProtocolManager.getInstance().updateStates(mRect[2],  mRect[3], angle);
    }

    public void onDestory() {
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(delayedTask);
            if (mWLScreen != null) {
                mWLScreen.stop();
                mWLScreen.release(mContext);
                mWLScreen = null;
            }
        }
    }
}
