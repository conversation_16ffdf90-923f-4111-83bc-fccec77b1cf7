package com.autoai.fundrive.platform;

import static android.content.Context.NOTIFICATION_SERVICE;
import static android.content.Context.RECEIVER_EXPORTED;

import android.app.Activity;
import android.app.Application;
import android.app.Dialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.bluetooth.le.ScanFilter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.Surface;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.autoai.fundrive.basemodule.R;
import com.autoai.fundrive.basemodule.activity.MainActivity;
import com.autoai.fundrive.basemodule.singleton.Singleton;
import com.autoai.fundrive.commontool.DevelopModel;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.SharePreferenceUtil;
import com.autoai.fundrive.commontool.StringUtil;
import com.autoai.fundrive.platform.auth.AuthModel;
import com.autoai.fundrive.platform.bean.CarBean;
import com.autoai.fundrive.platform.bean.ConnectorBean;
import com.autoai.fundrive.platform.bean.WLPlatformState;
import com.autoai.fundrive.platform.listener.LinkAdapterListener;
import com.autoai.fundrive.platform.listener.LinkConnectListener;
import com.autoai.fundrive.platform.listener.LinkFrameListener;
import com.autoai.fundrive.platform.listener.LinkPlatformListener;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.welink.platform.WLPlatform;
import com.autoai.welink.platform.WLPlatformListener;
import com.autoai.welink.screen.WLScreen;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 主要负责实现并提供平台应用功能接口。
 *
 * <AUTHOR>
 */
@Keep
public class WLPlatformManager implements Singleton {

    public static final String NAME = WLPlatformManager.class.getSimpleName();
    private Activity mContext;
    private WLPlatform mWLPlatform;
    private PlatformReceiver mPlatformReceiver;
    private LinkPlatformListener mLinkPlatformListener;
    private LinkAdapterListener mLinkAdapterListener;
    private LinkFrameListener mLinkFrameListener;
    private WLPlatformState mPlatformCarState = WLPlatformState.STATUS_NONE;

    private boolean isEnableExternal = false;
    private boolean isExternalCastScreen = false;
    /**
     * 是否处于录屏状态 true 是，false 否
     */
    private boolean isScreen = false;
    private boolean isFirstFrameData = false;

    private int huScreenWidth;
    private int huScreenHeight;
    private int densityDpi;
    private int logLevel;
    private String vehicleType;
    private String vehicleVersion = "";
    private String btMacAddress;

    // 新增字段以保持与WLConnectManager.getHuCarInfo()的数据一致性
    private String userID = "";
    private String vehicleID = "";

    // 连接监听器，替代WLConnectManager的连接管理
    private LinkConnectListener mLinkConnectListener;



    /**
     * 创建wifi-direct 群组
     */
    private String netWorkName;

    public Notification mNotification;

    private final HashMap<String, ConnectorBean> mAssignKeys = new HashMap<>();
    private static final int WL_PLATFORM_BLE_LINK_SCAN_TIMEOUT = 14 * 1000;
    private static final int WL_PLATFORM_HANDLER_WHAR_SEND = 100;
    private static final int WL_PLATFORM_HANDLER_WHAR_BLE_SCAN = 101;
    private static final int WL_PLATFORM_HANDLER_WHAR_BLE_JOIN = 102;
    private static final int WL_PLATFORM_HANDLER_WHAR_DELAYED_TIME = 1000;
    private boolean isFirstStarted = false;
    private boolean isBleInit = false;

    /**
     * app是否处于后台
     */
    private boolean isBackground = false;
    /**
     * 是否是AOA 连接
     */
    public boolean mAOA = true;

    /**
     * 纪录上次互联的热点名称和密码
     * 异常断开 手机后台互联时使用
     */
    private String mSsid;
    private String mPassword;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (WL_PLATFORM_HANDLER_WHAR_SEND == msg.what) {
                Bundle data = msg.getData();
                if (data != null) {
                    String packageName = data.getString(WLPlatformConfig.WL_PLATFORM_BUNDLE_PACKAGE_NAME);
                    String connectKey = data.getString(WLPlatformConfig.WL_PLATFORM_BUNDLE_CONNECT_KEY);
                    Intent connect = new Intent(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
                    connect.putExtra(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY, connectKey);
                    connect.setPackage(packageName);
                    mContext.sendBroadcast(connect);
                } else if (WL_PLATFORM_HANDLER_WHAR_BLE_SCAN == msg.what) {
                    if (mLinkPlatformListener != null) {
                        mLinkPlatformListener.onBleLinkScanTimeout();
                    }
                } else if (WL_PLATFORM_HANDLER_WHAR_BLE_JOIN == msg.what) {
                    if (mLinkPlatformListener != null) {
                        mLinkPlatformListener.onBleLinkJoinTimeout();
                    }
                }
            }
        }
    };

    public WLPlatformManager() {
    }

    /**
     * 初始化平台服务管理类
     *
     * @param activity - 上下文 ,
     * @param listener 三投平台手车连接状态接口
     */
    @Keep
    public void init(Activity activity, LinkPlatformListener listener, int logLevel) {
        LogManager.i("init -------------->");
        mContext = activity;
        this.logLevel = logLevel;
        mNotification = createNotification();
        mLinkPlatformListener = listener;
        mWLPlatform = WLPlatform.create(mContext, mNotification, new WLPlatformListener() {

            @Override
            public void onLinkConnected(int mScreenWidth, int mScreenHeight, int mDensityDpi, String mVehicleType, String mVehicleVersion, String mBtMacAddress, boolean isAOA) {
                LogManager.i("WLPlatformListener -------- onLinkConnected -------------->mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi + ",mVehicleType:" + mVehicleType + ",mVehicleVersion:" + mVehicleVersion + ",mBtMacAddress:" + mBtMacAddress + ",isAOA:" + isAOA);
                LogManager.i("onLinkConnected -------------> isAOA=" + isAOA + " mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi + ",mVehicleType:" + mVehicleType + ",mVehicleVersion:" + mVehicleVersion + ",mBtMacAddress:" + mBtMacAddress);
                LogManager.i("onLinkConnected -------------> mWLPlatform=" + mWLPlatform);
                if (mWLPlatform == null) {
                    return;
                }
                mAOA = isAOA;
                huScreenWidth = mScreenWidth;
                huScreenHeight = mScreenHeight;
                densityDpi = mDensityDpi;
                vehicleType = mVehicleType;
                vehicleVersion = mVehicleVersion;
                btMacAddress = mBtMacAddress;
                mAssignKeys.clear();
                mWLPlatform.changeFps(DevelopModel.getInstance().getFps(mContext));
                mWLPlatform.init("", null);
                isFirstFrameData = true;
            /*    if (mLinkFrameListener == null) {
                    LogManager.i("onLinkConnected --------start------>1111111");
                    mWLPlatform.start();
                } else {
                    LogManager.i("onLinkConnected --------start------>2222222");
                    mWLPlatform.start(0, 1, 0, 48);
                }*/
//                if (WLPlatformConfig.IS_VIDEO_TEST_MODEL) {
//                    // 内部测试用, 保存手机录制的视频
//                    mWLPlatform.toggleLocalVideo(true, mContext.getExternalFilesDir(null) + "/" + System.currentTimeMillis() + ".mp4");
//                }
                mPlatformCarState = WLPlatformState.STATUS_CAR_CONNECTED;
                stopWifiLink();
                hideGroupInfo();
                LogManager.i("onLinkConnected -----------hideGroupInfo");
                if (mLinkPlatformListener != null) {
                    LogManager.i("onLinkConnected ---------mLinkPlatformListener");
                    mLinkPlatformListener.onLinkConnected(vehicleType, isAOA);
                }
                AuthModel.INSTANCE.updateVehicleType(vehicleType);
            }

            @Override
            public void onLinkUnconnected(boolean isCrash) {
                LogManager.i("WLPlatformListener -------- onLinkUnconnected -------------->isCrash:" + isCrash);
                if (mWLPlatform == null) {
                    return;
                }
                mAOA = true;
                mWLPlatform.stop();
                mWLPlatform.deinit();
                startWifiLink();
                startWIFIAp();
                overlayDialog = null;
                //
                mPlatformCarState = WLPlatformState.STATUS_CAR_DISCONNECTED;
                isEnableExternal = false;
                isExternalCastScreen = false;
                isFirstFrameData = false;

                if (mLinkPlatformListener != null) {
                    mLinkPlatformListener.onLinkDisconnected();
                }
//                if (WLPlatformConfig.IS_VIDEO_TEST_MODEL) {
//                    mWLPlatform.toggleLocalVideo(false, null);
//                }
                WLPlatformConfig.isConnectcar = false;

                //解决无感互联发生异常断开时导致usb互联失败的问题
               /* Intent intent = mContext.getIntent();
                if (intent != null && intent.getAction() != null && intent.getAction().equals(UsbManager.ACTION_USB_ACCESSORY_ATTACHED)) {
                    if (intent.hasExtra("UsbAccessory")) {
                        UsbAccessory usbAccessory = intent.getParcelableExtra("UsbAccessory");
                        Intent usbIntent = new Intent();
                        usbIntent.setAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
                        usbIntent.putExtra("accessory", usbAccessory);
                        startLink(usbIntent);
                        mContext.setIntent(null);
                    } else {
                        startLink(intent);
                        mContext.setIntent(null);
                    }
                }*/
            }

            @Override
            public void onAppConnected(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppConnected -------------->connectStr:" + connectStr);
                mWLPlatform.mirror(connectStr);
                if (isPlatformApp(connectStr)) {
                    WLPlatformConfig.isConnectcar = true;
                }
            }

            @Override
            public void onAppDisconnected(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppDisconnected -------------->connectStr:" + connectStr);
                mWLPlatform.revoke(connectStr);
            }

            @Override
            public void onAppError(String connectStr, int errorCode) {
                LogManager.i("WLPlatformListener -------- onAppError -------------->errorCode :" + errorCode);
            }

            @Override
            public void onAppForeground(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppForeground -------------->");
            }

            @Override
            public void onAppBackground(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppBackground -------------->");
            }

            @Override
            public void onAppAction(String connectStr, int action) {
                LogManager.i("WLPlatformListener -------- onAppAction -------------->");
                switch (action) {
                    case WLPlatformConfig.WL_APP_ACTION_START_MIC:
                        //请求车机MIC声音
                        mWLPlatform.openMicrophone(connectStr);
                        break;
                    case WLPlatformConfig.WL_APP_ACTION_STOP_MIC:
                        //停止车机MIC声音
                        mWLPlatform.openMicrophone(null);
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onLinkHUMessageData(String data) {
                LogManager.i("WLPlatformListener -------- onLinkHUMessageData -------------->data:" + data);
                if (!TextUtils.isEmpty(data)) {
                    WLProtocolManager.getInstance().onReceiveHUCommand(data);
                }
            }

            @Override
            public void onLinkHUCanData(byte[] bytes) {
                if (bytes != null && bytes.length > 0) {
                    String data = new String(bytes);
                    WLProtocolManager.getInstance().onReceiveHUCommand(data);
                }
            }

            @Override
            public void onLinkTouch(MotionEvent motionEvent) {
                Dialog mDialog = getOverlayDialog();

                if (mDialog != null && mDialog.isShowing()) {
                    if (mDialog.dispatchTouchEvent(motionEvent)) {
                        return;
                    }
                }

              /*  if (isEnableExternal && isExternalCastScreen) {
                    if (mLinkAdapterListener != null && !mAOA) {
                        mLinkAdapterListener.onLinkTouch(motionEvent);
                    }
                } else {
                    mWLPlatform.touch(motionEvent);
                }*/
            }

            @Override
            public int onAppSound(String connectStr, String mark, int duration) {
                LogManager.i("WLPlatformListener -------- onAppSound -------------->mark:" + mark + ",duration:" + duration);
                int audioType = 0;
                if (isPlatformApp(connectStr)) {
                    try {
                        audioType = Integer.parseInt(mark);
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                }
                LogManager.i("WLPlatformListener -------- onAppSound -------------->audioType:" + audioType);
                return audioType;
            }

            @Override
            public void onMusicRegister(String connectStr) {
                LogManager.i("WLPlatformListener -------- onMusicRegister -------------->");

            }

            @Override
            public void onMusicUnregister(String connectStr) {
                LogManager.i("WLPlatformListener -------- onMusicUnregister -------------->");

            }

            @Override
            public void onMusicID3(String source, String artist, String title, String album, String lyric, int duration, Bitmap cover) {
                LogManager.i("WLPlatformListener -------- onMusicID3 -------------->");

            }

            @Override
            public void onMusicOrder(int order) {
                LogManager.i("WLPlatformListener -------- onMusicOrder -------------->");

            }

            @Override
            public void onMusicPCM(long position, long totalLen, int rate, int bit, int channel) {
                LogManager.i("WLPlatformListener -------- onMusicPCM -------------->totalLen:" + totalLen + ",position:" + position);

            }

            @Override
            public void onFrameData(byte[] bytes) {
                LogManager.i("WLPlatformListener -------- onFrameData -------------->size:" + bytes.length + ",isFirstFrameData:" + isFirstFrameData);
                if (mLinkFrameListener != null && isFirstFrameData) {
                    isFirstFrameData = false;
                    mLinkFrameListener.onFrameData(bytes);
                }
            }

            @Override
            public void onLinkSuspend() {
                LogManager.i("WLPlatformListener -------- onLinkSuspend -------------->");
                mWLPlatform.stop();
            }

            @Override
            public void onLinkResume() {
                LogManager.i("WLPlatformListener -------- onLinkResume -------------->");
                if (mLinkFrameListener == null) {
                    LogManager.i("onLinkConnected --------start------>1111111");
                    mWLPlatform.start();
                } else {
                    LogManager.i("onLinkConnected --------start------>2222222");
                    mWLPlatform.start(0, 1, 0, 48);
                }
            }

            @Override
            public void onLinkAOAReady() {
                LogManager.i("WLPlatformListener -------- onLinkAOAReady -------------->");
                mLinkPlatformListener.onConnecting();
            }

            @Override
            public void onHardwareGroupError(int reason) {
                LogManager.i("WLPlatformListener -------- onHardwareGroupError -------------->reason:" + reason);
                //error - 错误码 1: 设备不支持蓝牙BLE 2: 没有打开蓝牙 3: 没有打开Wi-Fi 4: 需要请求定位权限(ACCESS_FINE_LOCATION) 5: 服务发生异常 6: 创建Wi-Fi Direct GO失败
                if (mLinkPlatformListener != null) {
                    mLinkPlatformListener.onBleLinkError(reason);
                }
            }

            @Override
            public void onHardwareGroupStatusChanged(int status, String content) {
                LogManager.i("WLPlatformListener -------- onHardwareGroupStatusChanged -------------->status:" + status + ",content:" + content);

                // 0: Wi-Fi Direct GO广播，content: GO签名 1: BLE设备连接中... 2: BLE设备连接成功 3: BLE服务连接中... 4: BLE服务连接成功5: 读取版本中...
                // 6: 版本读取成功，content：版本号 7: 写Wi-Fi Direct GO签名 8: 签名写成功，content：签名9: Wi-Fi Direct GC连接，content: 网络密码10: 未找到对应包名的硬件心跳
                // -1: 设备连接失败 -2: 服务连接失败 -3: 版本读取失败 -4: 签名写失败 -5: 操作超时
                switch (status) {
                    case 0:
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
                        mHandler.sendEmptyMessageDelayed(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN, WL_PLATFORM_BLE_LINK_SCAN_TIMEOUT);
                        break;
                    case 1:
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
                        mHandler.sendEmptyMessageDelayed(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN, WL_PLATFORM_BLE_LINK_SCAN_TIMEOUT);
                        break;
                    case 9:
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
                        mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
                        break;
                    default:
                        break;
                }
                if (mLinkPlatformListener != null) {
                    mLinkPlatformListener.onBleLinkStatus(status);
                }
            }

            @Override
            public void onWiFiApConfig(String ssid, String password) {
                LogManager.i("WLPlatformListener -------- onWiFiApConfig -------------->ssid:" + ssid + ",password:" + password);
                mSsid = ssid;
                mPassword = password;
            }
        });
        mWLPlatform.enableLogFile(LogManager.isIsLoggable(),logLevel);
        mWLPlatform.enableLogCat(LogManager.isIsLoggable(),logLevel);
        registerReceiver();
        startWifiLink();
        startWIFIAp();
//        requestDirectLink();
        requestWiFiLink();
        LogManager.i("requestConnect -------------->logLevel111:" + logLevel );
    }

    public void setLoggable(boolean isLoggable) {
        if (mWLPlatform != null) {
            mWLPlatform.enableLogFile(isLoggable,logLevel);
            LogManager.i("requestConnect -------------->logLevel222:" + logLevel );

        }
    }


    /**
     * 连接三方app
     *
     * @param packageName - 应用包名 ,
     * @param connectKey  三投应用连接码
     */
    @Keep
    public void requestConnect(String packageName, String connectKey) {
        LogManager.i("requestConnect -------------->packageName:" + packageName + ",connectKey:" + connectKey);
        //连接车机状态下
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED) {
            mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_SEND);
            Message message = mHandler.obtainMessage();
            Bundle data = new Bundle();
            data.putString(WLPlatformConfig.WL_PLATFORM_BUNDLE_PACKAGE_NAME, packageName);
            data.putString(WLPlatformConfig.WL_PLATFORM_BUNDLE_CONNECT_KEY, connectKey);
            message.what = WL_PLATFORM_HANDLER_WHAR_SEND;
            message.setData(data);
            mHandler.sendMessageDelayed(message, WL_PLATFORM_HANDLER_WHAR_DELAYED_TIME);
        }
    }

    /**
     * 方法名称：requestWiFiLink
     * 方法描述：申请wifi互联功能
     * 方法参数：
     * 返回类型：
     */

    @Keep
    public void requestWiFiLink() {
        LogManager.i("requestWiFiLink -------------->isLinkWifi=" + isLinkWifi());
       /* if (!isLinkWifi()) {
            return;
        }
        startWifiLink();
        */
        registerWIFIActivityListener();
    }

    /**
     * 方法名称：requestDirectLink
     * 方法描述：申请wifi无感互联功能
     * 方法参数：
     * 返回类型：
     */

    @Keep
    public void requestDirectLink() {
        LogManager.i("requestDirectLink -------------->");
        if (isBleInit || !isLinkBleWifi()) {
            return;
        }
        isBleInit = true;
        netWorkName = SharePreferenceUtil.getString(mContext, "NETWORK_NAME", "");
        if(TextUtils.isEmpty(netWorkName)){
            netWorkName = Build.BRAND + StringUtil.getRandomString(6);
            SharePreferenceUtil.saveString(mContext,"NETWORK_NAME",netWorkName);
        }
        LogManager.i("requestDirectLink --------onPermissionGranted------> netWorkName："+netWorkName);
        startDirectLink();
        registerDirectActivityListener();
    }

    /**
     * 开始连接车机
     *
     * @param intent - 互联意图
     */
    @Keep
    public void startLink(Intent intent) {
        LogManager.i("startLink -------------->");
        if (mWLPlatform == null) {
            return;
        }
        //如果在连接车机中或者已连接车机，不在进行连接车机操作    如果连接的是无线则可以起动有线连接
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED && mAOA) {
            return;
        }
        stopDirectLink();
        mPlatformCarState = WLPlatformState.STATUS_CAR_CONNECTING;
        LogManager.i("startLink -------11111111------->");
        mWLPlatform.link(intent);
    }

    /**
     * 开始断开连接车机
     */
    @Keep
    public void stopLink() {
        LogManager.i("stopLink -------------->");
        if (mWLPlatform == null) {
            return;
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return;
        }
        mPlatformCarState = WLPlatformState.STATUS_CAR_DISCONNECTING;
        LogManager.i("stopLink -------11111111------->");
        mWLPlatform.unlink();
    }

    /**
     * 获取app连接码
     *
     * @param packageName - 应用包名 ,
     * @param cap         三投应用互联能力码
     */
    @Keep
    public String getConnectKey(String packageName, int cap) {
        LogManager.i("getConnectKey -------------->packageName:" + packageName + ",cap:" + cap);
        if (mWLPlatform == null) {
            return null;
        }
        String connectKey = mWLPlatform.assign(packageName, cap);

        ConnectorBean bean = new ConnectorBean();
        bean.setConnectKey(connectKey);
        bean.setPackageName(packageName);
        bean.setCap(cap);

        mAssignKeys.put(connectKey, bean);
        return connectKey;
    }

    /**
     * 依据范围获取投屏显示容器
     */
    @Keep
    public Surface getEnableExternal(Rect rect) {
        LogManager.i("getDisplaySurface -------------->");
        if (mWLPlatform == null) {
            return null;
        }
        isEnableExternal = true;

        return mWLPlatform.enableExternal(rect);
    }


    /**
     * 取消获取投屏显示容器
     */
    @Keep
    public void getDisEnableExternal() {
        LogManager.i("getDisplaySurface -------------->");
        if (mWLPlatform == null) {
            return;
        }
        isEnableExternal = false;
        mWLPlatform.disableExternal();
    }

    private Dialog overlayDialog;

    /**
     * 获取Overlay显示容器
     */
    @Keep
    public Dialog getOverlayDialog() {
        LogManager.i("getOverlayDialog -------------->");
        if (mWLPlatform == null) {
            return null;
        }
        if (overlayDialog == null) {
            overlayDialog = mWLPlatform.getOverlay();
        }
        if (overlayDialog != null && overlayDialog.getContext() != null) {
            LocaleUtil.changeLocaleConfig(overlayDialog.getContext(), LocaleUtil.locale);
        }
        return overlayDialog;
    }

    /**
     * 手机给车机发送消息交互协议
     *
     * @param protocolData - json格式协议字符串
     */
    @Keep
    public void sendMessageDataToCar(String protocolData) {
        LogManager.i("sendMessageDataToCar -------------->protocolData:" + protocolData);
        if (mWLPlatform == null) {
            return;
        }
        if (!TextUtils.isEmpty(protocolData)) {
            mWLPlatform.sendMessageData(protocolData);
        }
    }

    @Keep
    public void updateStates(int width, int height, int angle){
        LogManager.i("sendMessageDataToCar -------------->updateStates:" + width + ",height:" + height + ",angle:" + angle);
        if (mWLPlatform == null) {
            return;
        }
        mWLPlatform.updateStates(width,height,angle);
    }


    /**
     * 手机给车机发送candata交互协议
     *
     * @param protocolData - json格式协议字符串
     */
    @Keep
    public void sendCanDataToCar(String protocolData) {
        LogManager.i("sendCanDataToCar -------------->protocolData:" + protocolData);
        if (mWLPlatform == null) {
            return;
        }
        if (!TextUtils.isEmpty(protocolData)) {
            mWLPlatform.sendCanData(protocolData.getBytes());
        }
    }

    /**
     * 销毁平台服务管理类
     */
    @Keep
    public void onDestroy() {
        LogManager.i("onDestory -------------->");
        if (mWLPlatform != null) {
            stopWifiLink();
            stopWIFIAp();
            mWLPlatform.release();
            mWLPlatform = null;

        }
        isScreen = false;
        unRegisterReceiver();
        WLProtocolManager.getInstance().clearAllMethodListener();
        mPlatformCarState = WLPlatformState.STATUS_NONE;
        mContext = null;
    }

    /**
     * 设置适配三方投屏回调接口
     */
    @Keep
    public void registerAdapterListener(LinkAdapterListener listener) {
        LogManager.i("registerAdapterListener -------------->");
        mLinkAdapterListener = listener;
    }

    /**
     * 取消设置适配三方投屏回调接口
     */
    @Keep
    public void unRegisterAdapterListener() {
        LogManager.i("unRregisterAdapterListener -------------->");
        mLinkAdapterListener = null;
    }

    /**
     * 通知适配层互联成功状态
     */
    @Keep
    public void connectedAdapter() {
        // 添加调用栈追踪，确保能看到调用来源
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        String caller = stackTrace.length > 3 ? stackTrace[3].getClassName() + "." + stackTrace[3].getMethodName() : "unknown";
        LogManager.i("【FLOW_TRACE】connectedAdapter() called from: " + caller);

        LogManager.i("connectedAdapter -------------->");
        if (mLinkAdapterListener != null) {
            LogManager.i("connectedAdapter ------11111111111-------->");
            LogManager.i("【FLOW_TRACE】Passing parameters: width=" + huScreenWidth + ", height=" + huScreenHeight + ", dpi=" + densityDpi + ", vehicleType=" + vehicleType);
            mLinkAdapterListener.onLinkConnected(mContext, mNotification, huScreenWidth, huScreenHeight, densityDpi, vehicleType);
        } else {
            LogManager.w("【FLOW_TRACE】mLinkAdapterListener is null, cannot call onLinkConnected");
        }
    }

    /**
     * 通知适配层互联断开状态
     */
    @Keep
    public void disconnectedAdapter() {
        LogManager.i("disconnectedAdapter -------------->");
        if (mLinkAdapterListener != null) {
            isExternalCastScreen = false;
            mLinkAdapterListener.onLinkDisconnected();
        }
    }

    /**
     * 控制是否进入三方投屏方式
     */
    @Keep
    public void setExternalCastScreen(boolean isExternal) {
        LogManager.i("setExternalCastScreen -------------->");
        isExternalCastScreen = isExternal;
    }
    @Keep
    public void setScreenStatus(boolean mRecordScreen) {
        LogManager.i("setScreenStatus -------------->isScreen："+isScreen+",currentThread:"+Thread.currentThread().getName());
        isScreen = mRecordScreen;
        //当录屏终止 且处于后台时停止 无感和wifi互联
        if(!mRecordScreen && isBackground){
            if(mWLPlatform != null){
                stopDirectLink();
                stopWifiLink();
                stopWIFIAp();
            }
        }
    }
    public boolean isScreen() {
        LogManager.i("isScreen -------------->"+isScreen);
        return isScreen;
    }
    /**
     * 检测权限
     */
    @Keep
    public void activityResult(int requestCode, int resultCode, Intent data) {
        LogManager.i("activityResult -------------->");
        if (requestCode == WLScreen.SCREEN_CAPTURE_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            if (mLinkFrameListener == null) {
                LogManager.i("onLinkConnected --------start------>1111111");
                mWLPlatform.start();
            } else {
                LogManager.i("onLinkConnected --------start------>2222222");
                mWLPlatform.start(0, 1, 0, 48);
            }
        }
        if (mLinkAdapterListener != null) {
            mLinkAdapterListener.activityResult(requestCode, resultCode, data);
        }
    }

//    /**
//     * 是否输出log日志
//     */
//    @Keep
//    public void enableDebug(boolean isDebug) {
//        LogManager.i("enableDebug --------------> isDebug:" + isDebug);
//        if (mWLPlatform == null) {
//            return;
//        }
////        LogManager.i( "WLPlatform version:"+ WLPlatform.getVersion());
////        LogManager.i( "WLHardwareHub version:"+ WLHardwareHub.getVersion());
////        LogManager.i( "WLServer version:"+ WLServer.getVersion());
////        LogManager.i( "WLConnector version:"+ WLConnector.getVersion());
////        LogManager.i( "WLChannel version:"+ WLChannel.getVersion());
//        mWLPlatform.enableLogFile(isDebug);
//    }

    /**
     * 设置获取视频帧数据回调接口
     */
    @Keep
    public void registerFrameListener(LinkFrameListener listener) {
        LogManager.i("registerFrameListener -------------->");
        mLinkFrameListener = listener;
    }

    /**
     * 取消设置获取视频帧数据回调接口
     */
    @Keep
    public void unRegisterFrameListener() {
        LogManager.i("unRregisterFrameListener -------------->");
        mLinkFrameListener = null;
    }

    /**
     * 获取车机端link软件版本号
     */
    @Keep
    public String getCarLinkVersion() {
        if (mWLPlatform == null) {
            return "";
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return "";
        }
        return vehicleVersion;
    }

    /**
     * 获取车机端蓝牙地址
     */
    public String getHuBtMacAddress() {
        if (mWLPlatform == null) {
            return null;
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return null;
        }
        return btMacAddress;
    }

    @Override
    public void loadService(Context aContext) {
        LogManager.i("loadService -------------->");
    }

    @Override
    public void unloadService() {
        LogManager.i("unloadService -------------->");
        onDestroy();
    }

    /**
     * 开启监听三方app请求连接广播
     */
    private void registerReceiver() {
        LogManager.i("register PlatformReceiver -------------->");
        if (mPlatformReceiver == null) {
            mPlatformReceiver = new PlatformReceiver();
            IntentFilter intentFilter = new IntentFilter(WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                mContext.registerReceiver(mPlatformReceiver, intentFilter, RECEIVER_EXPORTED);
            } else {
                mContext.registerReceiver(mPlatformReceiver, intentFilter);
            }
        }
    }

    /**
     * 取消监听三方app请求连接广播
     */
    private void unRegisterReceiver() {
        LogManager.i("unRregisterReceiver -------------->");
        if (mPlatformReceiver != null) {
            mContext.unregisterReceiver(mPlatformReceiver);
            mPlatformReceiver = null;
        }
    }

    /**
     * 创建前台服务广播通知
     */
    private Notification createNotification() {
        PendingIntent pendingIntent;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), PendingIntent.FLAG_MUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), 0);
        }

        Notification.Builder builder = new Notification.Builder(mContext).setContentIntent(pendingIntent).setSmallIcon(R.drawable.ic_link_notifiy).setTicker("started").setWhen(System.currentTimeMillis())
                .setContentTitle(mContext.getResources().getString(R.string.welink_notification_service)).setContentText(mContext.getResources().getString(R.string.welink_notification_running));
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel notificationChannel = new NotificationChannel(mContext.getPackageName(), mContext.getResources().getString(R.string.welink_notification_name), NotificationManager.IMPORTANCE_MIN);
            NotificationManager manager = (NotificationManager) mContext.getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            builder.setChannelId(mContext.getPackageName());
        }
        return builder.build();
    }

    /**
     * 判断当前连接的是否是平台app
     */
    private boolean isPlatformApp(String connectStr) {
        ConnectorBean bean = mAssignKeys.get(connectStr);
        if (bean != null) {
            return mContext.getPackageName().equals(bean.getPackageName());
        } else {
            return false;
        }
    }

    /**
     * 方法名称：startWifiLink
     * 方法描述：开启wifi互联功能
     * 方法参数：
     * 返回类型：
     */

    public void startWifiLink() {
        LogManager.i("startWifiLink --------------> isBackground = " + isBackground+",isScreen："+isScreen);
        if (this.mWLPlatform == null || !isLinkWifi() || (isBackground && !isScreen)) {
            return;
        }
//            String udpType = ResourceHelp.getWIFIUDPType();
        String udpType = null;
        LogManager.i("startWifiLink -------222222------- udpType:" + udpType);
        this.mWLPlatform.startUDPBroadcast(udpType);
    }

    /**
     * 方法名称：stopWifiLink
     * 方法描述：关闭wifi互联功能
     * 方法参数：
     * 返回类型：
     */

    public void stopWifiLink() {
        LogManager.i("stopWifiLink -------------->");
        if (this.mWLPlatform == null || !isLinkWifi()) {
            return;
        }
        LogManager.i("stopWifiLink -------2222222------->");
        this.mWLPlatform.stopUDPBroadcast();
    }


    public boolean isBleInit() {
        return isBleInit;
    }

    /**
     * 方法名称：startDirectLink
     * 方法描述：开启wifi无感互联功能
     * 方法参数：
     * 返回类型：
     */

    @Keep
    public void startDirectLink() {
        LogManager.i("startDirectLink -------------->isLinkBleWifi=" + isLinkBleWifi() + ", isBackground = " + isBackground+",isScreen："+isScreen);
        LogManager.printStackTraceString("shecw1","startDirectLink -------------->isLinkBleWifi=" + isLinkBleWifi() + ", isBackground = " + isBackground+",isScreen："+isScreen);

        if (this.mWLPlatform == null || !isLinkBleWifi() ||  (isBackground && !isScreen)) {
            return;
        }
        List<ScanFilter> scanFilterList = new ArrayList<>();
        ScanFilter.Builder builder = new ScanFilter.Builder();
        builder.setDeviceName("SAIC_BLE");
        scanFilterList.add(builder.build());
        LogManager.i("startDirectLink -------2222222------->netWorkName="+netWorkName);
        this.mWLPlatform.destroyHardwareGroup();
        this.mWLPlatform.createHardwareGroup(netWorkName,WLPlatformConfig.WL_PLATFORM_BLELINK_CHANNEL, null);
    }

    /**
     * 方法名称：stopDirectLink
     * 方法描述：关闭wifi无感互联功能
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/09/18 16:58
     */

    @Keep
    public void stopDirectLink() {
        LogManager.i("stopDirectLink -------------->");
        if (this.mWLPlatform == null || !isLinkBleWifi()) {
            return;
        }

        LogManager.i("stopDirectLink -------2222222------->");
        this.mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_SCAN);
        this.mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_BLE_JOIN);
        this.mWLPlatform.destroyHardwareGroup();

    }
    /**
     * 开始扫描车机端设备，获取车机热点信息发起互联
     * 当前功能和无感互斥
     */
    public void startWIFIAp(){
        if (this.mWLPlatform == null || !isLinkWifiAP()) {
            return;
        }
        LogManager.i("startWIFIAp -------111111------->isBackground:"+isBackground+",isScreen:"+isScreen+",mSsid:"+mSsid);
       //异常断开 处于后台时，互联记录wifi，不开蓝牙启Ble扫描
        if(isBackground ){
            if(isScreen) {
                this.mWLPlatform.startAPConnect(mSsid, mPassword, null);
            }
            return;
        }
        LogManager.i("startWIFIAp -------2222222------->");
        this.mWLPlatform.startAPConnect(null,null,null);
    }
    /**
     * 停止扫描和互联车机热点
     */
    public void stopWIFIAp(){
        if (this.mWLPlatform == null || !isLinkWifiAP()) {
            return;
        }
        LogManager.i("stopWIFIAp -------2222222------->");
        this.mWLPlatform.stopAPConnect();
    }

    /**
     * ble 继续扫描和 添加WiFi dricet 创建成功监听
     */
    public void showGroupInfo() {
        if (this.mWLPlatform == null || !isLinkBleWifi()) {
            return;
        }
        mWLPlatform.showGroupInfo();
    }

    /**
     * ble 停止扫描和 移除WiFi dricet 创建成功监听
     */
    public void hideGroupInfo() {
        LogManager.i("hideGroupInfo -------------->");
        if (this.mWLPlatform == null ) {
            return;
        }
        LogManager.i("hideGroupInfo -------2222222------->");
        mWLPlatform.hideGroupInfo();
    }

    /**
     * 方法名称：registerDirectActivityListener
     * 方法描述：设置无感互联Activity生命周期监听
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/11/11 18:58
     */

    private void registerDirectActivityListener() {
        LogManager.i("registerDirectActivityListener -------------->");
        this.isFirstStarted = true;
        Application mApplication = mContext.getApplication();
        mApplication.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
                LogManager.i("Activity ---- onActivityStarted ------------>activity:" + activity.getClass().getSimpleName() + ",isFirstStarted:" + isFirstStarted);
                if (!"PermissionActivity".equals(activity.getClass().getSimpleName())) {
                    //如果是首次启动监听到前台，则拦截本次操作防止重复开启互联
                    if (isFirstStarted) {
                        return;
                    }
                    isBackground = false;
                    if (WLPlatformManager.this.mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED) {
                        return;
                    }
                    //link切换到前台，如果没有手车互联，则开启无感互联服务
                    WLPlatformManager.this.startDirectLink();
                }
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                LogManager.i("Activity ---- onActivityStopped ------------>activity:" + activity.getClass().getSimpleName() + "isFirstStarted:" + isFirstStarted);
                if (!"PermissionActivity".equals(activity.getClass().getSimpleName())) {
                    isFirstStarted = false;
                    isBackground = true;
                    if (WLPlatformManager.this.mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED || isScreen) {
                        return;
                    }
                    //link切换到后台，如果没有手车互联，则关闭销毁无感互联服务
                    WLPlatformManager.this.stopDirectLink();
                }
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
            }
        });
    }

    /**
     * 方法名称：registerWIFIActivityListener
     * 方法描述：设置wifi互联Activity生命周期监听
     * 方法参数：
     * 返回类型：
     */

    private void registerWIFIActivityListener() {
        LogManager.i("registerWIFIActivityListener -------------->");
        this.isFirstStarted = true;
        Application mApplication = mContext.getApplication();
        mApplication.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
                LogManager.i("Activity ---- onActivityStarted ------------>activity:" + activity.getClass().getName() + ",isFirstStarted:" + isFirstStarted);
                if (activity instanceof MainActivity) {
                    //如果是首次启动监听到前台，则拦截本次操作防止重复开启互联
                    if (isFirstStarted) {
                        return;
                    }
                    isBackground = false;
                    if (WLPlatformManager.this.mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED) {
                        return;
                    }
                    //link切换到前台，如果没有手车互联，则开启wifi互联服务
                    WLPlatformManager.this.startWifiLink();
                    WLPlatformManager.this.startWIFIAp();
                }
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                LogManager.i("Activity ---- onActivityStopped ------------>activity:" + activity.getClass().getName() + "isFirstStarted:" + isFirstStarted);
                if (activity instanceof MainActivity) {
                    isFirstStarted = false;
                    isBackground = true;
                    if (WLPlatformManager.this.mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED || isScreen) {
                        return;
                    }
                    //link切换到后台，如果没有手车互联，则关闭销毁wifi互联服务
                    WLPlatformManager.this.stopWifiLink();
                    WLPlatformManager.this.stopWIFIAp();
                }
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
            }
        });
    }

    public void setDriveSafetyVideoPlay(boolean driveSafetyOnline) {
        mWLPlatform.setDriveSafetyVideoPlay(driveSafetyOnline);
    }

    public void setDriveSafetySaveVideo(boolean driveSafetyOnline) {
        mWLPlatform.setDriveSafetySaveVideo(driveSafetyOnline);
    }

    public void setDriveSafetyOfflineSwitch(boolean driveSafetyOffline) {
        mWLPlatform.setDriveSafetyOfflineSwitch(driveSafetyOffline);
    }
    public void reqKeyFrame(int frame ,int time){
        mWLPlatform.reqKeyFrame(frame,time);
    }
    private class PlatformReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (mWLPlatform == null || intent == null) {
                return;
            }
            if (TextUtils.equals(WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION, intent.getAction())) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    String pkgName = bundle.getString(WLPlatformConfig.WL_PLATFORM_PACKAGE_NAME);
                    if (!TextUtils.isEmpty(pkgName)) {
                        String connectStr = mWLPlatform.assign(pkgName, WLPlatform.WL_CAP_MUSIC);
                        Intent connect = new Intent(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
                        connect.putExtra(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY, connectStr);
                        connect.setPackage(pkgName);
                        mContext.sendBroadcast(connect);
                    }
                }
            }
        }
    }

    public boolean isLinkBleWifi() {
        return false;
    }

    public boolean isLinkWifi() {
        return true;
    }

    /**
     * 是否支持 扫描车机设备 获取车机热点连接
     * 目前和无感功能互斥关系
     * @return
     */
    public boolean isLinkWifiAP() {
        return !isLinkBleWifi();
    }

    public void reqCurFrame(int action) {
        //云平返回ERROR，导致crash
            mWLPlatform.checkCurrentPlayStatus(action);
    }

    public int getHuScreenWidth() {
        return huScreenWidth;
    }

    public int getHuScreenHeight() {
        return huScreenHeight;
    }

    public int getDensityDpi() {
        return densityDpi;
    }

    /**
     * 获取车机信息，替代 WLConnectManager.getHuCarInfo()
     *
     * 数据一致性说明：
     * - 核心屏幕信息（width、height、dpi、vehicleType）与WLConnectManager完全一致
     * - userID和vehicleID在当前架构中设置为空字符串，不影响核心功能
     * - 数据来源：连接建立时从车机获取并缓存在WLPlatformManager中
     *
     * @return CarBean 包含车机屏幕信息，如果平台未初始化则返回null
     */
    public CarBean getHuCarInfo() {
        LogManager.i("【NEW_FLOW】getHuCarInfo() called");

        // 如果平台未初始化，返回null（与WLConnectManager行为一致）
        if (mWLPlatform == null) {
            LogManager.w("【NEW_FLOW】getHuCarInfo() - mWLPlatform is null, returning null");
            return null;
        }

        CarBean carBean = new CarBean();
        carBean.setHuScreenWidth(this.huScreenWidth);
        carBean.setHuScreenHeight(this.huScreenHeight);
        carBean.setDensityDpi(this.densityDpi);
        carBean.setVehicleType(this.vehicleType);

        // 注意：userID和vehicleID在当前架构中暂时设置为空字符串
        // 这不影响LayoutTypeUtils.isA2R()和BaseFloatingDialog的核心功能
        carBean.setUserID(this.userID);
        carBean.setVehicleID(this.vehicleID);

        LogManager.i("【NEW_FLOW】getHuCarInfo() - CarBean: " + carBean.toString());
        return carBean;
    }

    /**
     * 获取车型信息
     * @return 车型字符串
     */
    public String getVehicleType() {
        return vehicleType;
    }

    /**
     * 初始化连接管理，替代 WLConnectManager.init()
     *
     * 注意：在新架构中，WLPlatformManager已经包含完整的连接管理逻辑，
     * 不需要额外的ConnectReceiver，因为会造成循环调用。
     *
     * @param context 上下文
     * @param listener 连接监听器
     */
    @Keep
    public void initConnect(Context context, LinkConnectListener listener) {
        LogManager.i("【NEW_FLOW】initConnect() called - context: " + context.getClass().getSimpleName());

        // 设置连接监听器
        this.mLinkConnectListener = listener;

        // 注意：原WLConnectManager.init()中的ConnectReceiver在新架构中不需要，
        // 因为WLPlatformManager已经通过PlatformReceiver处理连接请求，
        // 再添加ConnectReceiver会造成自己给自己发广播的循环调用。

        LogManager.i("【NEW_FLOW】initConnect() completed - listener set, no additional receiver needed");
    }
}
