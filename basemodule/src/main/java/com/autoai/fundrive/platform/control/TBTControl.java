package com.autoai.fundrive.platform.control;

import com.autoai.welink.auto.WLTBTInfo;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.bean.TBTBean;

/**
 * 三投三方TBT功能接口，主要提供TBT实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class TBTControl {

    private final WLTBTInfo mWLTBTInfo;

    public TBTControl(WLTBTInfo mWLTBTInfo) {
        this.mWLTBTInfo = mWLTBTInfo;
    }

    /**
     * 启动TBT
     */
    public void startWLTBT() {
        LogManager.i("startWLTBT -------------->");
        if (mWLTBTInfo == null) {
            return;
        }

        mWLTBTInfo.startTBT();
    }

    /**
     * 关闭TBT
     */
    public void stopWLTBT() {
        LogManager.i("stopWLTBT -------------->");
        if (mWLTBTInfo == null) {
            return;
        }

        mWLTBTInfo.stopTBT();
    }


    /**
     * 更新导航路口信息
     */
    public void updateWLTBT(TBTBean bean) {
        LogManager.i("update WLTBT -------------->bean:" + bean.toString());
        if (mWLTBTInfo == null) {
            return;
        }

        mWLTBTInfo.updateTBT(bean.getCurrentRoadName(), bean.getRoadName(), bean.getRoadDistance(),
                bean.getRoadTurnIcon(), bean.getRemainDistance(), bean.getRemainTime());
    }
}
