package com.autoai.fundrive.platform;

import static android.content.Context.RECEIVER_EXPORTED;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.singleton.Singleton;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.bean.CarBean;
import com.autoai.fundrive.platform.bean.TBTBean;
import com.autoai.fundrive.platform.bean.WLCommandType;
import com.autoai.fundrive.platform.bean.WLConnectState;
import com.autoai.fundrive.platform.callback.LinkBTCallback;
import com.autoai.fundrive.platform.callback.LinkMICCallback;
import com.autoai.fundrive.platform.control.BTPhoneControl;
import com.autoai.fundrive.platform.control.MICControl;
import com.autoai.fundrive.platform.control.TBTControl;
import com.autoai.fundrive.platform.listener.LinkCommandListener;
import com.autoai.fundrive.platform.listener.LinkConnectListener;
import com.autoai.welink.auto.WLConnectListener;
import com.autoai.welink.auto.WLConnector;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 主要负责为三方应用提供功能实现接口。
 *
 * <AUTHOR>
 */
@Keep
public class WLConnectManager implements Singleton {

    public static final String NAME = WLConnectManager.class.getSimpleName();

    private Context mContext;
    private WLConnector mWLConnector;

    private MICControl mMICControl;
    private TBTControl mTBTControl;
    private BTPhoneControl mBTPhoneControl;

    private ConnectReceiver mConnectReceiver;
    private WLConnectState mWLConnectState = WLConnectState.STATUS_NONE;

    private final HashMap<String, ArrayList<LinkCommandListener>> mCommandListeners = new HashMap<>();

    private static final int WL_CONNECTOR_HANDLER_WHAR_SEND = 100;

    private LinkConnectListener connectListener;
    private WLConnectListener wlConnectListener;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (WL_CONNECTOR_HANDLER_WHAR_SEND == msg.what) {
                String connectKey = (String) msg.obj;
                if (wlConnectListener == null) {
                    wlConnectListener = new WLConnectListener() {

                        @Override
                        public boolean onConnected(WLConnector wlConnector) {
                            LogManager.w("【OLD_FLOW】WLConnectManager.onConnected() called - this should be skipped in new flow!");
                            LogManager.i("WLConnectListener -------- onConnected ------------>");

                            mWLConnectState = WLConnectState.STATUS_WELINK_CONNECTED;
                            mWLConnector = wlConnector;

                            mMICControl = new MICControl(mWLConnector.getConfiguration().getMicrophoneCapability());
                            mTBTControl = new TBTControl(mWLConnector.getConfiguration().getTBTInfoCapability());
                            mBTPhoneControl = new BTPhoneControl(mWLConnector.getConfiguration().getBluetoothPhoneCapability());

                            if (connectListener != null) {
                                connectListener.onConnected(mWLConnector.getConfiguration().getDisplayCapability());
                            }
                            LogManager.w("【OLD_FLOW】Calling connectedAdapter() from WLConnectManager - should use direct call instead!");
                            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                            platformManager.connectedAdapter();
                            return true;
                        }

                        @Override
                        public void onDisconnected() {
                            LogManager.i("WLConnectListener -------- onDisconnected ------------>");

                            mWLConnectState = WLConnectState.STATUS_WELINK_DISCONNECTED;
                            mWLConnector.release();
                            mWLConnector = null;

//                            mMusicControl = null;
//                            mTTSControl = null;
                            mMICControl = null;
                            mTBTControl = null;
                            mBTPhoneControl = null;

                            if (connectListener != null) {
                                connectListener.onDisconnected();
                            }
                            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                           if(platformManager != null) {
                               platformManager.disconnectedAdapter();
                           }
//                            System.exit(0);
                        }

                        @Override
                        public void onError(int errorCode) {
                            LogManager.i("WLConnectListener -------- onError ------------>errorCode:" + errorCode);

                            mWLConnectState = WLConnectState.STATUS_WELINK_DISCONNECTED;
                            if (mWLConnector != null) {
                                mWLConnector.release();
                                mWLConnector = null;
                            }
                            mMICControl = null;
                            mTBTControl = null;
                            mBTPhoneControl = null;

                            if (connectListener != null) {
                                connectListener.onConnectError(errorCode);
                            }
                            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                            platformManager.disconnectedAdapter();
                        }

                        @Override
                        public void onCommand(String command) {
                            LogManager.i("WLConnectListener -------- onCommand ------------>command:" + command);

                            if (!mCommandListeners.isEmpty()) {
                                try {
                                    JSONObject jsonObject = new JSONObject(command);
                                    if (jsonObject.has(WLPlatformConfig.WL_PLATFORM_COMMAND_TYPE)) {
                                        String type = jsonObject.getString(WLPlatformConfig.WL_PLATFORM_COMMAND_TYPE);
                                        ArrayList<LinkCommandListener> list = mCommandListeners.get(type);
                                        if (list != null && !list.isEmpty()) {
                                            for (LinkCommandListener mListener : list) {
                                                mListener.onReceiveCommand(command);
                                            }
                                        }
                                    }
                                } catch (JSONException e) {
                                    LogManager.e("WLConnectListener exception:" + e.getMessage());
                                }
                            }
                        }
                    };
                }
                WLConnector.connect(mContext, connectKey, wlConnectListener);
            }
        }
    };

    public WLConnectManager() {
    }

    /**
     * 初始化服务
     *
     * @param mContext 上下文
     * @param listener 互联投屏回调处理对象
     */
    @Keep
    public void init(Context mContext, LinkConnectListener listener) {
        LogManager.i("init -------------->");
        this.mContext = mContext;
        this.connectListener = listener;
        //开启监听平台的连接广播
        LogManager.i("register ConnectReceiver -------------->");
        if (mConnectReceiver == null) {
            mConnectReceiver = new ConnectReceiver();
            IntentFilter intentFilter = new IntentFilter(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
            if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.TIRAMISU){
                mContext.registerReceiver(mConnectReceiver, intentFilter,RECEIVER_EXPORTED);
            }else {
                mContext.registerReceiver(mConnectReceiver, intentFilter);
            }
        }
    }

    /**
     * 三方申请连接服务
     */
    @Keep
    public void requestConnect() {
        LogManager.i("requestConnect -------------->");
        Intent intent = new Intent(WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION);
        intent.putExtra(WLPlatformConfig.WL_PLATFORM_PACKAGE_NAME, mContext.getPackageName());
        mContext.sendBroadcast(intent);
    }

    /**
     * 三方开启连接服务
     *
     * @param connectKey - 三方应用互联能力字符串
     */
    @Keep
    public void startConnect(String connectKey) {
        LogManager.w("【OLD_FLOW】WLConnectManager.startConnect() called - this should be skipped in new flow!");
        LogManager.i("startConnect ---------->connectKey:" + connectKey);

        if (TextUtils.isEmpty(connectKey)) {
            return;
        }
        mWLConnectState = WLConnectState.STATUS_WELINK_CONNECTING;
        mHandler.removeMessages(WL_CONNECTOR_HANDLER_WHAR_SEND);

        Message message = mHandler.obtainMessage();
        message.what = WL_CONNECTOR_HANDLER_WHAR_SEND;
        message.obj = connectKey;
        mHandler.sendMessage(message);
    }

    /**
     * 三方断开连接服务
     */
    @Keep
    public void startDisConnect() {
        LogManager.i("startDisConnect ---------->");
        if (mWLConnector == null) {
            return;
        }
        if (mWLConnectState == WLConnectState.STATUS_WELINK_CONNECTED) {
            mWLConnectState = WLConnectState.STATUS_WELINK_DISCONNECTING;
            mWLConnector.disconnect();
        }
    }

    /**
     * 获取互联车机信息
     */
    @Keep
    public CarBean getHuCarInfo() {
        LogManager.i("getHuCarInfo ------->");

        if (mWLConnector == null) {
            return null;
        }

        CarBean mCarBean = new CarBean();

        mCarBean.setHuScreenWidth(mWLConnector.getConfiguration().getHUScreenWidth());
        mCarBean.setHuScreenHeight(mWLConnector.getConfiguration().getHUScreenHeight());
        mCarBean.setDensityDpi(mWLConnector.getConfiguration().getHUDensityDpi());
        mCarBean.setUserID(mWLConnector.getConfiguration().getUserID());
        mCarBean.setVehicleID(mWLConnector.getConfiguration().getVehicleID());
        mCarBean.setVehicleType(mWLConnector.getConfiguration().getVehicleType());

        LogManager.i("getHuCarInfo ------->CarBean:" + mCarBean);
        return mCarBean;
    }


    /**
     * 启动TBT
     */
    @Keep
    public void startWLTBT() {
        LogManager.i("startWLTBT -------------->");

        if (mTBTControl == null) {
            return;
        }
        mTBTControl.startWLTBT();
    }

    /**
     * 关闭TBT
     */
    @Keep
    public void stopWLTBT() {
        LogManager.i("stopWLTBT -------------->");

        if (mTBTControl == null) {
            return;
        }
        mTBTControl.stopWLTBT();
    }

    /**
     * 更新导航路口信息
     *
     * @param mTBTBean - 导航路口图信息
     */
    @Keep
    public void updataWLTBT(TBTBean mTBTBean) {
        LogManager.i("updataWLTBT -------------->");

        if (mTBTControl == null) {
            return;
        }
        mTBTControl.updateWLTBT(mTBTBean);
    }

    /**
     * 开始使用车机mic资源
     *
     * @param callback - MIC接收回调对象
     */
    @Keep
    public void startMicrophone(LinkMICCallback callback) {
        LogManager.i("startMicrophone -------------->");

        if (mMICControl == null) {
            return;
        }
        mMICControl.startMicrophone(callback);
    }

    /**
     * 停止使用车机mic资源
     */
    @Keep
    public void stopMicrophone() {
        LogManager.i("stopMicrophone -------------->");

        if (mMICControl == null) {
            return;
        }
        mMICControl.stopMicrophone();
    }

    /**
     * 判断车机是否支持录音
     */
    @Keep
    public boolean isSupportMicrophone() {
        LogManager.i("stopMicrophone -------------->");

        if (mMICControl == null) {
            return false;
        }
        return mMICControl.isSupportMicrophone();
    }

    /**
     * 拨打车机蓝牙电话
     */
    @Keep
    public void dialBTPhone(String number, LinkBTCallback callback) {
        LogManager.i("dialBTPhone ------->");

        if (mBTPhoneControl == null) {
            return;
        }
        mBTPhoneControl.dialBTPhone(number, callback);
    }

    /**
     * 挂断车机蓝牙电话
     */
    @Keep
    public void hangUpBTPhone() {
        LogManager.i("hangUpBTPhone ------->");

        if (mBTPhoneControl == null) {
            return;
        }
        mBTPhoneControl.hangUpBTPhone();
    }

    /**
     * 判断车机是否支持拨打电话
     */
    @Keep
    public boolean isSupportBTPhone() {
        LogManager.i("isSupportBTPhone ------->");

        if (mBTPhoneControl == null) {
            return false;
        }
        return mBTPhoneControl.isSupportBTPhone();
    }

    /**
     * 添加指定类型的命令回调监听
     *
     * @param mWLCommandType - 车机命令类型
     * @param mListener      - 接收车机命令回调处理对象
     */
    @Keep
    public void registerCommand(WLCommandType mWLCommandType, LinkCommandListener mListener) {
        String commandType = mWLCommandType.getType();
        LogManager.i("registerCommand -------------->commandType:" + commandType);

        if (mCommandListeners.containsKey(commandType)) {
            ArrayList<LinkCommandListener> list = mCommandListeners.get(commandType);
            if (list != null && !list.contains(mListener)) {
                list.add(mListener);
            }
        } else {
            ArrayList<LinkCommandListener> list = new ArrayList<>();
            list.add(mListener);
            mCommandListeners.put(commandType, list);
        }
    }

    /**
     * 移除指定类型的命令回调监听
     *
     * @param mWLCommandType - 车机命令类型
     * @param mListener      - 接收车机命令回调处理对象
     */
    @Keep
    public void unRegisterCommand(WLCommandType mWLCommandType, LinkCommandListener mListener) {
        String commandType = mWLCommandType.getType();
        LogManager.i("unRegisterCommand -------------->commandType:" + commandType);

        if (mCommandListeners.containsKey(commandType)) {
            ArrayList<LinkCommandListener> list = mCommandListeners.get(commandType);
            if (list != null) {
                list.remove(mListener);
            }
        }
    }

    /**
     * 销毁三方服务管理类
     */
    @Keep
    public void onDestroy() {
        LogManager.i("onDestory -------------->");

        if (mWLConnector != null) {
            mWLConnector.release();
        }
        unregisterReceiver();
        mContext = null;
        mMICControl = null;
        mTBTControl = null;
        mBTPhoneControl = null;
        mCommandListeners.clear();
        mWLConnectState = WLConnectState.STATUS_NONE;
    }

    @Override
    public void loadService(Context aContext) {
        LogManager.i("loadService -------------->");
    }

    @Override
    public void unloadService() {
        LogManager.i("unloadService -------------->");
        onDestroy();
    }

    /**
     * 取消监听平台的连接广播
     */
    private void unregisterReceiver() {
        LogManager.i("unregister ConnectReceiver-------------->");
        if (mConnectReceiver != null) {
            mContext.unregisterReceiver(mConnectReceiver);
            mConnectReceiver = null;
        }
    }

    private class ConnectReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                return;
            }
            if (intent.getAction().equals(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION)) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    String connectKey = bundle.getString(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY);
                    if (!TextUtils.isEmpty(connectKey)) {
                        startConnect(connectKey);
                    }
                }
            }
        }
    }

}
