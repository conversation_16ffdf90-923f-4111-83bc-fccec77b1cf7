package com.autoai.fundrive.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 交互协议处理车机端candata数据，此协议主要是用来实现惯导等功能。
 *
 * <AUTHOR>
 */
public class CanDataBaseBean extends BaseProtocolBean{

    /**
     * 平均速度
     */
    private double averageSpeed;

    private double latitude;
    private double longitude;
    private int latitudeDirection;
    private int longitudeDirection;
    private int altitude;

    private int azimuth;
    private int elevation;
    private int snr;

    /**
     * X 轴加速度
     */
    private int xAcc;
    /**
     * Y 轴加速度
     */
    private int yAcc;
    /**
     * Z 轴加速度
     */
    private int zAcc;

    /**
     * 1:手动挡 2：自动挡
     */
    private int gearType;
    private int gearPosition;

    /**
     * 1：晚上 0：白天
     */
    private int dayNightMode;
    private int yawAngularVelocity;

    private int espWegVl;
    private int espWegVr;
    private int espWegHl;
    private int espWegHr;

    /**
     * 点火状态
     * 0：off 1：on
     */
    private int ignitionStatusKls;
    /**
     * 点火状态15
     */
    private int ignitionStatusKl15;

    /**
     * 0/正 1/负
     */
    private int swaVz;
    /**
     * 角度值
     */
    private int swaValue;

    private String vin;

    private int brand;
    private int vehicleSegment;
    private int vehicleGeneration;
    private int vehicleDerivat;
    private int vehicleDerivatExtention;
    private int countryVariant;

    private int odT;
    private int icarT;

    /**
     * 备用字段
     */
    private int state;
    /**
     * 里程数
     */
    private int currentRangeValue;
    /**
     * 单位 ：0代表km，非0代表m
     */
    private int currentRangeUnit;

    /**
     * 倒车灯信号
     */
    private int reversingLightSignal;
    /**
     * 警报指定信号
     */
    private int wariningSpecifiSignal;


    @Override
    public void parse(JSONObject extData) throws JSONException {
        if(extData.has("vehicleSpeed")){
            JSONObject vehicleSpeed = extData.getJSONObject("vehicleSpeed");

            this.averageSpeed = vehicleSpeed.getDouble("averageSpeed");
        }

        if(extData.has("gnssInfo")){
            JSONObject gnssInfo = extData.getJSONObject("gnssInfo");

            this.latitude = gnssInfo.getDouble("latitude");
            this.longitude = gnssInfo.getDouble("longitude");
            this.latitudeDirection = gnssInfo.getInt("latitudeDirection");
            this.longitudeDirection = gnssInfo.getInt("longitudeDirection");
            this.altitude = gnssInfo.getInt("altitude");
        }

        if(extData.has("gnssSateliteInfo")){
            JSONObject gnssSateliteInfo = extData.getJSONObject("gnssSateliteInfo");
            this.azimuth = gnssSateliteInfo.getInt("azimuth");
            this.elevation = gnssSateliteInfo.getInt("elevation");
            this.snr = gnssSateliteInfo.getInt("snr");
        }

        if(extData.has("accelerateSpeed")){
            JSONObject accelerateSpeed = extData.getJSONObject("accelerateSpeed");
            this.xAcc = accelerateSpeed.getInt("x_acc");
            this.yAcc = accelerateSpeed.getInt("y_acc");
            this.zAcc = accelerateSpeed.getInt("z_acc");
        }

        if(extData.has("gearPosition")){
            JSONObject gear_type = extData.getJSONObject("gearPosition");
            this.gearType = gear_type.getInt("gear_type");
            this.gearPosition = gear_type.getInt("gear_position");
        }

        this.dayNightMode = extData.getInt("day_night_Mode");
        this.yawAngularVelocity = extData.getInt("yawAngularVelocity");

        if(extData.has("wheelSpeed")){
            JSONObject wheelSpeed = extData.getJSONObject("wheelSpeed");
            this.espWegVl = wheelSpeed.getInt("esp_weg_vl");
            this.espWegVr = wheelSpeed.getInt("esp_weg_vr");
            this.espWegHl = wheelSpeed.getInt("esp_weg_hl");
            this.espWegHr = wheelSpeed.getInt("esp_weg_hr");
        }

        this.ignitionStatusKls = extData.getInt("ignitionStatus_kls");
        this.ignitionStatusKl15 = extData.getInt("ignitionStatus_kl15");

        if(extData.has("steeringWheelAngle")){
            JSONObject steeringWheelAngle = extData.getJSONObject("steeringWheelAngle");
            this.swaVz = steeringWheelAngle.getInt("swa_vz");
            this.swaValue = steeringWheelAngle.getInt("swa_value");
        }

        this.vin = extData.getString("vin");

        if(extData.has("vehicleType")){
            JSONObject vehicleType = extData.getJSONObject("vehicleType");
            this.brand = vehicleType.getInt("brand");
            this.vehicleSegment = vehicleType.getInt("vehicle_segment");
            this.vehicleGeneration = vehicleType.getInt("vehicle_generation");
            this.vehicleDerivat = vehicleType.getInt("vehicle_derivat");
            this.vehicleDerivatExtention = vehicleType.getInt("vehicle_derivat_extention");
            this.countryVariant = vehicleType.getInt("country_variant");
        }

        if(extData.has("temperature")){
            JSONObject temperature = extData.getJSONObject("temperature");
            this.odT = temperature.getInt("od_t");
            this.icarT = temperature.getInt("icar_t");
        }

        if(extData.has("carBCCurrentRage")){
            JSONObject carBCCurrentRage = extData.getJSONObject("carBCCurrentRage");
            this.state = carBCCurrentRage.getInt("state");
            this.currentRangeValue = carBCCurrentRage.getInt("currentRangeValue");
            this.currentRangeUnit = carBCCurrentRage.getInt("currentRangeUnit");
        }

        this.reversingLightSignal = extData.getInt("reversingLightSignal");
        this.wariningSpecifiSignal = extData.getInt("wariningSpecifiSignal");

    }

    public double getAverageSpeed() {
        return averageSpeed;
    }

    public double getLatitude() {
        return latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public int getLatitudeDirection() {
        return latitudeDirection;
    }

    public int getLongitudeDirection() {
        return longitudeDirection;
    }

    public int getAltitude() {
        return altitude;
    }

    public int getAzimuth() {
        return azimuth;
    }

    public int getElevation() {
        return elevation;
    }

    public int getSnr() {
        return snr;
    }

    public int getxAcc() {
        return xAcc;
    }

    public int getyAcc() {
        return yAcc;
    }

    public int getzAcc() {
        return zAcc;
    }

    public int getGearType() {
        return gearType;
    }

    public int getGearPosition() {
        return gearPosition;
    }

    public int getDayNightMode() {
        return dayNightMode;
    }

    public int getYawAngularVelocity() {
        return yawAngularVelocity;
    }

    public int getEspWegVl() {
        return espWegVl;
    }

    public int getEspWegVr() {
        return espWegVr;
    }

    public int getEspWegHl() {
        return espWegHl;
    }

    public int getEspWegHr() {
        return espWegHr;
    }

    public int getIgnitionStatusKls() {
        return ignitionStatusKls;
    }

    public int getIgnitionStatusKl15() {
        return ignitionStatusKl15;
    }

    public int getSwaVz() {
        return swaVz;
    }

    public int getSwaValue() {
        return swaValue;
    }

    public String getVin() {
        return vin;
    }

    public int getBrand() {
        return brand;
    }

    public int getVehicleSegment() {
        return vehicleSegment;
    }

    public int getVehicleGeneration() {
        return vehicleGeneration;
    }

    public int getVehicleDerivat() {
        return vehicleDerivat;
    }

    public int getVehicleDerivatExtention() {
        return vehicleDerivatExtention;
    }

    public int getCountryVariant() {
        return countryVariant;
    }

    public int getOdT() {
        return odT;
    }

    public int getIcarT() {
        return icarT;
    }

    public int getState() {
        return state;
    }

    public int getCurrentRangeValue() {
        return currentRangeValue;
    }

    public int getCurrentRangeUnit() {
        return currentRangeUnit;
    }

    public int getReversingLightSignal() {
        return reversingLightSignal;
    }

    public int getWariningSpecifiSignal() {
        return wariningSpecifiSignal;
    }

    @Override
    public String toString() {
        return "CanDataBaseBean{" +
                "averageSpeed=" + averageSpeed +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", latitudeDirection=" + latitudeDirection +
                ", longitudeDirection=" + longitudeDirection +
                ", altitude=" + altitude +
                ", azimuth=" + azimuth +
                ", elevation=" + elevation +
                ", snr=" + snr +
                ", xAcc=" + xAcc +
                ", yAcc=" + yAcc +
                ", zAcc=" + zAcc +
                ", gearType=" + gearType +
                ", gearPosition=" + gearPosition +
                ", dayNightMode=" + dayNightMode +
                ", yawAngularVelocity=" + yawAngularVelocity +
                ", espWegVl=" + espWegVl +
                ", espWegVr=" + espWegVr +
                ", espWegHl=" + espWegHl +
                ", espWegHr=" + espWegHr +
                ", ignitionStatusKls=" + ignitionStatusKls +
                ", ignitionStatusKl15=" + ignitionStatusKl15 +
                ", swaVz=" + swaVz +
                ", swaValue=" + swaValue +
                ", vin='" + vin + '\'' +
                ", brand=" + brand +
                ", vehicleSegment=" + vehicleSegment +
                ", vehicleGeneration=" + vehicleGeneration +
                ", vehicleDerivat=" + vehicleDerivat +
                ", vehicleDerivatExtention=" + vehicleDerivatExtention +
                ", countryVariant=" + countryVariant +
                ", odT=" + odT +
                ", icarT=" + icarT +
                ", state=" + state +
                ", currentRangeValue=" + currentRangeValue +
                ", currentRangeUnit=" + currentRangeUnit +
                ", reversingLightSignal=" + reversingLightSignal +
                ", wariningSpecifiSignal=" + wariningSpecifiSignal +
                '}';
    }
}
