package com.autoai.fundrive.platform;

import androidx.annotation.Keep;

/**
 * 常量类，主要定义在三投模块中需要的常量。
 *
 * <AUTHOR>
 */
@Keep
public class WLPlatformConfig {

    private WLPlatformConfig() {
    }

    @Keep
    public static final String WL_PLATFORM_COMMAND_TYPE = "Type";
    @Keep
    public static final String WL_PLATFORM_COMMAND_MUSIC = "Music";
    @Keep
    public static final String WL_PLATFORM_COMMAND_NAVI = "Navi";
    @Keep
    public static final String WL_PLATFORM_COMMAND_ALL = "All";
    @Keep
    public static final String WL_PLATFORM_CONNECT_KEY = "welink-connection";
    @Keep
    public static final String WL_PLATFORM_PACKAGE_NAME = "package-name";
    @Keep
    public static final String WL_PLATFORM_CONNECT_ACTION = "com.autoai.welink.CONNECTION";
    @Keep
    public static final String WL_PLATFORM_REQUEST_ACTION = "com.autoai.welink.REQUEST_CONNECTION";
    @Keep
    public static final String WL_PLATFORM_BUNDLE_PACKAGE_NAME = "packageName";
    @Keep
    public static final String WL_PLATFORM_BUNDLE_CONNECT_KEY = "connectKey";

    public static final int WL_APP_ACTION_START_MIC = 3;
    public static final int WL_APP_ACTION_STOP_MIC = 4;

    public static boolean isConnectcar = false;
    public static final int WL_PLATFORM_OBSERVER_CONNECT_STAET = 100;

    @Keep
    public static int WL_PLATFORM_BLELINK_CHANNEL = 0;
    static final boolean IS_VIDEO_TEST_MODEL = false;
}
