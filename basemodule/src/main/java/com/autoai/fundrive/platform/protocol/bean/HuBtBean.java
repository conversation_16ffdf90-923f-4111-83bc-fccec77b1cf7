package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端蓝牙状态向手机端launcher发送的协议。
 */

public class HuBtBean extends BaseProtocolBean {

    /**
     * true：车机端蓝牙已开启
     * false：车机端蓝牙未开启
     */
    private boolean btState;
    /**
     * 车机端蓝牙mac地址
     */
    private String btMacName;

    public boolean isBtState() {
        return btState;
    }

    public String getBtMacName() {
        return btMacName;
    }

    @Override
    public String toString() {
        return "HuBtBean{" +
                "btState=" + btState +
                ", btMacName='" + btMacName + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.btState = extData.getBoolean(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_BTSTATE);
        this.btMacName = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_BTMACNAME);
    }


}
