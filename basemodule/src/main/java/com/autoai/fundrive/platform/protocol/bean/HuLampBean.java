package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端发送氛围灯类型向手机端 launcher发的协议。
 */
public class HuLampBean extends BaseProtocolBean {

    /**
     * 车型代码
     */
    private int vehicleInfo;
    /**
     * 车机型号
     */
    private String partNo;
    /**
     * 氛围灯色值类型
     * 1：使用RGB色值
     * 2：使用select色值
     */
    private int colorType;
    /**
     * r色值
     */
    private int colorR;
    /**
     * g色值
     */
    private int colorG;
    /**
     * b色值
     */
    private int colorB;
    /**
     * 自定义色值
     */
    private int colorSelectValue;

    public int getVehicleInfo() {
        return vehicleInfo;
    }

    public String getPartNo() {
        return partNo;
    }

    public int getColorType() {
        return colorType;
    }

    public int getColorR() {
        return colorR;
    }

    public int getColorG() {
        return colorG;
    }

    public int getColorB() {
        return colorB;
    }

    public int getColorSelectValue() {
        return colorSelectValue;
    }

    @Override
    public String toString() {
        return "HuLampBean{" +
                "vehicleInfo=" + vehicleInfo +
                ", partNo='" + partNo + '\'' +
                ", colorType=" + colorType +
                ", colorR=" + colorR +
                ", colorG=" + colorG +
                ", colorB=" + colorB +
                ", colorSelectValue=" + colorSelectValue +
                ", methodName=" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.vehicleInfo = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_VEHICLE);
        this.partNo = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_PARTNO);
        this.colorType = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORTYPE);

        if (extData.has(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORRGB)) {
            JSONObject rgb = extData.getJSONObject(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORRGB);
            this.colorR = rgb.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORR);
            this.colorG = rgb.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORG);
            this.colorB = rgb.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORB);
        }

        this.colorSelectValue = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_COLORSELECT);
    }


}
