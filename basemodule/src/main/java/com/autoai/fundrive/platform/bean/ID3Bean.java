package com.autoai.fundrive.platform.bean;

import android.graphics.Bitmap;

/**
 * Welink 三投三方ID3信息实体bean，主要封装ID3需要的参数信息。
 *
 * <AUTHOR>
 */
public class ID3Bean {
    /**
     * 音乐源
     */
    private String source;
    /**
     * 演唱者(创建者)
     */
    private String artist;
    /**
     * 歌曲名(电台名)
     */
    private String title;
    /**
     * 专辑名
     */
    private String album;
    /**
     * 歌词，LRC格式，可以为null
     */
    private String lyric;
    /**
     * 歌词格式类型 1:LRC, 2:QRC ,0:歌词为null
     */
    private int lyricType;
    /**
     * 歌曲时长 单位:秒
     */
    private int duration;
    /**
     * 歌曲封面，可以为null
     */
    private Bitmap cover;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getArtist() {
        return artist;
    }

    public void setArtist(String artist) {
        this.artist = artist;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAlbum() {
        return album;
    }

    public void setAlbum(String album) {
        this.album = album;
    }

    public String getLyric() {
        return lyric;
    }

    public void setLyric(String lyric) {
        this.lyric = lyric;
    }

    public int getLyricType() {
        return lyricType;
    }

    public void setLyricType(int lyricType) {
        this.lyricType = lyricType;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public Bitmap getCover() {
        return cover;
    }

    public void setCover(Bitmap cover) {
        this.cover = cover;
    }

    @Override
    public String toString() {
        return "ID3Bean{" +
                "source='" + source + '\'' +
                ", artist='" + artist + '\'' +
                ", title='" + title + '\'' +
                ", album='" + album + '\'' +
                ", lyric='" + lyric + '\'' +
                ", lyricType=" + lyricType +
                ", duration=" + duration +
                ", cover=" + cover +
                '}';
    }
}
