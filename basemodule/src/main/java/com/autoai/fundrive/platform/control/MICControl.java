package com.autoai.fundrive.platform.control;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.welink.auto.WLMicrophone;

/**
 * 三投三方MIC功能接口，主要提供MIC实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class MICControl {

    private final WLMicrophone mWLMicrophone;

    public MICControl(WLMicrophone mWLMicrophone) {
        this.mWLMicrophone = mWLMicrophone;
    }

    /**
     * 方法名称：startMicrophone
     * 方法描述：开始使用车机mic资源
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:18
     */


    public void startMicrophone(WLMicrophone.Callback callback){
        LogManager.i("startMicrophone ------->");
        if(mWLMicrophone == null){
            return;
        }
        mWLMicrophone.request(callback);
        LogManager.i("startMicrophone ----22222222--->");
    }


    /**
     * 方法名称：stopMicrophone
     * 方法描述：停止使用车机mic资源
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2019/11/22 11:18
     */


    public void stopMicrophone(){
        LogManager.i("stopMicrophone ------->");
        if(mWLMicrophone == null){
            return;
        }
        mWLMicrophone.stop();
        LogManager.i("stopMicrophone ----222222--->");
    }

    /**
     * 方法名称：isSupportMicrophone
     * 方法描述：判断车机是否支持录音
     * 方法参数：
     * 返回类型：
     * 创建人：wangzc
     * 创建时间：2020/5/9 13:24
    */

    public boolean isSupportMicrophone(){
        LogManager.i("stopMicrophone ------->");
        return mWLMicrophone != null;
    }
}
