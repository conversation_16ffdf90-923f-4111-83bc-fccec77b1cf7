package com.autoai.fundrive.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 车机端暂停或切换画面 补帧后，请求车机立即发送关键帧
 * time 时间 单位毫秒  frame 关键帧请求次数   time时间内请求frame个关键帧
 */
public class HuReqKeyFrame extends BaseProtocolBean{

    private int time;

    private int frame;


    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "HuReqKeyFrame{" +
                "time=" + time +
                ", frame=" + frame +
                '}';
    }

    public int getFrame() {
        return frame;
    }

    public void setFrame(int frame) {
        this.frame = frame;
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.time = extData.getInt("time");
        this.frame = extData.getInt("frame");
    }


}
