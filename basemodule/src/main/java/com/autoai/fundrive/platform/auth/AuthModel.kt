package com.autoai.fundrive.platform.auth

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject

object AuthModel {
    private const val TAG = "AuthModel"

    private val _flow = MutableSharedFlow<String>()
    val vehicleType: SharedFlow<String> = _flow

    fun updateVehicleType(vin: String?) {
        if(vin == null) return
        CoroutineScope(Dispatchers.Main).launch {
            Log.i(TAG, "#${Thread.currentThread().name}: updateVehicleType: vin = $vin")
            _flow.emit(vin)
        }
    }


    /**
     * 鉴权结果json
     * {
     *   "moduleName": "WeLink",
     *   "version": 0,
     *   "platform": "android",
     *   "command":
     *             {
     *              "method": "notifyAuthorityResult",
     *              "extData": {
     *                          "key": "result",
     *                          "value": 0
     *                         }
     *             }
     *  }
     */
    // {"moduleName":"WeLink","version":3,"platform":"android","command":{"method":"notifyAuthorityResult","extData":{"key":"onAuthResult","msg":"{\"code\":200,\"data\":{\"deviceStatus\":1,\"id\":398,\"validTime\":\"2124-09-01 14:19:50\"},\"message\":\"操作成功\"}"}}}
    fun assembleMessage(result: String): String {
        Log.i(TAG, "---- assemble authority result ------------>")
        val jsonObject = JSONObject()
        try {
            jsonObject.put("moduleName", "WeLink")
            jsonObject.put("version", 3)
            jsonObject.put("platform", "android")

            val extData = JSONObject()
            extData.put("key", "onAuthResult")
            extData.put("msg", result)

            val command = JSONObject()
            command.put("method", "notifyAuthorityResult")
            command.put("extData", extData)

            jsonObject.put("command", command)
        } catch (e: JSONException) {
            Log.e(TAG, "exception: ${e.message}")
        }
        return jsonObject.toString()
    }
}