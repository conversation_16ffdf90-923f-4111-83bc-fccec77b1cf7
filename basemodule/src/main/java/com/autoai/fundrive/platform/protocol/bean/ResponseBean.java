package com.autoai.fundrive.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是用来传输车机端语音相应相关指令消息通知给手机。
 */
public class ResponseBean extends BaseProtocolBean {
    /**
     * 类型
     * 1：指令响应成功
     * 2：指令响应异常
     */
    private int responseCallback;
    /**
     * 类型
     * 1：4.2.1.音量调节控制
     * 2：4.2.2.音量静音控制
     * 3：4.3.1.空调开关控制
     * 4：4.3.2空调温度控制
     * 5：4.3.3空调风量控制
     */
    private int requestType;

    public int getResponseCallback() {
        return responseCallback;
    }

    public int getRequestType() {
        return requestType;
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.responseCallback = extData.getInt("responseCallback");
        this.requestType = extData.getInt("requestType");
    }


}
