package com.autoai.fundrive.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
/**
 * 交互协议处理车机端candata数据，此协议主要是用来通知手机端车机端当前的故障信息。
 *
 * <AUTHOR>
 */
public class CanDataFZGBean extends BaseProtocolBean{

    /**
     * 是否有更新
     * 0：初始化数据 1：增加数据 2：删除数据
     */
    private int isDataUpdate;
    /**
     * 警告条数
     */
    private int warningCount;
    /**
     * 警告数据数组
     */
    private int[] warnings;

    public int getIsDataUpdate() {
        return isDataUpdate;
    }

    public int getWarningCount() {
        return warningCount;
    }

    public int[] getWarnings() {
        return warnings;
    }

    @Override
    public String toString() {
        return "CanDataFZGBean{" +
                "isDataUpdate=" + isDataUpdate +
                ", warningCount=" + warningCount +
                ", warnings=" + Arrays.toString(warnings) +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.isDataUpdate = extData.getInt("isDataUpdate");
        this.warningCount = extData.getInt("warningCount");
    }


}
