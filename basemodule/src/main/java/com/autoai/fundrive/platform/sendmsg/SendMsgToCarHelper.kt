package com.autoai.fundrive.platform.sendmsg

import com.autoai.fundrive.commontool.LogManager
import com.autoai.fundrive.platform.protocol.WLProtocolManager
import org.json.JSONException
import org.json.JSONObject

object SendMsgToCarHelper {
    @JvmStatic
    @JvmOverloads
    fun sendActionCarHome(key: String, extra: String = "") {
        LogManager.i("sendActionCarHome: key = $key, extra = $extra")
        val jsonObject = JSONObject()
        try {
            jsonObject.put("moduleName", "WeLink")
            jsonObject.put("version", 0)
            jsonObject.put("platform", "android")

            val command = JSONObject()
            command.put("method", "carHome")

            val extData = JSONObject()
            extData.put("key", key)
            extData.put("extra", extra)

            command.put("extData", extData)
            command.put("phoneHome", "")
            command.put("phoneBack", "")
            command.put("huHome", "")

            jsonObject.put("command", command)
        } catch (e: JSONException) {
            LogManager.e("updateStates exception:" + e.message)
        }
        val hidMessage = jsonObject.toString()
        LogManager.i("ScreenControl ---- updateStates ------------>hidMessage：$hidMessage")
        WLProtocolManager.getInstance().sendCommondMessage(hidMessage)
    }
}