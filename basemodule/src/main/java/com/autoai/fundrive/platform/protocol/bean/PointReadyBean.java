package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

public class PointReadyBean extends BaseProtocolBean{

    int controlState;
    public int getControlState() {
        return controlState;
    }

    @Override
    public String toString() {
        return "PointReadyBean{" +
                "controlState=" + controlState +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.controlState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_CONTROL);

    }
}
