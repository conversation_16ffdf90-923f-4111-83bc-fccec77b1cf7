package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 */

public class ReqPlayVideoBean extends BaseProtocolBean {
    /**
     * h5地址
     */
    public int action;



    @Override
    public String toString() {
        return "ReqPlayVideoBean{" +
                "action='" + action + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.action = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_REQ_CAR_DRIVER_STATUS_CHANGE);
    }

}
