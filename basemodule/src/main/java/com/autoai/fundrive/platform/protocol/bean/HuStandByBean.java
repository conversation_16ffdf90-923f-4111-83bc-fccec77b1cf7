package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端 standby模式 向手机端 launcher发的协议。
 */
public class HuStandByBean extends BaseProtocolBean {

    /**
     * 1：打开
     * 2：关闭
     */
    private int standbyState;

    public int getStandbyState() {
        return standbyState;
    }

    @Override
    public String toString() {
        return "HuStandByBean{" +
                "standbyState=" + standbyState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.standbyState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_STANDBY);
    }


}
