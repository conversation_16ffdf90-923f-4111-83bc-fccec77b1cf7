package com.autoai.fundrive.platform.protocol.manager;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.text.TextUtils;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.SharePreferenceUtil;
import com.autoai.fundrive.platform.protocol.bean.BaseProtocolBean;
import com.autoai.fundrive.platform.protocol.bean.HuPhoneBtMacAddressBean;
import com.autoai.fundrive.platform.protocol.listener.HUCommandListener;

/**
 * 手机蓝牙MAC地址管理器
 * 负责接收、存储和提供手机蓝牙MAC地址
 * 
 * <AUTHOR>
 */
public class PhoneBtMacAddressManager implements HUCommandListener {
    
    private static final String PREF_PHONE_BT_MAC = "phone_bt_mac_address";
    private static final Object LOCK = new Object();
    private Context mContext;
    
    // 单例实例
    private static PhoneBtMacAddressManager instance;
    
    /**
     * 私有构造函数，防止外部直接实例化
     */
    private PhoneBtMacAddressManager(Context context) {
        this.mContext = context.getApplicationContext();
    }
    
    /**
     * 获取PhoneBtMacAddressManager单例实例
     * @param context 上下文对象
     * @return PhoneBtMacAddressManager单例实例
     */
    public static synchronized PhoneBtMacAddressManager getInstance(Context context) {
        if (instance == null) {
            instance = new PhoneBtMacAddressManager(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 接收车机发送的协议命令
     * @param baseProtocolBean 协议数据Bean
     */
    @Override
    public void onReceiveCommand(BaseProtocolBean baseProtocolBean) {
        if (baseProtocolBean instanceof HuPhoneBtMacAddressBean) {
            HuPhoneBtMacAddressBean bean = (HuPhoneBtMacAddressBean) baseProtocolBean;
            String macAddress = bean.getMacAddress();
            
            LogManager.i("Received onPhoneBTMacAddress: " + macAddress);
            
            if (!TextUtils.isEmpty(macAddress)) {
                synchronized (LOCK) {
                    try {
                        // 存储MAC地址
                        SharePreferenceUtil.saveString(mContext, PREF_PHONE_BT_MAC, macAddress);
                        LogManager.i("Successfully stored phone BT MAC address: " + macAddress);
                    } catch (Exception e) {
                        LogManager.e("Failed to store phone BT MAC address: " + e.getMessage());
                    }
                }
            } else {
                LogManager.w("Received empty phone BT MAC address from car");
            }
        }
    }
    
    /**
     * 获取存储的手机蓝牙MAC地址
     * @return 存储的MAC地址，如果没有则返回空字符串
     */
    public String getPhoneBtMacAddress() {
        synchronized (LOCK) {
            try {
                String macAddress = SharePreferenceUtil.getString(mContext, PREF_PHONE_BT_MAC, "");
                LogManager.d("Retrieved stored phone BT MAC: " + (TextUtils.isEmpty(macAddress) ? "empty" : macAddress));
                return macAddress;
            } catch (Exception e) {
                LogManager.e("Failed to get stored phone BT MAC address: " + e.getMessage());
                return "";
            }
        }
    }
    
    /**
     * 获取手机蓝牙MAC地址，优先使用存储的MAC地址
     * 注意：Android 6.0+系统限制，无法直接获取真实的本地蓝牙MAC地址
     * 因此我们主要依赖车机发送给我们的MAC地址进行存储和使用
     *
     * @return MAC地址字符串，可能为空
     */
    public String getPhoneMacAddress() {
        // 直接返回存储的手机MAC地址
        String storedMac = getPhoneBtMacAddress();
        if (!TextUtils.isEmpty(storedMac)) {
            LogManager.i("Using stored phone MAC: " + storedMac);
            return storedMac;
        }

        // 如果没有存储的MAC地址，尝试获取本地蓝牙MAC（可能受系统限制）
        try {
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter != null) {
                @SuppressLint("MissingPermission")
                String address = bluetoothAdapter.getAddress();
                if (!TextUtils.isEmpty(address) && !"02:00:00:00:00:00".equals(address)) {
                    LogManager.i("Retrieved local Bluetooth MAC: " + address);
                    return address;
                } else {
                    LogManager.w("Local Bluetooth MAC is invalid or unavailable: " + address);
                }
            } else {
                LogManager.w("BluetoothAdapter is null");
            }
        } catch (Exception e) {
            LogManager.e("Failed to get local Bluetooth MAC: " + e.getMessage());
        }

        LogManager.w("No MAC address available, returning empty string");
        return "";
    }
}
