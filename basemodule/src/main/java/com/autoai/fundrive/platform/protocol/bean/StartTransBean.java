package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是用来实现 CRS3.0-SVW项目车机版本升级功能，车机端通知手开始传输数据。
 */
public class StartTransBean extends BaseProtocolBean {

    /**
     * 是否开始传输 boolean true:开始传输 false：传输异常
     */
    private boolean isTrans;

    public boolean isTrans() {
        return isTrans;
    }

    @Override
    public String toString() {
        return "StartTransBean{isTrans=" + isTrans + ", methodName='" + getMethodName() + "'}";
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.isTrans = extData.getBoolean(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_TRANS);
    }


}
