package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端发送中断音源类型向手机端 launcher发的协议。
 */
public class HuAudioBean extends BaseProtocolBean {
    /**
     * 0：导航
     * 2：微信
     * 3：天气预报（开机播报消息）
     * 4：语音助手提示音
     * 5：新闻
     * 6：后台语音助手提示音
     * 7：推送消息
     */
    private int audioType;

    public int getAudioType() {
        return audioType;
    }

    @Override
    public String toString() {
        return "HuAudioBean{" +
                "audioType=" + audioType +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.audioType = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_AUDIOTYPE);
    }


}
