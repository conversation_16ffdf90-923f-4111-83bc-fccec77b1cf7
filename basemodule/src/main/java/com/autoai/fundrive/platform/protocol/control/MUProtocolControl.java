package com.autoai.fundrive.platform.protocol.control;

import android.graphics.Point;
import android.text.TextUtils;

import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.protocol.WLProtocolConfig;
import com.autoai.fundrive.platform.protocol.manager.PhoneBtMacAddressManager;
import com.autoai.fundrive.platform.WLPlatformManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

/**
 * 交互协议手机端发送协议处理类，主要定义封装发送各种消息接口。
 *
 * <AUTHOR>
 */
public class MUProtocolControl {

    /**
     * 给车机端发送手机端自定义按键
     *
     * @param pressDownKey 手机端自定义按键
     */
    public void sendMuChannelToCar(int pressDownKey) {
        LogManager.i("sendMuChannelToCar ---------> pressDownKey:" + pressDownKey);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_DOWNKEY, pressDownKey);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MUCHANNEL, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendMuChannelToCar exception:" + e.getMessage());
        }
    }


    /**
     * 给车机端发送WeLink退出消息
     *
     * @param isExit - 是否退出
     */
    public void sendExitToCar(boolean isExit) {
        LogManager.i("sendExitToCar ---------> isExit:" + isExit);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_EXIT, isExit);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_EXITWELINK, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendExitToCar exception:" + e.getMessage());
        }
    }
    /**
     * 给车机端发送校准消息
     */
    public void sendSureCalibration() {
        LogManager.i("sendCalibration ---------> points:"  );
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_POINTS,"");
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_POINTREADY, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendCalibration exception:" + e.getMessage());
        }
    }
    public void sendSureCalibrationData(ArrayList<ArrayList<Float>> points) {
        LogManager.i("sendSureCalibrationData ---------> points:"  + points);
        try {
            JSONObject extData = new JSONObject();
            JSONArray pointArray = new JSONArray();
            for (int i = 0; i < points.size(); i++) {
                pointArray.put(new JSONArray(points.get(i)));
            }
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_POINTS,pointArray);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_SENDPOINT, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendSureCalibrationData exception:" + e.getMessage());
        }
    }

    /**
     * 给车机端发送手机屏幕大小消息
     *
     * @param width  - 手机宽，
     * @param height - 手机高
     */
    public void sendScreenSizeToCar(int width, int height) {
        LogManager.i("sendScreenSizeToCar ---------> width:" + width + ",height:" + height);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_WIDTH, width);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_HEIGHT, height);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_SCREENCHANGE, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendScreenSizeToCar exception:" + e.getMessage());
        }
    }


    /**
     * 给车机端发送手机互联状态消息
     *
     * @param isLink - 互联状态
     */
    public void sendAutoLinkToCar(boolean isLink) {
        LogManager.i("sendAutoLinkToCar ---------> isLink:" + isLink);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_STATE, isLink);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AUTOLINK, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendAutoLinkToCar exception:" + e.getMessage());
        }
    }


    /**
     * 给车机端发送手机拨打电话消息
     *
     * @param number - 电话号码
     */
    public void sendTelephoneToCar(String number) {
        LogManager.i("sendTelphoneToCar ---------> number:" + number);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_PHONE, number);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_CALLNUMBER, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendTelphoneToCar exception:" + e.getMessage());
        }
    }


    /**
     * 此协议主要是点击手机端常驻条的home键时，通知车机端置为后台；
     */
    public void sendHomeKeyToCar() {
        LogManager.i("sendHomeKeyToCar --------->");
        String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_HOMEKEY, new JSONObject());
        sendMessageDataCommand(data);
    }

    /**
     * 此协议主要是点击手机端常驻条的home键时，通知车机端置为后台；
     */
    public void sendHomeKeyToCar(String action) {
        LogManager.i("sendHomeKeyToCar --------->");
        JSONObject extData = new JSONObject();
        try {
            extData.put("action", action);
        } catch (JSONException e) {
            LogManager.e("sendHomeKeyToCar exception:" + e.getMessage());
        }
        String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_HOMEKEY, extData);
        sendMessageDataCommand(data);
    }

    /**
     * 此协议主要是手机端通知车机端停止当前音频pcm播报。
     */
    public void sendStopPlayPCMToCar(int pcmInfo) {
        LogManager.i("sendStopPlayPCMToCar ---------> pcmInfo:" + pcmInfo);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_PCMINFO, pcmInfo);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_STOPPLAY, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendStopPlayPCMToCar exception:" + e.getMessage());
        }
    }


    /**
     * 此协议主要是手机端通知车机端开启静音模式和取消静音模式
     *
     * @param state - 1 开启静音模式（静音模式下，除了VR的声音，其他声音都不进行播报），2 退出静音模式
     */
    public void sendChangeMuteToCar(int state) {
        LogManager.i("sendChangeMuteToCar ---------> state:" + state);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MUTE, state);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MUTE, extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            LogManager.e("sendChangeMuteToCar exception:" + e.getMessage());
        }
    }


    /**
     * 此协议主要是手机端通知车机端打开车载媒体
     */
    public void sendStartMediaToCar() {
        LogManager.i("sendStartMediaToCar --------->");
        try {
            JSONObject extData = new JSONObject();
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_STARTMEDIA, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendStartMediaToCar exception:" + e.getMessage());
        }
    }


    /**
     * 消息拦截通知
     *
     * @param type - 0-微信，1-QQ，2-短信
     */
    public void sendMsgReceiveToCar(int type) {
        LogManager.i("sendMsgReceiveToCar ---------> type:" + type);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_TYPE, type);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MSGRECEIVE, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendMsgReceiveToCar exception:" + e.getMessage());
        }
    }


    /**
     * 微信给车机发送命令消息
     */
    public void sendWeChatResponseToCar(int code) {
        LogManager.i("sendWeChatResponseToCar ---------> code:" + code);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_CODE, code);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_WXRESPONSE, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendWeChatResponseToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给车机发送蓝牙连接状态消息
     */
    public void sendBluetoothStateToCar(boolean state) {
        LogManager.i("sendBluetoothStateToCar ---------> state:" + state);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_BLUETOOTH, state);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_BLUETOOTH, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendBluetoothStateToCar exception:" + e.getMessage());
        }
    }


    /**
     * 给车机发送蓝牙匹配连接状态消息
     * @param state 蓝牙连接状态
     * @param deviceAddress 车机设备MAC地址， 不使用。
     */
    public void sendBluetoothPairedToCar(boolean state, String deviceAddress) {
        LogManager.i("sendBluetoothPairedToCar ---------> state:" + state + ", address:" + deviceAddress);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_BLUETOOTH_PAIRED, state ? "1" : "0");

            // 获取MAC地址使用存储的MAC地址
            String phoneMac = "";
            PhoneBtMacAddressManager manager = PhoneBtMacAddressManager.getInstance(null);
            if (manager != null) {
                phoneMac = manager.getPhoneMacAddress();
            }

            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_PHONE_MAC, phoneMac != null ? phoneMac : "");
            LogManager.i("Sending onCarBTConnected with phoneMac: " + (TextUtils.isEmpty(phoneMac) ? "empty" : phoneMac));

            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_BLUETOOTH_PAIRED, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendBluetoothPairedToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给车机发送手机端音乐播放状态
     *
     * @param state 播放状态 1(MEDIA_STATE_START)：开始播放；2(MEDIA_STATE_START)：停止播放
     */
    public void sendMediaStateToCar(int state) {
        LogManager.i("sendMediaStateToCar ---------> state:" + state);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MEDIA, state);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MEDIASTATE, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendMediaStateToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给车机发送手机端地图中心点数据的
     *
     * @param miniMapState 小地图当前状态；
     * @param centerPoix   中心点x轴坐标；
     * @param centerPoiy   centerPoiy；
     * @param showwidth    展示的宽；
     * @param showhigh     展示的高
     */
    public void sendMobileNaviStateToCar(boolean miniMapState, int centerPoix, int centerPoiy, int showwidth, int showhigh) {
        LogManager.i("sendMobileNaviStateToCar ---------> miniMapState:" + miniMapState + ",centerPoix:" + centerPoix + ",centerPoiy:" + centerPoiy + ",showwidth:" + showwidth + ",showhigh:" + showhigh);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MAP, miniMapState);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_POIX, centerPoix);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_POIY, centerPoiy);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_SHOW_WIDTH, showwidth);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_SHOW_HIGH, showhigh);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MOBILENAVI, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendMediaStateToCar exception:" + e.getMessage());
        }
    }

    /**
     * 音量调节控制
     *
     * @param controlVolume 具体调节
     * @param requestType   车机调节类型
     */
    public void sendControlVolumeToCar(int controlVolume, int requestType) {
        LogManager.i("sendControlVolumeToCar ---------> controlVolume:" + controlVolume + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_CONTROL_VOLUME, controlVolume);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_CONTROL_VOLUME, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendControlVolumeToCar exception:" + e.getMessage());
        }
    }

    /**
     * 音量静音控制
     *
     * @param muteVolume  具体调节
     * @param requestType 车机调节类型
     */
    public void sendMuteVolumeToCar(int muteVolume, int requestType) {
        LogManager.i("sendMuteVolumeToCar ---------> muteVolume:" + muteVolume + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_MUTE_VOLUME, muteVolume);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_MUTE_VOLUME, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendMuteVolumeToCar exception:" + e.getMessage());
        }
    }

    /**
     * 空调开关控制
     *
     * @param aircoSwitch 空调开关 类型 int 1：关闭空调 2：开启空调
     * @param requestType 车机调节类型 类型 int
     * @param zoneArr     温区 类型 int []
     */
    public void sendAircoSwitchToCar(int aircoSwitch, int requestType, int[] zoneArr) {
        LogManager.i("sendAircoSwitchToCar ---------> aircoSwitch:" + aircoSwitch + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIRCO_SWITCH, aircoSwitch);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);

            JSONArray jsonArray = new JSONArray();
            if (zoneArr != null) {
                for (int j : zoneArr) {
                    jsonArray.put(j);
                }
            }
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_ZONEARR, jsonArray);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AIRCON_SWITCH, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendAircoSwitchToCar exception:" + e.getMessage());
        }
    }

    /**
     * 空调温度控制
     *
     * @param airTemperatureType 温度单位 类型 int 1：摄氏度单位 2：华氏度单位
     * @param aircoTemp          空调温度调节 类型 double
     * @param requestType        车机调节类型 类型 int
     * @param zoneArr            温区 类型 int[]
     */
    public void sendAircoTempToCar(int airTemperatureType, double aircoTemp, int requestType, int[] zoneArr) {
        LogManager.i("sendAircoTempToCar ---------> airTemperatureType:" + airTemperatureType + ",aircoTemp:" + aircoTemp + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIR_TEMP_TYPE, airTemperatureType);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIRCO_TEMP, aircoTemp);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);

            JSONArray jsonArray = new JSONArray();
            if (zoneArr != null) {
                for (int j : zoneArr) {
                    jsonArray.put(j);
                }
            }
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_ZONEARR, jsonArray);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AIRCON_TEMP, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendAircoTempToCar exception:" + e.getMessage());
        }
    }

    /**
     * 空调风量控制
     *
     * @param climateMode    风量模式  类型 int 1：automatical 2：manual
     * @param airBlowingRate 类型 int automatical模式下： 1：soft 2：medium 3：intensive
     * @param requestType    车机调节类型 类型 int
     * @param zoneArr        温区 类型 int []
     */
    public void sendAircoBlowingrateToCar(int climateMode, int airBlowingRate, int requestType, int[] zoneArr) {
        LogManager.i("sendAircoTempToCar ---------> climateMode:" + climateMode + ",aircoBlowingRate:" + airBlowingRate + ",requestType:" + requestType);
        try {
            JSONObject extData = new JSONObject();
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_CLIMATE_MODE, climateMode);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_AIRCO_BLOWINGRATE, airBlowingRate);
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE, requestType);

            JSONArray jsonArray = new JSONArray();
            if (zoneArr != null) {
                for (int j : zoneArr) {
                    jsonArray.put(j);
                }
            }
            extData.put(WLProtocolConfig.MU_PROTOCOL_METHOD_FIELD_ZONEARR, jsonArray);

            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_AIRCON_BLOWINGGRATE, extData);
            sendCanDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendAircoTempToCar exception:" + e.getMessage());
        }
    }

    /**
     * 给海外项目打开车机蓝牙设置界面
     */
    public void sendOpenHuBtSettingToCar() {
        LogManager.i("sendOpenHuBtSettingToCar --------->");
        try {
            JSONObject extData = new JSONObject();
            extData.put("key", WLProtocolConfig.MU_PROTOCOL_METHOD_OPEN_HUBTSETTING);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_OPEN_HUBTSETTING, extData);
            sendMessageDataCommand(data);
        } catch (Exception e) {
            LogManager.e("sendOpenHuBtSettingToCar exception:" + e.getMessage());
        }
    }

    /**
     * 通知车机端当前手机是否正在播放视频
     *
     * @param msg - 字符串消息
     */
    public void sendPhonePlayVideoStateInfo(String msg) {
        LogManager.i("sendPhonePlayVideoStateInfo ---------> msg:" + msg);
        try {
            JSONObject extData = new JSONObject();
            extData.put("key", WLProtocolConfig.MU_PROTOCOL_METHOD_PHONE_PLAY_VEADIO_STATE);
            String data = getMethodProtocol(WLProtocolConfig.MU_PROTOCOL_METHOD_PHONE_PLAY_VEADIO_STATE,extData);
            sendMessageDataCommand(data);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 发送默认字符串格式消息
     *
     * @param msg - 字符串消息
     */
    public void sendCommondMessage(String msg) {
        LogManager.i("sendCommondMessage ---------> msg:" + msg);
        sendMessageDataCommand(msg);
    }







    /**
     * 获取指定方法的协议数据
     *
     * @param methodName - 发送协议的方法
     * @param extData    - JSONObject类型数据参数
     */
    private String getMethodProtocol(String methodName, JSONObject extData) {
        LogManager.d("sendMessageDataCommand methodName = " + methodName);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_MODULENAME, WLProtocolConfig.WL_PROTOCOL_VALUE_MODULENAME);
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_VERSION, WLProtocolConfig.WL_PROTOCOL_VALUE_VERSION);
            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_PLATFORM, WLProtocolConfig.WL_PROTOCOL_VALUE_PLATFORM);

            JSONObject commandObject = new JSONObject();
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_METHOD, methodName);
            commandObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA, extData);

            jsonObject.put(WLProtocolConfig.WL_PROTOCOL_FIELD_COMMAND, commandObject);
        } catch (JSONException e) {
            LogManager.e("getMethodProtocol exception:" + e.getMessage());
        }
        return jsonObject.toString();
    }


    /**
     * 手机给车机发送消息协议数据消息
     */
    private void sendMessageDataCommand(String command) {
        LogManager.i("sendMessageCommand ---------> command:" + command);
        if (!TextUtils.isEmpty(command)) {
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.sendMessageDataToCar(command);
        }
    }

    public void updateStates(int width, int height, int angle){
        LogManager.i("sendMessageDataToCar -------------->updateStates:" + width + ",height:" + height + ",angle:" + angle);
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.updateStates(width, height, angle);
    }

    /**
     * 手机给车机发送candata协议数据消息
     */
    private void sendCanDataCommand(String command) {
        LogManager.i("sendCanDataCommand ---------> command:" + command);
        if (!TextUtils.isEmpty(command)) {
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.sendCanDataToCar(command);
        }
    }
}
