package com.autoai.fundrive.platform.bean;
/**
 * Welink 三投互联车联信息实体bean，主要封装车辆需要的参数信息。
 *
 * <AUTHOR>
 */
public class CarBean {
    /**
     * 车机屏幕宽
     */
    private int huScreenWidth;
    /**
     * 车机屏幕高
     */
    private int huScreenHeight;
    /**
     * 车机屏幕密度
     */
    private int densityDpi;
    /**
     * 用户标识
     */
    private String userID;
    /**
     * 车辆标识
     */
    private String vehicleID;
    /**
     * 车辆类别
     */
    private String vehicleType;

    public int getHuScreenWidth() {
        return huScreenWidth;
    }

    public void setHuScreenWidth(int huScreenWidth) {
        this.huScreenWidth = huScreenWidth;
    }

    public int getHuScreenHeight() {
        return huScreenHeight;
    }

    public void setHuScreenHeight(int huScreenHeight) {
        this.huScreenHeight = huScreenHeight;
    }

    public int getDensityDpi() {
        return densityDpi;
    }

    public void setDensityDpi(int densityDpi) {
        this.densityDpi = densityDpi;
    }

    public String getUserID() {
        return userID;
    }

    public void setUserID(String userID) {
        this.userID = userID;
    }

    public String getVehicleID() {
        return vehicleID;
    }

    public void setVehicleID(String vehicleID) {
        this.vehicleID = vehicleID;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    @Override
    public String toString() {
        return "CarBean{" +
                "huScreenWidth=" + huScreenWidth +
                ", huScreenHeight=" + huScreenHeight +
                ", densityDpi=" + densityDpi +
                ", userID='" + userID + '\'' +
                ", vehicleID='" + vehicleID + '\'' +
                ", vehicleType='" + vehicleType + '\'' +
                '}';
    }
}
