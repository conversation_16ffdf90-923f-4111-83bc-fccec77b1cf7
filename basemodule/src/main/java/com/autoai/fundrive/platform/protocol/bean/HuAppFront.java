package com.autoai.fundrive.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 车机端点击menu键或home键会将SAICLink置为后台
 * state link前后台 boolean true：前台 false 后台
 */
public class HuAppFront extends BaseProtocolBean{

    private boolean state;

    public boolean isState() {
        return state;
    }

    public void setState(boolean state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "HuAppFront{" +
                "state=" + state +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.state = extData.getBoolean("state");
    }


}
