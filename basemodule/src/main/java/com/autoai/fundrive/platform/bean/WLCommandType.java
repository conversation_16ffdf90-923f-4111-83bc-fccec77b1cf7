package com.autoai.fundrive.platform.bean;

import com.autoai.fundrive.platform.WLPlatformConfig;

/**
 * Welink 三投三方接收车机命令类型枚举，主要封装车机命令类型。
 *
 * <AUTHOR>
 */
public enum WLCommandType {
    /**
     * 0 音乐
     */
    MUSIC(0, WLPlatformConfig.WL_PLATFORM_COMMAND_MUSIC),
    /**
     * 1 导航
     */
    NAVI(1, WLPlatformConfig.WL_PLATFORM_COMMAND_NAVI),
    /**
     * 2 通用
     */
    ALL(2, WLPlatformConfig.WL_PLATFORM_COMMAND_ALL);

    private int code;
    private String type;

    WLCommandType(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public static String type(int code) {
        for (WLCommandType m : WLCommandType.values()) {
            if (m.getCode() == code) {
                return m.getType();
            }
        }
        return ALL.getType();
    }

    public int getCode() {
        return code;
    }
    public void setCode(int code) {
        this.code = code;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }


}
