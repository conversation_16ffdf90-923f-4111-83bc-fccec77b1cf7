package com.autoai.fundrive.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.SupplicantState;
import android.net.wifi.WifiManager;

import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.WLPlatformManager;

/**
 * <AUTHOR> zhanggc
 * @version : 1.0
 * @description : java类作用描述 监听wifi 网络变化 ，重新启动wifi互联状态
 * @date : 2024/12/2 15:39
 */
public class NetworkReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        // 监听网络连接，包括wifi和移动数据的打开和关闭,以及连接上可用的连接都会接到监听
        // 特殊注意：如果if条件生效，那么证明当前是有连接wifi或移动网络的，如果有业务逻辑最好把esle场景酌情考虑进去！
        LogManager.e( "NetworkReceiver action:" + intent.getAction());
        String action = intent.getAction();
        if (WifiManager.NETWORK_STATE_CHANGED_ACTION.equals(action)) {
            NetworkInfo networkInfo = intent.getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO);
            if (networkInfo.isConnected()) {
                // WiFi已连接
                LogManager.e( "NetworkReceiver WiFi已连接");
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                if(platformManager != null) {
                    //重新启动wifi互联状态
                    platformManager.stopWifiLink();
                    platformManager.startWifiLink();
                }
            } else {
                // WiFi未连接
                LogManager.e( "NetworkReceiver WiFi未连接");
            }
        } else if (WifiManager.SUPPLICANT_STATE_CHANGED_ACTION.equals(action)) {
            SupplicantState supplicantState = intent.getParcelableExtra(WifiManager.EXTRA_NEW_STATE);
            switch (supplicantState) {
                case COMPLETED:
                    // WiFi连接成功
                    LogManager.e( "NetworkReceiver WiFi已连接");
                    break;
                case DISCONNECTED:
                    // WiFi断开连接
                    LogManager.e( "NetworkReceiver WiFi未连接");
                    break;
                // 其他状态可以根据需要处理
            }
        }
    }

}
