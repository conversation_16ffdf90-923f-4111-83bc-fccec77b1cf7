// Top-level build file where you can add configuration options common to all sub-projects/modules.
//buildscript {
//    apply from: "gradleshell/version.gradle"
//    apply from: "gradleshell/functions.gradle"
//}
//
//plugins {
//    id 'com.android.application' version '7.1.3' apply false
//    id 'com.android.library' version '7.1.3' apply false
//}
//
//task clean(type: Delete) {
//    delete rootProject.buildDir
//}

// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    apply from: "gradleshell/version.gradle"
    apply from: "gradleshell/functions.gradle"
    ext {
        kotlin_version = '1.9.0'
    }
    ext {
        kotlin_version = '1.9.0'
    }
    repositories {
        gradlePluginPortal()

        maven {
//            allowInsecureProtocol = true
            url "https://wdnexus.autoai.com/content/repositories/autoai-AVS/"
        }
        maven {
//            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/groups/public/'
        }
        maven {
//            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/repositories/jcenter'
        }


        google()
        mavenCentral()
    }
    dependencies {
//        classpath "com.android.tools.build:gradle:7.1.3"
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath "org.jetbrains.kotlin:kotlin-serialization:$kotlin_version"
    }
}

allprojects {
    repositories {

        maven {
//            allowInsecureProtocol = true
            url "https://wdnexus.autoai.com/content/repositories/autoai-AVS/"
        }
        maven {
//            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/groups/public/'
        }
        maven {
//            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/repositories/jcenter'
        }
        maven {
//            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/repositories/google'
        }
        maven {
//            allowInsecureProtocol = true
            url "https://wdnexus.autoai.com/content/repositories/releases/"
        }
        maven {
//            allowInsecureProtocol = true
            url "https://wdnexus.autoai.com/content/repositories/snapshots/"
        }

        maven { url 'https://maven.aliyun.com/repository/google' }
        google()
        mavenCentral()

    }

    //每次build构建 本地缓存的更新策略
    configurations.all {
        // check for updates every build
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }

}

task clean(type: Delete) {
    delete rootProject.buildDir
}