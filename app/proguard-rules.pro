# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keepclassmembers class * {
    public <fields>;
    public <methods>;
}

# 保留 FFmpegMediaMetadataRetriever 相关的 AAR 库
-keep class wseemann.media.** { *; }
-keepclassmembers class wseemann.media.** { *; }
-keep enum wseemann.media.** { *; }
-keepclassmembers enum wseemann.media.** { *; }
-keepclassmembers class * implements wseemann.media.** { *; }
-keepclassmembers interface * extends wseemann.media.** { *; }

# 保留 FFmpegMediaMetadataRetriever-native 相关的 AAR 库
-keep class wseemann.media.native.** { *; }
-keepclassmembers class wseemann.media.native.** { *; }
-keep enum wseemann.media.native.** { *; }
-keepclassmembers enum wseemann.media.native.** { *; }
-keepclassmembers class * implements wseemann.media.native.** { *; }
-keepclassmembers interface * extends wseemann.media.native.** { *; }

# 保留 FFmpegMediaMetadataRetriever-core 相关的 AAR 库
-keep class wseemann.media.core.** { *; }
-keepclassmembers class wseemann.media.core.** { *; }
-keep enum wseemann.media.core.** { *; }
-keepclassmembers enum wseemann.media.core.** { *; }
-keepclassmembers class * implements wseemann.media.core.** { *; }
-keepclassmembers interface * extends wseemann.media.core.** { *; }

# 保留 FFmpegMediaMetadataRetriever-native-armeabi-v7a 相关的 AAR 库
-keep class wseemann.media.native.armeabi_v7a.** { *; }
-keepclassmembers class wseemann.media.native.armeabi_v7a.** { *; }
-keep enum wseemann.media.native.armeabi_v7a.** { *; }
-keepclassmembers enum wseemann.media.native.armeabi_v7a.** { *; }
-keepclassmembers class * implements wseemann.media.native.armeabi_v7a.** { *; }
-keepclassmembers interface * extends wseemann.media.native.armeabi_v7a.** { *; }

-keep class org.tensorflow.** { *; }
-keep class org.tensorflow.lite.** { *; }
