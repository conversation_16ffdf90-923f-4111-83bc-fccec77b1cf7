//
apply plugin: 'com.android.application'
//
apply from: "../gradleshell/dependencies.gradle"
apply plugin: 'kotlin-android'
apply plugin: 'kotlinx-serialization'
//
android {
    compileSdkVersion rootProject.ext.android.compileSdk
    defaultConfig {
        applicationId "com.autoai.android.welink3"
        minSdkVersion rootProject.ext.android.minSdk
        targetSdkVersion rootProject.ext.android.targetSdk
        versionName rootProject.ext.android.versionName
        versionCode rootProject.ext.android.versionCode

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a"
        }
    }

    // Instead, use the bundle block to control which types of configuration APKs
    // you want your app bundle to support.
    bundle {
        language {
            // This property is set to true by default.
            // You can specify `false` to turn off
            // generating configuration APKs for language resources.
            // These resources are instead packaged with each base and
            // feature APK.
            // Continue reading below to learn about situations when an app
            // might change setting to `false`, otherwise consider leaving
            // the default on for more optimized downloads.
            enableSplit = false
        }
        density {
            // This property is set to true by default.
            enableSplit = false
        }
        abi {
            // This property is set to true by default.
            enableSplit = false
        }
    }
    signingConfigs {
        debug {
            storeFile file("../key/wedrive2024.jks")
            storePassword "wedrive2024"
            keyAlias "wedrive"
            keyPassword "wedrive2024"
        }

        release {
            storeFile file("../key/wedrive2024.jks")
            storePassword "wedrive2024"
            keyAlias "wedrive"
            keyPassword "wedrive2024"
        }
    }
    buildTypes {
        release {
            debuggable false
//            multiDexKeepFile file('multidex-config.txt')
//            // Enables code shrinking, obfuscation, and optimization
            minifyEnabled true
//            // Enables resource shrinking, which is performed by the
//            // Android Gradle plugin.
//            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            debuggable true
//            multiDexKeepFile file('multidex-config.txt')
//            // Enables code shrinking, obfuscation, and optimization
            minifyEnabled true
//            // Enables resource shrinking, which is performed by the
//            // Android Gradle plugin.
//            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    //打包
//    libraryVariants.all(assembleAAR(project))
    applicationVariants.all(assembleAPK(project))
}

dependencies {
//    implementation fileTree(dir: 'libs', include: ['*.jar'])
    //
    implementation COMMON_DEPEN.MAPBAR_ANDROID_APPFRAMEWORK
    //
    implementation project(':basemodule')
    implementation 'com.autoai.welink.logiclib.skincore:skinframework:0.1.0'
//    implementation 'com.autoai.welink.lib:bluetooth:2.0'
    implementation 'com.autoai.link.baselog:baselog:0.0.11'
    implementation "com.autoai.link.threadpool:threadpool:0.0.6"
//    implementation COMMON_DEPEN.DESIGN
//    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLSCREEN_HID


    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    implementation 'androidx.fragment:fragment-ktx:1.5.6'

    implementation 'com.blankj:utilcodex:1.31.0'

    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3'
//    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
//    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.6'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    debugImplementation 'com.facebook.stetho:stetho:1.5.0'
    debugImplementation 'com.facebook.stetho:stetho-okhttp3:1.5.0'
}