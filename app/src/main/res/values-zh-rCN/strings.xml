<resources>
    <string name="haiwai_launcher_app_name">手车互联</string>
    <string name="haiwai_launcher_welink_main1">您可以通过手机usb或者wifi连接车机，使用welink镜像投屏，通过蓝牙音频连接，通过车机播放手机声音。</string>
    <string name="haiwai_launcher_text_notice">

在您使用 GAZ Link前，请您仔细阅读并透彻理解本声明内容，一旦您使用GAZ Link，即表示您接受GAZ Link免责声明中全部条款。\n\n

1.     请勿在驾驶中操作本产品，以防发生交通事故或其他危险情况。否则由此造成的安全事故应自行承担责任。\n\n

2.     GAZ Link尊重所有用户的个人隐私权，GAZ Link不会收集您的任何个人信息。\n\n

3.     GAZ Link为您提供的服务均为免费，亦不涉及电话、数据访问或短信等相关可能需要付费的服务。\n\n

4.     通过GAZ Link投屏使用第三方产品和服务表示您认可并同意第三方的服务条款，请您仔细阅读第三方相关政策。对第三方提供的产品和服务，GAZ Link不承担任何责任。\n\n
    </string>
    <string name="haiwai_launcher_notice_url"><u>免责声明</u></string>
    <string name="haiwai_launcher_app_tips1">为您提供手机上车服务，手机上APP均可在车辆中控屏上展现并进行操作。</string>
    <string name="haiwai_launcher_app_btn_usb1">USB有线连接</string>
    <string name="haiwai_launcher_app_btn_usb2">使用USB线连接手机和车机</string>
    <string name="haiwai_launcher_app_btn_usb3">步骤一</string>
    <string name="haiwai_launcher_app_btn_permit1">无线连接</string>
    <string name="haiwai_launcher_app_btn_permit2">使用WiFi或无感连接车机</string>
    <string name="haiwai_launcher_app_btn_permit3">步骤二</string>
    <string name="haiwai_launcher_about">关于</string>
    <string name="haiwai_launcher_version">V%s</string>
    <string name="haiwai_launcher_all_rights_reserved">©2020 北京四维智联科技有限公司. All Rights Reserved.</string>
    <string name="haiwai_launcher_authorize_notice">无感投屏</string>
    <string name="haiwai_launcher_permission_notice_1st">无感投屏</string>
    <string name="haiwai_launcher_permission_notice_2st">确保手机的蓝牙和WiFi处于开启状态</string>
    <string name="haiwai_launcher_usb_notice">连接帮助</string>
    <string name="haiwai_launcher_connect_notice_1st">USB有线投屏</string>
    <string name="haiwai_launcher_connect_notice_2st">用USB数据线连接手机和车机</string>
    <string name="haiwai_launcher_disclaimer_notice">免责&amp;隐私</string>

<!--    车机端文案-->
    <string name="haiwai_launcher_mirroring_permission_title">正在获取投屏权限......</string>
    <string name="haiwai_launcher_mirroring_permission_content">请保证手机已解锁，如有弹窗，\n请选择确认或立即开始</string>
    <string name="haiwai_launcher_mirroring_permission_content2">为保证安全，行车中请勿操作手机</string>
    <string name="haiwai_launcher_mirroring_permission_cancel_title">已取消投屏服务</string>
    <string name="haiwai_launcher_mirroring_permission_cancel_content">若想重启投屏服务，请重新链接USB线</string>
    <string name="haiwai_launcher_auxiliary_title">正在获取无障碍权限…</string>
    <string name="haiwai_launcher_auxiliary_content">请在手机设置中开启GAZ Link“辅助模式”或“无障碍模式”</string>
    <string name="haiwai_launcher_auxiliary_content2">为保证安全，行车中请勿操作手机</string>
    <string name="haiwai_launcher_start_screen_content">连接成功，请操作需要投屏的内容</string>
    <string name="haiwai_launcher_start_screen_content2">为保证投屏，请维持手机解锁状态；\n为保证安全，行车中请勿操作手机。</string>

    <string name="haiwai_launcher_bluetooth_dialog_title">为保证音频服务，请正确将蓝牙匹配</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt1">1、打开车机蓝牙</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt2">系统设置>蓝牙>点击设备列表的\"+\"键</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt3">2、打开手机蓝牙</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt4">手机设置>启动蓝牙功能>查找设备连接>完成蓝牙连接\n（以实际手机为准）</string>
    <string name="haiwai_launcher_bluetooth_dialog_negative">取消</string>
    <string name="haiwai_launcher_bluetooth_dialog_positive">去设置</string>

    <!--wifi 无感互联提示语-->
    <string name="haiwai_launcher_wifi_connect_unsupported">设备不支持无感连接，请尝试USB或者WIfi连接</string>
    <string name="haiwai_launcher_wifi_no_blue_content">没有打开蓝牙，请打开后尝试重新连接</string>
    <string name="haiwai_launcher_wifi_no_open_content">没有打开Wi-Fi，请打开后尝试重新连接</string>
    <string name="haiwai_launcher_wifi_local_content">当前Welink没有定位权限，请授权后重试，或者选择通过USB线或Wi-Fi连接</string>
    <string name="haiwai_launcher_wifi_server_ex_content">当前服务发生异常，请尝试USB或者WIfi连接</string>
    <string name="haiwai_launcher_wifi_failed_content">创建Wi-Fi Direct GO失败，请尝试USB或者WIfi连接</string>


    <string name="haiwai_launcher_wifi_connect_scanning">正在扫描车辆设备...</string>
    <string name="haiwai_launcher_wifi_connect_connecting">已发现车辆设备，正在连接...</string>
    <string name="haiwai_launcher_wifi_connect_data_transmission">车辆设备连接成功，数据传输中...</string>

    <string name="haiwai_launcher_about_difficult">疑难解答</string>
    <string name="haiwai_launcher_eixt">再按一次退出应用</string>

</resources>
