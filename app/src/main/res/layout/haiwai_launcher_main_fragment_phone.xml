<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F6F7F9">

    <RelativeLayout
        android:id="@+id/welink1"
        android:layout_width="match_parent"
        android:layout_height="240dp">

        <FrameLayout
            android:layout_marginTop="46dp"
            android:id="@+id/btn_goto_about_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/btn_goto_about"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:clickable="true"
                android:focusable="true"
                android:paddingStart="20dp"
                android:paddingTop="2dp"
                android:paddingEnd="20dp"
                android:src="@drawable/haiwai_launcher_phont_adapter_about_icon" />
        </FrameLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:orientation="vertical"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/connection_status_image"
                android:layout_width="36dp"
                android:layout_height="36dp"/>

            <TextView
                android:id="@+id/connection_information"
                android:layout_marginTop="14dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#0E2F6A"
                android:textSize="16sp" />

        </LinearLayout>


        <TextView
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:layout_below="@id/btn_goto_about_container"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:layout_toEndOf="@+id/top_img"
            android:paddingRight="10dp"
            android:text="@string/haiwai_launcher_app_tips1"
            android:textColor="#000000"
            android:textSize="12sp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/top_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/btn_goto_about_container"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="9.5dp"
            android:src="@drawable/haiwai_launcher_phont_adapter_welink_link"
            android:visibility="gone" />
    </RelativeLayout>

    <TextView
        android:id="@+id/welink3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/welink1"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:text="@string/haiwai_launcher_welink_main1"
        android:textColor="#99000000"
        android:textSize="13sp" />

    <LinearLayout
        android:id="@+id/welink_btns"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/welink3"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginRight="24dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/btn_goto_usb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/haiwai_launcher_main_item_bg"
            android:focusable="true"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <ImageView
                android:id="@+id/icon_usb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/haiwai_launcher_phont_adapter_usb_icon" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_usb"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/haiwai_launcher_app_btn_usb1"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_usb2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_usb"
                    android:layout_marginTop="3dp"
                    android:text="@string/haiwai_launcher_app_btn_usb2"
                    android:textColor="#99000000"
                    android:textSize="12sp" />

            </LinearLayout>

            <TextView
                android:textSize="14sp"
                android:layout_width="wrap_content"
                android:textColor="#979797"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/haiwai_launcher_phont_adapter_usb_back"
                android:text="连接帮助 "></TextView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_goto_permission"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:background="@drawable/haiwai_launcher_main_item_bg"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <ImageView
                android:id="@+id/icon_permit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:src="@drawable/haiwai_launcher_phont_adapter_permit_icon" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/haiwai_launcher_app_btn_permit1"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_usb"
                    android:layout_marginTop="3dp"
                    android:text="@string/haiwai_launcher_app_btn_permit2"
                    android:textColor="#99000000"
                    android:textSize="12sp" />

            </LinearLayout>

            <TextView
                android:textColor="#979797"
                android:textSize="14sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/haiwai_launcher_phont_adapter_usb_back"
                android:text="连接帮助 "></TextView>
        </LinearLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            xmlns:app="http://schemas.android.com/apk/res-auto"
            xmlns:tools="http://schemas.android.com/tools"
            android:id="@+id/command_test_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:visibility="visible"
            android:visibility="gone">

            <Button
                android:id="@+id/screen_off"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="手机锁屏"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <Button
                android:id="@+id/screen_on"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="手机解锁"
                android:textSize="14sp"
                app:layout_constraintStart_toEndOf="@+id/screen_off"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />
            <Button
                android:id="@+id/stream_abort"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="投屏中断"
                android:textSize="14sp"
                app:layout_constraintStart_toEndOf="@+id/screen_on"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />
            <Button
                android:id="@+id/stream_recovery"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="视频流恢复"
                android:textSize="14sp"
                app:layout_constraintStart_toEndOf="@+id/stream_abort"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/btn_goto_notice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:text="@string/haiwai_launcher_disclaimer_notice"
        android:textColor="#0000FF"
        android:visibility="invisible" />
        <View
            android:layout_width="match_parent"
            android:id="@+id/touch_view"
            android:visibility="gone"
            android:layout_height="match_parent">
        </View>
        <View android:id="@+id/view"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:background="#00cccccc"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            />
</RelativeLayout>