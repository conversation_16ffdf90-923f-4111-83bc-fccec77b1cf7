<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F6F7F9"
    android:clickable="true"
    android:focusable="true">
    <View
        android:id="@+id/view_status_bar"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:background="@android:color/white" />
    <RelativeLayout
        android:background="#FFFFFF"
        android:layout_below="@+id/view_status_bar"
        android:id="@+id/btn_goback"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/im_goback"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/haiwai_launcher_phont_adapter_back"
            tools:ignore="ContentDescription" />

        <TextView
            android:padding="12dp"
            android:layout_centerHorizontal="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/haiwai_launcher_usb_notice"
            android:textColor="#000000"
            android:textSize="18sp"
            />
    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/btn_goback">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/usb1"
                android:layout_width="match_parent"
                android:layout_marginTop="40dp"
                android:gravity="center_horizontal"
                android:textStyle="bold"
                android:layout_height="wrap_content"
                android:layout_below="@id/btn_goback"
                android:text="@string/haiwai_launcher_connect_notice_1st"
                android:textColor="#000000"
                android:textSize="20sp" />

           <!-- <ImageView
                android:id="@+id/usb2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/usb1"
                android:layout_gravity="center"
                android:layout_marginTop="27dp"
                android:scaleType="fitCenter"
                android:src="@drawable/haiwai_launcher_phont_adapter_usb_pic" />
-->

            <TextView
                android:id="@+id/usb3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/usb1"
                android:layout_marginTop="8dp"
                android:gravity="center_horizontal"
                android:text="@string/haiwai_launcher_connect_notice_2st"
                android:textColor="#000000"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/usb4"
                android:layout_width="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_height="wrap_content"
                android:layout_below="@id/usb3"
                android:layout_gravity="center_horizontal"
                android:scaleType="fitStart"
                android:src="@drawable/haiwai_launcher_phont_adapter_usb_dialog1" />
        </LinearLayout>
    </ScrollView>
</RelativeLayout>