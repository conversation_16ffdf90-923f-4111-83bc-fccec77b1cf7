<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F6F8FA"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/btn_goback"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginStart="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/im_goback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/haiwai_launcher_phont_adapter_back"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/haiwai_launcher_privacypolicy"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/btn_goback">

        <TextView
            android:id="@+id/tv_notice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="36dp"
            android:layout_marginTop="26dp"
            android:layout_marginEnd="36dp"
            android:text="@string/haiwai_launcher_text_notice"
            android:textColor="#000000"
            android:textSize="15sp" />
    </ScrollView>


</RelativeLayout>