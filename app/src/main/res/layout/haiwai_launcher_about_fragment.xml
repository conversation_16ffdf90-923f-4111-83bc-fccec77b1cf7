<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fff6f7f9"
    android:clickable="true"
    android:focusable="true">
    <!-- 使用系统定义的 ActionBar 高度作为 View 的高度 -->
    <View
        android:id="@+id/view_status_bar"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:background="@android:color/white" />
    <RelativeLayout
        android:id="@+id/btn_goback"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/view_status_bar"
        android:layout_alignParentStart="true"
        android:background="#FFFFFF"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/im_goback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:paddingLeft="18dp"
            android:paddingRight="18dp"
            android:src="@drawable/haiwai_launcher_phont_adapter_back"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/haiwai_launcher_about"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/btn_goback"
        android:background="@drawable/haiwai_about_bg_f"
        android:layout_marginTop="24dp"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        >
        <ImageView
            android:id="@+id/welink_icon"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="60dp"
            android:src="@drawable/haiwai_launcher_welink_about_ic"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/haiwai_launcher_app_name"
            android:textAlignment="center"
            android:textColor="#000000"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/version_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textAlignment="center"
            android:textColor="#000000"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_isDebug"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/version_code"
            android:layout_marginStart="15.5dp"
            android:layout_marginTop="15.5dp"
            android:layout_marginEnd="15.5dp"
            android:layout_marginBottom="15.5dp"
            android:text="debug mode"
            android:textAlignment="center"
            android:textColor="#ffff0000"
            android:textSize="15sp" />
        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginTop="90dp"
            android:background="#0D000000"/>
        <LinearLayout
            android:id="@+id/ll_about_difficult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/haiwai_launcher_about_difficult"
                android:textColor="#ff000000"
                android:textSize="15sp" />


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="centerInside"
                    android:src="@drawable/haiwai_launcher_phont_about_help"
                    />

        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#0D000000"/>
        <LinearLayout
            android:id="@+id/ll_about_disclaimert"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/haiwai_launcher_disclaimer_notice"
                android:textColor="#ff000000"
                android:textSize="15sp" />


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="centerInside"
                android:src="@drawable/haiwai_launcher_phont_about_help"
                />
        </LinearLayout>
    </LinearLayout>




    <TextView
        android:id="@+id/tv_about"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="26dp"
        android:layout_marginBottom="15dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_all_rights_reserved"
        android:textColor="#000000"
        android:textSize="13sp" />

</RelativeLayout>