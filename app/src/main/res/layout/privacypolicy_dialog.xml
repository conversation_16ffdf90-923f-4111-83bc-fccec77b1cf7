<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:background="@drawable/btn_click_f"
    android:orientation="vertical"
    android:paddingBottom="24dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="22dp"
        android:text="@string/haiwai_launcher_reminder"
        android:textColor="#000"
        android:textStyle="bold"
        android:textSize="14sp" />

    <ScrollView
        android:layout_width="match_parent"
        android:scrollbars="none"
        android:layout_height="230dp"
       >
      <LinearLayout
          android:layout_width="match_parent"
          android:orientation="vertical"
          android:layout_height="match_parent">
          <TextView
              android:id="@+id/tv_text"
              android:layout_marginLeft="24dp"
              android:layout_marginTop="14dp"
              android:layout_marginRight="24dp"
              android:paddingBottom="20dp"
              android:lineSpacingExtra="4pt"
              android:layout_width="wrap_content"
              android:layout_height="match_parent"
              android:text="@string/haiwai_launcher_agreelinkbasicfunctionprivacypolicy"
              android:textColor="#323232"
              android:textSize="13sp" />
      </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:visibility="gone"
        android:orientation="horizontal">

        <com.a2ra9k.android.launcher.view.HookCheckBox
            android:layout_width="13dp"
            android:id="@+id/cb_hookview"
            android:layout_height="30dp"
            android:layout_marginLeft="24dp"
            app:hcb_check_circle_color="#057EFF"
            app:hcb_is_check="false"
            app:hcb_line_width="1dp"
            app:hcb_style="hollow_out"
            app:hcb_uncheck_circle_color="#818181" />

        <TextView
            android:layout_marginLeft="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/haiwai_launcher_next"
            android:textColor="#323232"
            android:textSize="11sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_marginTop="32dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_agree"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginRight="56dp"
            android:layout_marginLeft="56dp"
            android:background="@drawable/btn_click_n"
            android:gravity="center"
            android:padding="10dp"
            android:text="@string/haiwai_launcher_agreeand"
            android:textColor="#fff"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_noagree"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:padding="10dp"
            android:layout_marginRight="56dp"
            android:layout_marginLeft="56dp"
            android:text="@string/haiwai_launcher_exit_the_application"
            android:textColor="#FF9B97"
            android:textSize="13sp" />
    </LinearLayout>
</LinearLayout>