<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/screen_off_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/haiwai_launcher_bg_car"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/haiwai_launcher_welink_ic_1280"
        android:textColor="@android:color/white"
        tools:ignore="ContentDescription" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content"
        android:textColor="@android:color/white"
        android:textSize="18sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="15dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content2"
        android:textColor="@android:color/white"
        android:textSize="14sp" />
</LinearLayout>