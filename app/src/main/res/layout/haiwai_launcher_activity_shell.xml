<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:fitsSystemWindows="false">

    <com.mapbar.android.widget.MViewAnimator
        android:id="@+id/animator"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true"
        android:keepScreenOn="true" />
    <TextView
        android:id="@+id/launcher_wifi_top_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:gravity="center"
        android:layout_marginTop="36dp"
        android:visibility="gone"
        android:layout_gravity="center_horizontal"
        android:textColor="@android:color/white"
        android:background="@drawable/ap32_launcher_wifi_connect_top_bg"
       />
    <!-- dialog 父view-->
    <FrameLayout
        android:id="@+id/flyt_stratosphere_dialog"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
<View
    android:visibility="gone"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/viewtouch"
    ></View>

<!--    <Button-->
<!--        android:id="@+id/debug_send_msg"-->
<!--        android:text="发消息"-->
<!--        android:layout_width="360dp"-->
<!--        android:layout_height="360dp"-->
<!--        android:background="#00ff00"-->
<!--        />-->
</FrameLayout>