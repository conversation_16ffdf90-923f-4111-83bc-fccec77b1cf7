<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bluetooth_root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/haiwai_launcher_transparent_20">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:background="@drawable/haiwai_launcher_bluetooth_dialog_bg"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="15dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/haiwai_launcher_bluetooth_dialog_title"
            android:textColor="@android:color/white"
            android:textSize="12sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/haiwai_launcher_bluetooth_dialog_txt1"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:text="@string/haiwai_launcher_bluetooth_dialog_txt2"
            android:textColor="@color/haiwai_launcher_bluetooth_dialog_txt2_color"
            android:textSize="10sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/haiwai_launcher_bluetooth_dialog_txt3"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:text="@string/haiwai_launcher_bluetooth_dialog_txt4"
            android:textColor="@color/haiwai_launcher_bluetooth_dialog_txt2_color"
            android:textSize="10sp" />

        <!--        <LinearLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="24dp"-->
        <!--            android:layout_marginTop="10dp">-->

        <Button
            android:id="@+id/bluetooth_dialog_negative"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/haiwai_launcher_bluetooth_dialog_btn_bg"
            android:gravity="center"
            android:text="@string/haiwai_launcher_bluetooth_dialog_negative"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            tools:ignore="SmallSp" />

        <!--            <Button-->
        <!--                android:id="@+id/bluetooth_dialog_positive"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginStart="10dp"-->
        <!--                android:layout_weight="1"-->
        <!--                android:background="@drawable/haiwai_launcher_bluetooth_dialog_btn_bg"-->
        <!--                android:gravity="center"-->
        <!--                android:text="@string/haiwai_launcher_bluetooth_dialog_positive"-->
        <!--                android:textColor="@android:color/white"-->
        <!--                android:textSize="10sp"-->
        <!--                tools:ignore="SmallSp" />-->
        <!--        </LinearLayout>-->
    </LinearLayout>

</RelativeLayout>