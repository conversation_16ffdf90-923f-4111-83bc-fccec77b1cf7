<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/dialogRootView"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/group_status_bar"
        android:layout_width="156dp"
        android:layout_height="35dp"
        android:layout_gravity="start"
        android:background="@drawable/haiwai_launcher_phont_adapter_group_bg_rectangle_a2r"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/group_action"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:background="@drawable/haiwai_launcher_phont_adapter_group_more_open_a2r"
            android:contentDescription="@android:string/unknownName" />

        <ImageView
            android:id="@+id/car_home_action"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_marginStart="6dp"
            android:background="@drawable/haiwai_launcher_phont_adapte_group_home_car_a2r"
            android:contentDescription="@android:string/unknownName"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/phone_home_action"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_marginStart="6dp"
            android:background="@drawable/haiwai_launcher_phont_adapter_group_home_phone_a2r"
            android:contentDescription="@android:string/unknownName"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/back_action"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_marginStart="6dp"
            android:background="@drawable/haiwai_launcher_phont_adapter_group_back_a2r"
            android:contentDescription="@android:string/unknownName"
            android:visibility="visible" />
    </LinearLayout>
</FrameLayout>
