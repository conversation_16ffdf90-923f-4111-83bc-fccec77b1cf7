<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F6F8FA">

    <RelativeLayout
        android:id="@+id/welink1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/haiwai_launcher_phont_adapter_welink_bg">

        <ImageView
            android:id="@+id/btn_goto_about"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="23dp"
            android:paddingTop="23dp"
            android:paddingEnd="23dp"
            android:scaleType="fitEnd"
            android:src="@drawable/haiwai_launcher_phont_adapter_about_icon"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_below="@id/btn_goto_about"
            android:layout_marginStart="25dp"
            android:layout_toStartOf="@+id/top_img"
            android:gravity="center_vertical"
            android:lineSpacingExtra="6dp"
            android:text="@string/haiwai_launcher_app_tips1"
            android:textColor="#FFFFFF"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/top_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/btn_goto_about"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="9.5dp"
            android:layout_marginEnd="25dp"
            android:src="@drawable/haiwai_launcher_phont_adapter_welink_link"
            tools:ignore="ContentDescription" />

    </RelativeLayout>

    <TextView
        android:id="@+id/welink3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/welink1"
        android:layout_marginStart="18dp"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="18dp"
        android:text="@string/haiwai_launcher_welink_main1"
        android:textColor="#000000"
        android:textSize="15sp" />

    <LinearLayout
        android:id="@+id/welink_btns"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/welink3"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/btn_goto_usb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="15dp"
            android:layout_marginTop="11dp"
            android:layout_marginEnd="7dp"
            android:layout_weight="1"
            android:background="@drawable/haiwai_launcher_main_item_bg"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="12dp"
            android:paddingEnd="10dp"
            android:paddingBottom="22dp">

            <TextView
                android:id="@+id/tv_usb"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_marginTop="15dp"
                android:layout_toStartOf="@+id/icon_usb"
                android:gravity="center_vertical"
                android:text="@string/haiwai_launcher_app_btn_usb1"
                android:textColor="#000000"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_usb2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_usb"
                android:text="@string/haiwai_launcher_app_btn_usb2"
                android:textColor="#000000"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_usb3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_usb2"
                android:layout_marginTop="2dp"
                android:text="@string/haiwai_launcher_app_btn_usb3"
                android:textColor="#A3C8F9"
                android:textSize="12sp" />

            <ImageView
                android:id="@+id/icon_usb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="15dp"
                android:src="@drawable/haiwai_launcher_phont_adapter_usb_icon"
                tools:ignore="ContentDescription" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/btn_goto_permission"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="7dp"
            android:layout_marginTop="11dp"
            android:layout_marginEnd="15dp"
            android:layout_weight="1"
            android:background="@drawable/haiwai_launcher_main_item_bg"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="12dp"
            android:paddingEnd="10dp"
            android:paddingBottom="22dp">

            <TextView
                android:id="@+id/tv_permit"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_marginTop="15dp"
                android:layout_toStartOf="@+id/icon_permit"
                android:gravity="center_vertical"
                android:text="@string/haiwai_launcher_app_btn_permit1"
                android:textColor="#000000"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_permit2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_permit"
                android:layout_marginTop="1dp"
                android:text="@string/haiwai_launcher_app_btn_permit2"
                android:textColor="#000000"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_permit3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_permit2"
                android:layout_marginTop="2dp"
                android:text="@string/haiwai_launcher_app_btn_permit3"
                android:textColor="#A3C8F9"
                android:textSize="12sp" />

            <ImageView
                android:id="@+id/icon_permit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="15dp"
                android:src="@drawable/haiwai_launcher_phont_adapter_permit_icon"
                tools:ignore="ContentDescription" />
        </RelativeLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/btn_goto_notice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:text="@string/haiwai_launcher_disclaimer_notice"
        android:textColor="#0000FF"
        android:visibility="invisible" />

</RelativeLayout>