<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fragment_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/haiwai_launcher_bg_car"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:scaleType="centerInside"
        android:src="@mipmap/haiwai_launcher_welink_ic"
        android:textColor="@android:color/white"
        tools:ignore="ContentDescription" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content"
        android:textColor="@android:color/white"
        android:textSize="14sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="15dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content2"
        android:textColor="@android:color/white"
        android:textSize="14sp" />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/command_test_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone">

        <Button
            android:id="@+id/screen_off"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="手机锁屏"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <Button
            android:id="@+id/screen_on"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="手机解锁"
            android:textSize="14sp"
            app:layout_constraintStart_toEndOf="@+id/screen_off"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />
        <Button
            android:id="@+id/stream_abort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="投屏中断"
            android:textSize="14sp"
            app:layout_constraintStart_toEndOf="@+id/screen_on"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />
        <Button
            android:id="@+id/stream_recovery"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="视频流恢复"
            android:textSize="14sp"
            app:layout_constraintStart_toEndOf="@+id/stream_abort"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>