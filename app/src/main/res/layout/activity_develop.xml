<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/rl"
    android:background="#ccffffff">
    <View
        android:id="@+id/view_back"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/ic_back" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/view_back"
        android:orientation="vertical">
        <RelativeLayout android:id="@+id/ll_authenticate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="自动录屏授权"
                android:textColor="#646464"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_authenticate"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_toStartOf="@+id/tv_authenticate"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_authenticate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="10dp"
                android:text="开"
                android:textColor="#646464"
                android:textSize="16sp" />

        </RelativeLayout>

        <RelativeLayout android:id="@+id/ll_driving_safety"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingStart="20dp"
            android:paddingEnd="10dp"
            android:paddingBottom="10dp">

            <TextView
                android:id="@+id/tv_driving_safety"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="行车安全走行规制"
                android:textColor="#646464"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_driving_safety"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:checked="true"
                android:layout_toStartOf="@+id/tv_driving_safety_all"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_driving_safety_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_alignParentEnd="true"
                android:text="全部打开"
                android:textColor="#646464"
                android:textSize="16sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_driving_safety_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="40dp"
            android:paddingTop="10dp"
            android:paddingEnd="10dp"
            android:paddingBottom="10dp"
            android:visibility="visible" >

            <Switch
                android:id="@+id/switch_driving_safety_online"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_toStartOf="@+id/tv_driving_safety_online_status"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <Switch
                android:id="@+id/switch_driving_safety_online_opencv"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_below="@+id/tv_driving_safety_online"
                android:layout_alignTop="@+id/tv_driving_safety_online_opencv"
                android:layout_toStartOf="@+id/tv_driving_safety_online_opencv_status"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <Switch
                android:id="@+id/switch_driving_safety_offline"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_below="@+id/switch_driving_safety_online_opencv"
                android:layout_alignTop="@+id/tv_driving_safety_offline"
                android:layout_toStartOf="@+id/tv_driving_safety_offline_status"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_driving_safety_online_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="10dp"
                android:text="开"
                android:textColor="#646464"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_driving_safety_online_opencv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_driving_safety_online"
                android:layout_alignTop="@+id/tv_driving_safety_online_opencv"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="10dp"
                android:text="开"
                android:textColor="#646464"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_driving_safety_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_driving_safety_online_opencv"
                android:layout_marginTop="20dp"
                android:text="离线识别"
                android:textColor="#646464"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_driving_safety_online"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="是否将声音作为检测条件"
                android:textColor="#646464"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_driving_safety_online_opencv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_driving_safety_online"
                android:layout_marginTop="20dp"
                android:text="是否录制视频(重启后生效)"
                android:textColor="#646464"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_driving_safety_offline_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_driving_safety_online_opencv"
                android:layout_alignTop="@+id/tv_driving_safety_offline"
                android:layout_marginStart="10dp"
                android:text="开"
                android:layout_alignParentEnd="true"
                android:textColor="#646464"
                android:textSize="16sp" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:visibility="visible"
            android:gravity="center" >

            <Switch
                android:id="@+id/switch_log"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_toStartOf="@+id/tv_log_status"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_log_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开"
                android:layout_marginStart="10dp"
                android:layout_alignParentEnd="true"
                android:textColor="#646464"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_log"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="log日志状态"
                android:textColor="#646464"
                android:textSize="16sp" />
            <RadioGroup
                android:id="@+id/rg_log"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_log"
                android:paddingTop="10dp"
                android:paddingStart="10dp"
                android:orientation="vertical"
                android:visibility="visible">
                <RadioButton android:id="@+id/rb_log_v"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" 全部日志   "
                    android:gravity="center"
                    android:textColor="#646464"
                    android:textSize="16sp" />
                <RadioButton android:id="@+id/rb_log_d"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" log等级 W E   "
                    android:gravity="center"
                    android:textColor="#646464"
                    android:textSize="16sp" />
                <RadioButton android:id="@+id/rb_log_i"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:gravity="center"
                    android:text=" log等级 D   "
                    android:textColor="#646464"
                    android:textSize="16sp" />
                <RadioButton android:id="@+id/rb_log_w"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" log等级 w   "
                    android:gravity="center"
                    android:visibility="gone"
                    android:textColor="#646464"
                    android:textSize="16sp" />
                <RadioButton android:id="@+id/rb_log_e"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:gravity="center"
                    android:visibility="gone"
                    android:text=" log等级 e   "
                    android:textColor="#646464"
                    android:textSize="16sp" />
            </RadioGroup>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:visibility="visible"
            android:gravity="left" >

            <TextView
                android:id="@+id/tv_fps"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="FPS"
                android:textColor="#646464"
                android:textSize="16sp" />
            <RadioGroup
                android:id="@+id/rg_fps"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_fps"
                android:paddingTop="10dp"
                android:paddingStart="10dp"
                android:orientation="horizontal"
                android:visibility="visible">
                <RadioButton android:id="@+id/rb_fps_30"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text="30 "
                    android:gravity="center"
                    android:textColor="#646464"
                    android:textSize="16sp" />
                <RadioButton android:id="@+id/rb_fps_60"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_marginLeft="30dp"
                    android:text="60"
                    android:gravity="center"
                    android:textColor="#646464"
                    android:textSize="16sp" />

            </RadioGroup>
        </RelativeLayout>
    </LinearLayout>


</RelativeLayout>