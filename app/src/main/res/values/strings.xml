<resources>
    <string name="haiwai_launcher_app_name">GAZ Link</string>
    <string name="haiwai_launcher_welink_main1">You need do as follows to enjoy the service:</string>
    <string name="haiwai_launcher_text_notice">

Please read carfully and understand well on this statement before you use GAZ Link. Once used GAZ Link, it represents you have received all items of GAZ Link Disclaimer.\n\n
1. Do not operate this product during driving, avoiding accident or other dangerous condition. Otherwise the safety accident caused by it shall be beared by itself.\n\n
2. GAZ Link respects the personal privacy of all users and GAZ Link will not collect any of your personal information.\n\n
3. All service GAZ Link supplied are free, also it is not involved phone, data access or message related services that might need to pay.\n\n
4. Please read  the third party\'s relevant policies carefully, it represents you recognize and agree the third-party service terms when using third party product and service through GAZ Link projection. GAZ Link is not responsible for products and services provided by the third- parties.\n\n

    </string>
    <string name="haiwai_launcher_notice_url"><u>Disclaimer</u></string>
    <string name="haiwai_launcher_app_tips1">All Apps in your phone can be displayed and able to be operated on head unit.</string>
    <string name="haiwai_launcher_app_btn_usb1">Connect HU</string>
    <string name="haiwai_launcher_app_btn_usb2">Connect Help</string>
    <string name="haiwai_launcher_app_btn_usb3">Step 1</string>
    <string name="haiwai_launcher_app_btn_permit1">Projection\nauthorization</string>
    <string name="haiwai_launcher_app_btn_permit2">Authorize Help</string>
    <string name="haiwai_launcher_app_btn_permit3">Step 2</string>
    <string name="haiwai_launcher_about">About</string>
    <string name="haiwai_launcher_version">V%s</string>
    <string name="haiwai_launcher_all_rights_reserved">©2020 All Rights reserved by Beijing Autoai Tech. Co, Ltd</string>
    <string name="haiwai_launcher_authorize_notice">Projection Authorization</string>
    <string name="haiwai_launcher_permission_notice_1st">1. Allow GAZ Link to project on phone.</string>
    <string name="haiwai_launcher_permission_notice_2st">2. Turn on GAZ Link "Accessibility mode" or "barrior free mode" on phone side</string>
    <string name="haiwai_launcher_usb_notice">USB connect Instruction</string>
    <string name="haiwai_launcher_connect_notice_1st">1. Connect MU and HU through USB cable</string>
    <string name="haiwai_launcher_connect_notice_2st">2. Click confirm when Mobile phone appear the following prompt (It will automatically connect when choose default, more convenient)</string>
    <string name="haiwai_launcher_disclaimer_notice">Disclaimer</string>

    <!--    车机端文案-->
    <string name="haiwai_launcher_mirroring_permission_title">Obtaining projection authorization….</string>
    <string name="haiwai_launcher_mirroring_permission_content">Please ensure phone unlock, if with pop-up,\nplease choose confirm or start now</string>
    <string name="haiwai_launcher_mirroring_permission_content2">Ensure safety, please do not use phone\nduring driving</string>
    <string name="haiwai_launcher_mirroring_permission_cancel_title">Already cancel projection service</string>
    <string name="haiwai_launcher_mirroring_permission_cancel_content">If want to restart projection service,\nplease re-connect USB cable.</string>
    <string name="haiwai_launcher_auxiliary_title">Obtaining the accessibility permission….</string>
    <string name="haiwai_launcher_auxiliary_content">Please open GAZ Link "accessibility" or "barrior free" mode in the phone settings.</string>
    <string name="haiwai_launcher_auxiliary_content2">To keep safety, do not use phone during driving</string>
    <string name="haiwai_launcher_start_screen_content">Connect successfully, please operate\nthe content you want to project.</string>
    <string name="haiwai_launcher_start_screen_content2">To ensure projection, please keep phone unlock\nstate;To keep safety, please do not use phone\nduring driving.</string>

    <string name="haiwai_launcher_bluetooth_dialog_title">To ensure audio service,\nplease match Bluetooth correctly.</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt1">1. Turn on the Bluetooth of the car</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt2">System Settings>Bluetooth>Click the \"+\"\nbutton in the device list</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt3">2. Turn on the phone\'s Bluetooth</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt4">Phone Settings>Start Bluetooth function>Find device\nconnection>Complete Bluetooth connection\n(Subject to actual mobile phone)</string>
    <string name="haiwai_launcher_bluetooth_dialog_negative">Cancel</string>
    <string name="haiwai_launcher_bluetooth_dialog_positive">Settings</string>
    <string name="haiwai_launcher_next">下次不再提醒</string>
    <string name="haiwai_launcher_agreeand">同意并继续</string>
    <string name="haiwai_launcher_agree">同意</string>
    <string name="haiwai_launcher_noagree">不同意</string>
    <string name="haiwai_launcher_exit_the_application">退出应用</string>
    <string name="haiwai_launcher_privacypolicy">隐私政策</string>
    <string name="haiwai_launcher_reminder">温馨提示</string>
    <string name="haiwai_launcher_screen_casting_failed">投屏失败</string>
    <string name="haiwai_launcher_screen_casting_failed_other">投屏失败，请尝试重新操作。</string>
    <string name="haiwai_launcher_confirm">确认</string>
    <string name="haiwai_launcher_new_disclaimers">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;在您使用WeLink前，请您仔细阅读并透彻理解本声明内容，一旦您使用WeLink，即表示您接受WeLink免责声明中全部条款。\n
1.请勿在驾驶中操作本产品，以防发生交通事故或其他危险情况。否则由此造成的安全事故应自行承担责。\n
2.WeLink尊重所有用户的个人隐私权，WeLink不会收集您的任何个人信息。\n
3.WeLink为您提供的服务均为免费，亦不涉及电话数据访问或短信等相关可能需要付费的服务。\n
4.通过WeLink投屏使用第三方产品和服务表示您认可并同意第三方的服务条款，请您仔细阅读第三方相关政策。对第三方提供的产品和服务，WeLink不承担任何责任。\n
    如您已阅读并同意WeLink基本功能隐私政策，请点击“同意”，开始使用我们的产品及服务！如您不同意WeLink基本功能隐私政策，可点击“不同意”退出应用。
    </string>
    <string name="haiwai_launcher_linkbasicfunctionprivacypolicy">WeLink基本功能隐私政策</string>
    <string name="haiwai_launcher_agreelinkbasicfunctionprivacypolicy">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;需同意WeLink基本功能隐私政策我们才能继续为你提供完整服务。我们尊重你的隐私与选择,更好的体验与完整功能依赖你的授权。我们仅收集必要的信息,不会对你的利益造成损害。\n 如果你不同意提供必要的授权,很遗憾,你将无法使用所有相关内容。
</string>

    <!--wifi 无感互联提示语-->
    <string name="haiwai_launcher_wifi_connect_unsupported">设备不支持无感连接，请尝试USB或者WIfi连接</string>
    <string name="haiwai_launcher_wifi_no_blue_content">没有打开蓝牙，请打开后尝试重新连接</string>
    <string name="haiwai_launcher_wifi_no_open_content">没有打开Wi-Fi，请打开后尝试重新连接</string>
    <string name="haiwai_launcher_wifi_local_content">当前Welink没有定位权限，请授权后重试，或者选择通过USB线或Wi-Fi连接</string>
    <string name="haiwai_launcher_wifi_server_ex_content">当前服务发生异常，请尝试USB或者WIfi连接</string>
    <string name="haiwai_launcher_wifi_failed_content">创建Wi-Fi Direct GO失败，请尝试USB或者WIfi连接</string>


    <string name="haiwai_launcher_wifi_connect_scanning">正在扫描车辆设备...</string>
    <string name="haiwai_launcher_wifi_connect_connecting">已发现车辆设备，正在连接...</string>
    <string name="haiwai_launcher_wifi_connect_data_transmission">车辆设备连接成功，数据传输中...</string>

    <string name="haiwai_launcher_about_difficult">疑难解答</string>
    <string name="haiwai_launcher_eixt">再按一次退出应用</string>

    <string name="haiwai_launcher_searching_for_car_infotainment_system">正在搜索车机...</string>
    <string name="haiwai_launcher_connecting_car_machine">正在连接车机...</string>
    <string name="haiwai_launcher_connection_successful">连接成功！</string>
</resources>
