<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- progress涉及的style -->
    <declare-styleable name="NumberProgressBar">
        <attr name="progress" format="integer" />
        <attr name="max" format="integer" />
        <attr name="progress_unreached_color" format="color" />
        <attr name="progress_reached_color" format="color" />
        <attr name="progress_reached_bar_height" format="dimension" />
        <attr name="progress_unreached_bar_height" format="dimension" />
        <attr name="progress_text_size" format="dimension" />
        <attr name="progress_text_color" format="color" />
        <attr name="progress_text_offset" format="dimension" />
        <attr name="progress_text_visibility" format="enum">
            <enum name="visible" value="0" />
            <enum name="invisible" value="1" />
        </attr>
    </declare-styleable>
    <declare-styleable name="HookCheckBox">
        <!-- 选中时圆的颜色 -->
        <attr name="hcb_check_circle_color" format="color" />
        <!-- 未选中时圆的颜色 -->
        <attr name="hcb_uncheck_circle_color" format="color" />
        <!-- 选中时的钩子的颜色 -->
        <attr name="hcb_check_hook_color" format="color" />
        <!-- 未选中时的钩子的颜色 -->
        <attr name="hcb_uncheck_hook_color" format="color" />
        <!-- 是否选中 -->
        <attr name="hcb_is_check" format="boolean" />
        <!-- 风格 -->
        <attr name="hcb_style" format="enum">
            <!-- 普通，对勾是有颜色的 -->
            <enum name="normal" value="1" />
            <!-- 镂空，就是对勾是透明的 -->
            <enum name="hollow_out" value="2" />
        </attr>
        <!-- 线宽 -->
        <attr name="hcb_line_width" format="float|dimension|reference" />
    </declare-styleable>
</resources>