<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="base_launcher_LauncherTheme" parent="Theme.AppCompat.Light.NoActionBar">
       <!-- <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>-->

    </style>
    <style name="NumberProgressBar_Default">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
        <item name="max">100</item>
        <item name="progress">0</item>
        <item name="progress_unreached_color">#ff6900</item>
        <item name="progress_reached_color">#ff6900</item>
        <item name="progress_text_visibility">invisible</item>
        <item name="progress_text_size">0sp</item>
        <item name="progress_text_color">#00a2ff</item>
        <item name="progress_reached_bar_height">1dp</item>
        <item name="progress_unreached_bar_height">0dp</item>
    </style>
    <!--仿照ios风格的Dialog-->
    <style name="MainiosDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item><!--取消默认Dialog的windowFrame框-->
        <item name="android:windowNoTitle">true</item><!--设置无标题Dialog-->
        <item name="android:backgroundDimEnabled">true</item><!--是否四周变暗-->
        <item name="android:windowIsFloating">true</item><!-- 是否悬浮在activity上 -->
        <item name="android:windowContentOverlay">@null</item><!-- 取消默认ContentOverlay背景 -->
        <item name="android:windowBackground">@android:color/transparent
        </item><!--取消window默认背景 不然四角会有黑影-->
        <item name="android:windowMinWidthMajor">100%</item>
        <item name="android:windowMinWidthMinor">100%</item>
    </style>
</resources>