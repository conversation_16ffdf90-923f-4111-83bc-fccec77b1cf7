<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" >
    <item>
        <shape>
            <solid android:color="#fff" />
            <corners
                android:topRightRadius="24dp"
                android:topLeftRadius="24dp" />
        </shape>
    </item>
    <!--这里的right和bottom表示的是这一层图片的右边距和下边距，当然还有left和top-->
    <item android:right="1dp" android:left="1dp" android:top="1dp">
        <shape>
            <solid android:color="#fff" />
            <corners
                android:topRightRadius="24dp"
                android:topLeftRadius="24dp"  />
        </shape>
    </item>
</layer-list>