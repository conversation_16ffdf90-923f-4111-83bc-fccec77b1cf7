<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.a2ra9k.android.launcher">

<!--    <uses-permission android:name="android.permission.INTERNET" />-->

    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"></uses-permission>

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.LOCAL_MAC_ADDRESS" />
    <uses-permission android:name="android.hardware.usb.accessory" />
    <!--三投platform-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!--添加 适配14权限-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>



    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-feature android:name="android.hardwware.wifi.direct" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- 添加 WAKE_LOCK 权限，保持互联的时候屏幕不锁屏 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <application
        android:name="com.a2ra9k.android.launcher.HaiwaiApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:icon="@mipmap/haiwai_launcher_welink_ic"
        android:label="@string/haiwai_launcher_app_name"
        android:roundIcon="@mipmap/haiwai_launcher_welink_ic"
        android:supportsRtl="true"
        android:theme="@style/base_launcher_LauncherTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:theme,android:appComponentFactory"
        tools:targetApi="p">
        <activity
            android:name="com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity"
            android:alwaysRetainTaskState="true"
            android:clearTaskOnLaunch="false"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|fontScale|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:resizeableActivity="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustResize"
            tools:ignore="LockedOrientationActivity,NonResizeableActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <!-- category android:name="android.intent.category.HOME" / -->
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED"
                android:resource="@xml/haiwai_launcher_accessory_filter" />

            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.VIEW" />-->
            <!--                <category android:name="android.intent.category.DEFAULT" />-->
            <!--                <data android:scheme="wedrive.home" />-->
            <!--            </intent-filter>-->
        </activity>
    </application>
</manifest>
