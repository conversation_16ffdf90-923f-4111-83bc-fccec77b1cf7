package com.autoai.welink.network.converter

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import okhttp3.RequestBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type

/**
 * 将对象转换成 @Field/@FieldMap 参数，适用于表单方式的提交
 */
class FieldConverterFactory private constructor(private val gson: Gson, private val type: Type) : Converter.Factory() {
    override fun requestBodyConverter(
        type: Type,
        parameterAnnotations: Array<Annotation>,
        methodAnnotations: Array<Annotation>,
        retrofit: Retrofit
    ): Converter<*, RequestBody>? {
        if (this.type == type) {
            val adapter = gson.getAdapter(TypeToken.get(type))
            return FieldRequestBodyConverter(gson, adapter)
        }
        return null
    }

    companion object {
        @JvmOverloads
        fun create(type: Type, gson: Gson? = Gson()): FieldConverterFactory {
            if (gson == null) throw NullPointerException("gson == null")
            return FieldConverterFactory(gson, type)
        }
    }
}
