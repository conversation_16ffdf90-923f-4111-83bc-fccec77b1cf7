package com.autoai.welink.network.retrofit

import androidx.core.os.trace
import com.a2ra9k.android.launcher.BuildConfig
import com.autoai.welink.network.LicenseNetworkDataSource
import com.autoai.welink.network.converter.FieldConverterFactory
import com.autoai.welink.network.di.NetworkModule
import com.autoai.welink.network.model.LicenseWelinkActivateData
import com.autoai.welink.network.params.IPostObjectToFieldMap
import com.autoai.welink.network.params.LicenseWelinkActivateRequest
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST


private interface LicenseRetrofitNetworkApi {
    /**
     * @see <a href="https://navinfo.feishu.cn/docx/HwiSdFKZIou699xFsAoc046nnzh#share-JVEKd0Mmoo5kRcxHjmgc5HRYnjs">WeLink鉴权检查</a>
     */
    @POST("agent/license/licenseManage/welinkActivate")
    suspend fun welinkActivate(@Header("x-cid") cid:String, @Body request: LicenseWelinkActivateRequest): ResponseProtocol<LicenseWelinkActivateData>
}

const val LICENSE_TEST_URL = "http://test-wecockpit.autoai.com/"
const val LICENSE_RELEASE_URL = "http://beta-wecockpit-api.autoai.com/"
val LICENSE_BASE_URL = if(BuildConfig.DEBUG) LICENSE_TEST_URL else LICENSE_RELEASE_URL

object LicenseRetrofitNetwork : LicenseNetworkDataSource {
    private val licenseNetworkApi = trace("RetrofitLicenseNetwork") {
        Retrofit.Builder()
            .baseUrl(LICENSE_BASE_URL)
            .callFactory(NetworkModule.licenseOkHttpCallFactory())
            .addConverterFactory(FieldConverterFactory.create(IPostObjectToFieldMap::class.java)) //正常用不到了
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(LicenseRetrofitNetworkApi::class.java)
    }

    override suspend fun welinkActivate(request: LicenseWelinkActivateRequest): ResponseProtocol<LicenseWelinkActivateData> {
        return licenseNetworkApi.welinkActivate(request.vin!!, request)
    }
}