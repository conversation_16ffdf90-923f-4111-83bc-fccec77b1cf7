package com.autoai.welink.network

import com.autoai.welink.network.model.LicenseWelinkActivateData
import com.autoai.welink.network.params.LicenseWelinkActivateRequest
import com.autoai.welink.network.retrofit.ResponseProtocol

/**
 * Interface representing network calls to the NIA backend
 */
interface LicenseNetworkDataSource {
    /**
     * 2. 设备激活检查接口
     */
    suspend fun welinkActivate(request: LicenseWelinkActivateRequest): ResponseProtocol<LicenseWelinkActivateData>

}
