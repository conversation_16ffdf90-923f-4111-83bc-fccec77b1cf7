package com.autoai.welink.network.params

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

/**
 * 2. 设备激活检查接口
 */
@Serializable
data class LicenseWelinkActivateRequest(
    @SerializedName("tenantCode") val tenantCode: String,
    @SerializedName("projectCode") val projectCode: String,
    @SerializedName("vin") val vin: String?,
    @SerializedName("imei") val imei: String,
    @SerializedName("mac") val mac: String?,
)
