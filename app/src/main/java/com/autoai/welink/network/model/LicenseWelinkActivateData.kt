package com.autoai.welink.network.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

/**
 * 2. 设备激活检查接口
 */
@Serializable
data class LicenseWelinkActivateData(
    @SerializedName("id") val id: Int,
    @SerializedName("deviceStatus") val deviceStatus: Int,
    @SerializedName("validTime") val validTime: String,
){
    override fun toString(): String {
        return "LicenseWelinkActivateData(id=$id, deviceStatus=$deviceStatus, validTime='$validTime')"
    }
}
