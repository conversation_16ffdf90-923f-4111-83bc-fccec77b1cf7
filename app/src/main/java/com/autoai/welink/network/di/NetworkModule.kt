package com.autoai.welink.network.di
import androidx.core.os.trace
import com.autoai.welink.network.interceptro.LicenseHeaderInterceptor
import okhttp3.Call
import okhttp3.ConnectionPool
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor

internal object NetworkModule {
    private val connectionPool: ConnectionPool = ConnectionPool()

    fun licenseOkHttpCallFactory(): Call.Factory = trace("LicenseOkHttpClient") {
        OkHttpClient.Builder()
            .addInterceptor(LicenseHeaderInterceptor())
            .addInterceptor(HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
            .connectionPool(connectionPool)
            .build()
    }
}
