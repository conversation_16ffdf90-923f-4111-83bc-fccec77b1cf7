package com.autoai.welink.network.util

import android.util.Log

/**
 * 日志写文件和 logcat 输出
 * <AUTHOR>
 */
class LinkLog {
    companion object {
        var mDebugModeEnabled: Boolean = false

        /**
         * 该日志级别现在不会写入文件
         */
        @Deprecated("该日志级别 apollo 配置不会写入文件")
        @JvmStatic
        fun d(tag: String, msg: String) {
//            MR2Log.d(tag, msg)
            if (!mDebugModeEnabled) {
                Log.d(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        fun i(tag: String, msg: String) {
//            MR2Log.i(tag, msg)
            if (!mDebugModeEnabled) {
                Log.i(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        fun w(tag: String, msg: String) {
//            MR2Log.w(tag, msg)
            if (!mDebugModeEnabled) {
                Log.w(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        fun e(tag: String, msg: String) {
//            MR2Log.e(tag, msg)
            if (!mDebugModeEnabled) {
                Log.e(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        @JvmOverloads
        fun e(tag: String, msg: Throwable, msgPrefix: String = "") {
//            MR2Log.e(tag, msg)
            if (!mDebugModeEnabled) {
                Log.e(tag, msgPrefix + msg.stackTraceToString())
            }
        }

        private fun applyThreadNameIfEnable(msg: String): String {
            return "#${Thread.currentThread().name}: $msg"
        }
    }
}