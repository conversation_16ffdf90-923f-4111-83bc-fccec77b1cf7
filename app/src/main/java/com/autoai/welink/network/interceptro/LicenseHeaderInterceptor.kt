package com.autoai.welink.network.interceptro

import com.autoai.welink.network.helper.DeviceInfoHelper
import okhttp3.Interceptor
import okhttp3.Request.Builder
import okhttp3.Response

/**
 * 添加公共参数 https://navinfo.feishu.cn/docx/HwiSdFKZIou699xFsAoc046nnzh#share-FJM2dDduGoxzd8xhfwrcp4mwn8o
 */
internal class LicenseHeaderInterceptor: Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        return chain.proceed(
            chain.request().newBuilder()
                .addCommonHeader()
                .build())
    }

    private fun Builder.addCommonHeader(): Builder{
        return addHeader("x-v", "1.0")
//            .addHeader("x-cid", DeviceInfoHelper.getCidFromVinOrAndroidIdOrMacAdd())
            .addHeader("x-client", "vehicle")
            .addHeader("x-ts", System.currentTimeMillis().toString())
//            .addHeader("x-token", "") //非必须
//            .addHeader("x-guid", "") //非必须
            .addHeader("x-sign", "sign")//TODO:bruce 签名实现
            .addHeader("x-channel", "wecockpit")
            .addHeader("x-tenant", "wecockpit")
    }
}