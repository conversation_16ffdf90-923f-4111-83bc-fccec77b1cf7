package com.a2ra9k.android.launcher.service;

import android.content.Context;

import com.a2ra9k.android.launcher.service.impl.HUCommandImpl;
import com.a2ra9k.android.launcher.service.impl.LimitScreenImpl;
import com.autoai.fundrive.basemodule.singleton.Singleton;
import com.autoai.fundrive.commontool.LogManager;

public class HaiwaiService implements Singleton {

    public static final String NAME = HaiwaiService.class.getSimpleName();
    private HUCommandImpl mHUCommandImpl = null;
    private LimitScreenImpl mLimitScreenImpl = null;
    private OverlayDialogManager mOverlayDialogManager = null;
//    private BluetoothHelper mBluetoothHelper;

    public void startServiceImpl() {
        LogManager.d("start HaiwaiService");
        mHUCommandImpl.startHUCommand();
        mLimitScreenImpl.startLimitScreen();

        // 发送设备信息包含屏幕超时设置给车机
        sendDeviceInfoToCar();
    }

    /**
     * 发送设备信息包含屏幕超时设置给车机
     */
    public void sendDeviceInfoToCar() {
        if (mHUCommandImpl != null) {
            // 延迟发送，确保连接已建立
            android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            handler.postDelayed(() -> {
                mHUCommandImpl.sendDeviceInfoWithScreenTimeout();
            }, 1000); // 延迟1秒发送
        }
    }
   /* public void startLimitScreen() {
        LogManager.d("start startLimitScreen");
     mLimitScreenImpl.startLimitScreen();
    }*/

    public LimitScreenImpl getLimitScreen() {
        return mLimitScreenImpl;
    }

    public HUCommandImpl getHUCommand() {
        return mHUCommandImpl;
    }

    public OverlayDialogManager getDialogManager() {
        return mOverlayDialogManager;
    }

//    public BluetoothHelper getBluetoothService() {
//        return mBluetoothHelper;
//    }

    @Override
    public void loadService(Context context) {
        LogManager.d("loadService");
        mOverlayDialogManager = new OverlayDialogManager();
        mHUCommandImpl = new HUCommandImpl(context);
//        mBluetoothHelper = new BluetoothHelper(mOverlayDialogManager);
//        mLimitScreenImpl = new LimitScreenImpl( mBluetoothHelper,mOverlayDialogManager);
        mLimitScreenImpl = new LimitScreenImpl(context, mOverlayDialogManager);
    }

    @Override
    public void unloadService() {
        if (mHUCommandImpl != null) {
            mHUCommandImpl.stopHUCommand();
        }
        if (mLimitScreenImpl != null) {
            mLimitScreenImpl.stopLimitScreen();
        }
        LogManager.d("unloadService");
    }

    /**
     * 是否投屏
     *
     * @return boolean
     */
    public boolean screenProjectionStatus() {
        LogManager.d("screenProjectionStatus = " + mLimitScreenImpl);
        return mLimitScreenImpl != null && mLimitScreenImpl.isStartScreen();
    }

    /**
     * 是否连接USB互联
     *
     * @return boolean
     */
    public boolean huLinkedStatus() {
        LogManager.d("huLinkedStatus = " + mLimitScreenImpl);
        return mLimitScreenImpl != null && mLimitScreenImpl.isLinked();
    }
}
