package com.a2ra9k.android.launcher.service.impl;


import static com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity.DialogInit;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;

import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.OverlayDialogManager;
import com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.protocol.WLProtocolConfig;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.fundrive.platform.protocol.bean.BaseProtocolBean;
import com.autoai.fundrive.platform.protocol.bean.BeginPointBean;
import com.autoai.fundrive.platform.protocol.bean.CarReadyBean;
import com.autoai.fundrive.platform.protocol.bean.EnAndChBean;
import com.autoai.fundrive.platform.protocol.bean.EndPointBean;
import com.autoai.fundrive.platform.protocol.bean.HardWareBean;
import com.autoai.fundrive.platform.protocol.bean.HuAppFront;
import com.autoai.fundrive.platform.protocol.bean.HuPhoneBtMacAddressBean;
import com.autoai.fundrive.platform.protocol.bean.HuReqKeyFrame;
import com.autoai.fundrive.platform.protocol.bean.PointReadyBean;
import com.autoai.fundrive.platform.protocol.bean.ReqPlayVideoBean;
import com.autoai.fundrive.platform.protocol.manager.PhoneBtMacAddressManager;
import com.autoai.fundrive.platform.protocol.listener.HUCommandListener;
import com.autoai.welink.logiclib.skincore.SkinManager;
import com.autoai.welink.logiclib.skincore.constant.DeviceType;
import com.autoai.welink.platform.WLPlatform;

import java.util.Locale;

public class HUCommandImpl implements HUCommandListener {

    public static Locale lastLocale;
    public static boolean isDialogSkinInied = false;
    private int controlState;
    private boolean itAtTheFrontDesk = true;

    private final Context mContext;
    private ScreenTimeoutObserver mScreenTimeoutObserver;
    public HUCommandImpl(Context context) {
        this.mContext = context;
        LogManager.d("HUCommandImpl construct");
    }

    public void startHUCommand() {
        LogManager.d("start startHUCommand");
        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_ENANDCH, this);
        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_KEY, this);
        //---> she tips 接收消息类型
        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_IS_PHONE_PLAY_VIDEO, this);

        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_ON_HU_REQ_KEY_FRAME, this);

        // 注册手机蓝牙MAC地址协议监听器
        initPhoneBtMacAddressListener();

        // 启动屏幕超时设置监听器
        startScreenTimeoutObserver();

//        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_BEGINPOINT, this);
//        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_ENDPOINT, this);
//        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_READYRSP, this);
//        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_CARREADY, this);

    }

    /**
     * 初始化手机蓝牙MAC地址协议监听器
     */
    private void initPhoneBtMacAddressListener() {
        try {
            WLProtocolManager.getInstance().addMethodListener(
                WLProtocolConfig.HU_PROTOCOL_METHOD_PHONE_BT_MAC_ADDRESS,
                    PhoneBtMacAddressManager.getInstance(mContext)
            );
            LogManager.i("Successfully registered onPhoneBTMacAddress protocol listener in HUCommandImpl");
        } catch (Exception e) {
            LogManager.e("Failed to register onPhoneBTMacAddress protocol listener: " + e.getMessage());
        }
    }

    @Override
    public void onReceiveCommand(BaseProtocolBean baseProtocolBean) {
        lastLocale = new Locale(Locale.getDefault().getLanguage());
        LogManager.i("onReceiveCommand baseProtocolBean " + baseProtocolBean);
        if (baseProtocolBean instanceof EnAndChBean) {
            EnAndChBean enAndChBean = (EnAndChBean) baseProtocolBean;
            int controlState = enAndChBean.getControlState();
            LocaleUtil.locale = getLocal(controlState);
            LogManager.e("onReceiveCommand changeSkin");
            LogManager.d("resetResource onReceiveCommand changeCarLanguage EnAndChBean controlState=" + controlState);
            LogManager.d("resetResource onReceiveCommand changeCarLanguage EnAndChBean locale=" + LocaleUtil.locale);
            Locale locale = new Locale(LocaleUtil.locale.getLanguage(), LocaleUtil.locale.getCountry());
            LogManager.d("resetResource onReceiveCommand changeCarLanguage EnAndChBean locale22 =" + locale);

            SkinManager.getInstance().changeSkin(DeviceType.SCREEN_PHONE, "", locale);
            if (HUCommandImpl.isDialogSkinInied) {
                SkinManager.getInstance().changeSkin(DeviceType.SCREEN_CAR, "", locale);
            }
            PageChangeUtils.sendToPage(-1, Configs.PAGE_MAIN, Configs.PAGE_MAIN, locale);
            HaiwaiService haiwaiService = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
            if (haiwaiService != null) {
                OverlayDialogManager dialogManager = haiwaiService.getDialogManager();
                if (dialogManager != null) {
                    dialogManager.updateLayoutDirection(locale);
                }
            }
        } else if (baseProtocolBean instanceof HardWareBean) {
            HardWareBean hdBean = (HardWareBean)  baseProtocolBean;
            int keyState = hdBean.getKeyState();
            //0，1 旋转屏幕方向
            //10 再次拉起投屏授权弹窗
            if (keyState < 10) {
                if (!Settings.canDrawOverlays(HaiwaiLauncherActivity.getInstance())) {
                    HaiwaiLauncherActivity.getInstance().checkPermission();
                } else  {
                    HaiwaiLauncherActivity.getInstance().setOrientation(keyState);
                }
            } else  {
                HaiwaiLauncherActivity.getInstance().reauthorizationRecordScreen();
            }

        //判断应用是否在前台
        } else if (baseProtocolBean instanceof CarReadyBean){
            itAtTheFrontDesk = HaiwaiLauncherActivity.getInstance().isItAtTheFrontDesk();
            //开始校准
        }else if (baseProtocolBean instanceof BeginPointBean){
            if (controlState!=-1){
                if (controlState == 0 && itAtTheFrontDesk) {
                    HaiwaiLauncherActivity.getInstance().setSpot();
                }
            }
            //结束校准
        }else if (baseProtocolBean instanceof EndPointBean){
            HaiwaiLauncherActivity.getInstance().setStopSpot();
        }
        //是否支持校准
        else if (baseProtocolBean instanceof PointReadyBean){
            PointReadyBean readyBean = (PointReadyBean)  baseProtocolBean;
            LogManager.e("PointReadyBean == PointReadyBean  =  "+ readyBean.getControlState() );
            controlState = HaiwaiLauncherActivity.getInstance().setReady(readyBean.getControlState());
        }
        else if (baseProtocolBean instanceof ReqPlayVideoBean){
            ReqPlayVideoBean reqPlayVideoBean = (ReqPlayVideoBean)  baseProtocolBean;
            LogManager.e("ReqPlayVideoBean == PointReadyBean  =  "+ reqPlayVideoBean.toString() );
            //todo:收到车机端消息：将当前视频流停掉
//            LogManager.e("收到车机端消息：获取当前帧！！！！！" );

            //todo 1: 做一个入口能够get到
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            //挡位变化，发送当前挡位状态
            platformManager.reqCurFrame(reqPlayVideoBean.action);
        }else if(baseProtocolBean instanceof HuReqKeyFrame){
            HuReqKeyFrame huReqKeyFrame = (HuReqKeyFrame) baseProtocolBean;
            LogManager.e("HuReqKeyFrame   =  "+ huReqKeyFrame.toString() );
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            //挡位变化，发送当前挡位状态
            platformManager.reqKeyFrame(huReqKeyFrame.getFrame(),huReqKeyFrame.getTime());
        }
    }

    public static Locale getLocal(int controlState) {
        Locale locale;
        switch (controlState) {
            case 2:
                locale = Locale.ENGLISH;
                break;
            case 3:
                //西班牙
                locale = new Locale("es", "ES");
                break;
            case 4:
                //印尼
                locale = new Locale("in", "ID");
                break;
            case 5:
                //阿拉伯
                locale = new Locale("ar", "AE");
                break;
            case 6:
                //葡萄牙语
                locale = new Locale("pt", "PT");
                break;
            case 7:
                //越南
                locale = new Locale("vi", "VN");
                break;
            case 8:
                // 泰语
                locale = new Locale("th", "TH");
                break;
            case 9:
                // 马来语
                locale = new Locale("ms", "MY");
                break;
            case 10:
                // 马来语
                locale = new Locale("ru", "RU");
                break;
            case 0:
                // 繁体中文

                locale = Locale.TAIWAN;
                break;
            default:
                locale = Locale.SIMPLIFIED_CHINESE;
                break;
        }
        return locale;
    }

    public static boolean arLanguageChanged() {
        if (LocaleUtil.locale != null && lastLocale != null) {
            String language = LocaleUtil.locale.getLanguage();
            String lastLanguage = lastLocale.getLanguage();
            boolean ar = TextUtils.equals(language, "ar");
            boolean lastAr = TextUtils.equals(lastLanguage, "ar");
            return ar || lastAr;
        }
        return false;
    }

    public void languageRecovery() {
        LogManager.d("languageRecovery lastLocale=" + lastLocale);
        if (lastLocale != null) {
            SkinManager.getInstance().changeSkin(DeviceType.SCREEN_ALL, "", lastLocale);
            PageChangeUtils.sendToPage(-1, Configs.PAGE_MAIN, Configs.PAGE_MAIN, lastLocale);
        }
    }

    /**
     * 发送设备信息包含屏幕超时设置给车机
     * 在连接建立或需要更新设备信息时调用
     */
    public void sendDeviceInfoWithScreenTimeout() {
        try {
            // 获取设备基本信息
            String model = Build.MODEL;
            String deviceName = Build.DEVICE;
            String deviceBrand = Build.BRAND;
            String bluetoothName = getBluetoothName();

            // 获取屏幕超时设置
            int screenTimeout = getScreenTimeout();

            // 发送设备信息给车机
            WLProtocolManager.getInstance().sendDeviceInfo(model, deviceName, deviceBrand, bluetoothName, screenTimeout);

            LogManager.i("sendDeviceInfoWithScreenTimeout: 已发送设备信息和屏幕超时设置给车机");
        } catch (Exception e) {
            LogManager.e("sendDeviceInfoWithScreenTimeout: 发送设备信息失败 " + e.getMessage());
        }
    }

    /**
     * 获取系统屏幕超时设置
     *
     * @return 屏幕超时时间(毫秒)，获取失败时返回默认值30秒
     */
    private int getScreenTimeout() {
        try {
            int timeout = Settings.System.getInt(mContext.getContentResolver(), Settings.System.SCREEN_OFF_TIMEOUT, 30000);
            LogManager.i("getScreenTimeout: 成功读取屏幕超时设置 timeout=" + timeout + "ms (" + (timeout/1000) + "秒)");
            return timeout;
        } catch (Exception e) {
            LogManager.e("getScreenTimeout: 读取屏幕超时设置失败，使用默认值 " + e.getMessage());
            return 30000; // 默认30秒
        }
    }

    /**
     * 获取蓝牙设备名称
     *
     * @return 蓝牙设备名称，获取失败时返回空字符串
     */
    private String getBluetoothName() {
        try {
            android.bluetooth.BluetoothAdapter bluetoothAdapter = android.bluetooth.BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter != null) {
                @android.annotation.SuppressLint("MissingPermission")
                String bluetoothName = bluetoothAdapter.getName();
                return bluetoothName != null ? bluetoothName : "";
            }
        } catch (Exception e) {
            LogManager.e("getBluetoothName: 获取蓝牙名称失败 " + e.getMessage());
        }
        return "";
    }

    /**
     * 启动屏幕超时设置监听器
     */
    private void startScreenTimeoutObserver() {
        try {
            if (mScreenTimeoutObserver == null) {
                mScreenTimeoutObserver = new ScreenTimeoutObserver(new Handler(Looper.getMainLooper()));
                Uri uri = Settings.System.getUriFor(Settings.System.SCREEN_OFF_TIMEOUT);
                mContext.getContentResolver().registerContentObserver(uri, false, mScreenTimeoutObserver);
                LogManager.i("startScreenTimeoutObserver: 屏幕超时设置监听器已启动");
            }
        } catch (Exception e) {
            LogManager.e("startScreenTimeoutObserver: 启动屏幕超时监听器失败 " + e.getMessage());
        }
    }

    /**
     * 停止屏幕超时设置监听器
     */
    private void stopScreenTimeoutObserver() {
        try {
            if (mScreenTimeoutObserver != null) {
                mContext.getContentResolver().unregisterContentObserver(mScreenTimeoutObserver);
                mScreenTimeoutObserver = null;
                LogManager.i("stopScreenTimeoutObserver: 屏幕超时设置监听器已停止");
            }
        } catch (Exception e) {
            LogManager.e("stopScreenTimeoutObserver: 停止屏幕超时监听器失败 " + e.getMessage());
        }
    }

    /**
     * 屏幕超时设置变化监听器
     */
    private class ScreenTimeoutObserver extends ContentObserver {
        public ScreenTimeoutObserver(Handler handler) {
            super(handler);
        }

        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            LogManager.i("ScreenTimeoutObserver: 检测到屏幕超时设置变化，重新发送设备信息");
            // 延迟发送，避免频繁调用
            new Handler(Looper.getMainLooper()).postDelayed(HUCommandImpl.this::sendDeviceInfoWithScreenTimeout, 500);
        }
    }

    public void stopHUCommand() {
        DialogInit = false;
        LocaleUtil.locale = null;
        lastLocale = null;

        // 停止屏幕超时设置监听器
        stopScreenTimeoutObserver();
    }
}
