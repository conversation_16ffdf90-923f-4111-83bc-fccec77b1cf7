
package com.a2ra9k.android.launcher.view.page.main;

import android.os.Handler;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.jswebview.browse.BridgeWebView;
import com.a2ra9k.android.launcher.jswebview.view.ProgressBarWebView;
import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.impl.HUCommandImpl;
import com.a2ra9k.android.launcher.view.DisclaimersDialog;
import com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity;
import com.a2ra9k.android.launcher.view.page.PrivacyPolicyDialog;
import com.autoai.fundrive.basemodule.BaseApplication;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.CommonUtil;
import com.autoai.fundrive.commontool.DevelopModel;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.SharePreferenceUtil;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.sendmsg.SendMsgToCarHelper;
import com.autoai.welink.screen.WLScreen;

import java.util.List;
import java.util.Locale;

/**
 * 车机主页
 *
 * <AUTHOR>
 */
public class PhoneMainPage extends BaseMainPage {
    RelativeLayout parent;
    private DisclaimersDialog disclaimersDialog;
    private PrivacyPolicyDialog privacyPolicyDialog;
    private int clickCountDev = 0; // 点击计数
    private long lastClickTime = 0;   // 点击计数
    private static final int REQUIRED_CLICKS = 10;   // 需要的点击次数
    private static final int DOUBLE_CLICK_TIME_THRESHOLD = 200;   // 间隔小于500毫秒认为是连续点击
    private static final String  url_disclaimer = "https://wecockpit-obs.autoai.com/welink/welink-privacy-policy.html";
    private static final String  url_help = "https://wecockpit-obs.autoai.com/welink/welink-privacy-policy.html";
    /**
     * 状态栏高度
     */
    private int status_bar_height;
    private ImageView mConnectstatusImage;
    private RelativeLayout welink1;
    private TextView mconnectInformation;

    /**
     * @param appActivity 上下文环境
     * @param view        当前页面防止的容器View
     */
    public PhoneMainPage(@NonNull AppActivity appActivity,
                         @NonNull View view) {
        super(appActivity, view);
    }

    protected void initMainView() {
        int resourceId = HaiwaiLauncherActivity.getInstance().getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (resourceId > 0) {
                status_bar_height = HaiwaiLauncherActivity.getInstance().getResources().getDimensionPixelSize(resourceId);
                LogManager.d("status_bar_height----------"+status_bar_height);
            }
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_main_fragment_phone, mHolderView, false);
        showDialog();
        welink1 = view.findViewById(R.id.welink1);
        mConnectstatusImage = view.findViewById(R.id.connection_status_image);
        mconnectInformation = view.findViewById(R.id.connection_information);
        welink1.setBackgroundResource(R.drawable.haiwai_launcher_home_overlay_main_no_ic);
        mConnectstatusImage.setImageResource(R.drawable.haiwai_launcher_home_overlay_main_loading_img);
        mconnectInformation.setVisibility(View.VISIBLE);
        mConnectstatusImage.setVisibility(View.VISIBLE);
        mconnectInformation.setText(mconnectInformation.getResources().getText(R.string.haiwai_launcher_searching_for_car_infotainment_system));
        setAnimation(mConnectstatusImage);

        view.findViewById(R.id.btn_goto_usb).setOnClickListener(v -> initUSBView());
        view.findViewById(R.id.btn_goto_permission).setOnClickListener(v -> initAuthorizeView());
        view.findViewById(R.id.btn_goto_about).setOnClickListener(v ->
                initAboutView()
               /* initDisclaimerView()*/);
        view.findViewById(R.id.top_img).setOnClickListener(v -> {
            LogManager.d("PageChangeUtils-0---------");
//            PageChangeUtils.showDialog("app", new int[]{1005, -1}, 1005);
        });

        view.findViewById(R.id.screen_off).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("screen_off"));
        view.findViewById(R.id.screen_on).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("screen_on"));
        view.findViewById(R.id.stream_abort).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("stream_abort"));
        view.findViewById(R.id.stream_recovery).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("stream_recovery"));

        parent= view.findViewById(R.id.welink1);
        view.findViewById(R.id.view).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startDevClick();
            }
        });
      /*  view.findViewById(R.id.touch_view).setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                listener.onTouch(motionEvent);
                return false;
            }
        });*/
        Locale locale = Locale.getDefault();
        changeRlHeight(locale);
        addView(view);
        super.initMainView();
    }

    protected void initUSBView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_connect_help_fragment, mHolderView, false);
        showStatusHeight(view);
        View.OnClickListener clickListener = v -> removeView();
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        addView(view);
        initViewDirectionIfNeed();
        super.initUSBView();
    }
    private void showStatusHeight(View view){
        if(status_bar_height > 0) {
            view.findViewById(R.id.view_status_bar).setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, status_bar_height));
        }
    }
    protected void showDialog() {
        boolean aBoolean = SharePreferenceUtil.getBoolean(getContext(), "SaveNext", false);
        if (aBoolean) {
            HaiwaiLauncherActivity.getInstance().initPlatform();
        } else {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    disclaimersDialog = new DisclaimersDialog(getContext());
                    disclaimersDialog.setStatementClick(new DisclaimersDialog.DStatementClick() {
                        @Override
                        public void onDStatementClick() {
                            initDisclaimerView(url_disclaimer);
                            disclaimersDialog.dismiss();
                        }
                    });
                    disclaimersDialog.setAgreeListener(view -> {
                        SharePreferenceUtil.saveBoolean(getContext(), "SaveNext", true);
                        HaiwaiLauncherActivity.getInstance().initPlatform();
                        disclaimersDialog.dismiss();
                    });
                    disclaimersDialog.setNoAgreeListener(view -> {
                        privacyPolicyDialog = new PrivacyPolicyDialog(getContext());
                        privacyPolicyDialog.setStatementClick(new PrivacyPolicyDialog.StatementClick() {
                            @Override
                            public void onStatementClick() {
                                initDisclaimerView(url_disclaimer);
                                privacyPolicyDialog.dismiss();
                            }
                        });
                        privacyPolicyDialog.setAgreeListener(pview -> {
                            privacyPolicyDialog.dismiss();
                            SharePreferenceUtil.saveBoolean(getContext(), "SaveNext", true);
                            HaiwaiLauncherActivity.getInstance().initPlatform();
                        });
                        privacyPolicyDialog.setNoAgreeListener(pview -> {
                            privacyPolicyDialog.dismiss();
                            if (HaiwaiLauncherActivity.getInstance() != null) {
                                HaiwaiLauncherActivity.getInstance().finish();
                            }
                        });
                        privacyPolicyDialog.show();
                        disclaimersDialog.dismiss();

                    });
                    disclaimersDialog.show();
                }
            }, 1000);
        }
    }

    protected void initAuthorizeView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_authorize_help_fragment, mHolderView, false);
       showStatusHeight(view);
        View.OnClickListener clickListener = v -> removeView();
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        addView(view);
        initViewDirectionIfNeed();
        super.initAuthorizeView();
    }

    TextView versionCodeTv;
    TextView tvIsDebug;

    protected void initAboutView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_about_fragment, mHolderView, false);
        showStatusHeight(view);
        View.OnClickListener clickListener = v -> {
            removeView();
            versionCodeTv = null;
        };
        tvIsDebug = view.findViewById(R.id.tv_isDebug);
        boolean isDebug = DevelopModel.getInstance().getData(getContext().getApplicationContext()).isLogEnabled();;
        if (isDebug) {
            tvIsDebug.setVisibility(View.VISIBLE);
            LogManager.setIsFileLoggable(true);
            LogManager.setIsLoggable(true);
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.setLoggable(true);
            WLScreen.setLogMode(true);
        } else {
            tvIsDebug.setVisibility(View.GONE);
            LogManager.setIsFileLoggable(false);
            LogManager.setIsLoggable(false);
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.setLoggable(false);
            WLScreen.setLogMode(false);
        }
        tvIsDebug.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                tvIsDebug.setVisibility(View.GONE);
                LogManager.setIsFileLoggable(false);
                LogManager.setIsLoggable(false);
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                platformManager.setLoggable(false);
                WLScreen.setLogMode(false);
//                SharePreferenceUtil.getInstance(getContext().getApplicationContext()).setIsDebug(false);
                DevelopModel.getInstance().getData(getContext().getApplicationContext()).setLogEnabled(false);
            }
        });

        view.findViewById(R.id.welink_icon).setOnClickListener(new View.OnClickListener() {
            final static int COUNTS = 5;//点击次数
            final static long DURATION = 2 * 1000;//规定有效时间
            long[] mHits = new long[COUNTS];

            @Override
            public void onClick(View v) {
                System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
                //实现左移，然后最后一个位置更新距离开机的时间，如果最后一个时间和最开始时间小于DURATION，即连续5次点击
                mHits[mHits.length - 1] = SystemClock.uptimeMillis();
                if (mHits[0] >= (SystemClock.uptimeMillis() - DURATION)) {
                    boolean isDebug1 = DevelopModel.getInstance().getData(getContext().getApplicationContext()).isLogEnabled();
                    if (isDebug1) {
                        return;
                    }
                    tvIsDebug.setVisibility(View.VISIBLE);
                    DevelopModel.getInstance().switchLogLevel(getContext().getApplicationContext(),true,0);
                    LogManager.setIsFileLoggable(true);
                    LogManager.setIsLoggable(true);
                    WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                    platformManager.setLoggable(true);
                    WLScreen.setLogMode(true);
                    String tips = "您已在[" + DURATION + "]ms内连续点击【" + mHits.length + "】次了！！！";
                    Toast.makeText(getContext(), tips, Toast.LENGTH_SHORT).show();

                }
            }
        });
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        view.findViewById(R.id.ll_about_difficult).setOnClickListener(v -> initDisclaimerView(url_help));
        view.findViewById(R.id.ll_about_disclaimert).setOnClickListener(v -> initDisclaimerView(url_disclaimer));

       /* view.findViewById(R.id.driving).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SharePreferenceUtil.saveBoolean(getContext(), "driving",true);
                Toast.makeText(getContext(), "行车安全开启成功", Toast.LENGTH_SHORT).show();
            }
        });
        view.findViewById(R.id.driving2).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SharePreferenceUtil.saveBoolean(getContext(), "driving",false);
                Toast.makeText(getContext(), "行车安全关闭成功", Toast.LENGTH_SHORT).show();
            }
        });*/
        versionCodeTv = view.findViewById(R.id.version_code);
        updateVersionView();
        addView(view);
        initViewDirectionIfNeed();
        super.initAboutView();
    }

    protected void initDisclaimerView(String url) {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_disclaimer_fragment, mHolderView, false);
        showStatusHeight(view);
        View.OnClickListener clickListener = v -> {
            removeView();
            showDialog();
        };
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        addView(view);
        initViewDirectionIfNeed();
        ProgressBarWebView mProgressWebView = view.findViewById(R.id.mywebview);

        BridgeWebView mBridgeWebView = mProgressWebView.getWebView();
        mBridgeWebView.setBackgroundColor(ContextCompat.getColor(mAppActivity.getContext(),R.color.haiwai_launcher_bg));
        mProgressWebView.getProgressBar().setReachedBarColor(mAppActivity.getContext().getColor(R.color.haiwai_launcher_blue));
        mBridgeWebView.getSettings().setJavaScriptEnabled(false);
        mBridgeWebView.getSettings().setUseWideViewPort(false);
        mBridgeWebView.getSettings().setSupportZoom(false);
        mBridgeWebView.getSettings().setTextZoom(100);
        mBridgeWebView.getSettings().setAllowFileAccess(false);
        //解决加载某些h5页面不出来问题
        mBridgeWebView.getSettings().setDomStorageEnabled(true);
        //清空缓存
        mBridgeWebView.clearCache(true);
        //reSizeWebview(300);
//        mProgressWebView.loadUrl("https://wecockpit-obs.autoai.com/welink/welink-privacy-policy.html");
        mProgressWebView.loadUrl(url);
        super.initDisclaimerView();
    }

    @Override
    public boolean cannotViewBack() {
        LogManager.v("CarMainPage cannotViewBack");
        boolean aBoolean = SharePreferenceUtil.getBoolean(getContext(), "SaveNext", false);
        if (!aBoolean){
            showDialog();
        }
        return super.cannotViewBack();
    }

    protected void initPrivacyPolicyView() {
        LogManager.v("CarMainPage initPrivacyPolicyView");
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_privacypolicy_fragment, mHolderView, false);
        View.OnClickListener clickListener = v -> removeView();
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        addView(view);
        initViewDirectionIfNeed();
        super.initDisclaimerView();
    }

    @Override
    protected void initLinkSuccess() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_start_screen_page_phone, mHolderView, false);
        addView(view);

        view.findViewById(R.id.screen_off).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("screen_off"));
        view.findViewById(R.id.screen_on).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("screen_on"));
        view.findViewById(R.id.stream_abort).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("stream_abort"));
        view.findViewById(R.id.stream_recovery).setOnClickListener(v -> SendMsgToCarHelper.sendActionCarHome("stream_recovery"));

        super.initLinkSuccess();
    }

    protected void initDevelopView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.activity_develop, mHolderView, false);

        rbLogV = view.findViewById(R.id.rb_log_v);
        rbLogD = view.findViewById(R.id.rb_log_d);
        rbLogI = view.findViewById(R.id.rb_log_i);
        rbLogW = view.findViewById(R.id.rb_log_w);
        rbLogE = view.findViewById(R.id.rb_log_e);
        rbLogV.setButtonDrawable(R.drawable.rb_develop_selector);
        rbLogD.setButtonDrawable(R.drawable.rb_develop_selector);
        rbLogI.setButtonDrawable(R.drawable.rb_develop_selector);
        rbLogW.setButtonDrawable(R.drawable.rb_develop_selector);
        rbLogE.setButtonDrawable(R.drawable.rb_develop_selector);
        switchAuth = view.findViewById(R.id.switch_authenticate);
        rbFps30 = view.findViewById(R.id.rb_fps_30);
        rbFps60 = view.findViewById(R.id.rb_fps_60);
        rbFps30.setButtonDrawable(R.drawable.rb_develop_selector);
        rbFps60.setButtonDrawable(R.drawable.rb_develop_selector);
        tvAuth = view.findViewById(R.id.tv_authenticate);
        switchAuth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isAuth = switchAuth.isChecked();
                if (isAuth) {
                    tvAuth.setText("开");
                } else {
                    tvAuth.setText("关");
                }
                DevelopModel.getInstance().switchAuthWindow(getContext(), isAuth);
            }
        });
        switchDrivingSafety = view.findViewById(R.id.switch_driving_safety);
        switchDrivingVideoPlay = view.findViewById(R.id.switch_driving_safety_online);
        switchDrivingSafetySaveVideo = view.findViewById(R.id.switch_driving_safety_online_opencv);
        switchDrivingSafetyOffline = view.findViewById(R.id.switch_driving_safety_offline);
        tvDrivingSafetyAll = view.findViewById(R.id.tv_driving_safety_all);
        tvDrivingSafetyVideoPlay = view.findViewById(R.id.tv_driving_safety_online_status);
        tvDrivingSafetySaveVideo = view.findViewById(R.id.tv_driving_safety_online_opencv_status);
        tvDrivingSafetyOfflineStatus = view.findViewById(R.id.tv_driving_safety_offline_status);
        switchDrivingSafety.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isDrivingSafety = switchDrivingSafety.isChecked();
                if (isDrivingSafety) {
                    drivingSafetyAllStatus(isDrivingSafety, "全部打开", "开");
                } else {
                    drivingSafetyAllStatus(isDrivingSafety, "全部关闭", "关");
                }
                DevelopModel.getInstance().switchDriveSafetyVideoPlay(getContext(), isDrivingSafety);
                DevelopModel.getInstance().switchDriveSafetySaveVideo(getContext(), isDrivingSafety);
                DevelopModel.getInstance().switchDriveSafetyOffline(getContext(), isDrivingSafety);
            }
        });
        switchLog = view.findViewById(R.id.switch_log);
        tvLogStatus = view.findViewById(R.id.tv_log_status);
        rgLog = view.findViewById(R.id.rg_log);
        switchLog.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isLog = switchLog.isChecked();
                if (isLog) {
                    tvLogStatus.setText("开");
                    DevelopModel.getInstance().switchLogLevel(getContext(), isLog, 1);
                    rbLogV.setChecked(true);
                    rgLog.setVisibility(View.VISIBLE);
                } else {
                    tvLogStatus.setText("关");
                    DevelopModel.getInstance().switchLogLevel(getContext(), isLog, -1);
                    rgLog.setVisibility(View.INVISIBLE);
                    rgLog.clearCheck();
                }
            }
        });
        switchDrivingVideoPlay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                driveSafetyStatus(tvDrivingSafetyVideoPlay);
            }
        });
        switchDrivingSafetySaveVideo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                driveSafetyStatus(tvDrivingSafetySaveVideo);
            }
        });
        switchDrivingSafetyOffline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                driveSafetyStatus(tvDrivingSafetyOfflineStatus);
            }
        });
        rbLogV.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopModel.getInstance().switchLogLevel(getContext(), true, 7);
            }
        });
        rbLogD.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopModel.getInstance().switchLogLevel(getContext(), true, 3);
            }
        });
        rbLogI.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopModel.getInstance().switchLogLevel(getContext(), true, 2);
            }
        });
        rbLogW.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopModel.getInstance().switchLogLevel(getContext(), true, 1);
            }
        });
        rbLogE.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopModel.getInstance().switchLogLevel(getContext(), true, 0);
            }
        });
        view.findViewById(R.id.view_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeView();
            }
        });
        rbFps30.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopModel.getInstance().switchFps(getContext(), 30);
            }
        });
        rbFps60.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DevelopModel.getInstance().switchFps(getContext(), 60);
            }
        });
        addView(view);
        getDataInitView();
        super.initDevView();
    }

    private void updateVersionView() {
        if (versionCodeTv != null) {
            String packageVersionName = BaseApplication.getPackageVersionName();
            LogManager.d("phone initVersionView packageVersionName : " + packageVersionName);
            packageVersionName = String.format(versionCodeTv.getResources().getString(R.string.haiwai_launcher_version), packageVersionName);
            versionCodeTv.setText(packageVersionName);
        }
    }

    @Override
    public void removeView() {
        if (!stack.empty()) {
            if (stack.peek().equals(VIEW_TYPE_SUCCESS)) {
                return;
            }
            if (stack.peek().equals(VIEW_TYPE_MAIN)) {
                return;
            }
            int childIndex = mHolderView.getChildCount() - 1;
            mHolderView.removeViewAt(childIndex);
            if (!stack.empty()) {
                stack.pop();
            }
            //更新布局方向
            initViewDirectionIfNeed();
        }

    }

    @Override
    public void onReceiveData(int flag, int code, Object obj) {
        if (obj instanceof String) {
            String s = (String) obj;
            if (TextUtils.equals("MASK", s)) {
                //要求注释连接成功后显示指定布局
               // initLinkSuccess();
            }
        } else if (obj instanceof Integer) {
            int i = (int) obj;
            if (i == KeyEvent.KEYCODE_BACK) {
                removeView();
            } else if (i == 400) {
                //断开连接
                welink1.setBackgroundResource(R.drawable.haiwai_launcher_home_overlay_main_no_ic);
                mConnectstatusImage.setImageResource(R.drawable.haiwai_launcher_home_overlay_main_loading_img);
                mconnectInformation.setVisibility(View.VISIBLE);
                mConnectstatusImage.setVisibility(View.VISIBLE);
                mconnectInformation.setText(mconnectInformation.getResources().getText(R.string.haiwai_launcher_searching_for_car_infotainment_system));
                setAnimation(mConnectstatusImage);
            } else if (i == 100) {
                //连接中
                welink1.setBackgroundResource(R.drawable.haiwai_launcher_home_overlay_main_no_ic);
                mConnectstatusImage.setImageResource(R.drawable.haiwai_launcher_home_overlay_main_connecting_image);
                mconnectInformation.setText(mconnectInformation.getResources().getString(R.string.haiwai_launcher_connecting_car_machine));
            } else if (i == 200) {
                //连接成功
                welink1.setBackgroundResource(R.drawable.haiwai_launcher_home_overlay_main_no_ic);
                mConnectstatusImage.clearAnimation();
                mConnectstatusImage.setImageResource(R.drawable.haiwai_launcher_home_overlay_main_success);
                mconnectInformation.setText(mconnectInformation.getResources().getString(R.string.haiwai_launcher_connection_successful));
                mConnectstatusImage.postDelayed(() -> {
                    welink1.setBackgroundResource(R.drawable.haiwai_launcher_home_overlay_main_ic);
                    mconnectInformation.setVisibility(View.GONE);
                    mConnectstatusImage.setVisibility(View.GONE);
                },3000);
            }

        } else if (obj instanceof Locale) {
            Locale locale = (Locale) obj;
            LogManager.v("更换布局方向 onReceiveData 1 ：Phone");
            updateLayoutDirection(locale);
        }
    }


    private void initViewDirectionIfNeed() {
        //更新过阿拉伯语言就更新布局方向
        if (HUCommandImpl.arLanguageChanged()) {
            HaiwaiService haiwaiServiceImpl = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
            Locale locale = HUCommandImpl.lastLocale;
            //互联使用车机语言协议更新，反之用手机语言
            if (haiwaiServiceImpl.huLinkedStatus()) {
                locale = LocaleUtil.locale;
            }
            updateLayoutDirection(locale);
        }
    }

    /**
     * 更新布局方向
     *
     * @param locale language
     */
    private void updateLayoutDirection(Locale locale) {
        String language = locale.getLanguage();
        LogManager.v("更换布局方向 updateLayoutDirection：Phone-->" + language);
        changeRlHeight(locale);
        int direction;
        if (TextUtils.equals(language, "ar")) {
            direction = View.LAYOUT_DIRECTION_RTL;
        } else {
            direction = View.LAYOUT_DIRECTION_LTR;
        }
        List<View> views = CommonUtil.getAllChildViews(mHolderView);
        for (View item : views) {
            item.setLayoutDirection(direction);
        }
        updateVersionView();

    }

    private void changeRlHeight(Locale locale) {
        String language = locale.getLanguage();
        if (parent != null) {
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) parent.getLayoutParams();
            if (TextUtils.equals(language, "in") || TextUtils.equals(language, "th") || TextUtils.equals(language, "es")) {
                layoutParams.height = ((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 290, parent.getResources().getDisplayMetrics()));
            } else if (TextUtils.equals(language, "ms")) {
                layoutParams.height = ((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 300, parent.getResources().getDisplayMetrics()));
            } else {
                layoutParams.height = ((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 240, parent.getResources().getDisplayMetrics()));
            }
            parent.setLayoutParams(layoutParams);
        }

    }

   /* private SendTouchListener listener;
    public interface SendTouchListener {
        void onTouch(MotionEvent event);
    }
    public void getEventList(SendTouchListener listener) {
        this.listener = listener;
    }*/

    private void startDevClick() {
        long clickTime = SystemClock.uptimeMillis();
        // 检测点击次数是否达到目标
        if (clickTime - lastClickTime < DOUBLE_CLICK_TIME_THRESHOLD) { // 间隔小于500毫秒认为是连续点击
            clickCountDev++;
        } else {
            clickCountDev = 0;
        }
        lastClickTime = clickTime;

        if (clickCountDev == REQUIRED_CLICKS) {
            initDevelopView();
        }
    }
    private Switch switchAuth = null;
    private TextView tvAuth = null;
    private Switch switchDrivingSafety = null;
    private Switch switchDrivingVideoPlay = null;
    private Switch switchDrivingSafetySaveVideo = null;
    private Switch switchDrivingSafetyOffline = null;
    private TextView tvDrivingSafetyAll = null;
    private TextView tvDrivingSafetyVideoPlay = null;
    private TextView tvDrivingSafetySaveVideo = null;
    private TextView tvDrivingSafetyOfflineStatus = null;
    private Switch switchLog = null;
    private TextView tvLogStatus = null;
    private RadioGroup rgLog = null;
    private RadioButton rbLogV = null;
    private RadioButton rbLogI = null;
    private RadioButton rbLogD = null;
    private RadioButton rbLogW = null;
    private RadioButton rbLogE = null;
    private RadioButton rbFps30,rbFps60;
    private void drivingSafetyAllStatus(Boolean isDrivingSafety, String strAll, String str) {
        tvDrivingSafetyAll.setText(strAll);
        switchDrivingVideoPlay.setChecked(isDrivingSafety);
        switchDrivingSafetySaveVideo.setChecked(isDrivingSafety);
        switchDrivingSafetyOffline.setChecked(isDrivingSafety);
        tvDrivingSafetyVideoPlay.setText(str);
        tvDrivingSafetySaveVideo.setText(str);
        tvDrivingSafetyOfflineStatus.setText(str);
    }

    private void driveSafetyStatus(TextView tv) {
        boolean isVideoPlay = switchDrivingVideoPlay.isChecked();
        boolean isSaveVideo = switchDrivingSafetySaveVideo.isChecked();
        boolean isOffline = switchDrivingSafetyOffline.isChecked();
        if (tv.equals(tvDrivingSafetyOfflineStatus)) {
            if (isOffline) {
                tv.setText("开");
            } else {
                tv.setText("关");
            }
        } else if (tv.equals(tvDrivingSafetySaveVideo)) {
            if (isSaveVideo) {
                tv.setText("开");
            } else {
                tv.setText("关");
            }
        }else {
            if (isVideoPlay) {
                tv.setText("开");
            } else {
                tv.setText("关");
            }
        }

        if (isVideoPlay && isOffline && isSaveVideo) {
            switchDrivingSafety.setChecked(true);
            tvDrivingSafetyAll.setText("全部打开");
        }
        if (!isVideoPlay && !isOffline && !isSaveVideo) {
            switchDrivingSafety.setChecked(false);
            tvDrivingSafetyAll.setText("全部关闭");
        }
        DevelopModel.getInstance().switchDriveSafetyVideoPlay(getContext(), isVideoPlay);
        DevelopModel.getInstance().switchDriveSafetySaveVideo(getContext(), isSaveVideo);
        DevelopModel.getInstance().switchDriveSafetyOffline(getContext(), isOffline);
    }

    private void getDataInitView() {
        DevelopModel.SwitchData data = DevelopModel.getInstance().getData(getContext());
        switchAuth.setChecked(data.isAuthWindow());
        if (data.isAuthWindow()) {
            tvAuth.setText("开");
        } else {
            tvAuth.setText("关");
        }

        if (data.isDriveSafetyVideoPlay() && data.isDriveSafetySaveVideo() && data.isDriveSafetyOffline()) {
            switchDrivingSafety.setChecked(true);
            tvDrivingSafetyAll.setText("全部打开");
        } else {
            switchDrivingSafety.setChecked(false);
            tvDrivingSafetyAll.setText("全部关闭");
        }
        switchDrivingVideoPlay.setChecked(data.isDriveSafetyVideoPlay());
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.setDriveSafetyVideoPlay(data.isDriveSafetyVideoPlay());
        if (data.isDriveSafetyVideoPlay()) {
            tvDrivingSafetyVideoPlay.setText("开");
        } else {
            tvDrivingSafetyVideoPlay.setText("关");
        }

        switchDrivingSafetySaveVideo.setChecked(data.isDriveSafetySaveVideo());
        // todo 需WLPlatformManager中加setDriveSafetyOnlineOpencvSwitch方法
        platformManager.setDriveSafetySaveVideo(data.isDriveSafetySaveVideo());
        if (data.isDriveSafetySaveVideo()) {
            tvDrivingSafetySaveVideo.setText("开");
        } else {
            tvDrivingSafetySaveVideo.setText("关");
        }

        switchDrivingSafetyOffline.setChecked(data.isDriveSafetyOffline());
        platformManager.setDriveSafetyOfflineSwitch(data.isDriveSafetyOffline());
        if (data.isDriveSafetyOffline()) {
            tvDrivingSafetyOfflineStatus.setText("开");
        } else {
            tvDrivingSafetyOfflineStatus.setText("关");
        }

        switchLog.setChecked(data.isLogEnabled());
        if (data.isLogEnabled()) {
            tvLogStatus.setText("开");
            rgLog.setVisibility(View.VISIBLE);
        } else {
            tvLogStatus.setText("关");
            rgLog.setVisibility(View.INVISIBLE);
        }
        switch (data.getLogLevel()) {
            case 1:
                rbLogV.setChecked(true);
                break;
            case 2:
                rbLogD.setChecked(true);
                break;
            case 3:
                rbLogI.setChecked(true);
                break;
            case 4:
                rbLogW.setChecked(true);
                break;
            default:
                break;
        }
        if(data.getFps() == 60){
            rbFps60.setChecked(true);
        }else{
            rbFps30.setChecked(true);
        }
    }
    private void setAnimation(View view) {
        RotateAnimation rotateAnimation = new RotateAnimation(
                0,
                360,
                Animation.RELATIVE_TO_SELF, 0.5f,
                Animation.RELATIVE_TO_SELF, 0.5f
        );

        // 设置动画属性
        rotateAnimation.setDuration(2000);
        rotateAnimation.setRepeatCount(Animation.INFINITE); // 无限重复
        rotateAnimation.setFillAfter(true);
        // 应用动画到 ImageView
        view.startAnimation(rotateAnimation);
    }
}