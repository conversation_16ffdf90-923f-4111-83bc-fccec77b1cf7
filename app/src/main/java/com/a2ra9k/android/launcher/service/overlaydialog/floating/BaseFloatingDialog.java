package com.a2ra9k.android.launcher.service.overlaydialog.floating;

import android.app.Dialog;
import android.view.View;
import android.widget.ImageView;

import com.a2ra9k.android.launcher.LayoutTypeUtils;
import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.service.overlaydialog.base.BaseDialog;
import com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity;
import com.a2ra9k.android.launcher.view.page.main.BaseMainPage;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.hidscreen.WLScreenManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.bean.CarBean;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.welink.screen.WLScreen;
import com.mapbar.android.model.BasePage;

public abstract class BaseFloatingDialog extends BaseDialog {

    protected ImageView mGroupAction;
    protected ImageView mPhoneHomeAction;
    protected ImageView mBackAction;
    protected ImageView mCarHomeAction;
    protected int mStatusBarRes;
    /**
     * 滑动按键起始坐标
     */
    protected float mStartX;
    protected float mStartY;
    /**
     * 滑动状态
     */
    protected boolean mMove;
    protected boolean mPause;
    /**
     * 滑动按键展开背景宽
     */
    protected int mBackgroundWidth;
    protected int mBackgroundHeight;
    /**
     * 展开按键宽高
     */
    protected int mUploadLocationWidth, mUploadLocationHeight;
    /**
     * 车机宽高，pixel
     */
    protected int CAR_SCREEN_WIDTH_PIXEL;
    protected int CAR_SCREEN_HEIGHT_PIXEL;
    /**
     * 滑动按键起始边距
     */
    protected static final int POSITION_SPACING_START = 20;
    protected static final int POSITION_SPACING_BOTTOM = 20;
    /**
     * 滑动按键显示状态
     */
    protected int GROUP_STATUS_OPEN;
    protected int GROUP_STATUS_CLOSR;

    protected WLScreenManager mWLScreenManager;

    protected BaseFloatingDialog(int l, int t) {
        super(l, t);
    }

    protected Dialog initDialog() {
        //浮窗
        WLPlatformManager wlPlatformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        mWLScreenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
        //车机属性
        CarBean huCarInfo = wlPlatformManager.getHuCarInfo();
        if(huCarInfo != null) {
            CAR_SCREEN_WIDTH_PIXEL = huCarInfo.getHuScreenWidth();
            CAR_SCREEN_HEIGHT_PIXEL = huCarInfo.getHuScreenHeight();
        }
        Dialog overlayDialog = wlPlatformManager.getOverlayDialog();
        if(overlayDialog == null){
            return null;
        }
        if (LayoutTypeUtils.isA2R()) {
            GROUP_STATUS_OPEN = R.drawable.haiwai_launcher_phont_adapter_group_more_open_a2r;
            GROUP_STATUS_CLOSR = R.drawable.haiwai_launcher_phont_adapter_group_more_close_a2r;
            mStatusBarRes = R.drawable.haiwai_launcher_phont_adapter_group_bg_rectangle_a2r;
            overlayDialog.setContentView(R.layout.haiwai_launcher_overlay_dialog_rectangle_a2r);
        } else {
            GROUP_STATUS_OPEN = R.drawable.haiwai_launcher_phont_adapter_group_more_open;
            GROUP_STATUS_CLOSR = R.drawable.haiwai_launcher_phont_adapter_group_more_close;
            mStatusBarRes = R.drawable.haiwai_launcher_phont_adapter_group_bg_rectangle;
            overlayDialog.setContentView(R.layout.haiwai_launcher_overlay_dialog_rectangle);
        }
        return overlayDialog;
    }

    @Override
    public void updateLocationOpen() {
        mHandler.postDelayed(() -> {
            int[] backLocation = locationParamsWrapper(mBackAction);
            int[] phoneHomeLocation = locationParamsWrapper(mPhoneHomeAction);
            int[] carHomeLocation = locationParamsWrapper(mCarHomeAction);
            if (mWLScreenManager != null) {
                mWLScreenManager.updateLocation(phoneHomeLocation, backLocation, carHomeLocation);
            }
        }, 40);
    }

    protected void updateLocationClose() {
        int[] ints = {0, 0, 0, 0};
        mWLScreenManager.updateLocation(ints, ints, ints);
    }

    private int[] locationParamsWrapper(View v) {
        if (v == null) return new int[]{0, 0, 0, 0};
        int[] outLocation = new int[2];
        v.getLocationOnScreen(outLocation);
        int x = outLocation[0];
        int y = outLocation[1];
        return new int[]{x, y, mUploadLocationWidth, mUploadLocationHeight};
    }

    protected void actionPhoneHome() {
//        WLScreen.performAction(AccessibilityService.GLOBAL_ACTION_HOME);
//        WLScreen.performAction(2);
    }

    protected void actionCarHome() {
        WLProtocolManager.getInstance().sendActionCarHome();
    }

    protected void actionBack() {
        BasePage currentPage = HaiwaiLauncherActivity.getInstance().getActivity().getViewInterface().getCurrentPageObj().getPage();
        if (currentPage.isMainPage()) {
            //当前页面是主页
            if (currentPage instanceof BaseMainPage) {
                if (((BaseMainPage) currentPage).cannotViewBack()) {
//                    WLScreen.performAction(AccessibilityService.GLOBAL_ACTION_BACK);
//                    WLScreen.performAction(1);
                } else {
                    ((BaseMainPage) currentPage).removeView();
                }
            }
        } else {
            //当前不在主页
            PageChangeUtils.showPrevious(-1);
        }
    }
}
