package com.a2ra9k.android.launcher.ble;


import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.OnWiFiConnectListener;
import com.autoai.fundrive.platform.WLPlatformManager;

/**
 * <AUTHOR> zhanggc
 * @version : 1.0
 * @description :wifi 无感互联弹出提示
 * @date : 2021/1/12 14:46
 */
public class WiFiBleLinkTopManger implements OnWiFiConnectListener {

    /**
     * 正在扫描中
     */
    public final static int WIFI_CONNECT_SCANNING_CODE = 1;
    /**
     * 正在连接中
     */
    public final static int WIFI_CONNECT_CONNECTING_CODE = 2;
    /**
     * 数据传输中
     */
    public final static int WIFI_CONNECT_CONNECTING_TRANSMISSION = 3;
    /**
     * 顶部根布局
     */
    /**
     * 扫苗提示
     */
    private TextView tvTopView;
    private Boolean isCoonectCar = false;
    /**
     * 错误提示显示时间
     */
    private final int TIME = 10*1000;
    private Handler mHandler = new Handler(){
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what){
                case 0:
                    hideTopTip();
                    break;
                default:
                    break;
            }

        }
    };
    public WiFiBleLinkTopManger() {

    }

    public void setTvTopView(TextView view){
        this.tvTopView = view;
    }

    /**
     * 显示wifi 无感互联顶部提示
     *
     * @param
     */
    public void showTopTip(int content) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                if (tvTopView != null && !isCoonectCar) {
                    //隐藏无感 弹框提示
                    tvTopView.setVisibility(View.GONE);
                    tvTopView.setText(content);
                }
            }
        });
    }

    /**
     * 隐藏wifi 无感互联顶部提示
     */
    public void hideTopTip() {
        mHandler.removeMessages(0);
        if (tvTopView != null) {
            tvTopView.setVisibility(View.GONE);
        }
    }

    /**
     * i
     * 正在扫描中 WIFI_CONNECT_SCANNING_CODE = 1;
     * 正在连接中  WIFI_CONNECT_CONNECTING_CODE = 2;
     * 数据传输中 IFI_CONNECT_CONNECTING_TRANSMISSION = 3;
     */
    public void OnConnectListener(int status) {
        LogManager.d("OnConnectListener-------->status="+status);
        int code = status;
        if (status >= 0 && status < 8) {
            code = WIFI_CONNECT_SCANNING_CODE;
        }else if(status == 8){
            code = WIFI_CONNECT_CONNECTING_CODE;
        }else if(status == 9){
            code = WIFI_CONNECT_CONNECTING_TRANSMISSION;
        }
        int promptId = 0;
        switch (code) {
            case WIFI_CONNECT_SCANNING_CODE:
                promptId = R.string.haiwai_launcher_wifi_connect_scanning;
                break;
            case WIFI_CONNECT_CONNECTING_CODE:
                //扫描成功  有弹框则关闭扫描超时弹框

                promptId = R.string.haiwai_launcher_wifi_connect_connecting;
                break;
            case WIFI_CONNECT_CONNECTING_TRANSMISSION:
                promptId = R.string.haiwai_launcher_wifi_connect_data_transmission;
                break;
            default:
                break;
        }
        if (promptId !=0) {
            mHandler.removeMessages(0);
            showTopTip(promptId);
        } else {
            hideTopTip();
        }
    }


    /**
     * //error - 错误码 1: 设备不支持蓝牙BLE
     * 2: 没有打开蓝牙
     * 3: 没有打开Wi-Fi
     * 4: 需要请求定位权限(ACCESS_FINE_LOCATION)
     * 5: 服务发生异常
     * 6: 创建Wi-Fi Direct GO失败
     */
    public void OnError(int status) {
        LogManager.d("OnError-------->status="+status);
        if(status > 0 && status <= 6 ) {
            mHandler.sendEmptyMessageDelayed(0, TIME);
            switch (status){
                case 1:
                    showTopTip(R.string.haiwai_launcher_wifi_connect_unsupported);
                    break;
                case 2:
                    showTopTip(R.string.haiwai_launcher_wifi_no_blue_content);
                    break;
                case 3:
                    showTopTip(R.string.haiwai_launcher_wifi_no_open_content);
                    break;
                case 4:
                    showTopTip(R.string.haiwai_launcher_wifi_local_content);
                    break;
                case 5:
                    showTopTip(R.string.haiwai_launcher_wifi_server_ex_content);
                    break;
                case 6:
//                    showTopTip("当前手机型号不支持通过Wi-Fi连接车辆，请尝试使用USB连接");
                    showTopTip(R.string.haiwai_launcher_wifi_failed_content);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 无感互联一直没有扫描到车机超时
     */
    public void onBleLinkScanTimeout() {
        LogManager.d("onBleLinkScanTimeout------");
        mHandler.sendEmptyMessageDelayed(0,TIME);
        startWiFiLink();
    }

    /**
     * 无感互联扫描到车机但一直没有互联上超时
     */
    public void onBleLinkJoinTimeout() {
        LogManager.d("onBleLinkJoinTimeout------");
        startWiFiLink();
    }


    public void isConnectCar(boolean isConnectCar){
        LogManager.d("isConnectCar:"+isConnectCar);
        this.isCoonectCar = isConnectCar;
        if(!isConnectCar) {
//            showDialog(WiFiBleLinkPopDialog.DIALOGID_DISCONNECT);
            startWiFiLink();
        }else{
            hideTopTip();
        }
    }

    public void isAOA(boolean b) {
        LogManager.d("isAOA:"+b);
        if(b){
            stopWiFiLink();
            //是usb 连接   关闭所有ble 互联提示弹框
//            closeDialog(WiFiBleLinkPopDialog.DIALOGID_SCAN_TIMEOUT);
//            closeDialog(WiFiBleLinkPopDialog.DIALOGID_CONNECT_TIMEOUT);
//            closeDialog(WiFiBleLinkPopDialog.DIALOGID_DISCONNECT);
        }
    }


    public static void startWiFiLink(){
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        if(platformManager != null) {
           platformManager.startDirectLink();
        }
    }

    /**
     * 取消扫描
     */
    public static void stopWiFiLink(){
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        if(platformManager != null) {
            platformManager.stopDirectLink();
        }
    }
    public void onDestory() {
        tvTopView = null;
        mHandler.removeMessages(0);
    }
}
