package com.a2ra9k.android.launcher;

import android.util.Log;

import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.bean.CarBean;

public class LayoutTypeUtils {

    public static boolean isA2R() {
        WLPlatformManager wlPlatformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        CarBean huCarInfo = wlPlatformManager.getHuCarInfo();
        if (huCarInfo == null) {
            Log.d("LayoutTypeUtils", "isA2R is false ");
            return false;
        }
        int car_screen_width_pixel = huCarInfo.getHuScreenWidth();
        int car_screen_height_pixel = huCarInfo.getHuScreenHeight();
        boolean a2r = car_screen_width_pixel <= 800 && car_screen_height_pixel <= 480;
        Log.d("LayoutTypeUtils", "isA2R: " + a2r);
        return a2r;
    }
}
