package com.a2ra9k.android.launcher

import com.autoai.fundrive.basemodule.singleton.SingletonFactory
import com.autoai.fundrive.platform.WLPlatformManager
import com.autoai.fundrive.platform.auth.AuthModel
import com.autoai.welink.network.params.LicenseWelinkActivateRequest
import com.autoai.welink.network.retrofit.LicenseRetrofitNetwork
import com.autoai.welink.network.util.LinkLog
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

object DeviceAuthManager {
    private const val TAG = "DeviceAuthManager"
    fun init() {
        LinkLog.i(TAG, "init: invoke")
        CoroutineScope(Dispatchers.Main).launch {
            AuthModel.vehicleType.filter { it.isNotEmpty() }.collect{
                kotlin.runCatching {
                    LicenseRetrofitNetwork.welinkActivate(LicenseWelinkActivateRequest("autoai", "xiaopeng", it, "", ""))
                }.onSuccess {
                    delay(5000)
                    LinkLog.i(TAG, "init: onSuccess: it = $it")
                    val platformManager = SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME) as? WLPlatformManager
                    platformManager?.sendMessageDataToCar(AuthModel.assembleMessage(Gson().toJson(it)))
                }.onFailure {
                    LinkLog.e(TAG, it, "DeviceAuthManager: ")
                }
            }
        }
    }
}