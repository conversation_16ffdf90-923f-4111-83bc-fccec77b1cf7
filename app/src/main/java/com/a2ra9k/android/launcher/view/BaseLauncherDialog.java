package com.a2ra9k.android.launcher.view;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.presenter.BaseActivityAndDialogPresenter;
import com.autoai.fundrive.basemodule.BaseApplication;
import com.autoai.fundrive.basemodule.BaseModuleManager;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.activity.BaseDialogManager;
import com.autoai.fundrive.basemodule.activity.DialogManager;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.messagebus.MessageCenter;
import com.autoai.fundrive.messagebus.bean.ParamSet;
import com.mapbar.android.control.ViewBaseManager;
import com.mapbar.android.model.PageObject;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;


/**
 * 新架构车机首页
 *
 * <AUTHOR>
 */
public abstract class BaseLauncherDialog<V extends BaseActivityOrDialogView,
        P extends BaseActivityAndDialogPresenter<V>>
        extends DialogManager implements BaseDialogManager.OnKeyListener {

    private BaseModuleManager mModuleManager;
    private View maskingView;
    protected P presenter;
    protected V v;

    public BaseLauncherDialog(@NonNull ViewBaseManager aViewBaseManager) {
        super(aViewBaseManager);
        this.setOnKeyListener(this);
    }

    @Override
    public void onCreate() {
        MessageCenter.getDefault().subscribe(this, "dialog_masking", new MessageCenter.Callback<ParamSet<Object>>() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public void onEvent(ParamSet<Object> objectParamSet) {
                Map<String, Object> map = objectParamSet.getmParamList();
                LogManager.i( "dialog_masking onEvent: map-->" + map);
                Object dialogMasking = map.get("dialog_masking");
                LogManager.i( "dialog_masking onEvent: dialogMasking-->" + dialogMasking);
                if (dialogMasking instanceof Boolean) {
                    LogManager.i( "dialog_masking onEvent: dialogMasking instanceof Boolean");
                    boolean isDialogMasking = (boolean) dialogMasking;
                    LogManager.i( "dialog_masking onEvent: isDialogMasking-->" + isDialogMasking);
                    if (isDialogMasking) {
                        ViewGroup viewGroup = (ViewGroup) findView(getRootViewId());
                        if (maskingView != null) {
                            viewGroup.removeView(maskingView);
                        }
                        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                        maskingView = new View(getActivity().getContext());
                        LogManager.i( "dialog_masking onEvent: maskingView 1 -->" + maskingView.hashCode());
                        //蒙版拦截点击事件
                        maskingView.setOnTouchListener((v, event) -> true);
                        maskingView.setBackgroundColor(Color.BLACK);
                        maskingView.setLayoutParams(layoutParams);
                        viewGroup.addView(maskingView);
                    } else {
                        LogManager.i( "dialog_masking onEvent: maskingView 2 -->" + maskingView.hashCode());
                        if (maskingView != null) {
                            LogManager.i( "dialog_masking onEvent: maskingView != null");
                            ViewGroup viewGroup = (ViewGroup) findView(getRootViewId());
                            viewGroup.removeView(maskingView);
                            maskingView = null;
                        } else {
                            LogManager.i( "dialog_masking onEvent: maskingView == null");
                        }
                    }
                } else {
                    LogManager.i( "dialog_masking onEvent: dialogMasking is not instanceof Boolean");
                }
            }
        });
    }


    protected abstract Class<P> getPresenterClass();


    protected abstract V getSingletonView();

    @Override
    public PageObject getMainPage() {
        PageObject mainPage = createPage(Configs.PAGE_MAIN);
        mainPage.setModuleName("app");
        return mainPage;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (getActivity().getViewInterface().getCurrentPageObj().getPage().isMainPage()) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    //当前页面是主页
                    PageChangeUtils.showDialog("app", new int[]{-1, 1001}, 1001);
                }
            } else {
                //当前不在主页
                PageChangeUtils.showPrevious(-1);
            }
        }
        return true;
    }

    @Override
    public BaseModuleManager getModuleManager() {
        if (mModuleManager == null) {
            try {
                Class<?> clazz = Class.forName(BaseApplication.getModuleManagerClassName());
                Constructor<?> constructor = clazz.getDeclaredConstructor();
                mModuleManager = (BaseModuleManager) constructor.newInstance();
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | SecurityException | IllegalArgumentException | NoSuchMethodException | InvocationTargetException e) {
                LogManager.e( "", e);
            }
        }
        mModuleManager.setScreenIndex(getScreenIndex());
        return mModuleManager;
    }

//    @Override
//    public IHUSupportManager initHUSupportManager() {
//        //可以根据此时实现类内容处理车机自动适配横竖屏
//        return new HUSupportManagerImpl();
//    }

    @Override
    public void onDestroy() {
        MessageCenter.getDefault().unregister(this);
        if (maskingView != null) {
            ViewGroup viewGroup = (ViewGroup) findView(getRootViewId());
            viewGroup.removeView(maskingView);
            maskingView = null;
        }
        if (presenter != null) {
            presenter.detachView(getSingletonView(), getPresenterClass().getName());
            presenter = null;
        }
        super.onDestroy();
        v = null;
    }
}