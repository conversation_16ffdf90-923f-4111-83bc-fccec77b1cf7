package com.a2ra9k.android.launcher.service.overlaydialog;

import android.app.Dialog;

import com.a2ra9k.android.launcher.LayoutTypeUtils;
import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.service.overlaydialog.base.BaseDialog;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.platform.WLPlatformManager;

/**
 * 连接成功
 */
public class MaskDialog extends BaseDialog {

    public MaskDialog() {
    }

    @Override
    public void showDialog() {
        //浮窗
        WLPlatformManager wlPlatformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        Dialog overlayDialog = wlPlatformManager.getOverlayDialog();
        int layoutRes = LayoutTypeUtils.isA2R() ? R.layout.haiwai_launcher_overlay_dialog_mask_a2r : R.layout.haiwai_launcher_overlay_dialog_mask_a9k;
        overlayDialog.setContentView(layoutRes);
        overlayDialog.show();
    }
}
