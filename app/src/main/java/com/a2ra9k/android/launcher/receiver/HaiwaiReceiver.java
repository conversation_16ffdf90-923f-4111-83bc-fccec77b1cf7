package com.a2ra9k.android.launcher.receiver;

import android.content.Intent;
import android.text.TextUtils;

import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.OverlayDialogManager;
import com.autoai.fundrive.basemodule.broadcast.BroadcastService;
import com.autoai.fundrive.basemodule.broadcast.IBroadcastNotification;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.sendmsg.SendMsgToCarHelper;
import com.autoai.link.threadpool.ThreadPoolUtil;

public class HaiwaiReceiver implements IBroadcastNotification {

    private final HaiwaiService haiwaiService;
//    private volatile boolean mBluetooth;

    public HaiwaiReceiver(HaiwaiService service) {
        this.haiwaiService = service;
    }

    @Override
    public void broadcastNotify(Intent intent) {
        String action = intent.getAction();
        boolean screenProjectionStatus = haiwaiService.screenProjectionStatus();
        LogManager.d("broadcastNotify screenProjectionStatus(" + screenProjectionStatus + ")" + intent);
        if (!screenProjectionStatus) {
            return;
        }
        if (TextUtils.equals(action, Intent.ACTION_SCREEN_OFF)) {
            //锁屏
            LogManager.d("broadcastNotify ACTION_SCREEN_OFF");
            ThreadPoolUtil.getInstance().runOnUiThread(() -> {
                OverlayDialogManager overlayDialogManager = haiwaiService.getDialogManager();
//                overlayDialogManager.showMaskAndReset();
            });
            SendMsgToCarHelper.sendActionCarHome("screen_off");
        } else if (TextUtils.equals(action, Intent.ACTION_SCREEN_ON)) {
            //开屏
            LogManager.d("broadcastNotify ACTION_SCREEN_ON");
            ThreadPoolUtil.getInstance().runOnUiThread(this::screenHandlerActionPresentAndOn);
            SendMsgToCarHelper.sendActionCarHome("screen_on");
        } else if (TextUtils.equals(action, Intent.ACTION_USER_PRESENT)) {
            LogManager.d("broadcastNotify ACTION_USER_PRESENT");
            ThreadPoolUtil.getInstance().runOnUiThread(this::screenHandlerActionPresentAndOn);
        }
    }

    private void screenHandlerActionPresentAndOn() {
        OverlayDialogManager overlayDialogManager = haiwaiService.getDialogManager();
//        if (mBluetooth) {
//            dialogManager.showBluetooth();
//        } else {
        overlayDialogManager.showRectangleDialog();
        //暂时关闭 锁屏提示弹框，不显示手机功能浮窗按钮
//        overlayDialogManager.dismissOverlayDialog();
//        }
    }

    public void register() {
        BroadcastService broadcastService = (BroadcastService) SingletonFactory.getInstance().getSingleton(BroadcastService.NAME);
        broadcastService.registerBroadcastListener(this, Intent.ACTION_SCREEN_OFF, Intent.ACTION_SCREEN_ON, Intent.ACTION_USER_PRESENT);
    }

    public void unregister() {
        BroadcastService broadcastService = (BroadcastService) SingletonFactory.getInstance().getSingleton(BroadcastService.NAME);
        if (broadcastService != null) {
            broadcastService.unRegisterBroadcastListener(this, Intent.ACTION_SCREEN_OFF, Intent.ACTION_SCREEN_ON, Intent.ACTION_USER_PRESENT);
        }
    }
}
