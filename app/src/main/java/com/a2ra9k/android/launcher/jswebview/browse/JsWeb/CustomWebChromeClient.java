package com.a2ra9k.android.launcher.jswebview.browse.JsWeb;

import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebView;

import com.a2ra9k.android.launcher.jswebview.view.NumberProgressBar;


/**
 * Class description
 *
 * <AUTHOR>
 * @date 2016-07-08 14:05
 */

public class CustomWebChromeClient extends WebChromeClient {

    private NumberProgressBar mProgressBar;
    private final static int DEF = 95;
    private boolean mProgressBarVisible;

    public CustomWebChromeClient(NumberProgressBar progressBar,boolean mProgressBarVisible) {
        this.mProgressBar = progressBar;
        this.mProgressBarVisible = mProgressBarVisible;
    }

    @Override
    public void onProgressChanged(WebView view, int newProgress) {
        if (mProgressBarVisible){
            if (newProgress >= DEF) {
                mProgressBar.setVisibility(View.GONE);
            } else {
//                if (mProgressBar.getVisibility() == View.GONE) {
//                    mProgressBar.setVisibility(View.VISIBLE);
//                }

                mProgressBar.setProgress(newProgress);
            }
            super.onProgressChanged(view, newProgress);
        }else {
            mProgressBar.setVisibility(View.GONE);
        }
    }
}
