package com.a2ra9k.android.launcher.view.page;

import android.app.Dialog;
import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity;
import com.a2ra9k.android.launcher.view.HookCheckBox;


public class PrivacyPolicyDialog extends Dialog {
    private Context context;
    private TextView mTitle;
    private TextView mText,mNoAgree,mAgree;
    private HookCheckBox mDisclaimers;

    public PrivacyPolicyDialog(@NonNull Context context) {
        super(context, R.style.MainiosDialog);
        this.context = context;
        getWindow().setGravity(Gravity.BOTTOM);
        setCancelable(false);
        initView();
    }

    private void initView() {
        View inflate = LayoutInflater.from(context).inflate(R.layout.privacypolicy_dialog, null);
        mTitle = inflate.findViewById(R.id.tv_title);
        mText = inflate.findViewById(R.id.tv_text);
        mNoAgree = inflate.findViewById(R.id.tv_noagree);
        mAgree = inflate.findViewById(R.id.tv_agree);
        mDisclaimers = inflate.findViewById(R.id.cb_hookview);
        super.setContentView(inflate);
        SpannableStringBuilder  spannableString = new SpannableStringBuilder(context.getString(R.string.haiwai_launcher_agreelinkbasicfunctionprivacypolicy));
        spannableString.setSpan(new TextAgreementClick(), 11, 25, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mText.setMovementMethod(LinkMovementMethod.getInstance());
        //设置点击后的颜色为透明（有默认背景）
        mText.setHighlightColor(context.getResources().getColor(android.R.color.transparent));
        mText.setText(spannableString);
    }
    //同意
    public void setAgreeListener(View.OnClickListener listener) {
        mAgree.setOnClickListener(listener);

    }
    //不同意
    public void setNoAgreeListener(View.OnClickListener listener) {
        mNoAgree.setOnClickListener(listener);
    }
    //提醒
    public void getOnChangeListener(HookCheckBox.OnCheckChangeListener listener) {
        mDisclaimers.setOnCheckChangeListener(listener);
    }

    //内容
    public void setTextListener(String title, String text) {
       // mTitle.setText(title);
       // mText.setText(text);
    }
    private class TextAgreementClick extends ClickableSpan {

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            //设置文本的颜色
            ds.setColor(context.getColor(R.color.haiwai_launcher_blue));
            //超链接形式的下划线，false 表示不显示下划线，true表示显示下划线
            ds.setUnderlineText(false);
        }

        @Override
        public void onClick(View widget) {
            statementClick.onStatementClick();
        }
    }
    public StatementClick statementClick;
    public interface StatementClick {
        void onStatementClick();
    }
    public void setStatementClick(StatementClick statementClick){
        this.statementClick = statementClick;
    }
}
