package com.a2ra9k.android.launcher.view;

import static com.autoai.welink.logiclib.skincore.constant.DeviceType.SCREEN_CAR;
import static com.autoai.welink.logiclib.skincore.constant.DeviceType.SCREEN_PHONE;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.ble.WiFiBleLinkTopManger;
import com.a2ra9k.android.launcher.view.page.main.BaseMainPage;
import com.a2ra9k.android.launcher.pagemanager.PageManagerPhone;
import com.a2ra9k.android.launcher.presenter.ActivityAndDialogPresenter;
import com.a2ra9k.android.launcher.receiver.HaiwaiReceiver;
import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.impl.HUCommandImpl;
import com.autoai.fundrive.commontool.DevelopModel;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.fundrive.platform.sendmsg.SendMsgToCarHelper;
import com.autoai.welink.logiclib.skincore.SkinManager;
import com.autoai.fundrive.hidscreen.WLScreenManager;
import com.blankj.utilcode.util.AppUtils;
import com.mapbar.android.control.ViewBaseManager;
import com.mapbar.android.model.BasePage;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.activity.DialogManager;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;

import java.util.ArrayList;

public class HaiwaiLauncherActivity extends BaseLauncherActivity<ActivityOrDialogView, ActivityAndDialogPresenter> implements ActivityOrDialogView {
    private PageManager mPageManager;

    private static HaiwaiLauncherActivity INSTANCE;
    public static boolean DialogInit = false;

    private HaiwaiService haiwaiService;
    private HaiwaiReceiver haiwaiReceiver;

    private View view;
    /**
     * 模块中反射用到
     */
    @Keep
    public static HaiwaiLauncherActivity getInstance() {
        return INSTANCE;
    }
    private long mLastTime;
    @Override
    protected void attachBaseContext(Context base) {
        INSTANCE = this;
        super.attachBaseContext(base);
        LogManager.d("attachBaseContext");
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        LogManager.d("onCreate");
        SkinManager.getInstance().registerLayoutInflater(this, SCREEN_PHONE);
        requestPermissions(getMustRequestPermissions(),101);
        super.onCreate(savedInstanceState);
        wiFiBleLinkTopManger = new WiFiBleLinkTopManger();
        wiFiBleLinkTopManger.setTvTopView(findViewById(R.id.launcher_wifi_top_tv));
//        LogManager.d("onCreate  isWifiDirectSupported--->"+isWifiDirectSupported(this));


//        findViewById(R.id.debug_send_msg).setOnClickListener((view)->{
//            WLProtocolManager.getInstance().sendPhonePlayVideoStateInfo("true");
//        });
    }
    private boolean isWifiDirectSupported(Context ctx) {
        if(getPackageManager().hasSystemFeature("android.hardware.wifi.direct")){
            return true;
        }

    //Wifi direct available
        return false;
    }
    @Override
    protected void initDialogContext(Context context) {
        //
        SkinManager.getInstance().registerLayoutInflater(context, SCREEN_CAR);
        LogManager.e("initCarDialog");
        SkinManager.getInstance().initCarDialog(context);
        HUCommandImpl.isDialogSkinInied = true;
        if (LocaleUtil.locale != null) {
            SkinManager.getInstance().changeSkin(SCREEN_CAR, "", LocaleUtil.locale);
        }
        //
        super.initDialogContext(context);
        LogManager.d("initDialogContext start");
        DialogInit = true;
        PageChangeUtils.sendToPage(-1, Configs.PAGE_MAIN, Configs.PAGE_MAIN, LocaleUtil.locale);
        LogManager.d("initDialogContext end");
    }

    @Override
    protected void disconnect() {
        super.disconnect();
        SkinManager.getInstance().unregisterLayoutInflater(SCREEN_CAR);
    }

    @Override
    public void loadModuleService() {
        super.loadModuleService();
        haiwaiService = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
        haiwaiService.startServiceImpl();
        //开始校准超时处理
        if (haiwaiReceiver == null) {
            haiwaiReceiver = new HaiwaiReceiver(haiwaiService);
        }
        haiwaiReceiver.register();
    }

    @Override
    protected Class<ActivityAndDialogPresenter> getPresenterClass() {
        return ActivityAndDialogPresenter.class;
    }

    @Override
    protected ActivityOrDialogView getSingletonView() {
        return this;
    }

    @Override
    public PageManager getPageManager(AppActivity appActivity) {
        if (mPageManager == null) {
            mPageManager = new PageManagerPhone(appActivity);
        }
        return mPageManager;
    }

    @Override
    public DialogManager getDialogManager(ViewBaseManager viewBaseManager) {
        return new HaiwaiLauncherDialog(viewBaseManager);
    }

    @Override
    public int getRootViewId() {
        return R.id.main_container;
    }

    @Override
    public void connectionStatus(int status) {
        PageChangeUtils.sendToPage(0, Configs.PAGE_MAIN, Configs.PAGE_MAIN, status);

    }

    @Override
    public int getAnimatorResId() {
        return R.id.animator;
    }


    @Override
    public int getMainViewLayout() {
        return R.layout.haiwai_launcher_activity_shell;
    }

    @Override
    public void initPlatform() {
        super.initPlatform();
        DevelopModel.getInstance().syncDeviceSafetyStatus(getApplicationContext());
        boolean allPermissionsGranted = true;
        String permissions[] =getMustRequestPermissions();
            for(String permission  : permissions){
            if(checkSelfPermission(permission) != PackageManager.PERMISSION_GRANTED) {
                LogManager.d("allPermissionsGranted--->permission："+permission);
                allPermissionsGranted = false;
                break;
            }
        }
        //权限同意后开启互联
        if(allPermissionsGranted){
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            if (platformManager != null ) {
                platformManager.requestDirectLink();
            }
        }
    }

    @Override
    public View getUnuseView() {
        return null;
    }

    @Override
    public BasePopDialog createPopDialog(int i) {
//        return PopDialogFactory.createPopDialog(i, this);
        return null;
    }

    @Override
    public void unloadModuleService() {
        super.unloadModuleService();
        INSTANCE = null;
        if (haiwaiReceiver != null) {
            haiwaiReceiver.unregister();
            haiwaiReceiver = null;
        }
        WLScreenManager wlScreenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
        if (wlScreenManager != null) {
            wlScreenManager.unloadService();
        }
        haiwaiService = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        SkinManager.getInstance().unregisterLayoutInflater(SCREEN_PHONE);
        System.exit(0);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == 101) {
            // 检查所请求的权限是否已经被用户授权
            LogManager.i("grantResults.length -> "+grantResults.length);
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限已被授权
                // 可以执行需要该权限的操作
             /*   boolean aBoolean = SharePreferenceUtil.getBoolean(this, "SaveNext", false);
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                if (platformManager != null && aBoolean) {
                    platformManager.requestDirectLink();
                }*/
               /* boolean isOn = WLScreen.isAccessibilitySettingsOn(this);
                LogManager.d("isAccessibilitySettingsOn -------------->"+isOn);
                if(!isOn) {
                    Intent intent = new Intent("android.settings.ACCESSIBILITY_SETTINGS");
                    startActivity(intent);
                }*/
//                InputManagerTest.init();
            } else {
                // 权限被用户拒绝
                // 可以引导用户去设置页面手动开启权限
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        LogManager.i("onKeyDown KEYCODE_BACK? -> " + (keyCode == KeyEvent.KEYCODE_BACK));
        boolean returnFlag;
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            BasePage currentPage = getActivity().getViewInterface().getCurrentPageObj().getPage();
            if (currentPage.isMainPage()) {
                LogManager.i("onKeyDown isMainPage? -> true");
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    //当前页面是主页
                    LogManager.i("onKeyDown ACTION_DOWN? -> true");
                    if (currentPage instanceof BaseMainPage) {
                        if (((BaseMainPage) currentPage).cannotViewBack()) {
                            if(System.currentTimeMillis() -mLastTime <2000){
                                finish();
                            }else{
                                mLastTime = System.currentTimeMillis();
                                Toast.makeText(this, getString(R.string.haiwai_launcher_eixt), Toast.LENGTH_SHORT).show();
                            }
//                            moveTaskToBack(true);
                        } else {
                            BaseMainPage baseMainPage = (BaseMainPage) currentPage;
                            baseMainPage.removeView();
                        }
                    }
                } else {
                    LogManager.i("onKeyDown ACTION_DOWN? -> false");
                }
            } else {
                //当前不在主页
                LogManager.i("onKeyDown isMainPage? -> false");
                PageChangeUtils.showPrevious(-1);
            }
            returnFlag = true;
        } else {
            returnFlag = super.onKeyDown(keyCode, event);
        }
        return returnFlag;
    }

    protected String[] getMustRequestPermissions() {
        String[] permissions;
        if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.TIRAMISU){
            permissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION,
                    /*          Manifest.permission.CHANGE_WIFI_STATE,
                              Manifest.permission.ACCESS_WIFI_STATE,*/
                    Manifest.permission.NEARBY_WIFI_DEVICES,
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.POST_NOTIFICATIONS};
        }else if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.S){
            permissions = new String[]{
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    /* Manifest.permission.CHANGE_WIFI_STATE,
                     Manifest.permission.ACCESS_WIFI_STATE,*/
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
            };
        }else{
            permissions = new String[]{
                    Manifest.permission.ACCESS_FINE_LOCATION
            };
        }
        return permissions;
    }

    public void checkPermission() {
        Uri uri = Uri.parse("package:" + getPackageName());
        Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, uri);
        startActivity(intent);

    }

    public void setOrientation(int orientation) {
        OrientationParam param = new OrientationParam(orientation);
        if (view == null) {
            view = new View(getApplicationContext());
            getWindowManager().addView(view, param);
        } else  {
            getWindowManager().updateViewLayout(view, param);
        }
    }
    class OrientationParam extends WindowManager.LayoutParams {
        public OrientationParam(int orientation) {
            super(0, 0, 2038, 8, -3);
            this.gravity = 48;
            this.screenOrientation = orientation;

        }
    }
    ArrayList<ArrayList<Float>> views = new ArrayList<>();
    private boolean isItClickable = true;
    private boolean isItAtTheFrontDesk = false;
    public void setSpot(){
        views.clear();
        LogManager.e("setSpot == setSpot" );
        findViewById(R.id.viewtouch).setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent motionEvent) {
                if (motionEvent.getAction()==MotionEvent.ACTION_DOWN){
                    LogManager.e("fragment_container == ");
                    // 获取坐标
                    float x = motionEvent.getX(); // 获取X坐标
                    float y =  motionEvent.getY(); // 获取Y坐标
                    ArrayList<Float> pos1 = new ArrayList<>();
                    pos1.add(x);
                    pos1.add(y);
                    views.add(pos1);
                    LogManager.e("fragment_container == ["+x+","+y+"]");
                }
                return isItClickable;
            }
        });
    }
    public void setStopSpot(){
        WLScreenManager screenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
         screenManager.startScreen();
        LogManager.e("setStopSpot == setStopSpot" );
        isItClickable = false;
//        WLProtocolManager.getInstance().sendCalibrationData(views);
    }

  public int setReady(int controlState){
       LogManager.e("setReady == setReady" );
        if (controlState != 0){
          new Handler().postDelayed(new Runnable() {
              @Override
              public void run() {
                  WLScreenManager screenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
                  screenManager.startScreen();
              }
          },200);
        }
       return controlState;
    }

    /**
     * 授权录屏
     */
    public void reauthorizationRecordScreen() {
        Log.i("HaiwaiLauncherActivity", "reauthorizationRecordScreen AppUtils.isAppForeground() = " + AppUtils.isAppForeground());
        if(AppUtils.isAppForeground()){
            SendMsgToCarHelper.sendActionCarHome("reauthorize_record_screen", "foreground");
        } else {
            SendMsgToCarHelper.sendActionCarHome("reauthorize_record_screen", "background");
            return;
        }
        WLScreenManager screenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
        screenManager.startScreen();
    }

    @Override
    protected void onResume() {
        super.onResume();
        isItAtTheFrontDesk = true;
    }
    @Override
    protected void onStop() {
        super.onStop();
        isItAtTheFrontDesk = false;
    }
    public boolean isItAtTheFrontDesk() {
            return isItAtTheFrontDesk;
        }

}
