package com.a2ra9k.android.launcher.service.overlaydialog.base;

import android.app.Dialog;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;

import java.util.Locale;

public abstract class BaseDialog {

    /**
     * 左、上边距
     */
    protected int mLeft, mTop;
    protected Handler mHandler;
    protected View rootView;

    protected BaseDialog() {
        this(-1, -1);
    }

    protected BaseDialog(int l, int t) {
        mLeft = l;
        mTop = t;
        mHandler = new Handler(Looper.getMainLooper());
    }

    public abstract void showDialog();

    /**
     * 更新布局方向
     *
     * @param locale language
     */
    public void updateLayoutDirection(Locale locale) {
        if (rootView instanceof ViewGroup) {
            LogManager.v("更换布局方向 updateLayoutDirection：Car-->" + locale.getLanguage());
            ViewGroup viewGroup = (ViewGroup) rootView;
            int direction = TextUtils.equals(locale.getLanguage(), "ar") ?
                    View.LAYOUT_DIRECTION_RTL : View.LAYOUT_DIRECTION_LTR;
            viewGroup.setLayoutDirection(direction);
        }
    }

    public void dismissOverlayDialog() {
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        WLPlatformManager wlPlatformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        if (wlPlatformManager != null) {
            Dialog overlayDialog = wlPlatformManager.getOverlayDialog();
            if (overlayDialog != null) {
                overlayDialog.dismiss();
            }
        }
    }

    public void updateLocationOpen() {

    }

    public int getLeft() {
        return mLeft;
    }

    public int getTop() {
        return mTop;
    }

}
