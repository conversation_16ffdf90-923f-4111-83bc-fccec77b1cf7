//package com.a2ra9k.android.launcher.service;
//
//import android.bluetooth.BluetoothAdapter;
//import android.bluetooth.BluetoothDevice;
//import android.content.Context;
//import android.text.TextUtils;
//
//import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
//import com.autoai.fundrive.commontool.LogManager;
//import com.autoai.fundrive.platform.WLPlatformManager;
//import com.wedrive.android.welink.bluetooth.v2.BluetoothManager;
//import com.wedrive.android.welink.bluetooth.v2.WLBluetoothMuListener;
//
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//
//public class BluetoothHelper {
//
//    private BluetoothManager mBluetoothManager = null;
//    private final OverlayDialogManager mOverlayDialogManager;
//
//    public BluetoothHelper(OverlayDialogManager overlayDialogManager) {
//        mOverlayDialogManager = overlayDialogManager;
//        LogManager.d("OverlayDialogManager BluetoothService construct");
//    }
//
//    static class WLBluetoothMuListenerInner implements WLBluetoothMuListener {
//
//        @Override
//        public void onStateBTChanged(int state) {
//            LogManager.d("OverlayDialogManager onStateBTChanged state = " + state);
//            HaiwaiService haiwaiService = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
//            if (haiwaiService != null) {
////                LogManager.d("onStateBTChanged state=" + state);
////                if (state == BluetoothState.STATUS_BT_PAIR_FAILED) {
////                    OverlayDialogManager dialogManager = haiwaiService.getDialogManager();
////                    dialogManager.showBluetooth();
////                } else {
////                    OverlayDialogManager dialogManager = haiwaiService.getDialogManager();
////                    dialogManager.showRectangleDialog();
////                }
//                OverlayDialogManager dialogManager = haiwaiService.getDialogManager();
//                dialogManager.showRectangleDialog();
//            }
//
//        }
//    }
//
//    public void connect(Context context) {
//        if (context == null) {
//            mOverlayDialogManager.showRectangleDialog();
//            return;
//        }
//
//        WLPlatformManager wlPlatformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
//        String huBtMacAddress = wlPlatformManager.getHuBtMacAddress();
//        LogManager.d("OverlayDialogManager connect huBtMacAddress" + huBtMacAddress);
//        if (checkCurBT(huBtMacAddress)) {
//            mOverlayDialogManager.showRectangleDialog();
//            return;
//        }
//        mBluetoothManager = new BluetoothManager(context, new WLBluetoothMuListenerInner());
//        mBluetoothManager.connect(huBtMacAddress, context, "android", 0);
//    }
//
//    public void destroy() {
//        if (mBluetoothManager != null) {
//            mBluetoothManager.unregisterReceivers();
//            mBluetoothManager = null;
//        }
//        LogManager.d("OverlayDialogManager BluetoothHelper destroy");
//    }
//
//    /**
//     * 判断已连蓝牙设备是不是当前所互联的设备
//     *
//     * @param btMacAddress 互联设备的蓝牙MAC地址
//     * @return true：互联设备和已连蓝牙设备是同一个；false：反之
//     */
//    private boolean checkCurBT(String btMacAddress) {
//        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
//        if (adapter == null) {
//            LogManager.d(" -->OverlayDialogManager BluetoothHelper bluetooth is not support.");
//            return false;
//        }
//        try {
//            if (!TextUtils.isEmpty(btMacAddress)) {
//                BluetoothDevice bd = adapter.getRemoteDevice(btMacAddress);
//                Method isConnectedMethod = BluetoothDevice.class.getDeclaredMethod("isConnected", (Class<?>[]) null);
//                boolean isConnected = (boolean) isConnectedMethod.invoke(bd, (Object[]) null);
//                LogManager.d("OverlayDialogManager BluetoothHelper checkCurBT bluetooth name = " + bd.getName() + ", address = " + bd.getAddress() + ", isConnected = " + isConnected);
//                if (isConnected) {
//                    return true;
//                }
//            }
//        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
//            e.printStackTrace();
//            LogManager.d("OverlayDialogManager BluetoothHelper checkCurBT Exception " + e);
//        }
//        return false;
//    }
//}
