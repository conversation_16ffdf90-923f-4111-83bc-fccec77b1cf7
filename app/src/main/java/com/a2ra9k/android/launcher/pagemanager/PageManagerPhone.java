package com.a2ra9k.android.launcher.pagemanager;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.view.page.main.PhoneMainPage;
import com.mapbar.android.model.BasePage;
import com.mapbar.android.model.PageObject;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.basemodule.activity.AppActivity;

/**
 * 手机端页面管理
 *
 * <AUTHOR>
 */
public class PageManagerPhone implements PageManager {

    private final AppActivity mAppActivity;

    public PageManagerPhone(@NonNull AppActivity aAppActivity) {
        mAppActivity = aAppActivity;
    }

    @SuppressLint("InflateParams")
    @Override
    public PageObject createPage(int index) {
        BasePage page = null;
        View view = null;
        if (index == Configs.PAGE_MAIN) {
            view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_main_page_phone, null);
            page = new PhoneMainPage(mAppActivity, view);
        }
        if (page == null) {
            return null;
        }
        return new PageObject(index, view, page);
    }

}