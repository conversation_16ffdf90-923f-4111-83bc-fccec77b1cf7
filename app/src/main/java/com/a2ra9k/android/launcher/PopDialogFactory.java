package com.a2ra9k.android.launcher;

import android.content.Context;

import com.autoai.fundrive.basemodule.widget.BasePopDialog;
import com.autoai.fundrive.commontool.LogManager;


public class PopDialogFactory {

    public static BasePopDialog createPopDialog(int aIndex, Context aContext) {
        BasePopDialog returnDialog = null;
        LogManager.d("PopDialogFactory-aIndex---------"+aIndex);
        if (returnDialog == null) {
            switch (aIndex) {
                case 1001:
                    //退出APP提示框
//                    returnDialog = new ExitPopDialog(aIndex);
                    break;
                case 1002:
                    //网络异常提示框
//                    returnDialog = new NetErrorPopDialog(aIndex);
                    break;
                case 1003:
                case 1004:
                case 1005:
                    //wifi 无感互联弹框
//                    returnDialog = new WiFiBleLinkPopDialog(aIndex);
                    break;
                default:
                    break;
            }
        }
        return returnDialog;
    }
}
