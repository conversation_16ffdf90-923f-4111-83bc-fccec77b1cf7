package com.a2ra9k.android.launcher.service.overlaydialog.floating;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.graphics.BitmapFactory;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.a2ra9k.android.launcher.R;

/**
 * 悬浮按钮
 */
public class RectangleDialog extends BaseFloatingDialog {

    private LinearLayout mStatusBar;

    public RectangleDialog(int l, int t) {
        super(l, t);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void showDialog() {
        Dialog overlayDialog = initDialog();
        if(overlayDialog == null){
            return;
        }
        rootView = overlayDialog.findViewById(R.id.dialogRootView);
        rootView.setVisibility(View.GONE);
        mGroupAction = overlayDialog.findViewById(R.id.group_action);
        mPhoneHomeAction = overlayDialog.findViewById(R.id.phone_home_action);
        mBackAction = overlayDialog.findViewById(R.id.back_action);
        mCarHomeAction = overlayDialog.findViewById(R.id.car_home_action);
        mStatusBar = overlayDialog.findViewById(R.id.group_status_bar);
//        logTv = overlayDialog.findViewById(R.id.logTv);
        //初始化展开控件属性
        groupBarClose();
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeResource(mStatusBar.getResources(), mStatusBarRes, options);
        mBackgroundWidth = options.outWidth;
        mBackgroundHeight = options.outHeight;
        BitmapFactory.decodeResource(mStatusBar.getResources(), GROUP_STATUS_CLOSR, options);
        mUploadLocationWidth = options.outWidth;
        mUploadLocationHeight = options.outHeight;
        //监听
//        overlayDialog.setOnKeyListener((dialog, keyCode, event) -> event.getKeyCode() == KeyEvent.KEYCODE_BACK);
        mStatusBar.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    mMove = false;
                    mStartX = event.getRawX();
                    mStartY = event.getRawY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    mMove = true;
                    //展开不能拖动
                    if ((int) mGroupAction.getTag() == GROUP_STATUS_OPEN) {
                        mPause = true;
                        return true;
                    }
                    int x = (int) (event.getRawX() - mStartX);
                    int y = (int) (event.getRawY() - mStartY);
                    mLeft = mStatusBar.getLeft() + x;
                    mTop = mStatusBar.getTop() + y;
                    int right = mStatusBar.getRight() + x;
                    int bottom = mStatusBar.getBottom() + y;
                    //边界限定
                    if (mLeft <= 0 || right >= CAR_SCREEN_WIDTH_PIXEL) {
                        mLeft = mStatusBar.getLeft();
                        right = mStatusBar.getRight();
                    }
                    if (mTop <= 0 || bottom >= CAR_SCREEN_HEIGHT_PIXEL) {
                        mTop = mStatusBar.getTop();
                        bottom = mStatusBar.getBottom();
                    }
                    mStatusBar.layout(mLeft, mTop, right, bottom);
                    mStartX = event.getRawX();
                    mStartY = event.getRawY();
                    break;
                case MotionEvent.ACTION_UP:
                    v.performClick();
                    int marginRight = 0;
                    if (!mMove) {
                        int status = (int) mGroupAction.getTag();
                        if (status == GROUP_STATUS_OPEN) {
                            mPause = false;
                            groupBarClose();
                            updateLocationClose();
                        } else {
                            groupBarOpen();
                            //越界计算，复位使用
                            marginRight = CAR_SCREEN_WIDTH_PIXEL - mLeft - mBackgroundWidth;
                            updateLocationOpen();
                        }
                    }
                    if (!mPause) {
                        int left = marginRight < 0 ? mLeft + marginRight : mLeft;
                        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mStatusBar.getLayoutParams();
                        layoutParams.setMargins(left, mTop, 0, 0);
                        mStatusBar.setLayoutParams(layoutParams);
                    }
                    break;
            }
            return true;
        });
        mCarHomeAction.setOnClickListener(v -> actionCarHome());
        mPhoneHomeAction.setOnClickListener(v -> actionPhoneHome());
        mBackAction.setOnClickListener(v -> actionBack());
        //定位
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mStatusBar.getLayoutParams();
        if (mLeft < 0) {
            mLeft = POSITION_SPACING_START;
        }
        if (mTop < 0) {
            mTop = CAR_SCREEN_HEIGHT_PIXEL - mBackgroundHeight - POSITION_SPACING_BOTTOM;
        }
        layoutParams.setMargins(mLeft, mTop, 0, 0);
        mStatusBar.setLayoutParams(layoutParams);

        overlayDialog.show();
    }

    private void groupBarOpen() {
        mStatusBar.setBackgroundResource(mStatusBarRes);
        mGroupAction.setTag(GROUP_STATUS_OPEN);
        mGroupAction.setBackgroundResource(GROUP_STATUS_CLOSR);
        mBackAction.setVisibility(View.VISIBLE);
        mPhoneHomeAction.setVisibility(View.VISIBLE);
        mCarHomeAction.setVisibility(View.VISIBLE);
    }

    private void groupBarClose() {
        mStatusBar.setBackgroundResource(android.R.color.transparent);
        mGroupAction.setTag(GROUP_STATUS_CLOSR);
        mGroupAction.setBackgroundResource(GROUP_STATUS_OPEN);
        mBackAction.setVisibility(View.GONE);
        mPhoneHomeAction.setVisibility(View.GONE);
        mCarHomeAction.setVisibility(View.GONE);
    }
}

