package com.a2ra9k.android.launcher;

import android.app.Application;

import com.autoai.fundrive.commontool.DevelopModel;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.SharePreferenceUtil;
import com.autoai.welink.logiclib.skincore.SkinManager;
import com.autoai.fundrive.basemodule.BaseApplication;
import com.autoai.welink.screen.WLScreen;

public class HaiwaiApplication extends Application {

    @Override
    public void onCreate() {
    /*    if (BuildConfig.DEBUG){
             SharePreferenceUtil.getInstance(getApplicationContext()).setIsDebug(true);
        }*/
        boolean isDebug = DevelopModel.getInstance().getData(this).isLogEnabled();
        //SharePreferenceUtil.getInstance(getApplicationContext()).getIsDebug();
        LogManager.setIsLoggable(isDebug);
        LogManager.setIsFileLoggable(isDebug);
        LogManager.init(this);
        WLScreen.setLogMode(isDebug);
//        com.autoai.welink.platform.utiliy.AnFileLog.enableLogCat(isDebug);
        LogManager.registerUncaughtExceptionHandler(this);
        super.onCreate();
        BaseApplication.init(this, BuildConfig.class, ModuleManager.class);
        SkinManager.getInstance().initPhone(this);
//        com.autoai.welink.platform.utiliy.AnFileLog.enableLogCat(isDebug);
//        com.autoai.welink.channel.utiliy.AnFileLog.enableLogCat(isDebug);
        LogManager.i("initPhone context-->" + this+",isDebug:"+isDebug);

        DeviceAuthManager.INSTANCE.init();
    }
}
