package com.a2ra9k.android.launcher.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HIDSupportImpl {
    private final Map<String, List<String>> mSupportList;
    public static final String VEHICLE_HID = "0000001";
    public static final String HAIWAI_HID_SN = "HPSQDZQNX650LHLNTIJ6PLUS083";

    /**
     * 互联以后从车机端获取到的SN码
     */
    private String sSN = null;

    public HIDSupportImpl() {
        mSupportList = new HashMap<>(8);

        List<String> haiwaiList = new ArrayList<>(8);
        haiwaiList.add(VEHICLE_HID);
        mSupportList.put(HAIWAI_HID_SN, haiwaiList);
    }

    public void setSN(String aSN) {
        sSN = aSN;
    }

    public boolean isHuSupport(String aName) {
        return sSN != null && mSupportList != null && mSupportList.containsKey(sSN) && mSupportList.get(sSN).contains(aName);
    }
}
