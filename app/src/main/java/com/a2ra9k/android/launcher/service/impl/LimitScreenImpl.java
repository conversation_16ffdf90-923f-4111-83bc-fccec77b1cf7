package com.a2ra9k.android.launcher.service.impl;

import android.content.Context;
import android.os.PowerManager;
import android.view.KeyEvent;

import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.OverlayDialogManager;
import com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity;
import com.a2ra9k.android.launcher.view.page.main.BaseMainPage;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.hidscreen.LimitScreenListener;
import com.autoai.fundrive.hidscreen.WLScreenManager;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.mapbar.android.model.BasePage;

public class LimitScreenImpl {

    //开始投屏标记
    private volatile boolean mScreenProjection;
    //互联标记，为处理阿拉伯语言交互新增
    private volatile boolean mLinked;
    private final OverlayDialogManager mOverlayDialogManager;
//    private final BluetoothHelper mBluetoothHelper;
    
    // 添加 PowerManager 和 WakeLock
    private PowerManager.WakeLock mWakeLock;
    private final Context mContext;

    public LimitScreenImpl(Context context, OverlayDialogManager overlayDialogManager) {
        mOverlayDialogManager = overlayDialogManager;
        mContext = context;
        // 初始化 PowerManager
        PowerManager powerManager = (PowerManager) mContext.getSystemService(Context.POWER_SERVICE);
        if (powerManager != null) {
            // 保持应用后台的时候屏幕不锁屏
            mWakeLock = powerManager.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK, "LimitScreenImpl:WakeLock");
        }
        LogManager.d("LimitScreenImpl construct");
    }

    public void startLimitScreen() {
        LogManager.d("LimitScreenImpl startLimitScreen");
        WLScreenManager screenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
        screenManager.setLimitScreenListener(new LimitScreenListener() {

            @Override
            public void onRequestAuxiliaryMode() {
                LogManager.d("LimitScreenImpl onRequestAuxiliaryMode");
//                ThreadPoolUtil.getInstance().runOnUiThread(() ->
//                PageChangeUtils.showPage(Configs.PAGE_MAIN, Configs.PAGE_MAIN, "app", Configs.PAGE_AUXILIARY, null, true, null, null));
            }

            @Override
            public void onRequestFullScreenLimit() {
                LogManager.d("LimitScreenImpl onRequestFullScreenLimit");
                ThreadPoolUtil.getInstance().runOnUiThread(() ->
                        PageChangeUtils.showPage(Configs.PAGE_MAIN, Configs.PAGE_MAIN, "app", Configs.PAGE_MIRRORING_SUC, null, true, null, null));
            }

            @Override
            public void onStartScreen() {
                LogManager.d("LimitScreenImpl onStartScreen");
                mScreenProjection = true;
                ThreadPoolUtil.getInstance().runOnUiThread(() -> {
                    PageChangeUtils.showPage(Configs.SCREEN_CAR, Configs.VIEW_PAGE_FLAG, "app", Configs.PAGE_SUCCESS, null, true, null, null);
                    PageChangeUtils.sendToPage(Configs.VIEW_PAGE_FLAG, Configs.PAGE_MAIN, "MASK");
                });
//                mBluetoothHelper.connect(HaiwaiLauncherActivity.getInstance());
            }

            @Override
            public void onStopScreen() {
                LogManager.d("LimitScreenImpl onStopScreen");
                if (mScreenProjection) {
                    mScreenProjection = false;
                    ThreadPoolUtil.getInstance().runOnUiThread(() ->
                            PageChangeUtils.sendToPage(Configs.PAGE_MAIN, Configs.PAGE_MAIN, KeyEvent.KEYCODE_BACK));
                }
                mOverlayDialogManager.dismissOverlayDialog();
                ThreadPoolUtil.getInstance().runOnUiThread(() ->
                        PageChangeUtils.showPage(Configs.PAGE_MAIN, Configs.PAGE_MAIN, "app", Configs.PAGE_MIRRORING_FAIL, null, true, null, null));
            }

            @Override
            public void onLink() {
                mLinked = true;
                // 互联成功，获取 WakeLock
                acquireWakeLock();
                
                WLScreenManager screenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
                //TODO:bruce 拉起手机录屏授权弹窗
                screenManager.startScreen();
            }

            @Override
            public void onUnLink() {
                LogManager.d("LimitScreenImpl onUnLink");
                mLinked = false;
                // 互联断开，释放 WakeLock
                releaseWakeLock();
                
                ThreadPoolUtil.getInstance().runOnUiThread(() -> {
                    HaiwaiService service = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
                    HUCommandImpl huCommand = service.getHUCommand();
                    huCommand.languageRecovery();

                    BasePage currentPage = HaiwaiLauncherActivity.getInstance().getActivity().getViewInterface().getCurrentPageObj().getPage();
                    if (currentPage instanceof BaseMainPage) {
                        BaseMainPage mainPage = (BaseMainPage) currentPage;
                        mainPage.removeSuccessView();
                    }
                });
            }

            @Override
            public void onLinkStarted() {
                LogManager.d("LimitScreenImpl onLinkStarted");
//                mOverlayDialogManager.updateLocationOpen();
            }

            @Override
            public void onLinkStopped() {
                LogManager.d("LimitScreenImpl onLinkStopped");
            }

            @Override
            public void onResumeScreen() {
                LogManager.d("LimitScreenImpl onResumeScreen");
            }

            @Override
            public void onPauseScreen() {
                LogManager.d("LimitScreenImpl onPauseScreen");
            }
        });
    }

    // 获取 WakeLock
    private void acquireWakeLock() {
        LogManager.d("LimitScreenImpl acquireWakeLock");
        if (mWakeLock != null && !mWakeLock.isHeld()) {
            try {
                mWakeLock.acquire();
                LogManager.d("LimitScreenImpl WakeLock acquired successfully");
            } catch (Exception e) {
                LogManager.e("LimitScreenImpl acquireWakeLock error: " + e.getMessage());
            }
        }
    }

    // 释放 WakeLock
    private void releaseWakeLock() {
        LogManager.d("LimitScreenImpl releaseWakeLock");
        if (mWakeLock != null && mWakeLock.isHeld()) {
            try {
                mWakeLock.release();
                LogManager.d("LimitScreenImpl WakeLock released successfully");
            } catch (Exception e) {
                LogManager.e("LimitScreenImpl releaseWakeLock error: " + e.getMessage());
            }
        }
    }

    public void stopLimitScreen() {
        LogManager.d("LimitScreenImpl stopLimitScreen");
        mScreenProjection = false;
        // 确保在停止限制屏幕时释放 WakeLock
        releaseWakeLock();
//        mBluetoothHelper.destroy();
    }

    public boolean isStartScreen() {
        return mScreenProjection;
    }

    public boolean isLinked() {
        return mLinked;
    }


}
