ext {
    //app相关的版本
    android = [
            compileSdk      : 34,
            minSdk          : 24,
            targetSdk       : 34,
            versionCode     : 23,
            versionName     : '1.0.23',
            jvmTarget       : '1.8',
    ]
    COMMON_DEPEN = [
            MAPBAR_ANDROID_APPFRAMEWORK    : 'com.autoai.welink.lib:appframework:1.0.8',
            MAPBAR_ANDROID_ONE_APPFRAMEWORK: 'com.autoai.welink.lib:OneappFramework:1.0.0',
            MAPBAR_ANDROID_GUID            : 'com.autoai.welink.lib:guid:1.3.1',
            MAPBAR_ANDROID_MIRROR          : 'com.autoai.welink.lib:mirror:1.6.1',
//            //互联所用的第一方库
            MAPBAR_ANDROID_WLPLATFORM      : 'com.autoai.avs.link:wlplatform:0.1.0.0.14.33',
            MAPBAR_ANDROID_WLCONNECTOR     : 'com.autoai.avs.link:wlconnector:0.1.0.0.14.33',
            MAPBAR_ANDROID_WLHARDWAREHUB   : 'com.autoai.avs.link:wlhardwarehub:0.1.0.0.14.33',
            MAPBAR_ANDROID_WLSERVER        : 'com.autoai.avs.link:wlserver:0.1.0.0.14.33',
            MAPBAR_ANDROID_WLCHANNEL       : 'com.autoai.avs.link:wlchannel:0.1.0.0.14.33',
            MAPBAR_ANDROID_WLSCREEN       : 'com.autoai.avs.link:wlscreen:0.1.0.0.14.33',
//            //扩展投屏库
            MAPBAR_ANDROID_WLSCREEN_HID    : 'com.autoai.welink.platform.lib:wlscreen_hid:1.5.1.20',
            VIVO_CAR_SDK                   : 'com.autoai.welink.lib:vivo-car-sdk:2.0.56',
//            WELINK_NETHTTPS_MOBSTAT        : 'com.autoai.welink.lib:mobstat0516:1.0.0',
//            WELINK_AIUI                    : 'com.autoai.welink.lib.aiui:shangqi:1.0.0',
//            WELINK_MSC                     : 'com.autoai.welink.lib.msc:shangqi:1.0.0',
//
            WELINK_LIBSEMANTICSSPEECHAPI   : 'com.autoai.welink.lib:libsemanticsspeechapi-release:1.0.0',
            //RXJava
            RXJAVA2_RXJAVA                 : 'io.reactivex.rxjava2:rxjava:2.1.6',
            RXJAVA2_RXANDROID              : 'io.reactivex.rxjava2:rxandroid:2.0.1',
//            //guava- welintimer定时器，并发线程处理 用到了
//            GUAVA_JRE                      : 'com.google.guava:guava:28.1-jre',
            GUAVA_ANDROID                  : 'com.google.guava:guava:28.1-android',
//            //json数据解析
            GSON                           : 'com.google.code.gson:gson:2.8.5',
            FASTJSON                       : 'com.alibaba:fastjson:1.2.83',
//
            LOADSIR                        : 'com.kingja.loadsir:loadsir:1.3.6',
            //
            COMMONS_IO                     : 'commons-io:commons-io:2.6',
            //AndroidX
            V4                             : 'androidx.legacy:legacy-support-v4:1.0.0',
            V7                             : 'androidx.appcompat:appcompat:1.2.0',
            DESIGN                         : 'com.google.android.material:material:1.2.1',
//            CARDVIEW                       : 'androidx.cardview:cardview:1.0.0',
            ANNOTATIONS                    : 'androidx.annotation:annotation:1.1.0',
            RECYCLERVIEW                   : 'androidx.recyclerview:recyclerview:1.0.0',
            CONSTRAINT_LAYOUT              : 'androidx.constraintlayout:constraintlayout:2.0.4',
//
            JESSYAN_AUTOSIZE               : 'me.jessyan:autosize:1.2.1',
    ]
}
