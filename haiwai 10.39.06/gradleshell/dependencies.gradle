//在每个项目组件Project的每个Module的build.gradle中执行
//当前project是Application还library类型判断
PluginContainer pluginContainer = project.getPlugins()
Plugin plugin = pluginContainer.findPlugin('com.android.application')
def isApp = (plugin && plugin.toString().contains('AppPlugin'))
//版本信息,版本信息根据各个模块来的，不可以使用rootProject
if (!project.hasProperty("packageVersionName")) {
    project.ext.packageVersionName = getPackageVersionName(project)
    if(isApp){
        project.logger.log(LogLevel.LIFECYCLE, "packageVersionName = $packageVersionName")
    }
}
//各类参数配置到项目代码环境
android {
    defaultConfig {
        //版本信息
        buildConfigField("String", "packageVersionName", "\"${project.ext.packageVersionName}\"")
    }
}
//整体排除所有废弃的代码
configurations {
    all*.exclude group: 'com.autoai.welink.lib.commonlibs', module: 'commontool'
    all*.exclude group: 'com.autoai.welink.lib.commonlibs', module: 'messagebus'
    all*.exclude group: 'com.autoai.welink.lib.commonlibs', module: 'humanager'
    all*.exclude group: 'com.autoai.welink.lib.commonlibs', module: 'welinkplatform'
    all*.exclude group: 'com.autoai.welink.lib.commonlibs', module: 'statistics'
    all*.exclude group: 'fakepath', module: 'libammsdk'

//    all*.exclude group: 'com.autoai.welink.platform.lib', module: 'wlplatform'
//    all*.exclude group: 'com.autoai.welink.platform.lib', module: 'wlconnector'
//    all*.exclude group: 'com.autoai.welink.platform.lib', module: 'wlhardwarehub'
//    all*.exclude group: 'com.autoai.welink.platform.lib', module: 'wlserver'
//    all*.exclude group: 'com.autoai.welink.platform.lib', module: 'wlchannel'

//    all*.exclude group: 'com.google.guava', module: 'guava'
    all*.exclude group: 'org.simple', module: 'androideventbus'
    all*.exclude group: 'com.autoai.welink.lib', module: 'volley'
//    all*.exclude group: 'jp.wasabeef', module: 'glide-transformations'
    all*.exclude group: 'com.nostra13.universalimageloader', module: 'universal-image-loader'
//    all*.exclude group: 'commons-io', module: 'commons-io'
    all*.exclude group: 'com.autoai.welink.lib', module: 'NetHttps20131017'


//    all*.exclude group: 'junit', module: 'junit'
//    all*.exclude group: 'androidx.test', module: 'runner'
//    all*.exclude group: 'androidx.test', module: 'rules'
//    all*.exclude group: 'androidx.test', module: 'junit'
//    all*.exclude group: 'androidx.test.espresso', module: 'espresso-core'
//    all*.exclude group: 'androidx.test.espresso', module: 'espresso-contrib'
//    all*.exclude group: 'androidx.test.espresso', module: 'espresso-web'
//    all*.exclude group: 'androidx.test.espresso', module: 'espresso-idling-resource'
//    all*.exclude group: 'androidx.test.espresso', module: 'espresso-intents'
//    all*.exclude group: 'org.mockito', module: 'mockito-android'
//    all*.exclude group: 'org.robolectric', module: 'robolectric'


    all*.exclude group: 'com.android.support', module: 'support-media-compat'
    all*.exclude group: 'com.android.support', module: 'support-compat'
    all*.exclude group: 'com.android.support', module: 'versionedparcelable'
}


project.logger.log(LogLevel.LIFECYCLE, "compileSdkVer = ${rootProject.ext.android.compileSdk}   , " +
        "minSdkVer = ${rootProject.ext.android.minSdk}   , " +
        "targetSdkVer   = ${rootProject.ext.android.targetSdk}   ")

dependencies {
    //使用maven仓库中的投屏sdk
    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLPLATFORM
    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLCONNECTOR
    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLSERVER
    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLCHANNEL
    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLSCREEN

//    implementation COMMON_DEPEN.MAPBAR_ANDROID_SCALEVIEW
    implementation COMMON_DEPEN.GSON
    //
    implementation COMMON_DEPEN.RXJAVA2_RXJAVA
    implementation COMMON_DEPEN.RXJAVA2_RXANDROID

    implementation COMMON_DEPEN.JESSYAN_AUTOSIZE
//    implementation COMMON_DEPEN.MAPBAR_ANDROID_KEYBOARDLIB

    implementation COMMON_DEPEN.V4
    implementation COMMON_DEPEN.V7
    implementation COMMON_DEPEN.ANNOTATIONS
    implementation COMMON_DEPEN.RECYCLERVIEW
    implementation COMMON_DEPEN.DESIGN
    implementation COMMON_DEPEN.CONSTRAINT_LAYOUT
//    //测试
//    testImplementation COMMON_DEPEN.JUNIT
//    testImplementation COMMON_DEPEN.TEST_MOCKITO_CORE
//    testImplementation COMMON_DEPEN.TEST_MOCKITO_ANDROID
//    testImplementation COMMON_DEPEN.TEST_ROBOLECTRIC
//    androidTestImplementation COMMON_DEPEN.TEST_MOCKITO_CORE
//    androidTestImplementation COMMON_DEPEN.TEST_MOCKITO_ANDROID
//    androidTestImplementation COMMON_DEPEN.TEST_ROBOLECTRIC
//    androidTestImplementation COMMON_DEPEN.TEST_JUNIT
//    androidTestImplementation COMMON_DEPEN.TEST_RUNNER
//    androidTestImplementation COMMON_DEPEN.TEST_RULES
//    androidTestImplementation COMMON_DEPEN.TEST_ESPRESSO_CORE
//    androidTestImplementation(COMMON_DEPEN.TEST_ESPRESSO_CONTRIB) {
//        transitive false
//    }
//    androidTestImplementation COMMON_DEPEN.TEST_ESPRESSO_WEB
//    androidTestImplementation COMMON_DEPEN.TEST_ESPRESSO_IDLING_RESOURCE
//    androidTestImplementation COMMON_DEPEN.TEST_ESPRESSO_INTENTS
}


