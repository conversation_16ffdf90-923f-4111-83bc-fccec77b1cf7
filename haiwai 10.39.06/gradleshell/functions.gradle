//版本信息,版本信息根据各个模块来的，不可以使用rootProject
static def getPackageVersionName(Project project) {
    if (project.name == project.rootProject.name) {
        return ""
    }
    def gitVersion = 'git log -1 --pretty=%h'.execute([], project.rootDir).text.trim()
    def createTime = new Date().format("yyMMddHHmmss", TimeZone.getTimeZone("GMT+08:00"))
    "${project.rootProject.ext.android.versionName}_${createTime}_${gitVersion}"
//    "${project.rootProject.ext.android.versionName}"
}

static def assembleAPK(Project project) {
    return { variant ->
        def buildType = variant.buildType.name
        //只处理release版本
        if (buildType.contains("release")) {
            variant.outputs.each {
                if (project.rootProject.isOfficialVersion == 'true') {
                    it.outputFileName = "FunDrive_Link_手机端_${project.rootProject.ext.android.versionName}.apk"
                } else {
                    it.outputFileName = "FunDrive_Link_手机端_${getPackageVersionName(project)}.apk"
                }
                project.logger.log(LogLevel.LIFECYCLE, "assembleAPK: FileName ：${it.outputFileName}")
            }
            //这样我们就把自定义的task挂到build构建过程中了
            def action = {
                def outputPath = project.rootProject.rootDir.absolutePath + "/apks"
                project.logger.log(LogLevel.LIFECYCLE, "assembleAPK: buildTask.doLast ：$outputPath")
                def file = new File(outputPath)
                if (file.exists()) {
                    file.deleteDir()
                }
                file.mkdirs()
                project.copy {
                    project.logger.log(LogLevel.LIFECYCLE, "assembleAPK: variant.outputs[0].outputFile${variant.outputs[0].outputFile}")
                    from variant.outputs[0].outputFile
                    into outputPath
                    // 重命名导出名称
//                    project.rename {
//                        def createTime = new Date().format("yyyyMMddHHmmss", TimeZone.getTimeZone("GMT+08:00"))
//                        def fileName = "santou_v${defaultConfig.versionName}_${createTime}_${buildType}.apk"
//                        println "打包成功的APK名称 ：" + outputPath
//                        fileName
//                    }
//                    project.logger.log(LogLevel.LIFECYCLE, "apk name （After copying to the specified folder） ：${apkFileName}")
                }
            }
            def buildTaskRelease = project.tasks.findByName('assembleRelease')
            if (buildTaskRelease) {
                buildTaskRelease.doLast(action)
            }
            def buildTaskDebug = project.tasks.findByName('assembleDebug')
            if (buildTaskDebug) {
                buildTaskDebug.doLast(action)
            }
        }
    }
}

ext.getPackageVersionName = this.&getPackageVersionName
ext.assembleAPK = this.&assembleAPK
