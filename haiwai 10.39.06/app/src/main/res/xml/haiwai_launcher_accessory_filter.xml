<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--<usb-accessory manufacturer="Baidu" model="CarLife" version="1.0.0" />--><!--
    <usb-accessory manufacturer="Mapbar, Inc" model="WeLink" version="1.0" description="WeLink" uri="Mapbar, url"/>-->
    <!--<usb-accessory manufacturer="Navinfo" model="SVW WeLink" version="1.0"/>-->
    <!--<usb-accessory manufacturer="Mapbar, Inc" model="WeLink" version="1.0"/>-->

    <!-- [UsbAccessory[mManufacturer=AutoAi, Inc, mModel=SVWlinkSKD, mDescription=SVW Link, mVersion=1.0, mUri=https://connectivity.saicskoda.com.cn, mSerialNumberReader=android.hardware.usb.IUsbSerialReader$Stub$Proxy@679a5e6]]-->

    <usb-accessory
        manufacturer="AutoAi, Inc"
        model="SVWlink"
        version="1.0" />

    <usb-accessory
        manufacturer="AutoAi, Inc"
        model="SVWlinkSKD"
        version="1.0" />

    <usb-accessory
        manufacturer="Mapbar, Inc"
        model="WeLink"
        version="1.0" />
    <usb-accessory
        manufacturer="Navinfo"
        model="SVW WeLink"
        version="1.0" />
    <usb-accessory
        description="FunDrive Link"
        manufacturer="AutoAi, Inc"
        model="FunDrive Link"
        version="1.0" />
    <usb-accessory
        description="WeLink"
        manufacturer="Mapbar, Inc"
        model="WeLink"
        version="1.0" />
    <!--    <usb-accessory-->
    <!--        description="AionLink"-->
    <!--        manufacturer="AutoAi, Inc"-->
    <!--        model="AionLink"-->
    <!--        version="1.0" />-->
</resources>