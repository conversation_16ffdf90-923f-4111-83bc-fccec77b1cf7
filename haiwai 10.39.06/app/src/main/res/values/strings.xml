<resources>
    <string name="haiwai_launcher_app_name">FunDrive Link</string>
    <string name="haiwai_launcher_welink_main1">You need do as follows to enjoy the service:</string>
    <string name="haiwai_launcher_text_notice">

Please read carfully and understand well on this statement before you use FunDrive Link. Once used FunDrive Link, it represents you have received all items of FunDrive Link Disclaimer.\n\n
1. Do not operate this product during driving, avoiding accident or other dangerous condition. Otherwise the safety accident caused by it shall be beared by itself.\n\n
2. FunDrive Link respects the personal privacy of all users and FunDrive Link will not collect any of your personal information.\n\n
3. All service FunDrive Link supplied are free, also it is not involved phone, data access or message related services that might need to pay.\n\n
4. Please read  the third party\'s relevant policies carefully, it represents you recognize and agree the third-party service terms when using third party product and service through FunDrive Link projection. FunDrive Link is not responsible for products and services provided by the third- parties.\n\n

    </string>
    <string name="haiwai_launcher_notice_url"><u>Disclaimer</u></string>
    <string name="haiwai_launcher_app_tips1">All Apps in your phone can be displayed and able to be operated on head unit.</string>
    <string name="haiwai_launcher_app_btn_usb1">Connect HU</string>
    <string name="haiwai_launcher_app_btn_usb2">Connect Help</string>
    <string name="haiwai_launcher_app_btn_usb3">Step 1</string>
    <string name="haiwai_launcher_app_btn_permit1">Projection\nauthorization</string>
    <string name="haiwai_launcher_app_btn_permit2">Authorize Help</string>
    <string name="haiwai_launcher_app_btn_permit3">Step 2</string>
    <string name="haiwai_launcher_about">About</string>
    <string name="haiwai_launcher_version">V%s</string>
    <string name="haiwai_launcher_all_rights_reserved">©2020 All Rights reserved by Beijing Autoai Tech. Co, Ltd</string>
    <string name="haiwai_launcher_authorize_notice">Projection Authorization</string>
    <string name="haiwai_launcher_permission_notice_1st">1. Allow FunDrive Link to project on phone.</string>
    <string name="haiwai_launcher_permission_notice_2st">2. Turn on FunDrive Link "Accessibility mode" or "barrior free mode" on phone side</string>
    <string name="haiwai_launcher_usb_notice">USB connect Instruction</string>
    <string name="haiwai_launcher_connect_notice_1st">1. Connect MU and HU through USB cable</string>
    <string name="haiwai_launcher_connect_notice_2st">2. Click confirm when Mobile phone appear the following prompt (It will automatically connect when choose default, more convenient)</string>
    <string name="haiwai_launcher_disclaimer_notice">Disclaimer</string>

    <!--    车机端文案-->
    <string name="haiwai_launcher_mirroring_permission_title">Obtaining projection authorization….</string>
    <string name="haiwai_launcher_mirroring_permission_content">Please ensure phone unlock, if with pop-up,\nplease choose confirm or start now</string>
    <string name="haiwai_launcher_mirroring_permission_content2">Ensure safety, please do not use phone\nduring driving</string>
    <string name="haiwai_launcher_mirroring_permission_cancel_title">Already cancel projection service</string>
    <string name="haiwai_launcher_mirroring_permission_cancel_content">If want to restart projection service,\nplease re-connect USB cable.</string>
    <string name="haiwai_launcher_auxiliary_title">Obtaining the accessibility permission….</string>
    <string name="haiwai_launcher_auxiliary_content">Please open FunDrive Link "accessibility" or "barrior free" mode in the phone settings.</string>
    <string name="haiwai_launcher_auxiliary_content2">To keep safety, do not use phone during driving</string>
    <string name="haiwai_launcher_start_screen_content">Connect successfully, please operate\nthe content you want to project.</string>
    <string name="haiwai_launcher_start_screen_content2">To ensure projection, please keep phone unlock\nstate;To keep safety, please do not use phone\nduring driving.</string>

    <string name="haiwai_launcher_bluetooth_dialog_title">To ensure audio service,\nplease match Bluetooth correctly.</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt1">1. Turn on the Bluetooth of the car</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt2">System Settings>Bluetooth>Click the \"+\"\nbutton in the device list</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt3">2. Turn on the phone\'s Bluetooth</string>
    <string name="haiwai_launcher_bluetooth_dialog_txt4">Phone Settings>Start Bluetooth function>Find device\nconnection>Complete Bluetooth connection\n(Subject to actual mobile phone)</string>
    <string name="haiwai_launcher_bluetooth_dialog_negative">Cancel</string>
    <string name="haiwai_launcher_bluetooth_dialog_positive">Settings</string>
</resources>
