<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fragment_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <TextView
        android:id="@+id/title_content_amp_haiwai"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/content_amp_haiwai"
        android:layout_marginBottom="17dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_auxiliary_title"
        android:textColor="@android:color/white"
        android:textSize="12dp" />

    <TextView
        android:id="@+id/content_amp_haiwai"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/haiwai_launcher_auxiliary_content"
        android:textColor="@android:color/white"
        android:textSize="11dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/content_amp_haiwai"
        android:layout_marginTop="17dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_auxiliary_content2"
        android:textColor="@android:color/white"
        android:textSize="11dp" />
</RelativeLayout>