<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fragment_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:gravity="center">

    <TextView
        android:id="@+id/title_content_haiwai"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/haiwai_launcher_mirroring_permission_title"
        android:textColor="@android:color/white"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/content_haiwai"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_content_haiwai"
        android:layout_marginTop="22dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_mirroring_permission_content"
        android:textColor="@android:color/white"
        android:textSize="12dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/content_haiwai"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_mirroring_permission_content2"
        android:textColor="@android:color/white"
        android:textSize="12dp" />

</RelativeLayout>