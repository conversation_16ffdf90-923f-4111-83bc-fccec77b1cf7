<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fragment_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/haiwai_launcher_bg_car"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:scaleType="centerInside"
        android:src="@mipmap/haiwai_launcher_welink_ic"
        android:textColor="@android:color/white"
        tools:ignore="ContentDescription" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content"
        android:textColor="@android:color/white"
        android:textSize="14sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="15dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content2"
        android:textColor="@android:color/white"
        android:textSize="14sp" />
</LinearLayout>