<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/screen_off_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/haiwai_launcher_bg_car"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center">

    <ImageView
        android:id="@+id/title_screen_off_haiwai"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:src="@drawable/haiwai_launcher_welink_ic_800"
        android:textColor="@android:color/white" />

    <TextView
        android:id="@+id/content_screen_off_haiwai"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content"
        android:textColor="@android:color/white"
        android:textSize="14dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/content_screen_off_haiwai"
        android:layout_marginStart="15dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="15dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_start_screen_content2"
        android:textColor="@android:color/white"
        android:textSize="12dp" />
</RelativeLayout>