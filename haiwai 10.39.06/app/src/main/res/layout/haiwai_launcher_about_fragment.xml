<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F6F8FA"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:id="@+id/btn_goback"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginStart="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/im_goback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/haiwai_launcher_phont_adapter_back"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/haiwai_launcher_about"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold" />
    </LinearLayout>


    <ImageView
        android:id="@+id/welink_icon"
        android:layout_width="match_parent"
        android:layout_height="109dp"
        android:layout_below="@id/btn_goback"
        android:layout_gravity="center"
        android:layout_marginTop="71dp"
        android:src="@mipmap/haiwai_launcher_welink_ic"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/version_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/welink_icon"
        android:layout_marginStart="15.5dp"
        android:layout_marginTop="15.5dp"
        android:layout_marginEnd="15.5dp"
        android:layout_marginBottom="15.5dp"
        android:textAlignment="center"
        android:textColor="#000000"
        android:textSize="15sp" />
    <TextView
        android:id="@+id/tv_isDebug"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/version_code"
        android:layout_marginStart="15.5dp"
        android:layout_marginTop="15.5dp"
        android:layout_marginEnd="15.5dp"
        android:layout_marginBottom="15.5dp"
        android:textAlignment="center"
        android:text="debug mode"
        android:textColor="#ffff0000"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/btn_goto_notice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/tv_about"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:text="@string/haiwai_launcher_notice_url"
        android:textColor="#0066FF"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/tv_about"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="26dp"
        android:layout_marginBottom="15dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_all_rights_reserved"
        android:textColor="#000000"
        android:textSize="13sp" />

</RelativeLayout>