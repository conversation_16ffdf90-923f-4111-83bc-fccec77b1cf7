<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/haiwai_launcher_bg_car"
    android:fitsSystemWindows="true">

        <LinearLayout
            android:id="@+id/rlyt_main_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

        <com.mapbar.android.widget.MViewAnimator
            android:id="@+id/animator"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_above="@+id/navi_bar"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:keepScreenOn="true" />
        </LinearLayout>

        <!-- 免责声明提示view-->
        <FrameLayout
            android:id="@+id/lay_disclaimer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#191e25"
            android:visibility="gone" />

        <!-- 免责声明条款view-->
        <FrameLayout
            android:id="@+id/lay_disclaimerClause"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <include layout="@layout/dialog_rootview" />
</FrameLayout>