<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fragment_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:background="@android:color/transparent">

    <TextView
        android:id="@+id/title_content_mpcp_haiwai"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/haiwai_launcher_mirroring_permission_cancel_title"
        android:textColor="@android:color/white"
        android:textSize="18dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_content_mpcp_haiwai"
        android:layout_marginTop="25dp"
        android:gravity="center"
        android:text="@string/haiwai_launcher_mirroring_permission_cancel_content"
        android:textColor="@android:color/white"
        android:textSize="14dp" />
</RelativeLayout>