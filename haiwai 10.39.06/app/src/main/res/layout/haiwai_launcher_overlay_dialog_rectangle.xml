<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialogRootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/group_status_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/haiwai_launcher_phont_adapter_group_bg_rectangle"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/group_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/haiwai_launcher_phont_adapter_group_more_open"
            android:contentDescription="@android:string/unknownName" />

        <ImageView
            android:id="@+id/car_home_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:contentDescription="@android:string/unknownName"
            android:src="@drawable/haiwai_launcher_phont_adapter_group_home_car"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/phone_home_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:contentDescription="@android:string/unknownName"
            android:src="@drawable/haiwai_launcher_phont_adapter_group_home_phone"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/back_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:contentDescription="@android:string/unknownName"
            android:src="@drawable/haiwai_launcher_phont_adapter_group_back"
            android:visibility="visible" />
    </LinearLayout>

    <!--    <TextView-->
    <!--        android:id="@+id/logTv"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent" />-->
</FrameLayout>
