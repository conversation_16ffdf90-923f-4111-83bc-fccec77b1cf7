<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F6F8FA"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:id="@+id/btn_goback"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginStart="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/im_goback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/haiwai_launcher_phont_adapter_back"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/haiwai_launcher_authorize_notice"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold" />
    </LinearLayout>

    <TextView
        android:id="@+id/permission1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/btn_goback"
        android:layout_marginStart="54.5dp"
        android:layout_marginTop="22dp"
        android:text="@string/haiwai_launcher_permission_notice_1st"
        android:textColor="#000000"
        android:textSize="15sp" />

    <ImageView
        android:id="@+id/permission2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/permission1"
        android:layout_gravity="center"
        android:layout_marginTop="23dp"
        android:src="@drawable/haiwai_launcher_phont_adapter_permission1" />

    <TextView
        android:id="@+id/permission3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/permission2"
        android:layout_marginStart="54.5dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="50dp"
        android:text="@string/haiwai_launcher_permission_notice_2st"
        android:textColor="#000000"
        android:textSize="15dp"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/permission4"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/permission3"
        android:layout_gravity="center"
        android:layout_marginTop="25dp"
        android:src="@drawable/haiwai_launcher_phont_adapte_permission2"
        android:visibility="gone" />
</RelativeLayout>