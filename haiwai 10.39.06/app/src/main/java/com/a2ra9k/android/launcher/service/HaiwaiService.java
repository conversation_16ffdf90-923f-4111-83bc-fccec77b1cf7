package com.a2ra9k.android.launcher.service;

import android.content.Context;

import com.a2ra9k.android.launcher.service.impl.HUCommandImpl;
import com.a2ra9k.android.launcher.service.impl.LimitScreenImpl;
import com.autoai.fundrive.basemodule.singleton.Singleton;
import com.autoai.fundrive.commontool.LogManager;

public class HaiwaiService implements Singleton {

    public static final String NAME = HaiwaiService.class.getSimpleName();
    private HUCommandImpl mHUCommandImpl = null;
    private LimitScreenImpl mLimitScreenImpl = null;
    private OverlayDialogManager mOverlayDialogManager = null;
//    private BluetoothHelper mBluetoothHelper;

    public void startServiceImpl() {
        LogManager.d("start HaiwaiService");
        mLimitScreenImpl.startLimitScreen();
        mHUCommandImpl.startHUCommand();
    }

    public LimitScreenImpl getLimitScreen() {
        return mLimitScreenImpl;
    }

    public HUCommandImpl getHUCommand() {
        return mHUCommandImpl;
    }

    public OverlayDialogManager getDialogManager() {
        return mOverlayDialogManager;
    }

//    public BluetoothHelper getBluetoothService() {
//        return mBluetoothHelper;
//    }

    @Override
    public void loadService(Context context) {
        LogManager.d("loadService");
        mOverlayDialogManager = new OverlayDialogManager();
        mHUCommandImpl = new HUCommandImpl();
//        mBluetoothHelper = new BluetoothHelper(mOverlayDialogManager);
//        mLimitScreenImpl = new LimitScreenImpl( mBluetoothHelper,mOverlayDialogManager);
        mLimitScreenImpl = new LimitScreenImpl(mOverlayDialogManager);
    }

    @Override
    public void unloadService() {
        if (mHUCommandImpl != null) {
            mHUCommandImpl.stopHUCommand();
        }
        if (mLimitScreenImpl != null) {
            mLimitScreenImpl.stopLimitScreen();
        }
        LogManager.d("unloadService");
    }

    /**
     * 是否投屏
     *
     * @return boolean
     */
    public boolean screenProjectionStatus() {
        LogManager.d("screenProjectionStatus = " + mLimitScreenImpl);
        return mLimitScreenImpl != null && mLimitScreenImpl.isStartScreen();
    }

    /**
     * 是否连接USB互联
     *
     * @return boolean
     */
    public boolean huLinkedStatus() {
        LogManager.d("huLinkedStatus = " + mLimitScreenImpl);
        return mLimitScreenImpl != null && mLimitScreenImpl.isLinked();
    }
}
