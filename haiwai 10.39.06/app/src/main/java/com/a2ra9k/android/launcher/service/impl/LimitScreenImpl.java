package com.a2ra9k.android.launcher.service.impl;

import android.view.KeyEvent;

import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.OverlayDialogManager;
import com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity;
import com.a2ra9k.android.launcher.view.page.main.BaseMainPage;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.hidscreen.LimitScreenListener;
import com.autoai.fundrive.hidscreen.WLScreenManager;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.mapbar.android.model.BasePage;

public class LimitScreenImpl {

    //开始投屏标记
    private volatile boolean mScreenProjection;
    //互联标记，为处理阿拉伯语言交互新增
    private volatile boolean mLinked;
    private final OverlayDialogManager mOverlayDialogManager;
//    private final BluetoothHelper mBluetoothHelper;

    public LimitScreenImpl(OverlayDialogManager overlayDialogManager) {
//    public LimitScreenImpl(BluetoothHelper bluetoothHelper, OverlayDialogManager overlayDialogManager) {
        mOverlayDialogManager = overlayDialogManager;
//        mBluetoothHelper = bluetoothHelper;
        LogManager.d("LimitScreenImpl construct");
    }

    public void startLimitScreen() {
        LogManager.d("LimitScreenImpl startLimitScreen");
        WLScreenManager screenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
        screenManager.setLimitScreenListener(new LimitScreenListener() {

            @Override
            public void onRequestAuxiliaryMode() {
                LogManager.d("LimitScreenImpl onRequestAuxiliaryMode");
            }

            @Override
            public void onRequestFullScreenLimit() {
                LogManager.d("LimitScreenImpl onRequestFullScreenLimit");
                ThreadPoolUtil.getInstance().runOnUiThread(() ->
                        PageChangeUtils.showPage(Configs.PAGE_MAIN, Configs.PAGE_MAIN, "app", Configs.PAGE_MIRRORING_SUC, null, true, null, null));
            }

            @Override
            public void onStartScreen() {
                LogManager.d("LimitScreenImpl onStartScreen");
                mScreenProjection = true;
                ThreadPoolUtil.getInstance().runOnUiThread(() -> {
                    PageChangeUtils.showPage(Configs.SCREEN_CAR, Configs.VIEW_PAGE_FLAG, "app", Configs.PAGE_SUCCESS, null, true, null, null);
                    PageChangeUtils.sendToPage(Configs.VIEW_PAGE_FLAG, Configs.PAGE_MAIN, "MASK");
                });
//                mBluetoothHelper.connect(HaiwaiLauncherActivity.getInstance());
            }

            @Override
            public void onStopScreen() {
                LogManager.d("LimitScreenImpl onStopScreen");
                if (mScreenProjection) {
                    mScreenProjection = false;
                    ThreadPoolUtil.getInstance().runOnUiThread(() ->
                            PageChangeUtils.sendToPage(Configs.PAGE_MAIN, Configs.PAGE_MAIN, KeyEvent.KEYCODE_BACK));
                }
                mOverlayDialogManager.dismissOverlayDialog();
                ThreadPoolUtil.getInstance().runOnUiThread(() ->
                        PageChangeUtils.showPage(Configs.PAGE_MAIN, Configs.PAGE_MAIN, "app", Configs.PAGE_MIRRORING_FAIL, null, true, null, null));
            }

            @Override
            public void onLink() {
                LogManager.d("LimitScreenImpl onLink");
                mLinked = true;
                WLScreenManager screenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
                screenManager.startScreen();
            }

            @Override
            public void onUnLink() {
                LogManager.d("LimitScreenImpl onUnLink");
                mLinked = false;
                ThreadPoolUtil.getInstance().runOnUiThread(() -> {
                    HaiwaiService service = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
                    HUCommandImpl huCommand = service.getHUCommand();
                    huCommand.languageRecovery();

                    BasePage currentPage = HaiwaiLauncherActivity.getInstance().getActivity().getViewInterface().getCurrentPageObj().getPage();
                    if (currentPage instanceof BaseMainPage) {
                        BaseMainPage mainPage = (BaseMainPage) currentPage;
                        mainPage.removeSuccessView();
                    }
                });
            }

            @Override
            public void onLinkStarted() {
                LogManager.d("LimitScreenImpl onLinkStarted");
                mOverlayDialogManager.updateLocationOpen();
            }

            @Override
            public void onLinkStopped() {
                LogManager.d("LimitScreenImpl onLinkStopped");
            }

            @Override
            public void onResumeScreen() {
                LogManager.d("LimitScreenImpl onResumeScreen");
            }

            @Override
            public void onPauseScreen() {
                LogManager.d("LimitScreenImpl onPauseScreen");
            }
        });
    }

    public void stopLimitScreen() {
        LogManager.d("LimitScreenImpl stopLimitScreen");
        mScreenProjection = false;
//        mBluetoothHelper.destroy();
    }

    public boolean isStartScreen() {
        return mScreenProjection;
    }

    public boolean isLinked() {
        return mLinked;
    }


}
