
package com.a2ra9k.android.launcher.view.page.main;

import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.commontool.LogManager;

/**
 * 车机主页
 *
 * <AUTHOR>
 */
public class CarMainPage extends BaseMainPage {
    /**
     * @param appActivity 上下文环境
     * @param view        当前页面防止的容器View
     */
    public CarMainPage(@NonNull AppActivity appActivity,
                       @NonNull View view) {
        super(appActivity, view);
        LogManager.v("CarMainPage create");
    }

    protected void initMainView() {
        LogManager.v("CarMainPage initMainView");
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_main_fragment_car, mHolderView, false);
        view.findViewById(R.id.btn_goto_usb).setOnClickListener(v -> initUSBView());
        view.findViewById(R.id.btn_goto_permission).setOnClickListener(v -> initAuthorizeView());
        view.findViewById(R.id.btn_goto_about).setOnClickListener(v -> initAboutView());
        addView(view);
        super.initMainView();
    }

    protected void initUSBView() {
        LogManager.v("CarMainPage initUSBView");
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_connect_help_fragment, mHolderView, false);
        view.findViewById(R.id.btn_goback).setOnClickListener(v -> removeView());
        addView(view);
        super.initMainView();
    }

    protected void initAuthorizeView() {
        LogManager.v("CarMainPage initAuthorizeView");
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_authorize_help_fragment, mHolderView, false);
        view.findViewById(R.id.btn_goback).setOnClickListener(v -> removeView());
        addView(view);
        super.initMainView();
    }

    protected void initAboutView() {
        LogManager.v("CarMainPage initAboutView");
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_about_fragment, mHolderView, false);
        view.findViewById(R.id.btn_goback).setOnClickListener(v -> removeView());
        view.findViewById(R.id.btn_goto_notice).setOnClickListener(v -> initDisclaimerView());
        addView(view);
        super.initMainView();
    }

    protected void initDisclaimerView() {
        LogManager.v("CarMainPage initDisclaimerView");
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_disclaimer_fragment, mHolderView, false);
        view.findViewById(R.id.btn_goback).setOnClickListener(v -> removeView());
        addView(view);
        super.initMainView();
    }

    @Override
    public void removeView() {
        int childIndex = mHolderView.getChildCount() - 1;
        mHolderView.removeViewAt(childIndex);
        if (!stack.empty()) {
            stack.pop();
        }
    }
}