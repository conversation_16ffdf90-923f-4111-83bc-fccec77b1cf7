package com.a2ra9k.android.launcher;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity;
import com.a2ra9k.android.launcher.view.HaiwaiLauncherDialog;
import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.autoai.fundrive.hidscreen.WLScreenManager;
import com.autoai.fundrive.platform.WLConnectManager;
import com.autoai.fundrive.basemodule.BaseModuleManager;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务模块配置
 *
 * <AUTHOR>
 */
public class ModuleManager extends BaseModuleManager {
    @NonNull
    @Override
    protected Map<String, String> initModuleList() {
        Map<String, String> aModuleList = new HashMap<>();
        //壳工程
        aModuleList.put("app", HaiwaiLauncherActivity.class.getName());
        //页面加载错误
        aModuleList.put("error", HaiwaiLauncherActivity.class.getName());
        return aModuleList;
    }

    @NonNull
    @Override
    protected Map<String, String> initHuModuleList() {
        Map<String, String> aModuleList = new HashMap<>();
        //壳工程
        aModuleList.put("app", HaiwaiLauncherDialog.class.getName());
        //页面加载错误
        aModuleList.put("error", HaiwaiLauncherDialog.class.getName());
        return aModuleList;
    }

    @NonNull
    @Override
    protected Map<String, String> initWelinkServiceList() {
        Map<String, String> serviceList = super.initWelinkServiceList();
        serviceList.put(WLScreenManager.NAME, WLScreenManager.class.getName());
        serviceList.put(WLConnectManager.NAME, WLConnectManager.class.getName());
        serviceList.put(HaiwaiService.NAME, HaiwaiService.class.getName());
        return serviceList;
    }

    @Override
    public String[] preInitModule() {
        return new String[]{"screen"};
    }
}