package com.a2ra9k.android.launcher.view;

import android.content.Context;
import android.os.Bundle;
import android.view.KeyEvent;

import com.a2ra9k.android.launcher.presenter.BaseActivityAndDialogPresenter;
import com.autoai.fundrive.basemodule.BaseApplication;
import com.autoai.fundrive.basemodule.BaseModuleManager;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.activity.MainActivity;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.presenter.PresenterManager;
import com.autoai.fundrive.commontool.LogManager;
import com.mapbar.android.model.PageObject;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;


/**
 * 新架构手机首页
 *
 * <AUTHOR>
 */
public abstract class BaseLauncherActivity<V extends BaseActivityOrDialogView,
        P extends BaseActivityAndDialogPresenter<V>>
        extends MainActivity {

    protected BaseLauncherActivity() {
    }

    protected P presenter;
//    private V v;

    private BaseModuleManager mModuleManager = null;
//    private BaseLauncherReceiver mBaseLauncherReceiver = null;

    @Override
    protected void attachBaseContext(Context base) {
        for (String s : android.os.Build.SUPPORTED_ABIS) {
            LogManager.d("attachBaseContext: CPU_ABI --> " + s);
        }
        super.attachBaseContext(base);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        ScaleCalculator.init(this, 0, 1280, 720, 2f);
        presenter = PresenterManager.getInstance().getPresenter(getPresenterClass().getName(), this);
        presenter.attachView(getSingletonView());
    }

    protected abstract Class<P> getPresenterClass();

    protected abstract V getSingletonView();

    @Override
    public void loadModuleService() {
        super.loadModuleService();
//        if (mBaseLauncherReceiver == null) {
//            mBaseLauncherReceiver = new BaseLauncherReceiver(this);
//        }
//        mBaseLauncherReceiver.register();
    }

    @Override
    public void unloadModuleService() {
//        if (mBaseLauncherReceiver != null) {
//            mBaseLauncherReceiver.unregister();
//            mBaseLauncherReceiver = null;
//        }
    }

    @Override
    public PageObject getMainPage() {
        PageObject pageObject = createPage(Configs.PAGE_MAIN);
        pageObject.setModuleName("app");
        return pageObject;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        LogManager.i("onKeyDown:::keyCode:::" + keyCode);
        boolean returnFlag;
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (getActivity().getViewInterface().getCurrentPageObj().getPage().isMainPage()) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    //当前页面是主页
                    PageChangeUtils.showDialog("app", new int[]{1001, -1}, 1001);
                }
            } else {
                //当前不在主页
                PageChangeUtils.showPrevious(-1);
            }
            returnFlag = true;
        } else {
            returnFlag = super.onKeyDown(keyCode, event);
        }
        return returnFlag;
    }

    /**
     * 业务模块配置
     */
    @Override
    public BaseModuleManager getModuleManager() {
        if (mModuleManager == null) {
            try {
                Class<?> clazz = Class.forName(BaseApplication.getModuleManagerClassName());
                Constructor<?> constructor = clazz.getDeclaredConstructor();
                mModuleManager = (BaseModuleManager) constructor.newInstance();
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | SecurityException | IllegalArgumentException | NoSuchMethodException | InvocationTargetException e) {
                LogManager.e("getModuleManager", e);
            }
        }
        return mModuleManager;
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        presenter.detachView(getSingletonView(), getPresenterClass().getName());

        mModuleManager.onDestroy();
        mModuleManager = null;
    }
}