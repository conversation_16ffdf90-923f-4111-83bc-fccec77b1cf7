package com.a2ra9k.android.launcher.service;

import com.a2ra9k.android.launcher.service.overlaydialog.MaskDialog;
import com.a2ra9k.android.launcher.service.overlaydialog.base.BaseDialog;
import com.a2ra9k.android.launcher.service.overlaydialog.floating.RectangleDialog;
import com.autoai.fundrive.commontool.LogManager;

import java.util.Locale;

public class OverlayDialogManager {

    private int mLastLeft = -1;
    private int mLastTop = -1;
    private BaseDialog dialog;

    public OverlayDialogManager() {
        LogManager.d("OverlayDialogManager construct");
    }

//    public BaseDialog getDialog() {
//        return dialog;
//    }

//    public void showBluetooth() {
//        LogManager.d("OverlayDialogManager showBluetooth");
//        dialog = new BluetoothDialog();
//        dialog.showDialog();
//    }

    public void showMaskAndReset() {
        LogManager.d("OverlayDialogManager showMaskAndReset");
        if (dialog != null) {
            mLastTop = dialog.getTop();
            mLastLeft = dialog.getLeft();
        }
        dialog = new MaskDialog();
        dialog.showDialog();
    }

    public void showRectangleDialog() {
        LogManager.d("OverlayDialogManager showRectangleDialog mLastLeft=" + mLastLeft + ", mLastTop=" + mLastTop);
        dialog = new RectangleDialog(mLastLeft, mLastTop);
        dialog.showDialog();
    }

    public void dismissOverlayDialog() {
        LogManager.d("OverlayDialogManager dismissOverlayDialog");
        if (dialog != null) {
            dialog.dismissOverlayDialog();
            dialog = null;
        }
    }

    /**
     * 更新布局方向
     *
     * @param locale language
     */
    public void updateLayoutDirection(Locale locale) {
        if (dialog != null) {
            dialog.updateLayoutDirection(locale);
        }
    }

    public void updateLocationOpen() {
        LogManager.d("OverlayDialogManager updateLocationOpen");
        if (dialog != null) {
            dialog.updateLocationOpen();
        }
    }
}
