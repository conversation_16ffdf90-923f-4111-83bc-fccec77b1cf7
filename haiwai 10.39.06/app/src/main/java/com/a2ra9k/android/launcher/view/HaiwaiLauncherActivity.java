package com.a2ra9k.android.launcher.view;

import static com.autoai.welink.logiclib.skincore.constant.DeviceType.SCREEN_CAR;
import static com.autoai.welink.logiclib.skincore.constant.DeviceType.SCREEN_PHONE;

import android.Manifest;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.Keep;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.view.page.main.BaseMainPage;
import com.a2ra9k.android.launcher.pagemanager.PageManagerPhone;
import com.a2ra9k.android.launcher.presenter.ActivityAndDialogPresenter;
import com.a2ra9k.android.launcher.receiver.HaiwaiReceiver;
import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.impl.HUCommandImpl;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.welink.logiclib.skincore.SkinManager;
import com.autoai.fundrive.hidscreen.WLScreenManager;
import com.mapbar.android.control.ViewBaseManager;
import com.mapbar.android.model.BasePage;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.activity.DialogManager;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;

public class HaiwaiLauncherActivity extends BaseLauncherActivity<ActivityOrDialogView, ActivityAndDialogPresenter> implements ActivityOrDialogView {
    private PageManager mPageManager;

    private static HaiwaiLauncherActivity INSTANCE;
    public static boolean DialogInit = false;

    private HaiwaiService haiwaiService;
    private HaiwaiReceiver haiwaiReceiver;

    /**
     * 模块中反射用到
     */
    @Keep
    public static HaiwaiLauncherActivity getInstance() {
        return INSTANCE;
    }

    @Override
    protected void attachBaseContext(Context base) {
        INSTANCE = this;
        super.attachBaseContext(base);
        LogManager.d("attachBaseContext");
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        LogManager.d("onCreate");
        SkinManager.getInstance().registerLayoutInflater(this, SCREEN_PHONE);
        if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.TIRAMISU){
            requestPermissions(new String[]{Manifest.permission.POST_NOTIFICATIONS}, 101);
        }
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initDialogContext(Context context) {
        //
        SkinManager.getInstance().registerLayoutInflater(context, SCREEN_CAR);
        LogManager.e("initCarDialog");
        SkinManager.getInstance().initCarDialog(context);
        HUCommandImpl.isDialogSkinInied = true;
        if (LocaleUtil.locale != null) {
            SkinManager.getInstance().changeSkin(SCREEN_CAR, "", LocaleUtil.locale);
        }
        //
        super.initDialogContext(context);
        LogManager.d("initDialogContext start");
        DialogInit = true;
        PageChangeUtils.sendToPage(-1, Configs.PAGE_MAIN, Configs.PAGE_MAIN, LocaleUtil.locale);
        LogManager.d("initDialogContext end");
    }

    @Override
    protected void disconnect() {
        super.disconnect();
        SkinManager.getInstance().unregisterLayoutInflater(SCREEN_CAR);
    }

    @Override
    public void loadModuleService() {
        super.loadModuleService();
        haiwaiService = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
        haiwaiService.startServiceImpl();
        if (haiwaiReceiver == null) {
            haiwaiReceiver = new HaiwaiReceiver(haiwaiService);
        }
        haiwaiReceiver.register();
    }

    @Override
    protected Class<ActivityAndDialogPresenter> getPresenterClass() {
        return ActivityAndDialogPresenter.class;
    }

    @Override
    protected ActivityOrDialogView getSingletonView() {
        return this;
    }

    @Override
    public PageManager getPageManager(AppActivity appActivity) {
        if (mPageManager == null) {
            mPageManager = new PageManagerPhone(appActivity);
        }
        return mPageManager;
    }

    @Override
    public DialogManager getDialogManager(ViewBaseManager viewBaseManager) {
        return new HaiwaiLauncherDialog(viewBaseManager);
    }

    @Override
    public int getRootViewId() {
        return R.id.main_container;
    }

    @Override
    public int getAnimatorResId() {
        return R.id.animator;
    }

    @Override
    public int getMainViewLayout() {
        return R.layout.haiwai_launcher_activity_shell;
    }

    @Override
    public View getUnuseView() {
        return null;
    }

    @Override
    public BasePopDialog createPopDialog(int i) {
//        return PopDialogFactory.createPopDialog(i, this);
        return null;
    }

    @Override
    public void unloadModuleService() {
        super.unloadModuleService();
        INSTANCE = null;
        if (haiwaiReceiver != null) {
            haiwaiReceiver.unregister();
            haiwaiReceiver = null;
        }
        WLScreenManager wlScreenManager = (WLScreenManager) SingletonFactory.getInstance().getSingleton(WLScreenManager.NAME);
        if (wlScreenManager != null) {
            wlScreenManager.unloadService();
        }
        haiwaiService = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        SkinManager.getInstance().unregisterLayoutInflater(SCREEN_PHONE);
        System.exit(0);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        LogManager.i("onKeyDown KEYCODE_BACK? -> " + (keyCode == KeyEvent.KEYCODE_BACK));
        boolean returnFlag;
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            BasePage currentPage = getActivity().getViewInterface().getCurrentPageObj().getPage();
            if (currentPage.isMainPage()) {
                LogManager.i("onKeyDown isMainPage? -> true");
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    //当前页面是主页
                    LogManager.i("onKeyDown ACTION_DOWN? -> true");
                    if (currentPage instanceof BaseMainPage) {
                        if (((BaseMainPage) currentPage).cannotViewBack()) {
                            moveTaskToBack(true);
                        } else {
                            BaseMainPage baseMainPage = (BaseMainPage) currentPage;
                            baseMainPage.removeView();
                        }
                    }
                } else {
                    LogManager.i("onKeyDown ACTION_DOWN? -> false");
                }
            } else {
                //当前不在主页
                LogManager.i("onKeyDown isMainPage? -> false");
                PageChangeUtils.showPrevious(-1);
            }
            returnFlag = true;
        } else {
            returnFlag = super.onKeyDown(keyCode, event);
        }
        return returnFlag;
    }

//    @Override
//    protected String[] getMustRequestPermissions() {
//        return new String[]{Manifest.permission.READ_EXTERNAL_STORAGE,
//                Manifest.permission.WRITE_EXTERNAL_STORAGE,};
//    }
}
