package com.a2ra9k.android.launcher.view.page.main;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.presenter.HaiwaiPreseneter;
import com.mapbar.android.model.BasePage;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.IBaseView;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.presenter.PresenterManager;

import java.util.Stack;

public abstract class BaseMainPage extends BasePage implements IBaseView {
    protected static final String VIEW_TYPE_MAIN = "main";
    protected static final String VIEW_TYPE_USB = "usb";
    protected static final String VIEW_TYPE_AUTHORIZE = "Authorize";
    protected static final String VIEW_TYPE_ABOUT = "About";
    protected static final String VIEW_TYPE_DISCLAIMER = "Disclaimer";
    protected static final String VIEW_TYPE_SUCCESS = "Mask";

    protected final FrameLayout mHolderView;
    protected final AppActivity mAppActivity;
    protected Stack<String> stack = new Stack<>();

    /**
     * @param appActivity 上下文环境
     * @param view        当前页面防止的容器View
     */
    public BaseMainPage(@NonNull AppActivity appActivity,
                        @NonNull View view) {
        super(appActivity.getContext(), view, null);
        this.mAppActivity = appActivity;
        this.mHolderView = view.findViewById(R.id.fragment_container);
        HaiwaiPreseneter mPresenter = PresenterManager.getInstance().getPresenter(HaiwaiPreseneter.class.getName(), mAppActivity.getContext());
        mPresenter.attachView(this);
        initMainView();
    }


    protected void initMainView() {
        stack.push(VIEW_TYPE_MAIN);
    }

    protected void initUSBView() {
        stack.push(VIEW_TYPE_USB);
    }

    protected void initAuthorizeView() {
        stack.push(VIEW_TYPE_AUTHORIZE);
    }

    protected void initAboutView() {
        stack.push(VIEW_TYPE_ABOUT);
    }

    protected void initDisclaimerView() {
        stack.push(VIEW_TYPE_DISCLAIMER);
    }

    protected void initLinkSuccess() {
        stack.push(VIEW_TYPE_SUCCESS);
    }

    protected void addView(View view) {
        mHolderView.addView(view, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
    }

    public abstract void removeView();

    @Override
    public Context getContext() {
        return mAppActivity.getContext();
    }

    @Override
    public int getMyViewPosition() {
        return Configs.PAGE_MAIN;
    }

    @Override
    public boolean isMainPage() {
        return true;
    }

    public boolean cannotViewBack() {
        if (stack.empty()) {
            return true;
        } else {
            return VIEW_TYPE_MAIN.equals(stack.peek()) || VIEW_TYPE_SUCCESS.equals(stack.peek());
        }
    }

    public void removeSuccessView() {
        if (!stack.empty() && stack.peek().equals(VIEW_TYPE_SUCCESS)) {
            stack.pop();
            int childIndex = mHolderView.getChildCount() - 1;
            mHolderView.removeViewAt(childIndex);
        }
    }

    @Override
    public boolean destoryFlag() {
        return false;
    }
}
