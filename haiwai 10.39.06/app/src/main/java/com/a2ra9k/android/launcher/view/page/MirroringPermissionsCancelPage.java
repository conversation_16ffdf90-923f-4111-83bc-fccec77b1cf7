
package com.a2ra9k.android.launcher.view.page;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.presenter.HaiwaiPreseneter;
import com.mapbar.android.model.BasePage;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.IBaseView;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.presenter.PresenterManager;

/**
 * 取消权限，停止投屏服务
 *
 * <AUTHOR>
 */
public class MirroringPermissionsCancelPage extends BasePage implements IBaseView {
    protected AppActivity mAppActivity;
    protected HaiwaiPreseneter mPresenter;

    /**
     * @param appActivity 上下文环境
     * @param view        当前页面防止的容器View
     */
    public MirroringPermissionsCancelPage(@NonNull AppActivity appActivity,
                                          @NonNull View view) {
        super(appActivity.getContext(), view, null);
        this.mAppActivity = appActivity;
        mPresenter = PresenterManager.getInstance().getPresenter(HaiwaiPreseneter.class.getName(), mAppActivity.getContext());
        mPresenter.attachView(this);
    }

    @Override
    public Context getContext() {
        return mAppActivity.getContext();
    }

    @Override
    public int getMyViewPosition() {
        return Configs.PAGE_MIRRORING_FAIL;
    }
}