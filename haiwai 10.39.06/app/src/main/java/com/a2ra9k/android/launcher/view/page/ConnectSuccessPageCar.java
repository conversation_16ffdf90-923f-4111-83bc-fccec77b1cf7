package com.a2ra9k.android.launcher.view.page;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.View;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.presenter.HaiwaiPreseneter;
import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.OverlayDialogManager;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.page.IBaseView;
import com.autoai.fundrive.basemodule.presenter.PresenterManager;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.mapbar.android.model.BasePage;


/**
 * 投屏成功
 *
 * <AUTHOR>
 */
public class ConnectSuccessPageCar extends BasePage implements IBaseView {
    protected AppActivity mAppActivity;
    protected HaiwaiPreseneter mPresenter;

    /**
     * @param appActivity 上下文环境
     * @param view        当前页面防止的容器View
     */
    public ConnectSuccessPageCar(@NonNull AppActivity appActivity,
                                 @NonNull View view) {
        super(appActivity.getContext(), view, null);
        LogManager.v("更换布局方向 ConnectSuccessPageCar create");
        this.mAppActivity = appActivity;
        mPresenter = PresenterManager.getInstance().getPresenter(HaiwaiPreseneter.class.getName(), mAppActivity.getContext());
        mPresenter.attachView(this);

        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                LogManager.d("StartScreenPageCar OverlayDialogManager showRectangleDialog");
                HaiwaiService haiwaiService = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
                if (haiwaiService != null) {
                    OverlayDialogManager dialogManager = haiwaiService.getDialogManager();
                    dialogManager.showRectangleDialog();
                }
            }
        });
    }


    @Override
    public Context getContext() {
        return mAppActivity.getContext();
    }

    @Override
    public int getMyViewPosition() {
        return Configs.PAGE_SUCCESS;
    }

}