
package com.a2ra9k.android.launcher.view.page.main;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.impl.HUCommandImpl;
import com.autoai.fundrive.basemodule.BaseApplication;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.CommonUtil;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.commontool.SharePreferenceUtil;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.welink.screen.WLScreen;

import java.util.List;
import java.util.Locale;

/**
 * 车机主页
 *
 * <AUTHOR>
 */
public class PhoneMainPage extends BaseMainPage {
    RelativeLayout parent;
    /**
     * @param appActivity 上下文环境
     * @param view        当前页面防止的容器View
     */
    public PhoneMainPage(@NonNull AppActivity appActivity,
                         @NonNull View view) {
        super(appActivity, view);
    }

    protected void initMainView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_main_fragment_phone, mHolderView, false);
        view.findViewById(R.id.btn_goto_usb).setOnClickListener(v -> initUSBView());
        view.findViewById(R.id.btn_goto_permission).setOnClickListener(v -> initAuthorizeView());
        view.findViewById(R.id.btn_goto_about).setOnClickListener(v -> initAboutView());
        parent= view.findViewById(R.id.welink1);
        Locale locale = Locale.getDefault();
        changeRlHeight(locale);
        addView(view);
        super.initMainView();
    }

    protected void initUSBView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_connect_help_fragment, mHolderView, false);
        View.OnClickListener clickListener = v -> removeView();
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        addView(view);
        initViewDirectionIfNeed();
        super.initUSBView();
    }

    protected void initAuthorizeView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_authorize_help_fragment, mHolderView, false);
        View.OnClickListener clickListener = v -> removeView();
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        addView(view);
        initViewDirectionIfNeed();
        super.initAuthorizeView();
    }

    TextView versionCodeTv;
    TextView tvIsDebug;
    protected void initAboutView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_about_fragment, mHolderView, false);
        View.OnClickListener clickListener = v -> {
            removeView();
            versionCodeTv = null;
        };
         tvIsDebug = view.findViewById(R.id.tv_isDebug);
        boolean isDebug = SharePreferenceUtil.getInstance(getContext().getApplicationContext()).getIsDebug();
        if (isDebug){
            tvIsDebug.setVisibility(View.VISIBLE);
            LogManager.setIsFileLoggable(true);
            LogManager.setIsLoggable(true);
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.setLoggable(true);
            WLScreen.setLogMode(true);
        }else {
            tvIsDebug.setVisibility(View.GONE);
            LogManager.setIsFileLoggable(false);
            LogManager.setIsLoggable(false);
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.setLoggable(false);
            WLScreen.setLogMode(false);
        }
        tvIsDebug.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                tvIsDebug.setVisibility(View.GONE);
                LogManager.setIsFileLoggable(false);
                LogManager.setIsLoggable(false);
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                platformManager.setLoggable(false);
                WLScreen.setLogMode(false);
                SharePreferenceUtil.getInstance(getContext().getApplicationContext()).setIsDebug(false);
            }
        });

        view.findViewById(R.id.welink_icon).setOnClickListener(new View.OnClickListener() {
            final static int COUNTS = 5;//点击次数
            final static long DURATION = 2 * 1000;//规定有效时间
            long[] mHits = new long[COUNTS];

            @Override
            public void onClick(View v) {
                System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
                //实现左移，然后最后一个位置更新距离开机的时间，如果最后一个时间和最开始时间小于DURATION，即连续5次点击
                mHits[mHits.length - 1] = SystemClock.uptimeMillis();
                if (mHits[0] >= (SystemClock.uptimeMillis() - DURATION)) {
                    boolean isDebug1 = SharePreferenceUtil.getInstance(getContext().getApplicationContext()).getIsDebug();
                    if (isDebug1){
                        return;
                    }
                    tvIsDebug.setVisibility(View.VISIBLE);
                    SharePreferenceUtil.getInstance(getContext().getApplicationContext()).setIsDebug(true);
                    LogManager.setIsFileLoggable(true);
                    LogManager.setIsLoggable(true);
                    WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                    platformManager.setLoggable(true);
                    WLScreen.setLogMode(true);
                    String tips = "您已在[" + DURATION + "]ms内连续点击【" + mHits.length + "】次了！！！";
                    Toast.makeText(getContext(), tips, Toast.LENGTH_SHORT).show();

                }
            }
        });
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        view.findViewById(R.id.btn_goto_notice).setOnClickListener(v -> initDisclaimerView());
        versionCodeTv = view.findViewById(R.id.version_code);
        updateVersionView();
        addView(view);
        initViewDirectionIfNeed();
        super.initAboutView();
    }

    protected void initDisclaimerView() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_disclaimer_fragment, mHolderView, false);
        View.OnClickListener clickListener = v -> removeView();
        view.findViewById(R.id.im_goback).setOnClickListener(clickListener);
        addView(view);
        initViewDirectionIfNeed();
        super.initDisclaimerView();
    }

    @Override
    protected void initLinkSuccess() {
        LayoutInflater layoutInflater = LayoutInflater.from(getContext());
        View view = layoutInflater.inflate(R.layout.haiwai_launcher_start_screen_page_phone, mHolderView, false);
        addView(view);
        super.initLinkSuccess();
    }

    private void updateVersionView() {
        if (versionCodeTv != null) {
            String packageVersionName = BaseApplication.getPackageVersionName();
            LogManager.d("phone initVersionView packageVersionName : " + packageVersionName);
            packageVersionName = String.format(versionCodeTv.getResources().getString(R.string.haiwai_launcher_version), packageVersionName);
            versionCodeTv.setText(packageVersionName);
        }
    }

    @Override
    public void removeView() {
        if (!stack.empty()) {
            if (stack.peek().equals(VIEW_TYPE_SUCCESS)) {
                return;
            }
            if (stack.peek().equals(VIEW_TYPE_MAIN)) {
                return;
            }
            int childIndex = mHolderView.getChildCount() - 1;
            mHolderView.removeViewAt(childIndex);
            if (!stack.empty()) {
                stack.pop();
            }
            //更新布局方向
            initViewDirectionIfNeed();
        }

    }

    @Override
    public void onReceiveData(int flag, int code, Object obj) {
        if (obj instanceof String) {
            String s = (String) obj;
            if (TextUtils.equals("MASK", s)) {
                initLinkSuccess();
            }
        } else if (obj instanceof Integer) {
            int i = (int) obj;
            if (i == KeyEvent.KEYCODE_BACK) {
                removeView();
            }
        } else if (obj instanceof Locale) {
            Locale locale = (Locale) obj;
            LogManager.v("更换布局方向 onReceiveData 1 ：Phone");
            updateLayoutDirection(locale);
        }
    }


    private void initViewDirectionIfNeed() {
        //更新过阿拉伯语言就更新布局方向
        if (HUCommandImpl.arLanguageChanged()) {
            HaiwaiService haiwaiServiceImpl = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
            Locale locale = HUCommandImpl.lastLocale;
            //互联使用车机语言协议更新，反之用手机语言
            if (haiwaiServiceImpl.huLinkedStatus()) {
                locale = LocaleUtil.locale;
            }
            updateLayoutDirection(locale);
        }
    }

    /**
     * 更新布局方向
     *
     * @param locale language
     */
    private void updateLayoutDirection(Locale locale) {
        String language = locale.getLanguage();
        LogManager.v("更换布局方向 updateLayoutDirection：Phone-->" + language);
        changeRlHeight(locale);
        int direction;
        if (TextUtils.equals(language, "ar")) {
            direction = View.LAYOUT_DIRECTION_RTL;
        } else {
            direction = View.LAYOUT_DIRECTION_LTR;
        }
        List<View> views = CommonUtil.getAllChildViews(mHolderView);
        for (View item : views) {
            item.setLayoutDirection(direction);
        }
        updateVersionView();

    }

    private void changeRlHeight(Locale locale) {
        String language = locale.getLanguage();
        if (parent!=null){
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) parent.getLayoutParams();
            if (TextUtils.equals(language,"in") ||TextUtils.equals(language,"th") || TextUtils.equals(language,"es") ){
                layoutParams.height=((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 290, parent.getResources().getDisplayMetrics()));
            }else if ( TextUtils.equals(language,"ms")){
                layoutParams.height=((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 300, parent.getResources().getDisplayMetrics()));
            }else {
                layoutParams.height=((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 240, parent.getResources().getDisplayMetrics()));
            }
            parent.setLayoutParams(layoutParams);
        }

    }


}