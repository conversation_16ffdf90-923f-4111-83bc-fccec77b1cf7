package com.a2ra9k.android.launcher.view;

import android.annotation.SuppressLint;
import android.view.KeyEvent;

import androidx.annotation.Keep;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.pagemanager.PageManagerCar;
import com.a2ra9k.android.launcher.presenter.ActivityAndDialogPresenter;
import com.mapbar.android.control.ViewBaseManager;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;

public class HaiwaiLauncherDialog extends BaseLauncherDialog<ActivityOrDialogView, ActivityAndDialogPresenter> implements ActivityOrDialogView {

    private PageManager mPageManager;
    //    private View maskingView;
    @SuppressLint("StaticFieldLeak")
    private static HaiwaiLauncherDialog instance = null;

    @SuppressWarnings("unused")
    @Keep
    public static HaiwaiLauncherDialog getInstance() {
        return instance;
    }

    public HaiwaiLauncherDialog(@NonNull ViewBaseManager aViewBaseManager) {
        super(aViewBaseManager);
        instance = this;
    }

    @Override
    protected Class<ActivityAndDialogPresenter> getPresenterClass() {
        return ActivityAndDialogPresenter.class;
    }

    @Override
    protected ActivityOrDialogView getSingletonView() {
        return this;
    }

    @Override
    public PageManager getPageManager(AppActivity appActivity) {
        if (mPageManager == null) {
            mPageManager = new PageManagerCar(appActivity);
        }
        return mPageManager;
    }

    @Override
    @LayoutRes
    public int getMainViewLayout() {
        return R.layout.haiwai_launcher_dailog_shell;
    }

    @Override
    public int getAnimatorResId() {
        return R.id.animator;
    }

    @Override
    public int getRootViewId() {
        return R.id.main_container;
    }

    @Override
    public BasePopDialog createPopDialog(int index) {
//        return PopDialogFactory.createPopDialog(index, getContext());
        return null;
    }

    @Override
    public void unloadModuleService() {
        instance = null;
        mPageManager = null;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
//        if (keyCode == KeyEvent.KEYCODE_BACK) {
        //全投屏状态手机有浮层，不处理back
//            if (mHaiwaiServiceImpl != null && !mHaiwaiServiceImpl.screenProjectionStatus()) {
//                PageChangeUtils.sendToPage(PageIndex.VIEW_POSITION_MAIN, PageIndex.VIEW_POSITION_MAIN, KeyEvent.KEYCODE_BACK);
//            }
//        }
        return true;
    }
}
