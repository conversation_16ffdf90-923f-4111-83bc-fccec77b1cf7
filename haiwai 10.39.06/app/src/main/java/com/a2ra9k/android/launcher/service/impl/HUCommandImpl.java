package com.a2ra9k.android.launcher.service.impl;


import static com.a2ra9k.android.launcher.view.HaiwaiLauncherActivity.DialogInit;

import android.text.TextUtils;

import com.a2ra9k.android.launcher.service.HaiwaiService;
import com.a2ra9k.android.launcher.service.OverlayDialogManager;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageChangeUtils;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.protocol.WLProtocolConfig;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.fundrive.platform.protocol.bean.BaseProtocolBean;
import com.autoai.fundrive.platform.protocol.bean.EnAndChBean;
import com.autoai.fundrive.platform.protocol.listener.HUCommandListener;
import com.autoai.welink.logiclib.skincore.SkinManager;
import com.autoai.welink.logiclib.skincore.constant.DeviceType;

import java.util.Locale;

public class HUCommandImpl implements HUCommandListener {

    public static Locale lastLocale;
    public static boolean isDialogSkinInied = false;

    public HUCommandImpl() {
        LogManager.d("HUCommandImpl construct");
    }

    public void startHUCommand() {
        LogManager.d("start startHUCommand");
        WLProtocolManager.getInstance().addMethodListener(WLProtocolConfig.HU_PROTOCOL_METHOD_ENANDCH, this);
    }

    @Override
    public void onReceiveCommand(BaseProtocolBean baseProtocolBean) {
        lastLocale = new Locale(Locale.getDefault().getLanguage());
        LogManager.i("onReceiveCommand baseProtocolBean " + baseProtocolBean);
        if (baseProtocolBean instanceof EnAndChBean) {
            EnAndChBean enAndChBean = (EnAndChBean) baseProtocolBean;
            int controlState = enAndChBean.getControlState();
            LocaleUtil.locale = getLocal(controlState);
            LogManager.e("onReceiveCommand changeSkin");
            LogManager.d("resetResource onReceiveCommand changeCarLanguage EnAndChBean controlState=" + controlState);
            LogManager.d("resetResource onReceiveCommand changeCarLanguage EnAndChBean locale=" + LocaleUtil.locale);
            Locale locale = new Locale(LocaleUtil.locale.getLanguage(), LocaleUtil.locale.getCountry());
            LogManager.d("resetResource onReceiveCommand changeCarLanguage EnAndChBean locale22 =" + locale);

            SkinManager.getInstance().changeSkin(DeviceType.SCREEN_PHONE, "", locale);
            if (HUCommandImpl.isDialogSkinInied) {
                SkinManager.getInstance().changeSkin(DeviceType.SCREEN_CAR, "", locale);
            }
            PageChangeUtils.sendToPage(-1, Configs.PAGE_MAIN, Configs.PAGE_MAIN, locale);
            HaiwaiService haiwaiService = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
            if (haiwaiService != null) {
                OverlayDialogManager dialogManager = haiwaiService.getDialogManager();
                if (dialogManager != null) {
                    dialogManager.updateLayoutDirection(locale);
                }
            }
        }
    }

    public static Locale getLocal(int controlState) {
        Locale locale;
        switch (controlState) {
            case 2:
                locale = Locale.ENGLISH;
                break;
            case 3:
                //西班牙
                locale = new Locale("es", "ES");
                break;
            case 4:
                //印尼
                locale = new Locale("in", "ID");
                break;
            case 5:
                //阿拉伯
                locale = new Locale("ar", "AE");
                break;
            case 6:
                //葡萄牙语
                locale = new Locale("pt", "PT");
                break;
            case 7:
                //越南
                locale = new Locale("vi", "VN");
                break;
            case 8:
                // 泰语
                locale = new Locale("th", "TH");
                break;
            case 9:
                // 马来语
                locale = new Locale("ms", "MY");
                break;
            case 0:
                // 繁体中文

                locale = Locale.TAIWAN;
                break;
            default:
                locale = Locale.SIMPLIFIED_CHINESE;
                break;
        }
        return locale;
    }

    public static boolean arLanguageChanged() {
        if (LocaleUtil.locale != null && lastLocale != null) {
            String language = LocaleUtil.locale.getLanguage();
            String lastLanguage = lastLocale.getLanguage();
            boolean ar = TextUtils.equals(language, "ar");
            boolean lastAr = TextUtils.equals(lastLanguage, "ar");
            return ar || lastAr;
        }
        return false;
    }

    public void languageRecovery() {
        LogManager.d("languageRecovery lastLocale=" + lastLocale);
        if (lastLocale != null) {
            SkinManager.getInstance().changeSkin(DeviceType.SCREEN_ALL, "", lastLocale);
            PageChangeUtils.sendToPage(-1, Configs.PAGE_MAIN, Configs.PAGE_MAIN, lastLocale);
        }
    }

    public void stopHUCommand() {
        DialogInit = false;
        LocaleUtil.locale = null;
        lastLocale = null;
    }
}
