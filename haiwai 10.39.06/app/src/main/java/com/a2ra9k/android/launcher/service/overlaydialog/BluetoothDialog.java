//package com.a2ra9k.android.launcher.service.overlaydialog;
//
//import android.app.Dialog;
//
//import com.a2ra9k.android.launcher.LayoutTypeUtils;
//import com.a2ra9k.android.launcher.R;
//import com.a2ra9k.android.launcher.service.overlaydialog.base.BaseDialog;
//import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
//import com.autoai.fundrive.commontool.LogManager;
//import com.autoai.fundrive.platform.WLPlatformManager;
//
///**
// * 蓝牙提示
// */
//public class BluetoothDialog extends BaseDialog {
//
//    @Override
//    public void showDialog() {
//        //浮窗
//        WLPlatformManager wlPlatformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
//        Dialog overlayDialog = wlPlatformManager.getOverlayDialog();
//        int layoutRes = LayoutTypeUtils.isA2R() ? R.layout.haiwai_launcher_bluetooth_dialog_car_a2r : R.layout.haiwai_launcher_bluetooth_dialog_car_a9k;
//        overlayDialog.setContentView(layoutRes);
//        //
//        overlayDialog.findViewById(R.id.bluetooth_root_view).setOnClickListener(v -> LogManager.d("点击了：蓝牙提示面板"));
////        overlayDialog.findViewById(R.id.bluetooth_dialog_positive).setOnClickListener(v ->
////                //打开车机蓝牙设备
////                WLProtocolManager.getInstance().sendOpenHuBtSettingToCar());
//        overlayDialog.findViewById(R.id.bluetooth_dialog_negative).setOnClickListener(v -> {
////            HaiwaiService haiwaiService = (HaiwaiService) SingletonFactory.getInstance().getSingleton(HaiwaiService.NAME);
////            BluetoothHelper bluetoothHelper = haiwaiService.getBluetoothService();
////            bluetoothHelper.destroy();
////            OverlayDialogManager overlayDialogManager = haiwaiService.getDialogManager();
////            overlayDialogManager.showRectangleDialog();
//        });
//        overlayDialog.show();
//    }
//
//}
