package com.a2ra9k.android.launcher.pagemanager;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import com.a2ra9k.android.launcher.LayoutTypeUtils;
import com.a2ra9k.android.launcher.R;
import com.a2ra9k.android.launcher.view.page.AuxiliaryModePage;
import com.a2ra9k.android.launcher.view.page.MirroringPermissionsCancelPage;
import com.a2ra9k.android.launcher.view.page.MirroringPermissionsPage;
import com.a2ra9k.android.launcher.view.page.ConnectSuccessPageCar;
import com.a2ra9k.android.launcher.view.page.main.CarMainPage;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.commontool.LogManager;
import com.mapbar.android.model.BasePage;
import com.mapbar.android.model.PageObject;

/**
 * 车机端页面管理
 *
 * <AUTHOR>
 */
public class PageManagerCar implements PageManager {

    private final AppActivity mAppActivity;

    public PageManagerCar(@NonNull AppActivity aAppActivity) {
        mAppActivity = aAppActivity;
    }

    @SuppressLint("InflateParams")
    @Override
    public PageObject createPage(int index) {
        return LayoutTypeUtils.isA2R() ? createA2rPage(index) : createA9kPage(index);
    }

    @SuppressLint("InflateParams")
    private PageObject createA2rPage(int index) {
        LogManager.i("createA2rPage");
        BasePage page = null;
        View view = null;
        switch (index) {
            case Configs.PAGE_MAIN:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_main_page_car, null);
                page = new CarMainPage(mAppActivity, view);
                break;
            case Configs.PAGE_MIRRORING_SUC:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_mirroring_permission_page_a2r, null);
                page = new MirroringPermissionsPage(mAppActivity, view);
                break;
            case Configs.PAGE_MIRRORING_FAIL:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_mirroring_permission_cancel_page_a2r, null);
                page = new MirroringPermissionsCancelPage(mAppActivity, view);
                break;
            case Configs.PAGE_AUXILIARY:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_auxiliary_mode_page, null);
                page = new AuxiliaryModePage(mAppActivity, view);
                break;
            case Configs.PAGE_SUCCESS:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_overlay_dialog_mask_a2r, null);
                page = new ConnectSuccessPageCar(mAppActivity, view);
                break;
            default:
                break;
        }
        if (page == null) {
            return null;
        }
        return new PageObject(index, view, page);
    }

    @SuppressLint("InflateParams")
    private PageObject createA9kPage(int index) {
        LogManager.i("createA9kPage");
        BasePage page = null;
        View view = null;
        switch (index) {
            case Configs.PAGE_MAIN:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_main_page_car, null);
                page = new CarMainPage(mAppActivity, view);
                break;
            case Configs.PAGE_MIRRORING_SUC:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_mirroring_permission_page_a9k, null);
                page = new MirroringPermissionsPage(mAppActivity, view);
                break;
            case Configs.PAGE_MIRRORING_FAIL:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_mirroring_permission_cancel_page_a9k, null);
                page = new MirroringPermissionsCancelPage(mAppActivity, view);
                break;
            case Configs.PAGE_AUXILIARY:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_auxiliary_mode_page, null);
                page = new AuxiliaryModePage(mAppActivity, view);
                break;
            case Configs.PAGE_SUCCESS:
                view = LayoutInflater.from(mAppActivity.getContext()).inflate(R.layout.haiwai_launcher_overlay_dialog_mask_a9k, null);
                page = new ConnectSuccessPageCar(mAppActivity, view);
                break;
            default:
                break;
        }
        if (page == null) {
            return null;
        }
        return new PageObject(index, view, page);
    }
}