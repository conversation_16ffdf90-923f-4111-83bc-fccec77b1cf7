//
apply plugin: 'com.android.application'
//
apply from: "../gradleshell/dependencies.gradle"
//
android {
    compileSdkVersion rootProject.ext.android.compileSdk
    defaultConfig {
        applicationId "com.a2ra9k.android.launcher"
        minSdkVersion rootProject.ext.android.minSdk
        targetSdkVersion rootProject.ext.android.targetSdk
        versionName rootProject.ext.android.versionName
        versionCode rootProject.ext.android.versionCode

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        debug {
            storeFile file("../key/wedrive2014.keystore")
            storePassword "wedrive2014"
            keyAlias "wedrive"
            keyPassword "wedrive2014"
        }

        release {
            storeFile file("../key/wedrive2014.keystore")
            storePassword "wedrive2014"
            keyAlias "wedrive"
            keyPassword "wedrive2014"
        }
    }
    buildTypes {
        release {
            multiDexKeepFile file('multidex-config.txt')
//            // Enables code shrinking, obfuscation, and optimization
//            minifyEnabled true
//            // Enables resource shrinking, which is performed by the
//            // Android Gradle plugin.
//            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            multiDexKeepFile file('multidex-config.txt')
//            // Enables code shrinking, obfuscation, and optimization
//            minifyEnabled true
//            // Enables resource shrinking, which is performed by the
//            // Android Gradle plugin.
//            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    //打包
//    libraryVariants.all(assembleAAR(project))
    applicationVariants.all(assembleAPK(project))
}

dependencies {
//    implementation fileTree(dir: 'libs', include: ['*.jar'])
    //
    implementation COMMON_DEPEN.MAPBAR_ANDROID_APPFRAMEWORK
    //
    implementation project(':basemodule')
    implementation 'com.autoai.welink.logiclib.skincore:skinframework:0.1.0'
//    implementation 'com.autoai.welink.lib:bluetooth:2.0'
    implementation 'com.autoai.link.baselog:baselog:0.0.11'
    implementation "com.autoai.link.threadpool:threadpool:0.0.6"
//    implementation COMMON_DEPEN.DESIGN
//    implementation COMMON_DEPEN.MAPBAR_ANDROID_WLSCREEN_HID
}