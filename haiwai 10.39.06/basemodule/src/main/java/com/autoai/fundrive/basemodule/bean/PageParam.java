package com.autoai.fundrive.basemodule.bean;

import android.view.animation.Animation;

import androidx.annotation.NonNull;

public class PageParam {
    private int updateScreenIndex = 0;
    private String fromModuleName = null;
    private int fromModuleId = -1;
    private int flag = -1;
    private String toModuleName = null;
    private int toModuleId = -1;
    private Object paramData = null;
    private Animation fromAnimation = null;
    private Animation toAnimation = null;
    private boolean isClearPage = false;

    public int getUpdateScreenIndex() {
        return updateScreenIndex;
    }

    public void setUpdateScreenIndex(int updateScreenIndex) {
        this.updateScreenIndex = updateScreenIndex;
    }

    public boolean isClearPage() {
        return isClearPage;
    }

    public void setClearPage(boolean clearPage) {
        isClearPage = clearPage;
    }

    public String getFromModuleName() {
        return fromModuleName;
    }

    public void setFromModuleName(String fromModuleName) {
        this.fromModuleName = fromModuleName;
    }

    public int getFromModuleId() {
        return fromModuleId;
    }

    public void setFromModuleId(int fromModuleId) {
        this.fromModuleId = fromModuleId;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getToModuleName() {
        return toModuleName;
    }

    public void setToModuleName(String toModuleName) {
        this.toModuleName = toModuleName;
    }

    public int getToModuleId() {
        return toModuleId;
    }

    public void setToModuleId(int toModuleId) {
        this.toModuleId = toModuleId;
    }

    public Object getParamData() {
        return paramData;
    }

    public void setParamData(Object paramData) {
        this.paramData = paramData;
    }

    public Animation getFromAnimation() {
        return fromAnimation;
    }

    public void setFromAnimation(Animation fromAnimation) {
        this.fromAnimation = fromAnimation;
    }

    public Animation getToAnimation() {
        return toAnimation;
    }

    public void setToAnimation(Animation toAnimation) {
        this.toAnimation = toAnimation;
    }

    @NonNull
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("\nupdateScreenIndex : ").append(updateScreenIndex);
        stringBuilder.append("\nfromModuleName : ").append(fromModuleName);
        stringBuilder.append("\nfromModuleId : ").append(fromModuleId);
        stringBuilder.append("\nflag : ").append(flag);
        stringBuilder.append("\ntoModuleName : ").append(toModuleName);
        stringBuilder.append("\ntoModuleId : ").append(toModuleId);
        stringBuilder.append("\nparamData : ").append(paramData);
        stringBuilder.append("\nfromAnimation : ").append(fromAnimation);
        stringBuilder.append("\ntoAnimation : ").append(toAnimation);
        stringBuilder.append("\nisClearPage : ").append(isClearPage);
        return stringBuilder.toString();
    }

    public void reset() {
        fromModuleName = null;
        fromModuleId = -1;
        flag = -1;
        toModuleName = null;
        toModuleId = -1;
        paramData = null;
        fromAnimation = null;
        toAnimation = null;
        isClearPage = false;
    }
}
