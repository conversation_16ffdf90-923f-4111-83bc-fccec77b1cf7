package com.autoai.fundrive.basemodule.widget;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.R;

import java.util.ArrayList;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
public class AOAToast {
    public static final int LENGTH_SHORT = 0;
    public static final int LENGTH_LONG = 1;

    private Toast mToast;
    private final List<Toast> mAOAToasts = new ArrayList<>();

    private final AppActivity mAppActivity;
    private CharSequence sText;
    private int sTime;

    private View mView;
    private Integer mGravity;
    private int xOff = 0;
    private int yOff = 0;
    private int mRootViewId;

    public void makeText(CharSequence text, int time, int rootViewId) {
        sText = text;
        sTime = time;
        mRootViewId = rootViewId;
    }

    public void makeText(int text, int time, int rootViewId) {
        makeText(mAppActivity.getContext().getResources().getText(text), time,rootViewId);
    }

    protected AOAToast(AppActivity aAppActivity) {
        mAppActivity = aAppActivity;
    }

    public void setView(View view) {
        mView = view;
    }

    public void setGravity(Integer gravity) {
        setGravity(gravity, 0, 0);
    }

    public void setGravity(Integer gravity, int xOff, int yOff) {
        mGravity = gravity;
        this.xOff = xOff;
        this.yOff = yOff;
    }

    public void setDuration(int time) {
        sTime = time;
    }

    public void show() {
        Toast toast;
        if (mView != null) {
            toast = new Toast(mAppActivity, mView, mRootViewId, sTime, xOff, yOff);
        } else {
            toast = new Toast(mAppActivity, sText, mRootViewId, sTime, xOff, yOff);
        }
        if (mGravity != null) {
            toast.setGravity(mGravity);
        }
        mAOAToasts.add(toast);
        if (mToast == null || !mToast.isShowing) {
            refresh();
        }
    }

    private void refresh() {
        if (!mAOAToasts.isEmpty()) {
            mToast = mAOAToasts.get(0);
            mToast.show();
            long tmp;
            switch (mToast.showTime) {
                case LENGTH_LONG:
                    tmp = 3500;
                    break;
                case LENGTH_SHORT:
                default:
                    tmp = 2000;
                    break;
            }
            mHandler.sendEmptyMessageDelayed(0, tmp);
        }
    }

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 0) {
                mToast.dismiss();
                if (!mAOAToasts.isEmpty()) {
                    mAOAToasts.remove(0);
                }
                mGravity = null;
                xOff = 0;
                yOff = 0;
                refresh();
            }
        }
    };

    private class Toast {
        private FrameLayout mFrameLayout;
        private View mDialogView;

        /**
         * 对话框是否在显示
         */
        private boolean isShowing;

        private int showTime;
        private Integer mGravity;

        public Toast(AppActivity aAppActivity, CharSequence content, int rootViewId, int time, int xOff, int yOff) {
            mDialogView = LayoutInflater.from(aAppActivity.getContext()).inflate(R.layout.welink_layout_toast, null);
            TextView tvContent = mDialogView.findViewById(R.id.message);
            tvContent.getBackground().setAlpha(125);
            tvContent.setText(content);
            init(aAppActivity, rootViewId, time, xOff, yOff);

        }

        public Toast(AppActivity aAppActivity, View view, int rootViewId, int time, int xOff, int yOff) {
            mDialogView = view;
            init(aAppActivity, rootViewId, time, xOff, yOff);
        }

        private void init(AppActivity aAppActivity, int rootViewId, int time, int xOff, int yOff) {
            showTime = time;
            mFrameLayout = (FrameLayout) aAppActivity.findView(rootViewId);
            if (mFrameLayout == null) {
                return;
            }

            FrameLayout.LayoutParams paramsParent =
                    new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT,
                            FrameLayout.LayoutParams.WRAP_CONTENT);
            paramsParent.gravity = Gravity.CENTER;
            paramsParent.setMargins(xOff, yOff, 0, 0);
            mDialogView.setLayoutParams(paramsParent);
        }

        public void setGravity(int gravity) {
            mGravity = gravity;
            FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) mDialogView.getLayoutParams();
            params.gravity = gravity;
            if (!isViewNull()) {
                mDialogView.setLayoutParams(params);
            }
        }

        /**
         * 取消对话框
         */
        public void dismiss() {
            if (mFrameLayout == null || isViewNull()) {
                return;
            }
            isShowing = false;
            mFrameLayout.removeView(mDialogView);
        }

        /**
         * 显示对话框
         */
        public void show() {
            if (mFrameLayout == null || isViewNull()) {
                return;
            }
            isShowing = true;
            mFrameLayout.addView(mDialogView);
        }

        private boolean isViewNull() {
            return mDialogView == null;
        }
    }
}
