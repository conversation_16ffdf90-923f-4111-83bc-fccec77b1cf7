package com.autoai.fundrive.basemodule.broadcast;

import android.content.Context;
import android.content.Intent;

import com.autoai.fundrive.basemodule.singleton.Singleton;

import java.util.List;

/**
 * 广播服务
 * 主要负责分发模块注册的广播通知
 *
 * <AUTHOR>
 */
public class BroadcastService implements Singleton {
    public static final String NAME = BroadcastService.class.getSimpleName();
    private BroadcastMapping mBroadcastMapping;
    private BroadcastDispatcher mBroadcastDispatcher;
    private BroadcastManager mBroadcastManager;

    /**
     * 注册广播
     *
     * @param listener 对外接口
     * @param action   广播事件
     */
    public void registerBroadcastListener(IBroadcastNotification listener, String... action) {
//        LogManager.d("add action=" + Arrays.toString(action) + " & IBroadcastNotification=" + listener);
        if (mBroadcastMapping != null) {
            mBroadcastMapping.addAction(listener, action);
        }
        if (mBroadcastManager != null) {
            mBroadcastManager.registerSystem(action);
        }
    }

    /**
     * 删除广播
     * 不同广播事件action，可对应同一接口，此方法会删除全部
     *
     * @param listener 对外接口
     */
    public void unRegisterBroadcastListener(IBroadcastNotification listener) {
//        LogManager.d("remove IBroadcastNotification=" + listener);
        if (mBroadcastMapping != null) {
            mBroadcastMapping.removeListener(listener);
        }
        unregisterall();
    }

    /**
     * 删除广播
     * 过滤广播事件，删除对应注册的接口
     * 注：传入的action要与注册的action内容要完全匹配才能删除，不会删除包含关系的action
     *
     * @param listener 对外接口
     * @param action   广播事件
     */
    public void unRegisterBroadcastListener(IBroadcastNotification listener, String... action) {
//        LogManager.d("remove action=" + Arrays.toString(action) + " & IBroadcastNotification=" + listener);
        if (mBroadcastMapping != null) {
            mBroadcastMapping.removeListener(listener, action);
        }
        unregisterall();
    }

    /**
     * 删除广播
     * 删除匹配的广播事件对应的所有接口
     * 注：传入的action要与注册的action内容要完全匹配才能删除，不会删除包含关系的action
     *
     * @param action 广播事件
     */
    public void clearBroadcastAction(String... action) {
//        LogManager.d("remove action=" + Arrays.toString(action));
        if (mBroadcastMapping != null) {
            mBroadcastMapping.removeAction(action);
        }
        unregisterall();
    }

    public void actionBroadcastWithIntent(Intent intent) {
//        LogManager.d("dispatch intent=" + intent + " & BroadcastMapping=" + mBroadcastMapping + " & BroadcastDispatcher=" + mBroadcastDispatcher);
        if (mBroadcastMapping != null) {
            List<IBroadcastNotification> listeners = mBroadcastMapping.getListenerByIntentAction(intent);
            if (listeners != null && listeners.size() > 0) {
//                LogManager.d("dispatch listener size=" + listeners.size());
                if (mBroadcastDispatcher != null) {
                    mBroadcastDispatcher.dispatchBroadcast(intent, listeners);
                }
            }
        }
    }

    /**
     * 删除广播事件，全部
     */
    public void clearAll() {
        if (mBroadcastMapping != null) {
            mBroadcastMapping.clear();
        }
        unregisterall();
//        LogManager.d("clearAll");
    }

    @Override
    public void loadService(Context aContext) {
        mBroadcastMapping = new BroadcastMapping();
        mBroadcastDispatcher = new BroadcastDispatcher();
        mBroadcastManager = new BroadcastManager(aContext);
//        LogManager.d("loadService Context=" + aContext);
    }

    @Override
    public void unloadService() {
        clearAll();
        if (mBroadcastDispatcher != null) {
            mBroadcastDispatcher.shutdown();
            mBroadcastDispatcher = null;
        }
        mBroadcastMapping = null;
        mBroadcastManager = null;
//        LogManager.d("unloadService");
    }

    private void unregisterall() {
        if (mBroadcastMapping != null
                && mBroadcastMapping.isEmpty()
                && mBroadcastManager != null) {
            mBroadcastManager.unregister();
        }
//        LogManager.d("unregisterall");
    }
}
