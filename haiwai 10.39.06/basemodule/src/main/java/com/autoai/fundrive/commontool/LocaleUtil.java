package com.autoai.fundrive.commontool;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.util.DisplayMetrics;

import androidx.annotation.NonNull;

import java.util.Locale;

public class LocaleUtil {
    public static Locale locale = null;

    public static void changeLocaleConfig(@NonNull Context context, Locale loc) {
        Resources resources = context.getResources();
        if (resources != null) {
            Configuration configuration = resources.getConfiguration();
            DisplayMetrics displayMetrics = resources.getDisplayMetrics();
            configuration.setLocale(loc);
            //更新配置
            context.getResources().updateConfiguration(configuration, displayMetrics);
        }
    }
}
