package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是用来实现 CRS3.0-SVW项目车机版本升级功能，车机版本升级的相关信息。
 */
public class HuUpdateBean extends BaseProtocolBean {

    /**
     * 车机端版本
     */
    private String version;
    /**
     * 车机版本包名
     */
    private String packageName;
    /**
     * 车机model号
     */
    private String model;


    public String getVersion() {
        return version;
    }

    public String getPackageName() {
        return packageName;
    }

    public String getModel() {
        return model;
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.version = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_VERSION);
        this.packageName = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_PACKAGE);
        this.model = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_MODEL);
    }


    @Override
    public String toString() {
        return "HuUpdateBean{" +
                "version='" + version + '\'' +
                ", packageName='" + packageName + '\'' +
                ", model='" + model + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }
}
