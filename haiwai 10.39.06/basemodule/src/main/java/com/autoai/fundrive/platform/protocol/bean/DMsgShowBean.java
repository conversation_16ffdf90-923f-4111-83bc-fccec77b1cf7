package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端 D类消息的 H5 界面通知手机端 launcher来进行展示
 */

public class DMsgShowBean extends BaseProtocolBean {
    /**
     * h5地址
     */
    private String h5Address;

    public String getH5Address() {
        return h5Address;
    }

    @Override
    public String toString() {
        return "DMsgShowBean{" +
                "h5Address='" + h5Address + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.h5Address = extData.getString(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_HADDRESS);
    }


}
