package com.autoai.fundrive.platform;

import static android.content.Context.NOTIFICATION_SERVICE;
import static android.content.Context.RECEIVER_EXPORTED;

import android.app.Activity;
import android.app.Dialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.Surface;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.R;
import com.autoai.fundrive.basemodule.singleton.Singleton;
import com.autoai.fundrive.commontool.LocaleUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.bean.ConnectorBean;
import com.autoai.fundrive.platform.bean.WLPlatformState;
import com.autoai.fundrive.platform.listener.LinkAdapterListener;
import com.autoai.fundrive.platform.listener.LinkFrameListener;
import com.autoai.fundrive.platform.listener.LinkPlatformListener;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.welink.platform.WLPlatform;
import com.autoai.welink.platform.WLPlatformListener;

import java.util.HashMap;

/**
 * 主要负责实现并提供平台应用功能接口。
 *
 * <AUTHOR>
 */
@Keep
public class WLPlatformManager implements Singleton {

    public static final String NAME = WLPlatformManager.class.getSimpleName();
    private Activity mContext;
    private WLPlatform mWLPlatform;
    private PlatformReceiver mPlatformReceiver;
    private LinkPlatformListener mLinkPlatformListener;
    private LinkAdapterListener mLinkAdapterListener;
    private LinkFrameListener mLinkFrameListener;
    private WLPlatformState mPlatformCarState = WLPlatformState.STATUS_NONE;

    private boolean isEnableExternal = false;
    private boolean isExternalCastScreen = false;
    private boolean isFirstFrameData = false;

    private int huScreenWidth;
    private int huScreenHeight;
    private int densityDpi;
    private String vehicleType;
    private String vehicleVersion = "";
    private String btMacAddress;

    public Notification mNotification;

    private final HashMap<String, ConnectorBean> mAssignKeys = new HashMap<>();

    private static final int WL_PLATFORM_HANDLER_WHAR_SEND = 100;
    private static final int WL_PLATFORM_HANDLER_WHAR_DELAYED_TIME = 1000;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (WL_PLATFORM_HANDLER_WHAR_SEND == msg.what) {
                Bundle data = msg.getData();
                if (data != null) {
                    String packageName = data.getString(WLPlatformConfig.WL_PLATFORM_BUNDLE_PACKAGE_NAME);
                    String connectKey = data.getString(WLPlatformConfig.WL_PLATFORM_BUNDLE_CONNECT_KEY);
                    Intent connect = new Intent(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
                    connect.putExtra(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY, connectKey);
                    connect.setPackage(packageName);
                    mContext.sendBroadcast(connect);
                }
            }
        }
    };

    public WLPlatformManager() {
    }

    /**
     * 初始化平台服务管理类
     *
     * @param activity - 上下文 ,
     * @param listener 三投平台手车连接状态接口
     */
    @Keep
    public void init(Activity activity, LinkPlatformListener listener) {
        LogManager.i("init -------------->");
        mContext = activity;
        mNotification = createNotification();
        mLinkPlatformListener = listener;
        mWLPlatform = WLPlatform.create(mContext, mNotification, new WLPlatformListener() {

            @Override
            public void onLinkConnected(int mScreenWidth, int mScreenHeight, int mDensityDpi, String mVehicleType, String mVehicleVersion, String mBtMacAddress, boolean isAOA) {
                LogManager.i("WLPlatformListener -------- onLinkConnected -------------->mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi + ",mVehicleType:" + mVehicleType + ",mVehicleVersion:" + mVehicleVersion + ",mBtMacAddress:" + mBtMacAddress + ",isAOA:" + isAOA);
                if (mWLPlatform == null) {
                    return;
                }

                huScreenWidth = mScreenWidth;
                huScreenHeight = mScreenHeight;
                densityDpi = mDensityDpi;
                vehicleType = mVehicleType;
                vehicleVersion = mVehicleVersion;
                btMacAddress = mBtMacAddress;
                mAssignKeys.clear();

                mWLPlatform.init("", null);

                isFirstFrameData = true;
                if (mLinkFrameListener == null) {
                    LogManager.i("onLinkConnected --------start------>1111111");
                    mWLPlatform.start();
                } else {
                    LogManager.i("onLinkConnected --------start------>2222222");
                    mWLPlatform.start(0, 1, 0, 48);
                }

                mPlatformCarState = WLPlatformState.STATUS_CAR_CONNECTED;

                if (mLinkPlatformListener != null) {
                    mLinkPlatformListener.onLinkConnected(vehicleType, isAOA);
                }
            }

            @Override
            public void onLinkUnconnected(boolean isCrash) {
                LogManager.i("WLPlatformListener -------- onLinkUnconnected -------------->isCrash:" + isCrash);
                if (mWLPlatform == null) {
                    return;
                }

                mWLPlatform.stop();
                mWLPlatform.deinit();
                overlayDialog = null;
                //
                mPlatformCarState = WLPlatformState.STATUS_CAR_DISCONNECTED;
                isEnableExternal = false;
                isExternalCastScreen = false;
                isFirstFrameData = false;

                if (mLinkPlatformListener != null) {
                    mLinkPlatformListener.onLinkDisconnected();
                }

                WLPlatformConfig.isConnectcar = false;
            }

            @Override
            public void onAppConnected(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppConnected -------------->connectStr:" + connectStr);
                mWLPlatform.mirror(connectStr);
                if (isPlatformApp(connectStr)) {
                    WLPlatformConfig.isConnectcar = true;
                }
            }

            @Override
            public void onAppDisconnected(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppDisconnected -------------->connectStr:" + connectStr);
                mWLPlatform.revoke(connectStr);
            }

            @Override
            public void onAppError(String connectStr, int errorCode) {
                LogManager.i("WLPlatformListener -------- onAppError -------------->errorCode :" + errorCode);
            }

            @Override
            public void onAppForeground(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppForeground -------------->");
            }

            @Override
            public void onAppBackground(String connectStr) {
                LogManager.i("WLPlatformListener -------- onAppBackground -------------->");
            }

            @Override
            public void onAppAction(String connectStr, int action) {
                LogManager.i("WLPlatformListener -------- onAppAction -------------->");
                switch (action) {
                    case WLPlatformConfig.WL_APP_ACTION_START_MIC:
                        //请求车机MIC声音
                        mWLPlatform.openMicrophone(connectStr);
                        break;
                    case WLPlatformConfig.WL_APP_ACTION_STOP_MIC:
                        //停止车机MIC声音
                        mWLPlatform.openMicrophone(null);
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onLinkHUMessageData(String data) {
                LogManager.i("WLPlatformListener -------- onLinkHUMessageData -------------->data:" + data);
                if (!TextUtils.isEmpty(data)) {
                    WLProtocolManager.getInstance().onReceiveHUCommand(data);
                }
            }

            @Override
            public void onLinkHUCanData(byte[] bytes) {
                if (bytes != null && bytes.length > 0) {
                    String data = new String(bytes);
                    WLProtocolManager.getInstance().onReceiveHUCommand(data);
                }
            }

            @Override
            public void onLinkTouch(MotionEvent motionEvent) {
                Dialog mDialog = getOverlayDialog();

                if (mDialog != null && mDialog.isShowing()) {
                    if (mDialog.dispatchTouchEvent(motionEvent)) {
                        return;
                    }
                }

                if (isEnableExternal && isExternalCastScreen) {
                    if (mLinkAdapterListener != null) {
                        mLinkAdapterListener.onLinkTouch(motionEvent);
                    }
                } else {
                    mWLPlatform.touch(motionEvent);
                }
            }

            @Override
            public int onAppSound(String connectStr, String mark, int duration) {
                LogManager.i("WLPlatformListener -------- onAppSound -------------->mark:" + mark + ",duration:" + duration);
                int audioType = 0;
                if (isPlatformApp(connectStr)) {
                    try {
                        audioType = Integer.parseInt(mark);
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                }
                LogManager.i("WLPlatformListener -------- onAppSound -------------->audioType:" + audioType);
                return audioType;
            }

            @Override
            public void onMusicRegister(String connectStr) {
                LogManager.i("WLPlatformListener -------- onMusicRegister -------------->");

            }

            @Override
            public void onMusicUnregister(String connectStr) {
                LogManager.i("WLPlatformListener -------- onMusicUnregister -------------->");

            }

            @Override
            public void onMusicID3(String source, String artist, String title, String album, String lyric, int duration, Bitmap cover) {
                LogManager.i("WLPlatformListener -------- onMusicID3 -------------->");

            }

            @Override
            public void onMusicOrder(int order) {
                LogManager.i("WLPlatformListener -------- onMusicOrder -------------->");

            }

            @Override
            public void onMusicPCM(long position, long totalLen, int rate, int bit, int channel) {
                LogManager.i("WLPlatformListener -------- onMusicPCM -------------->totalLen:" + totalLen + ",position:" + position);

            }

            @Override
            public void onFrameData(byte[] bytes) {
                LogManager.i("WLPlatformListener -------- onFrameData -------------->size:" + bytes.length + ",isFirstFrameData:" + isFirstFrameData);
                if (mLinkFrameListener != null && isFirstFrameData) {
                    isFirstFrameData = false;
                    mLinkFrameListener.onFrameData(bytes);
                }
            }

            @Override
            public void onLinkSuspend() {
                LogManager.i("WLPlatformListener -------- onLinkSuspend -------------->");
                mWLPlatform.stop();
            }

            @Override
            public void onLinkResume() {
                LogManager.i("WLPlatformListener -------- onLinkResume -------------->");
                if (mLinkFrameListener == null) {
                    LogManager.i("onLinkConnected --------start------>1111111");
                    mWLPlatform.start();
                } else {
                    LogManager.i("onLinkConnected --------start------>2222222");
                    mWLPlatform.start(0, 1, 0, 48);
                }
            }

            @Override
            public void onLinkAOAReady() {
                LogManager.i("WLPlatformListener -------- onLinkAOAReady -------------->");
            }

            @Override
            public void onHardwareGroupError(int reason) {
                LogManager.i("WLPlatformListener -------- onHardwareGroupError -------------->reason:" + reason);
            }

            @Override
            public void onHardwareGroupStatusChanged(int status, String content) {
                LogManager.i("WLPlatformListener -------- onHardwareGroupStatusChanged -------------->status:" + status + ",content:" + content);
            }

        });
        mWLPlatform.enableLogFile(LogManager.isIsLoggable());
        registerReceiver();
//        startWifiLink();
//        requestDirectLink();
    }

    public void setLoggable( boolean isLoggable){
        if (mWLPlatform!=null){
            mWLPlatform.enableLogFile(isLoggable);
        }
    }


    /**
     * 连接三方app
     *
     * @param packageName - 应用包名 ,
     * @param connectKey  三投应用连接码
     */
    @Keep
    public void requestConnect(String packageName, String connectKey) {
        LogManager.i("requestConnect -------------->packageName:" + packageName + ",connectKey:" + connectKey);
        //连接车机状态下
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED) {
            mHandler.removeMessages(WL_PLATFORM_HANDLER_WHAR_SEND);
            Message message = mHandler.obtainMessage();
            Bundle data = new Bundle();
            data.putString(WLPlatformConfig.WL_PLATFORM_BUNDLE_PACKAGE_NAME, packageName);
            data.putString(WLPlatformConfig.WL_PLATFORM_BUNDLE_CONNECT_KEY, connectKey);
            message.what = WL_PLATFORM_HANDLER_WHAR_SEND;
            message.setData(data);
            mHandler.sendMessageDelayed(message, WL_PLATFORM_HANDLER_WHAR_DELAYED_TIME);
        }
    }

    /**
     * 开始连接车机
     *
     * @param intent - 互联意图
     */
    @Keep
    public void startLink(Intent intent) {
        LogManager.i("startLink -------------->");
        if (mWLPlatform == null) {
            return;
        }
        //如果在连接车机中或者已连接车机，不在进行连接车机操作
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_CONNECTED) {
            return;
        }
//        stopDirectLink();
        mPlatformCarState = WLPlatformState.STATUS_CAR_CONNECTING;
        LogManager.i("startLink -------11111111------->");
        mWLPlatform.link(intent);
    }

    /**
     * 开始断开连接车机
     */
    @Keep
    public void stopLink() {
        LogManager.i("stopLink -------------->");
        if (mWLPlatform == null) {
            return;
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return;
        }
        mPlatformCarState = WLPlatformState.STATUS_CAR_DISCONNECTING;
        LogManager.i("stopLink -------11111111------->");
        mWLPlatform.unlink();
    }

    /**
     * 获取app连接码
     *
     * @param packageName - 应用包名 ,
     * @param cap         三投应用互联能力码
     */
    @Keep
    public String getConnectKey(String packageName, int cap) {
        LogManager.i("getConnectKey -------------->packageName:" + packageName + ",cap:" + cap);
        if (mWLPlatform == null) {
            return null;
        }
        String connectKey = mWLPlatform.assign(packageName, cap);

        ConnectorBean bean = new ConnectorBean();
        bean.setConnectKey(connectKey);
        bean.setPackageName(packageName);
        bean.setCap(cap);

        mAssignKeys.put(connectKey, bean);
        return connectKey;
    }

    /**
     * 依据范围获取投屏显示容器
     */
    @Keep
    public Surface getEnableExternal(Rect rect) {
        LogManager.i("getDisplaySurface -------------->");
        if (mWLPlatform == null) {
            return null;
        }
        isEnableExternal = true;
        return mWLPlatform.enableExternal(rect);
    }


    /**
     * 取消获取投屏显示容器
     */
    @Keep
    public void getDisEnableExternal() {
        LogManager.i("getDisplaySurface -------------->");
        if (mWLPlatform == null) {
            return;
        }
        isEnableExternal = false;
        mWLPlatform.disableExternal();
    }

    private Dialog overlayDialog;

    /**
     * 获取Overlay显示容器
     */
    @Keep
    public Dialog getOverlayDialog() {
        LogManager.i("getOverlayDialog -------------->");
        if (mWLPlatform == null) {
            return null;
        }
        if (overlayDialog == null) {
            overlayDialog = mWLPlatform.getOverlay();
        }
        if(overlayDialog != null && overlayDialog.getContext() != null) {
            LocaleUtil.changeLocaleConfig(overlayDialog.getContext(), LocaleUtil.locale);
        }
        return overlayDialog;
    }

    /**
     * 手机给车机发送消息交互协议
     *
     * @param protocolData - json格式协议字符串
     */
    @Keep
    public void sendMessageDataToCar(String protocolData) {
        LogManager.i("sendMessageDataToCar -------------->protocolData:" + protocolData);
        if (mWLPlatform == null) {
            return;
        }
        if (!TextUtils.isEmpty(protocolData)) {
            mWLPlatform.sendMessageData(protocolData);
        }
    }


    /**
     * 手机给车机发送candata交互协议
     *
     * @param protocolData - json格式协议字符串
     */
    @Keep
    public void sendCanDataToCar(String protocolData) {
        LogManager.i("sendCanDataToCar -------------->protocolData:" + protocolData);
        if (mWLPlatform == null) {
            return;
        }
        if (!TextUtils.isEmpty(protocolData)) {
            mWLPlatform.sendCanData(protocolData.getBytes());
        }
    }

    /**
     * 销毁平台服务管理类
     */
    @Keep
    public void onDestroy() {
        LogManager.i("onDestory -------------->");
        if (mWLPlatform != null) {
//            stopWifiLink();
            mWLPlatform.release();
            mWLPlatform = null;
        }
        unRegisterReceiver();
        WLProtocolManager.getInstance().clearAllMethodListener();
        mPlatformCarState = WLPlatformState.STATUS_NONE;
        mContext = null;
    }

    /**
     * 设置适配三方投屏回调接口
     */
    @Keep
    public void registerAdapterListener(LinkAdapterListener listener) {
        LogManager.i("registerAdapterListener -------------->");
        mLinkAdapterListener = listener;
    }

    /**
     * 取消设置适配三方投屏回调接口
     */
    @Keep
    public void unRegisterAdapterListener() {
        LogManager.i("unRregisterAdapterListener -------------->");
        mLinkAdapterListener = null;
    }

    /**
     * 通知适配层互联成功状态
     */
    @Keep
    public void connectedAdapter() {
        LogManager.i("connectedAdapter -------------->");
        if (mLinkAdapterListener != null) {
            LogManager.i("connectedAdapter ------11111111111-------->");
            mLinkAdapterListener.onLinkConnected(mContext, mNotification, huScreenWidth, huScreenHeight, densityDpi, vehicleType);
        }
    }

    /**
     * 通知适配层互联断开状态
     */
    @Keep
    public void disconnectedAdapter() {
        LogManager.i("disconnectedAdapter -------------->");
        if (mLinkAdapterListener != null) {
            isExternalCastScreen = false;
            mLinkAdapterListener.onLinkDisconnected();
        }
    }

    /**
     * 控制是否进入三方投屏方式
     */
    @Keep
    public void setExternalCastScreen(boolean isExternal) {
        LogManager.i("setExternalCastScreen -------------->");
        isExternalCastScreen = isExternal;
    }

    /**
     * 检测权限
     */
    @Keep
    public void activityResult(int requestCode, int resultCode, Intent data) {
        LogManager.i("activityResult -------------->");
        if (mLinkAdapterListener != null) {
            mLinkAdapterListener.activityResult(requestCode, resultCode, data);
        }
    }

//    /**
//     * 是否输出log日志
//     */
//    @Keep
//    public void enableDebug(boolean isDebug) {
//        LogManager.i("enableDebug --------------> isDebug:" + isDebug);
//        if (mWLPlatform == null) {
//            return;
//        }
////        LogManager.i( "WLPlatform version:"+ WLPlatform.getVersion());
////        LogManager.i( "WLHardwareHub version:"+ WLHardwareHub.getVersion());
////        LogManager.i( "WLServer version:"+ WLServer.getVersion());
////        LogManager.i( "WLConnector version:"+ WLConnector.getVersion());
////        LogManager.i( "WLChannel version:"+ WLChannel.getVersion());
//        mWLPlatform.enableLogFile(isDebug);
//    }

    /**
     * 设置获取视频帧数据回调接口
     */
    @Keep
    public void registerFrameListener(LinkFrameListener listener) {
        LogManager.i("registerFrameListener -------------->");
        mLinkFrameListener = listener;
    }

    /**
     * 取消设置获取视频帧数据回调接口
     */
    @Keep
    public void unRegisterFrameListener() {
        LogManager.i("unRregisterFrameListener -------------->");
        mLinkFrameListener = null;
    }

    /**
     * 获取车机端link软件版本号
     */
    @Keep
    public String getCarLinkVersion() {
        if (mWLPlatform == null) {
            return "";
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return "";
        }
        return vehicleVersion;
    }

    /**
     * 获取车机端蓝牙地址
     */
    public String getHuBtMacAddress() {
        if (mWLPlatform == null) {
            return null;
        }
        //如果在断开车机中或者已断开车机，不在进行断开车机操作
        if (mPlatformCarState == WLPlatformState.STATUS_CAR_DISCONNECTED) {
            return null;
        }
        return btMacAddress;
    }

    @Override
    public void loadService(Context aContext) {
        LogManager.i("loadService -------------->");
    }

    @Override
    public void unloadService() {
        LogManager.i("unloadService -------------->");
        onDestroy();
    }

    /**
     * 开启监听三方app请求连接广播
     */
    private void registerReceiver() {
        LogManager.i("register PlatformReceiver -------------->");
        if (mPlatformReceiver == null) {
            mPlatformReceiver = new PlatformReceiver();
            IntentFilter intentFilter = new IntentFilter(WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION);
            if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.TIRAMISU){
                mContext.registerReceiver(mPlatformReceiver, intentFilter,RECEIVER_EXPORTED);
            }else {
                mContext.registerReceiver(mPlatformReceiver, intentFilter);
            }
        }
    }

    /**
     * 取消监听三方app请求连接广播
     */
    private void unRegisterReceiver() {
        LogManager.i("unRregisterReceiver -------------->");
        if (mPlatformReceiver != null) {
            mContext.unregisterReceiver(mPlatformReceiver);
            mPlatformReceiver = null;
        }
    }

    /**
     * 创建前台服务广播通知
     */
    private Notification createNotification() {
        PendingIntent pendingIntent;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), PendingIntent.FLAG_MUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(mContext, 0, new Intent(mContext, mContext.getClass()), 0);
        }

        Notification.Builder builder = new Notification.Builder(mContext).setContentIntent(pendingIntent).setSmallIcon(R.drawable.haiwai_launcher_welink_ic_notifiy).setTicker("started").setWhen(System.currentTimeMillis())
                .setContentTitle(mContext.getResources().getString(R.string.welink_notification_service)).setContentText(mContext.getResources().getString(R.string.welink_notification_running));
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel notificationChannel = new NotificationChannel(mContext.getPackageName(), mContext.getResources().getString(R.string.welink_notification_name), NotificationManager.IMPORTANCE_MIN);
            NotificationManager manager = (NotificationManager) mContext.getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            builder.setChannelId(mContext.getPackageName());
        }
        return builder.build();
    }

    /**
     * 判断当前连接的是否是平台app
     */
    private boolean isPlatformApp(String connectStr) {
        ConnectorBean bean = mAssignKeys.get(connectStr);
        if (bean != null) {
            return mContext.getPackageName().equals(bean.getPackageName());
        } else {
            return false;
        }
    }

    private class PlatformReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (mWLPlatform == null || intent == null) {
                return;
            }
            if (TextUtils.equals(WLPlatformConfig.WL_PLATFORM_REQUEST_ACTION, intent.getAction())) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    String pkgName = bundle.getString(WLPlatformConfig.WL_PLATFORM_PACKAGE_NAME);
                    if (!TextUtils.isEmpty(pkgName)) {
                        String connectStr = mWLPlatform.assign(pkgName, WLPlatform.WL_CAP_MUSIC);
                        Intent connect = new Intent(WLPlatformConfig.WL_PLATFORM_CONNECT_ACTION);
                        connect.putExtra(WLPlatformConfig.WL_PLATFORM_CONNECT_KEY, connectStr);
                        connect.setPackage(pkgName);
                        mContext.sendBroadcast(connect);
                    }
                }
            }
        }
    }
}
