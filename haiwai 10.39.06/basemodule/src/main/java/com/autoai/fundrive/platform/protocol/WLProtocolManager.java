package com.autoai.fundrive.platform.protocol;

import android.text.TextUtils;

import androidx.annotation.Keep;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.protocol.control.HUProtocolControl;
import com.autoai.fundrive.platform.protocol.control.MUProtocolControl;
import com.autoai.fundrive.platform.protocol.listener.HUCommandListener;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 交互协议管理类，主要统一处理手机发送端的消息和接收车机端接收的消息。
 *
 * <AUTHOR>
 */
@Keep
public class WLProtocolManager {

    private final MUProtocolControl muProtocolControl;
    private final HUProtocolControl huProtocolControl;

    private static WLProtocolManager mWLProtocolManager;

    private WLProtocolManager() {
        this.muProtocolControl = new MUProtocolControl();
        this.huProtocolControl = new HUProtocolControl();
    }

    @Keep
    public static WLProtocolManager getInstance() {
        if (mWLProtocolManager == null) {
            synchronized (WLProtocolManager.class) {
                if (mWLProtocolManager == null) {
                    mWLProtocolManager = new WLProtocolManager();
                }
            }
        }
        return mWLProtocolManager;
    }

    /**
     * 接收并解析处理platform平台传递过的车机消息
     */
    public void onReceiveHUCommand(String command) {
        LogManager.i("onReceiveHUCommand ---------> command:" + command);
        try {
            JSONObject obj = new JSONObject(command);
            obj = obj.getJSONObject(WLProtocolConfig.WL_PROTOCOL_FIELD_COMMAND);
            String method = obj.getString(WLProtocolConfig.WL_PROTOCOL_FIELD_METHOD);
            JSONObject extData = obj.getJSONObject(WLProtocolConfig.WL_PROTOCOL_FIELD_EXTDATA);
            if (!TextUtils.isEmpty(method)) {
                huProtocolControl.parseCommand(method, extData);
            }
        } catch (Exception e) {
            LogManager.e("onReceiveHUCommand exception:" + e.getMessage());
        }
    }

    /**
     * 添加指定方法的监听用来接收手机端的协议消息广播
     */
    @Keep
    public void addMethodListener(String methodName, HUCommandListener mListener) {
        LogManager.i("addMethodListener ---------> methodName:" + methodName);
        huProtocolControl.addListener(methodName, mListener);
    }

    /**
     * 移除指定方法的监听禁止接收手机端的协议消息广播
     */
    @Keep
    public void removeMethodListener(String methodTag, HUCommandListener mListener) {
        LogManager.i("addMethodListener ---------> methodTag:" + methodTag);
        huProtocolControl.removeListener(methodTag, mListener);
    }

    /**
     * 清空所有方法的监听禁止接收手机端的协议消息广播
     */
    @Keep
    public void clearAllMethodListener() {
        LogManager.i("clearMethodListener --------->");
        huProtocolControl.clearListener();
    }


    /**
     * 给车机端发送手机端自定义按键
     *
     * @param pressDownKey 手机端自定义按键
     */
    @Keep
    public void sendMuChannelToCar(int pressDownKey) {
        LogManager.i("sendMuChannelToCar ---------> pressDownKey:" + pressDownKey);
        muProtocolControl.sendMuChannelToCar(pressDownKey);
    }

    /**
     * 给车机端发送WeLink退出消息
     *
     * @param isExit - 是否退出
     */
    @Keep
    public void sendExitToCar(boolean isExit) {
        LogManager.i(" isExit:" + isExit);
        muProtocolControl.sendExitToCar(isExit);
    }

    /**
     * 给车机端发送手机屏幕大小消息
     *
     * @param width  - 手机宽，
     * @param height - 手机高
     */
    @Keep
    public void sendScreenSizeToCar(int width, int height) {
        LogManager.i("sendScreenSizeToCar ---------> width:" + width + ",height:" + height);
        muProtocolControl.sendScreenSizeToCar(width, height);
    }


    /**
     * 给车机端发送手机互联状态消息
     *
     * @param isLink - 互联状态
     */
    @Keep
    public void sendAutoLinkToCar(boolean isLink) {
        LogManager.i("sendAutoLinkToCar ---------> isLink:" + isLink);
        muProtocolControl.sendAutoLinkToCar(isLink);
    }


    /**
     * 给车机端发送手机拨打电话消息
     *
     * @param number - 电话号码
     */
    @Keep
    public void sendTelephoneToCar(String number) {
        LogManager.i("sendTelephoneToCar ---------> number:" + number);
        muProtocolControl.sendTelephoneToCar(number);
    }


    /**
     * 此协议主要是点击手机端常驻条的home键时，通知车机端置为后台；
     */
    @Keep
    public void sendHomeKeyToCar() {
        LogManager.i("sendHomeKeyToCar --------->");
        muProtocolControl.sendHomeKeyToCar();
    }

    public void sendHomeKeyToCar(String action) {
        LogManager.i("sendHomeKeyToCar : action = " + action);
        muProtocolControl.sendHomeKeyToCar(action);
    }

    /**
     * 此协议主要是手机端通知车机端停止当前音频pcm播报
     *
     * @param pcmInfo 消息状态
     */
    @Keep
    public void sendStopPlayPCMToCar(int pcmInfo) {
        LogManager.i("sendStopPlayPCMToCar ---------> pcmInfo:" + pcmInfo);
        muProtocolControl.sendStopPlayPCMToCar(pcmInfo);
    }


    /**
     * 此协议主要是手机端通知车机端开启静音模式和取消静音模式
     *
     * @param state - 1 开启静音模式（静音模式下，除了VR的声音，其他声音都不进行播报），2 退出静音模式
     */
    @Keep
    public void sendChangeMuteToCar(int state) {
        LogManager.i("sendChangeMuteToCar ---------> state:" + state);
        muProtocolControl.sendChangeMuteToCar(state);
    }


    /**
     * 此协议主要是手机端通知车机端打开车载媒体
     */
    @Keep
    public void sendStartMediaToCar() {
        LogManager.i("sendStartMediaToCar --------->");
        muProtocolControl.sendStartMediaToCar();
    }


    /**
     * 消息拦截通知
     *
     * @param type - 0-微信，1-QQ，2-短信
     */
    @Keep
    public void sendMsgReceiveToCar(int type) {
        LogManager.i("sendMsgReceiveToCar ---------> type:" + type);
        muProtocolControl.sendMsgReceiveToCar(type);
    }


    /**
     * 微信给车机发送命令消息
     */
    @Keep
    public void sendWeChatResponseToCar(int code) {
        LogManager.i("sendWeChatResponseToCar ---------> code:" + code);
        muProtocolControl.sendWeChatResponseToCar(code);
    }

    /**
     * 给车机发送蓝牙连接状态消息
     */
    @Keep
    public void sendBluetoothStateToCar(boolean state) {
        LogManager.i("sendBluetoothStateToCar ---------> state:" + state);
        muProtocolControl.sendBluetoothStateToCar(state);
    }

    /**
     * 给车机发送手机端音乐播放状态
     *
     * @param state 播放状态 1(MEDIA_STATE_START)：开始播放；2(MEDIA_STATE_START)：停止播放
     */
    @Keep
    public void sendMediaStateToCar(int state) {
        LogManager.i("sendBluetoothStateToCar ---------> state:" + state);
        muProtocolControl.sendMediaStateToCar(state);
    }

    /**
     * 给车机发送手机端地图中心点数据的
     *
     * @param miniMapState 小地图当前状态；centerPoix 中心点x轴坐标；centerPoiy centerPoiy；showwidth 展示的宽；showhigh 展示的高
     */
    @Keep
    public void sendMobileNaviStateToCar(boolean miniMapState, int centerPoix, int centerPoiy, int showwidth, int showhigh) {
        LogManager.i("sendMobileNaviStateToCar ---------> miniMapState:" + miniMapState + ",centerPoix:" + centerPoix + ",centerPoiy:" + centerPoiy + ",showwidth:" + showwidth + ",showhigh:" + showhigh);
        muProtocolControl.sendMobileNaviStateToCar(miniMapState, centerPoix, centerPoiy, showwidth, showhigh);
    }

    /**
     * 音量调节控制
     *
     * @param controlVolume 具体调节 - requestType 车机调节类型
     */
    @Keep
    public void sendControlVolumeToCar(int controlVolume, int requestType) {
        LogManager.i("sendControlVolumeToCar ---------> controlVolume:" + controlVolume + ",requestType:" + requestType);
        muProtocolControl.sendControlVolumeToCar(controlVolume, requestType);
    }

    /**
     * 音量静音控制
     *
     * @param muteVolume  具体调节
     * @param requestType 车机调节类型
     */
    @Keep
    public void sendMuteVolumeToCar(int muteVolume, int requestType) {
        LogManager.i("sendMuteVolumeToCar ---------> muteVolume:" + muteVolume + ",requestType:" + requestType);
        muProtocolControl.sendMuteVolumeToCar(muteVolume, requestType);
    }

    /**
     * 空调开关控制
     *
     * @param aircoSwitch 空调开关 类型 int 1：关闭空调 2：开启空调
     * @param requestType 车机调节类型 类型 int
     * @param zoneArr     温区 类型 int []
     */

    @Keep
    public void sendAircoSwitchToCar(int aircoSwitch, int requestType, int[] zoneArr) {
        LogManager.i("sendAircoSwitchToCar ---------> aircoSwitch:" + aircoSwitch + ",requestType:" + requestType);
        muProtocolControl.sendAircoSwitchToCar(aircoSwitch, requestType, zoneArr);
    }

    /**
     * 空调温度控制
     *
     * @param airTemperatureType 温度单位 类型 int 1：摄氏度单位 2：华氏度单位
     * @param aircoTemp          空调温度调节 类型 double
     * @param requestType        车机调节类型 类型 int
     * @param zoneArr            温区 类型 int[]
     */
    @Keep
    public void sendAircoTempToCar(int airTemperatureType, double aircoTemp, int requestType, int[] zoneArr) {
        LogManager.i("sendAircoTempToCar ---------> airTemperatureType:" + airTemperatureType + ",aircoTemp:" + aircoTemp + ",requestType:" + requestType);
        muProtocolControl.sendAircoTempToCar(airTemperatureType, aircoTemp, requestType, zoneArr);
    }

    /**
     * 空调风量控制
     *
     * @param climateMode      风量模式 类型 int 1：automatical 2：manual
     * @param aircoBlowingrate 空调风量调节 类型 int automatical模式下： 1：soft 2：medium 3：intensive
     * @param requestType      车机调节类型 类型 int
     * @param zoneArr          温区 类型 int []
     */
    @Keep
    public void sendAircoBlowingrateToCar(int climateMode, int aircoBlowingrate, int requestType, int[] zoneArr) {
        LogManager.i("sendAircoBlowingrateToCar ---------> climateMode:" + climateMode + ",aircoBlowingrate:" + aircoBlowingrate + ",requestType:" + requestType);
        muProtocolControl.sendAircoBlowingrateToCar(climateMode, aircoBlowingrate, requestType, zoneArr);
    }

    /**
     * 给海外项目打开车机蓝牙设置界面
     */
    public void sendOpenHuBtSettingToCar() {
        LogManager.i("sendOpenHuBtSettingToCar --------->");
        muProtocolControl.sendOpenHuBtSettingToCar();
    }

    /**
     * 发送默认字符串格式消息
     *
     * @param msg - 字符串消息
     */
    @Keep
    public void sendCommondMessage(String msg) {
        LogManager.i("sendCommondMessage ---------> msg:" + msg);
        muProtocolControl.sendCommondMessage(msg);
    }

    public void sendActionCarHome() {
        LogManager.i("ScreenControl ---- updateStates ------------>");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android");

            JSONObject command = new JSONObject();
            command.put("method", "carHome");

            JSONObject extData = new JSONObject();
            extData.put("key", "actionCarHome");
            command.put("extData", extData);
            command.put("phoneHome", "");
            command.put("phoneBack", "");
            command.put("huHome", "");

            jsonObject.put("command", command);

        } catch (JSONException e) {
            LogManager.e("updateStates exception:" + e.getMessage());
        }
        String hidMessage = jsonObject.toString();
        LogManager.i("ScreenControl ---- updateStates ------------>hidMessage：" + hidMessage);
        WLProtocolManager.getInstance().sendCommondMessage(hidMessage);
    }
}
