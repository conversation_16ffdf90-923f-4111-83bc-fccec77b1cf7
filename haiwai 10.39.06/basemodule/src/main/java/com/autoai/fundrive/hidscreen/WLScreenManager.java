package com.autoai.fundrive.hidscreen;

import android.app.Activity;
import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.view.MotionEvent;

import com.autoai.fundrive.basemodule.BaseApplication;
import com.autoai.fundrive.basemodule.singleton.Singleton;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.listener.LinkAdapterListener;
import com.autoai.fundrive.platform.WLPlatformManager;

public class WLScreenManager implements Singleton {
    public static final String NAME = WLScreenManager.class.getSimpleName();

    private ScreenControl mScreenControl;

    @Override
    public void loadService(Context context) {
        LogManager.i("WLScreenManager ---- loadService ------------>");
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.registerAdapterListener(new LinkAdapterListener() {

            /**
             * 连接车机成功，设置投屏参数
             */
            @Override
            public void onLinkConnected(Activity mContext, Notification mNotification, int huScreenWidth, int huScreenHeight, int densityDpi, String vehicleType) {
                LogManager.i("WLScreenManager ---- onLinkConnected ------------>");
                if (mScreenControl == null) {
                    return;
                }
                mScreenControl.link(mContext, mNotification, huScreenWidth, huScreenHeight, densityDpi);
            }

            /**
             * 断开连接车机处理，断开投屏连接
             */
            @Override
            public void onLinkDisconnected() {
                LogManager.i("WLScreenManager ---- onLinkDisconnected ------------>");
                if (mScreenControl == null) {
                    return;
                }
                mScreenControl.unLink();
            }

            /**
             * 设置投屏按键处理，传递按键触摸事件
             */
            @Override
            public void onLinkTouch(MotionEvent motionEvent) {
                LogManager.i("WLScreenManager ---- onLinkTouch ------------>");
                if (mScreenControl == null) {
                    return;
                }
                mScreenControl.onTouch(motionEvent);
            }

            /**
             * 接收系统权限
             */
            @Override
            public void activityResult(int requestCode, int resultCode, Intent data) {
                LogManager.i("WLScreenManager ---- activityResult ------------>");
                if (mScreenControl == null) {
                    return;
                }
                mScreenControl.activityResult(requestCode, resultCode, data);
            }
        });
        this.mScreenControl = new ScreenControl();
        this.mScreenControl.registerActivityListener(BaseApplication.getBaseApplication());
    }

    @Override
    public void unloadService() {
        LogManager.i("WLScreenManager ---- unloadService ------------>");
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.unRegisterAdapterListener();
        this.mScreenControl.unregisterActivityListener(BaseApplication.getBaseApplication());
        this.mScreenControl = null;
    }

    /**
     * 设置全投屏权限监听
     */
    public void setLimitScreenListener(LimitScreenListener mLimitScreenListener) {
        LogManager.i("WLScreenManager ---- setLimitScreenListener ------------>");
        if (this.mScreenControl == null) {
            return;
        }
        this.mScreenControl.setLimitScreenListener(mLimitScreenListener);
    }

    /**
     * 开启全投屏
     */
    public void startScreen() {
        LogManager.i("WLScreenManager ---- startScreen ------------>");
        if (this.mScreenControl == null) {
            return;
        }
        this.mScreenControl.startScreen();
    }

    /**
     * 停止全投屏
     */
    public void stopScreen() {
        LogManager.i("WLScreenManager ---- stopScreen ------------>");
        if (this.mScreenControl == null) {
            return;
        }
        this.mScreenControl.stopScreen();
    }

    /**
     * 暂停全投屏
     */
    public void pauseScreen() {
        LogManager.i("WLScreenManager ---- pauseScreen ------------>");
        if (this.mScreenControl == null) {
            return;
        }
        this.mScreenControl.pauseScreen();
    }

    /**
     * 恢复全投屏
     */
    public void resumeScreen() {
        LogManager.i("WLScreenManager ---- resumeScreen ------------>");
        if (this.mScreenControl == null) {
            return;
        }
        this.mScreenControl.resumeScreen();
    }

    /**
     * 更新位置信息
     */
    public void updateLocation(int[] pHome, int[] pBack, int[] hHome) {
        LogManager.i("WLScreenManager ---- updateLocation ------------>");
        if (this.mScreenControl == null) {
            return;
        }
        this.mScreenControl.updateHid(pHome, pBack, hHome);
    }

}
