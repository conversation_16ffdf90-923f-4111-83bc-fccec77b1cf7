package com.autoai.fundrive.platform.protocol.control;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.protocol.WLProtocolConfig;
import com.autoai.fundrive.platform.protocol.bean.AirCondionBean;
import com.autoai.fundrive.platform.protocol.bean.CanDataSIAOILBean;
import com.autoai.fundrive.platform.protocol.bean.CanDataServiceBean;
import com.autoai.fundrive.platform.protocol.bean.EnAndChBean;
import com.autoai.fundrive.platform.protocol.bean.HuAudioBean;
import com.autoai.fundrive.platform.protocol.bean.HuNaviBean;
import com.autoai.fundrive.platform.protocol.bean.HuNormalBean;
import com.autoai.fundrive.platform.protocol.bean.HuRVCBean;
import com.autoai.fundrive.platform.protocol.bean.HuStandByBean;
import com.autoai.fundrive.platform.protocol.bean.MiniNaviBean;
import com.autoai.fundrive.platform.protocol.bean.MuAudioBean;
import com.autoai.fundrive.platform.protocol.listener.HUCommandListener;
import com.autoai.fundrive.platform.protocol.bean.BaseProtocolBean;
import com.autoai.fundrive.platform.protocol.bean.CanDataBaseBean;
import com.autoai.fundrive.platform.protocol.bean.CanDataFZGBean;
import com.autoai.fundrive.platform.protocol.bean.CanDataFZGDSBean;
import com.autoai.fundrive.platform.protocol.bean.DMsgShowBean;
import com.autoai.fundrive.platform.protocol.bean.HardWareBean;
import com.autoai.fundrive.platform.protocol.bean.HuBtBean;
import com.autoai.fundrive.platform.protocol.bean.HuBtPhoneBean;
import com.autoai.fundrive.platform.protocol.bean.HuChannelBean;
import com.autoai.fundrive.platform.protocol.bean.HuLampBean;
import com.autoai.fundrive.platform.protocol.bean.HuPModeBean;
import com.autoai.fundrive.platform.protocol.bean.HuUpdateBean;
import com.autoai.fundrive.platform.protocol.bean.HuVRBean;
import com.autoai.fundrive.platform.protocol.bean.MXMsgSwitchBean;
import com.autoai.fundrive.platform.protocol.bean.PlayWXMsgBean;
import com.autoai.fundrive.platform.protocol.bean.ResponseBean;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 交互协议处理车机端消息处理类，主要定义封装监听接收处理各种消息状态。
 *
 * <AUTHOR>
 */
public class HUProtocolControl {

    private final HashMap<String, ArrayList<HUCommandListener>> mListeners = new HashMap<>();

    /**
     * 添加指定方法的监听用来接收手机端的协议消息广播
     */
    public void addListener(String methodName, HUCommandListener mListener) {
        LogManager.i("addListener ---------> methodName:" + methodName);

        if (mListeners.containsKey(methodName)) {
            ArrayList<HUCommandListener> list = mListeners.get(methodName);
            if (list != null && !list.contains(mListener)) {
                list.add(mListener);
            }
        } else {
            ArrayList<HUCommandListener> list = new ArrayList<>();
            list.add(mListener);
            mListeners.put(methodName, list);
        }
    }


    /**
     * 移除指定方法的监听禁止接收手机端的协议消息广播
     */
    public void removeListener(String methodName, HUCommandListener mListener) {
        LogManager.i("removeListener ---------> methodName:" + methodName);

        if (mListeners.containsKey(methodName)) {
            ArrayList<HUCommandListener> list = mListeners.get(methodName);
            if (list != null) {
                list.remove(mListener);
            }
        }
    }

    /**
     * 清空所有方法的监听禁止接收手机端的协议消息广播
     */
    public void clearListener() {
        LogManager.i("clearListener --------->");
        mListeners.clear();
    }

    /**
     * 获取指定方法的协议数据
     *
     * @param methodName - 接收协议的方法,extData - JSONObject类型数据参数
     */
    public void parseCommand(String methodName, JSONObject extData) {
        LogManager.i("parseCommand ---------> methodName:" + methodName + ",extData:" + extData.toString());
        try {
            BaseProtocolBean mBaseProtocolBean = getCommandBean(methodName);
            if (mBaseProtocolBean != null) {
                mBaseProtocolBean.parse(extData);
                sendCommand(methodName, mBaseProtocolBean);
            }
        } catch (Exception e) {
            LogManager.e("parseCommand exception:" + e.getMessage());
        }
    }

    /**
     * 给注册接收的协议广播回调处理消息
     *
     * @param methodName - 接收协议的方法,mBaseProtocolBean - 回调参数
     */
    private void sendCommand(String methodName, BaseProtocolBean mBaseProtocolBean) {
        LogManager.i("sendCommand ---------> methodName:" + methodName);

        ArrayList<HUCommandListener> list = mListeners.get(methodName);

        if (list != null && !list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                list.get(i).onReceiveCommand(mBaseProtocolBean);
            }
        }
    }

    /**
     * 根据方法名称返回指定的协议实体bean
     *
     * @param methodName - 方法名称
     */
    private BaseProtocolBean getCommandBean(String methodName) {
        LogManager.i("getCommandBean ---------> methodName:" + methodName);
        BaseProtocolBean mBaseProtocolBean = null;
        switch (methodName) {
            case WLProtocolConfig.HU_PROTOCOL_METHOD_UPDATEINFO:
                mBaseProtocolBean = new HuUpdateBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_RESUMEUPDAE:
                mBaseProtocolBean = new HuNormalBean();
                break;
//            case WLProtocolConfig.HU_PROTOCOL_METHOD_STARTTRABS:
//                mBaseProtocolBean = new StartTransBean();
//                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_HUCHANNEL:
                mBaseProtocolBean = new HuChannelBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CONTROLAUDIO:
                mBaseProtocolBean = new MuAudioBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_HARDWAREKEY:
                mBaseProtocolBean = new HardWareBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_MININAVI:
                mBaseProtocolBean = new MiniNaviBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_MMSGHSHOW:
                mBaseProtocolBean = new DMsgShowBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_WXMSGSWITCH:
                mBaseProtocolBean = new MXMsgSwitchBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_PLAYWXMSG:
                mBaseProtocolBean = new PlayWXMsgBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_BTPHONE:
                mBaseProtocolBean = new HuBtPhoneBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_BTSTATE:
                mBaseProtocolBean = new HuBtBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_PMODESTATE:
                mBaseProtocolBean = new HuPModeBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_RVCSTATE:
                mBaseProtocolBean = new HuRVCBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_STANDBYSTATE:
                mBaseProtocolBean = new HuStandByBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_INTERAUDIO:
                mBaseProtocolBean = new HuAudioBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_MOSPHERELAMP:
                mBaseProtocolBean = new HuLampBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_NAVISTATE:
                mBaseProtocolBean = new HuNaviBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_VRSTATE:
                mBaseProtocolBean = new HuVRBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_BASE:
                mBaseProtocolBean = new CanDataBaseBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_SERVICE:
                mBaseProtocolBean = new CanDataServiceBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_SIAOIL:
                mBaseProtocolBean = new CanDataSIAOILBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_FZG:
                mBaseProtocolBean = new CanDataFZGBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_CANDATA_FZGDS:
                mBaseProtocolBean = new CanDataFZGDSBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_ENANDCH:
                mBaseProtocolBean = new EnAndChBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_RESPONSE:
                mBaseProtocolBean = new ResponseBean();
                break;
            case WLProtocolConfig.HU_PROTOCOL_METHOD_AIRCONDION:
                mBaseProtocolBean = new AirCondionBean();
                break;
            default:
                break;
        }
        if (mBaseProtocolBean != null) {
            mBaseProtocolBean.setMethodName(methodName);
        }
        return mBaseProtocolBean;
    }

}
