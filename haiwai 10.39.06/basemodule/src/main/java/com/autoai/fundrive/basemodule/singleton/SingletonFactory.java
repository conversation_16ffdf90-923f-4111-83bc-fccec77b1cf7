package com.autoai.fundrive.basemodule.singleton;

import android.content.Context;

import java.lang.ref.WeakReference;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SingletonFactory {

    private Map<String, String> mServiceMap = null;
    private WeakReference<Context> mContextWeakReference = null;
    /**
     * 保存单例实例
     */
    private final Map<String, Singleton> mSingletonMap = new ConcurrentHashMap<>();

    private volatile static SingletonFactory INSTANCE;

    public static SingletonFactory getInstance() {
        if (INSTANCE == null) {
            synchronized (SingletonFactory.class) {
                if (INSTANCE == null) {
                    INSTANCE = new SingletonFactory();
                }
            }
        }
        return INSTANCE;
    }

    public void initSingletonFactory(Map<String, String> serviceMap, Context aContext) {
        mServiceMap = new HashMap<>(serviceMap);
        mContextWeakReference = new WeakReference<>(aContext);
        mSingletonMap.clear();
    }

    public Singleton getSingleton(String tag) {
        if (mSingletonMap.containsKey(tag)) {
            return mSingletonMap.get(tag);
        } else {
            if (mServiceMap != null && mServiceMap.containsKey(tag)) {
                return addSingleton(tag, mServiceMap.get(tag));
            } else {
                return null;
            }
        }
    }

    public Singleton addSingleton(String tag, String className) {
        Singleton singleton;
        synchronized (mSingletonMap) {
            if (!mSingletonMap.containsKey(tag)) {
                singleton = newInstance(className);
                if (singleton != null) {
                    mSingletonMap.put(tag, singleton);
                    singleton.loadService(mContextWeakReference.get());
                }
            } else {
                singleton = mSingletonMap.get(tag);
            }
        }
        return singleton;
    }

    private Singleton newInstance(String className) {
        Singleton singleton = null;
        if (className != null && className.length() != 0) {
            Class<?> serviceClass;
            try {
                serviceClass = Class.forName(className);
                //实现Singleton接口，需要有一个无参数的构造函数
                singleton = (Singleton) serviceClass.getDeclaredConstructor().newInstance();
            } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException e) {
                e.printStackTrace();
            }
        }
        return singleton;
    }


    public void releaseSingletonFactory() {
        for (Map.Entry<String, Singleton> entry : mSingletonMap.entrySet()) {
            Singleton singleton = entry.getValue();
            try {
                singleton.unloadService();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        mSingletonMap.clear();
        mServiceMap.clear();
        mServiceMap = null;
        mContextWeakReference.clear();
        mContextWeakReference = null;
    }

    public synchronized static void onDestroy() {
        if (INSTANCE != null) {
            INSTANCE.releaseSingletonFactory();
            INSTANCE = null;
        }
    }
}
