package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端 p-mode 模式 向手机端 launcher发的协议。
 */
public class HuPModeBean extends BaseProtocolBean {

    /***
     * pmodeState 类型
     * 1：打开
     * 2：关闭
     */
    private int pmodeState;

    public int getPmodeState() {
        return pmodeState;
    }

    @Override
    public String toString() {
        return "HuPModeBean{" +
                "pmodeState=" + pmodeState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.pmodeState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_PMODE);
    }


}
