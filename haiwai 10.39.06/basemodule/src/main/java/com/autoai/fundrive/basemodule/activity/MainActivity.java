package com.autoai.fundrive.basemodule.activity;

import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.WLConnectManager;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.autoai.welink.platform.WLPlatform;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.fundrive.platform.listener.LinkConnectListener;
import com.autoai.fundrive.platform.listener.LinkPlatformListener;
import com.mapbar.android.control.ViewBaseManager;
import com.mapbar.android.model.PageObject;
import com.autoai.fundrive.basemodule.BaseModuleManager;
import com.autoai.fundrive.basemodule.BuildConfig;
import com.autoai.fundrive.basemodule.Configs;
import com.autoai.fundrive.basemodule.page.PageManager;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.messagebus.MessageCenter;
import com.autoai.fundrive.messagebus.bean.ParamSet;

import java.util.HashMap;
import java.util.Map;

import me.jessyan.autosize.internal.CancelAdapt;

public abstract class MainActivity extends Activity implements ActivityService, CancelAdapt {

    private BaseModuleActivity mBaseModuleActivity = null;
    /**
     * 适配返回键
     * 车机端DialogManager需要暴露给子类，
     */
    protected volatile DialogManager mDialogManager = null;
    /**
     * 适配返回键
     * 需要把互联状态暴漏给子类，用于判断当前时一车机端为准还是手机端为准
     */
    protected boolean isConnectCar = false;
    protected boolean isAOA = true;
    private BroadcastReceiver mBroadcastReceiver = null;

    private PageManager mPageManager = null;
    private IHUAndMUChangePages mHUMUChangePages;

    private final LinkConnectListener connectListener = new LinkConnectListener() {
        @Override
        public boolean onConnected(Dialog dialog) {
            LogManager.d("onConnected start ----------->");
            if (dialog == null) {
                LogManager.d("dialog is null, onConnected start is not support display ----------->");
                return false;
            }
            initDialogContext(dialog.getContext());
            broadcastConnectStatus(true);
            if (mDialogManager == null) {
                mDialogManager = getDialogManager(mBaseModuleActivity.getViewBaseManager());
                mDialogManager.setScreenIndex(mBaseModuleActivity.getViewBaseManager().addViewManager(mDialogManager));
                mDialogManager.initModuleManager();
            }
            runOnUiThread(() -> {
                if (mHUMUChangePages != null) {
                    mHUMUChangePages.showPageOnHU(mDialogManager, dialog, mBaseModuleActivity);
                    mHUMUChangePages.showPageOnMU(mBaseModuleActivity, getUnuseView());
                }
            });
            return true;
        }

        @Override
        public void onDisconnected() {
            LogManager.d("onDisconnected ----------->");
            disconnect();
        }

        @Override
        public void onConnectError(int type) {
            LogManager.d("onError type:" + type);
            onDisconnected();
        }
    };
    private final LinkPlatformListener platformListener = new LinkPlatformListener() {

        @Override
        public void onLinkDisconnected() {
            LogManager.d("onLinkDisconnected ------------->");
            MainActivity.this.isAOA = true;
        }

        @Override
        public void onLinkConnected(String vehicleType, boolean isAOA) {
            LogManager.d("onLinkConnected ------------->");
            MainActivity.this.isAOA = isAOA;
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            int cap = WLPlatform.WL_CAP_DISPLAY | WLPlatform.WL_CAP_SOUND | WLPlatform.WL_CAP_MUSIC | WLPlatform.WL_CAP_MICROPHONE | WLPlatform.WL_CAP_TBTINFO | WLPlatform.WL_CAP_BLUETOOTHPHONE;
            String key = platformManager.getConnectKey(getPackageName(), cap);
            WLConnectManager connectManager = (WLConnectManager) SingletonFactory.getInstance().getSingleton(WLConnectManager.NAME);
            connectManager.startConnect(key);
        }
    };

    /**
     * 预留给各个业务launcher在车机互联成功做车机端环境的初始化操作，类似车机端Application中的一些初始化实现
     * 当前用到的：换肤、多语言适配
     */
    protected void initDialogContext(Context context) {

    }

    protected void disconnect() {
        broadcastConnectStatus(false);
        runOnUiThread(() -> {
            if (mHUMUChangePages != null) {
                mHUMUChangePages.clearMuPage(mBaseModuleActivity);
                mHUMUChangePages.clearHUPage(mDialogManager, mBaseModuleActivity);
            }
            if (mDialogManager != null) {
                mDialogManager.onDestroy();
                mDialogManager = null;
            }
        });
    }

    private void broadcastConnectStatus(boolean status) {
        isConnectCar = status;
        //
        Map<String, Boolean> param = new HashMap<>(4);
        param.put("isConnectCar", status);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().postSticky(paramSet, Configs.COMMON_BROADCAST);
    }

    private void startLink(Intent intent) {
        ThreadPoolUtil.getInstance().getDefaultExecutor().execute(new Runnable() {
            @Override
            public void run() {
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                if (intent.hasExtra("UsbAccessory")) {
                    UsbAccessory usbAccessory = intent.getParcelableExtra("UsbAccessory");
                    Intent usbIntent = new Intent();
                    usbIntent.setAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
                    usbIntent.putExtra("accessory", usbAccessory);
                    platformManager.startLink(usbIntent);
                } else {
                    platformManager.startLink(intent);
                }
            }
        });
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        mBaseModuleActivity = new BaseModuleActivity(this);
        mBaseModuleActivity.setScreenIndex(0);
        mBaseModuleActivity.setRootViewId(getRootViewId());
        mPageManager = getPageManager(mBaseModuleActivity);

        BaseModuleManager moduleManager = getModuleManager();
        moduleManager.setScreenIndex(mBaseModuleActivity.getScreenIndex());
        mBaseModuleActivity.setModuleManager(moduleManager);
        mBaseModuleActivity.setAnimatorResId(getAnimatorResId());
        mBaseModuleActivity.init();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        LogManager.d("onNewIntent");
        setIntent(intent);
        if (!BuildConfig.IS_THIRDAPP) {
            if (getIntent() != null && UsbManager.ACTION_USB_ACCESSORY_ATTACHED.equals(getIntent().getAction())) {
                startLink(getIntent());
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(getMainViewLayout());
//        XPermissionManager.getInstance(this).setActivity(this);

        initPageView();

//        requestMustPermissions();
        mBaseModuleActivity.loadService();
        initPlatform();
    }

    private void initPlatform() {
        LogManager.d("initPlatform -------------->");
        //非第三方app
        if (!BuildConfig.IS_THIRDAPP) {
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            WLConnectManager connectManager = (WLConnectManager) SingletonFactory.getInstance().getSingleton(WLConnectManager.NAME);
            platformManager.init(this, platformListener);
            connectManager.init(this, connectListener);
            if (getIntent() != null && getIntent().getAction() != null && getIntent().getAction().equals(UsbManager.ACTION_USB_ACCESSORY_ATTACHED)) {
                startLink(getIntent());
            }
        } else {
            //第三方app
            WLConnectManager connectManager = (WLConnectManager) SingletonFactory.getInstance().getSingleton(WLConnectManager.NAME);
            connectManager.init(this, connectListener);
        }
    }

//    /**
//     * grantResults对应于申请的结果，这里的数组对应于申请时的第二个权限字符串数组。
//     * 如果你同时申请两个权限，那么grantResults的length就为2，分别记录你两个权限的申请结果
//     */
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        XPermissionManager.getInstance(this).onRequestPermissionsResult(requestCode, permissions, grantResults, this);
//    }

    @Override
    protected void onResume() {
        super.onResume();

//        // 用于用户跳转到设置页面后再次做权限检查
//        if (XPermissionManager.getInstance(this).needRequestMustPermission()) {
//            requestMustPermissions();
//        }
        Map<String, Boolean> param = new HashMap<>(4);
        param.put("isAppLifeForeground", true);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, Configs.COMMON_BROADCAST);
    }

    @Override
    protected void onPause() {
        super.onPause();
        Map<String, Boolean> param = new HashMap<>(4);
        param.put("isAppLifeForeground", false);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, Configs.COMMON_BROADCAST);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LogManager.d("onDestroy---->");
        if (!BuildConfig.IS_THIRDAPP) {
            if (mBroadcastReceiver != null) {
                unregisterReceiver(mBroadcastReceiver);
                mBroadcastReceiver = null;
            }
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.stopLink();
        } else {
            WLConnectManager connectManager = (WLConnectManager) SingletonFactory.getInstance().getSingleton(WLConnectManager.NAME);
            connectManager.startDisConnect();
        }
        if (mDialogManager != null) {
            mDialogManager.onDestroy();
            mDialogManager = null;
        }

        mPageManager = null;
        mHUMUChangePages = null;
        mBaseModuleActivity.onDestroy();
        mBaseModuleActivity = null;
    }

    @Override
    public void loadModuleService() {
        BaseModuleManager moduleManager = getModuleManager();
        String[] preArray = moduleManager.preInitModule();
        if (preArray != null && preArray.length > 0) {
            for (String s : preArray) {
                initModule(s);
            }
        }
    }

    protected void initModule(String aModuleName) {
        BaseModuleManager moduleManager = getModuleManager();
        if (moduleManager != null) {
            moduleManager.loadModuleService(BaseModuleManager.TYPE_ACTIVITY, aModuleName);
        }
    }

    @Override
    public AppActivity getActivity() {
        LogManager.d(mBaseModuleActivity.toString());
        return mBaseModuleActivity;
    }

    @Override
    public PageObject createPage(int index) {
        LogManager.d("createPage");
        return mPageManager.createPage(index);
    }

    public abstract PageObject getMainPage();

    public abstract PageManager getPageManager(AppActivity aAppActivity);

    public abstract DialogManager getDialogManager(ViewBaseManager aViewBaseManager);

    public abstract BaseModuleManager getModuleManager();

    public abstract int getRootViewId();

    public abstract int getAnimatorResId();

    public abstract int getMainViewLayout();

    public abstract View getUnuseView();

    private void initPageView() {
        ViewGroup view = this.findViewById(getAnimatorResId());
        PageObject mainPage = getMainPage();
        view.addView(mainPage.getView());
        mBaseModuleActivity.getViewInterface().pushPage(mainPage, -1, null);
        mBaseModuleActivity.addMessageManager(mBaseModuleActivity);

        mHUMUChangePages = new ClearHUPages();

    }

//    /**
//     * 申请必要权限
//     */
//    private void requestMustPermissions() {
//        // 防止onResume中多次调用
//        String[] permissions = getMustRequestPermissions();
//        XPermissionManager.getInstance(this).requestPermissions(XPermissionManager.PERMISSION_MUST_REQ, permissions,
//                new XPermissionManager.OnPermissionListener() {
//                    @Override
//                    public void onPermissionGranted() {
//                        mBaseModuleActivity.loadService();
//                        initPlatform();
//                    }
//
//                    @Override
//                    public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied, String permissionNames) {
//                        // 被拒绝 重置申请
//                        int[] index = {1, 1};
//                        PageChangeUtils.showDialog("app", index, 1);
//                    }
//
//                    @Override
//                    public void onShowTips(int requestCode, boolean needShowTips) {
//                        if (needShowTips) {
//                            int[] index = {2, 2};
//                            PageChangeUtils.showDialog("app", index, 2);
//                        } else {
//                            PageChangeUtils.closeDialog(-1, 2);
//                        }
//                    }
//                });
//        XPermissionManager.getInstance(this).setNeedRequestMustPermission(false);
//    }
//
//    /**
//     * 获取必要权限数组
//     */
//    protected abstract String[] getMustRequestPermissions();

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.activityResult(requestCode, resultCode, data);
    }
}
