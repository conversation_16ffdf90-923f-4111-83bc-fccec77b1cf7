package com.autoai.fundrive.basemodule.broadcast;

import android.content.Intent;
import android.os.Handler;
import android.os.HandlerThread;

import java.util.List;

/**
 * 广播分发
 * 主要处理发送广播通知
 *
 * <AUTHOR>
 */
public final class BroadcastDispatcher {

    private HandlerThread mSendThread;
    private Handler mSenderHandler;

    BroadcastDispatcher() {
    }

    void dispatchBroadcast(Intent intent, List<IBroadcastNotification> listeners) {
        if (mSendThread == null) {
            mSendThread = new HandlerThread("welink_broadcast_dispatcher") {
                @Override
                protected void onLooperPrepared() {
                    mSenderHandler = new Handler(getLooper());
                    synchronized (BroadcastDispatcher.this) {
                        BroadcastDispatcher.this.notifyAll();
                    }
                }
            };
            mSendThread.start();
        }
        if (mSenderHandler == null) {
            synchronized (BroadcastDispatcher.this) {
                try {
                    BroadcastDispatcher.this.wait();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
//        LogManager.d("start send to listener[" + listeners.size() + "]");
        mSenderHandler.post(new SendRunnable(intent, listeners));
    }

    private static class SendRunnable implements Runnable {

        //广播内容
        Intent intent;
        //welink对外接口
        List<IBroadcastNotification> listeners;

        SendRunnable(Intent intent, List<IBroadcastNotification> listeners) {
            this.intent = intent;
            this.listeners = listeners;
        }

        @Override
        public void run() {
            for (IBroadcastNotification notify : listeners) {
                if (notify != null) {
                    notify.broadcastNotify(intent);
                }
//                LogManager.d("SendRunnable [" + notify + "]" + intent);
            }
        }
    }

    void shutdown() {
        if (mSenderHandler != null) {
            mSendThread.quit();
            mSendThread = null;
            mSenderHandler = null;
        }
//        LogManager.d("dispatcher send shutdown");
    }
}
