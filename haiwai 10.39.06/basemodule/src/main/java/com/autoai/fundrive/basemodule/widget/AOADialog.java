package com.autoai.fundrive.basemodule.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.XmlResourceParser;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.autoai.fundrive.commontool.StringUtil;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.commontool.CommonUtil;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;

/**
 *
 */

public class AOADialog {
    private final FrameLayout mFrameLayout;
    private View mBgView;
    private View mDialogView;
    private boolean isCancelableOnTouchOutSide;
    private boolean isCancelable = true;

    private final AppActivity mAppActivity;

    private boolean isShowing;// 对话框是否在显示

    public AOADialog(AppActivity aAppActivity, int rootViewId) {
        mAppActivity = aAppActivity;
        mFrameLayout = (FrameLayout) aAppActivity.findView(rootViewId);
        if (mFrameLayout == null) {
            return;
        }
        initBgView(mAppActivity.getContext());
    }

    /**
     * 初始化背景view
     *
     * @param context Context
     */
    private void initBgView(Context context) {
        mBgView = new LinearLayout(context);
        mBgView.setBackgroundColor(Color.parseColor("#CC000000"));
        mBgView.setOnClickListener(v -> {
            if (!isCancelableOnTouchOutSide) {
                return;
            }
            cancel();
        });
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        mBgView.setLayoutParams(params);
    }

    /**
     * 设置背景颜色
     *
     * @param color 颜色值
     */
    public void setBackgroundColor(int color) {
        if (mBgView != null) {
            mBgView.setBackgroundColor(color);
        }
    }

    /**
     * 取消对话框
     */
    private void cancel() {
        if (mOnCancelListener != null) {
            mOnCancelListener.onCancel();
        }
        dismiss();
    }

    /**
     * 取消对话框
     */
    public void dismiss() {
        if (mFrameLayout == null || isViewNull()) {
            return;
        }
        isShowing = false;
        mFrameLayout.removeView(mBgView);
        mFrameLayout.removeView(mDialogView);
    }

    private long mCurrentTime;

    /**
     * 显示对话框
     */
    public void show() {
        if (isShowing || mFrameLayout == null || isViewNull()) {
            return;
        }
        isShowing = true;
        mFrameLayout.addView(mBgView);
        mFrameLayout.addView(mDialogView);
        mCurrentTime = System.currentTimeMillis();
    }

    /**
     * 点击对话框外是否取消对话框
     *
     * @param b true：取消；false：不取消
     */
    public void setCanceledOnTouchOutside(boolean b) {
        isCancelableOnTouchOutSide = b;
    }

    /**
     * 点击返回按键是否取消对话框
     *
     * @param b true：取消；false；不取消
     */
    public void setCancelable(boolean b) {
        isCancelable = b;
    }

    /**
     * 设置对话框的View
     *
     * @param v 要设置的View
     */
    public void setContentView(View v) {
        if (isShowing || v == null) {
            return;
        }
        mDialogView = v;
        initListener();
    }

    /**
     * 设置对话框的View
     *
     * @param id 要设置的View的id
     */
    public void setContentView(int id) {
        if (isShowing || id < 0) {
            return;
        }
        View view = LayoutInflater.from(mAppActivity.getContext()).inflate(id, null);

        String width = "";
        String height = "";
        int w;
        int h;

        XmlResourceParser parser = mAppActivity.getContext().getResources().getLayout(id);
        try {
            int event = parser.getEventType();
            while (event != XmlPullParser.END_DOCUMENT) {
                if (event == XmlPullParser.START_TAG) {
                    String name = parser.getName();
                    if (!TextUtils.isEmpty(name)) {
                        for (int i = 0; i < parser.getAttributeCount(); i++) {
                            String vName = parser.getAttributeName(i);
                            String vValue = parser.getAttributeValue(i);
                            if ("layout_width".equals(vName)) {
                                width = vValue;
                            }
                            if ("layout_height".equals(vName)) {
                                height = vValue;
                            }
                        }
                        break;
                    }
                }
                event = parser.next();
            }
        } catch (XmlPullParserException | IOException e) {
            LogManager.e("", e);
        }

        switch (width) {
            case "match_parent":
            case "-1":
                w = FrameLayout.LayoutParams.MATCH_PARENT;
                break;
            case "wrap_content":
            case "-2":
                w = FrameLayout.LayoutParams.WRAP_CONTENT;
                break;
            default:
                Float tmp = StringUtil.getFloatFromString(width);
                if (tmp == null) {
                    w = FrameLayout.LayoutParams.WRAP_CONTENT;
                } else {
                    w = CommonUtil.dip2px(mAppActivity.getContext(), tmp);
                }
                break;
        }

        switch (height) {
            case "match_parent":
            case "-1":
                h = FrameLayout.LayoutParams.MATCH_PARENT;
                break;
            case "wrap_content":
            case "-2":
                h = FrameLayout.LayoutParams.WRAP_CONTENT;
                break;
            default:
                Float tmp = StringUtil.getFloatFromString(height);
                if (tmp == null) {
                    h = FrameLayout.LayoutParams.WRAP_CONTENT;
                } else {
                    h = CommonUtil.dip2px(mAppActivity.getContext(), tmp);
                }
                break;
        }

        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(w, h);

        params.gravity = Gravity.CENTER;
        view.setLayoutParams(params);
        setContentView(view);
    }

    /**
     * 监听返回按键
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initListener() {
        mDialogView.setOnTouchListener((v, event) -> true);

        mDialogView.setOnKeyListener((v, keyCode, event) -> {
            onKeyDown(v, keyCode, event);
            if (System.currentTimeMillis() - mCurrentTime < 200) {
                return false;
            }
            if (!isCancelable) {
                return true;
            } else {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    cancel();
                    return true;
                }
                return false;
            }
        });
        mDialogView.setFocusable(true);
        mDialogView.setFocusableInTouchMode(true);
        mDialogView.requestFocus();
    }

    public void onKeyDown(View v, int keyCode, KeyEvent event) {

    }

    private boolean isViewNull() {
        return (mBgView == null || mDialogView == null);
    }

    /**
     * 设置显示View的布局参数
     *
     * @param params 布局参数
     */
    public void setLayoutParams(ViewGroup.LayoutParams params) {
        if (mDialogView != null) {
            mDialogView.setLayoutParams(params);
        }
    }

    /**
     * 获取View的布局参数
     *
     * @return 布局参数
     */
    public ViewGroup.LayoutParams getParams() {
        return mDialogView.getLayoutParams();
    }

    /**
     * 获取View布局中的控件
     *
     * @param id 控件的id
     * @return 获取的View
     */
    public View findViewById(int id) {
        if (id < 0) {
            return null;
        }
        return mDialogView.findViewById(id);
    }

    /**
     * 点击取消按钮的监听
     */
    public interface OnCancelListener {
        void onCancel();
    }

    private OnCancelListener mOnCancelListener;

    /**
     * 设置取消按钮的监听器
     *
     * @param listener 监听器
     */
    public void setOnCancelListener(OnCancelListener listener) {
        mOnCancelListener = listener;
    }

    /**
     * 对话框是否在显示
     *
     * @return true:显示；false:没显示
     */
    public boolean isShowing() {
        return isShowing;
    }
}
