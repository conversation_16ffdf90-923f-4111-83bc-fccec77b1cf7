package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是用来实现车机端通知手机d端进行中英文切换。
 */
public class EnAndChBean extends BaseProtocolBean {

    /**
     * 中英文 int 1:中文 2：英文
     */
    private int controlState;

    public int getControlState() {
        return controlState;
    }

    @Override
    public String toString() {
        return "EnAndChBean{" +
                "controlState=" + controlState +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.controlState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_CONTROLSTATE);
    }


}
