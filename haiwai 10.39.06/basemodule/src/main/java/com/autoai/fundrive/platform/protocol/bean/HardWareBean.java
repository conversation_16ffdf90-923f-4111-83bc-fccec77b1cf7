package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端点击硬按键向手车机端launcher发送的协议
 */
public class HardWareBean extends BaseProtocolBean {

    /**
     * 1：短按
     * 2：长按
     * 3：双击
     */
    private int keyState;

    public int getKeyState() {
        return keyState;
    }

    @Override
    public String toString() {
        return "HardWareBean{" +
                "keyState=" + keyState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.keyState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_KEY);
    }


}
