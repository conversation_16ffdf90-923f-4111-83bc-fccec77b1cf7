package com.autoai.fundrive.platform.protocol.bean;

public class ZoneArr {
    /**
     *  温区序号 类型 int
     */
    private int zoneNum;
    /**
     *   温区 类型 int 0：不可调 1：当前关闭，可以调 2： 当前打开，可以调
     */
    private int zoneState;
    /**
     *  温度单位 类型 int 1：摄氏度 2：华氏度
     */
    private int airTemperatureType;
    /**
     *  温度 类型 doule
     */
    private double temperatureArea;
    /**
     *   风量档位 类型 int
     */
    private int airVolume;
    /**
     *  风量模式 类型 int 1：automatical 2：manual
     */
    private int climateMode;
    /**
     *  automatical模式下风量类型 类型 int 1：soft 2：medium 3：intensive
     */
    private int autoMode;

    public int getZoneNum() {
        return zoneNum;
    }

    public void setZoneNum(int zoneNum) {
        this.zoneNum = zoneNum;
    }

    public int getZoneState() {
        return zoneState;
    }

    public void setZoneState(int zoneState) {
        this.zoneState = zoneState;
    }

    public int getAirTemperatureType() {
        return airTemperatureType;
    }

    public void setAirTemperatureType(int airTemperatureType) {
        this.airTemperatureType = airTemperatureType;
    }

    public double getTemperatureArea() {
        return temperatureArea;
    }

    public void setTemperatureArea(double temperatureArea) {
        this.temperatureArea = temperatureArea;
    }

    public int getAirVolume() {
        return airVolume;
    }

    public void setAirVolume(int airVolume) {
        this.airVolume = airVolume;
    }

    public int getClimateMode() {
        return climateMode;
    }

    public void setClimateMode(int climateMode) {
        this.climateMode = climateMode;
    }

    public int getAutoMode() {
        return autoMode;
    }

    public void setAutoMode(int autoMode) {
        this.autoMode = autoMode;
    }

    @Override
    public String toString() {
        return "ZoneArr{ zoneNum=" + zoneNum +
                ", zoneState=" + zoneState +
                ", airTemperatureType=" + airTemperatureType +
                ", temperatureArea=" + temperatureArea +
                ", airVolume=" + airVolume +
                ", climateMode=" + climateMode +
                ", autoMode=" + autoMode +
                '}';
    }
}
