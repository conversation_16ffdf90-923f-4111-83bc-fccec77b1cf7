package com.autoai.fundrive.basemodule.activity;

///**
// * Permission 工具Activity
// * <AUTHOR>
// */
//public class PermissionActivity extends Activity {
//    private static final String PARAM_PERMISSION = "param_permission";
//    private static final String PARAM_REQUEST_CODE = "param_request_code";
//
//    private static XPermissionManager.OnPermissionListener permissionListener;
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_permission);
//
//        String[] mPermissions = getIntent().getStringArrayExtra(PARAM_PERMISSION);
//        int mRequestCode = getIntent().getIntExtra(PARAM_REQUEST_CODE, -1);
//
//        if (mPermissions == null || mRequestCode < 0 || permissionListener == null) {
//            this.finish();
//            return;
//        }
//
//        //检查是否已授权
//        if (XPermissionManager.getInstance(this).checkPermissions(mPermissions)) {
//            permissionListener.onPermissionGranted();
//            finish();
//            return;
//        }
//        ActivityCompat.requestPermissions(this, mPermissions, mRequestCode);
//    }
//
//    /**
//     * 请求权限
//     */
//    public static void requestPermission(Context context, String[] permissions, int requestCode, XPermissionManager.OnPermissionListener iPermission) {
//        permissionListener = iPermission;
//        Intent intent = new Intent(context, PermissionActivity.class);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
//        Bundle bundle = new Bundle();
//        bundle.putStringArray(PARAM_PERMISSION, permissions);
//        bundle.putInt(PARAM_REQUEST_CODE, requestCode);
//        intent.putExtras(bundle);
//        context.startActivity(intent);
//        if (context instanceof Activity) {
//            ((Activity) context).overridePendingTransition(0, 0);
//        }
//    }
//
//    /**
//     * grantResults对应于申请的结果，这里的数组对应于申请时的第二个权限字符串数组。
//     * 如果你同时申请两个权限，那么grantResults的length就为2，分别记录你两个权限的申请结果
//     */
//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        XPermissionManager.getInstance(this).onRequestPermissionsResult(requestCode,permissions,grantResults,this);
//        finish();
//    }
//
//    @Override
//    public void finish() {
//        super.finish();
//        overridePendingTransition(0,0);
//    }
//}
