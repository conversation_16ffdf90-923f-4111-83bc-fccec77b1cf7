package com.autoai.fundrive.basemodule;

import android.content.Context;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.activity.ActivityService;
import com.autoai.fundrive.basemodule.activity.AppActivity;
import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.WLConnectManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.link.threadpool.ThreadPoolUtil;
import com.mapbar.android.model.PageObject;
import com.autoai.fundrive.basemodule.broadcast.BroadcastService;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;


/**
 * 各个设备上运行模块的初始化
 *
 * <AUTHOR>
 */
public abstract class BaseModuleManager {
    /**
     * 匹配初始化Activity中的各个模块信息
     */
    public static final int TYPE_ACTIVITY = 1;
    /**
     * 匹配初始化Dialog中的各个模块信息
     */
    public static final int TYPE_DIALOG = 2;

    @IntDef({TYPE_ACTIVITY, TYPE_DIALOG})
    @Retention(RetentionPolicy.SOURCE)
    private @interface ModuleType {
    }

    /**
     * 保存各个模块 Activity的ActivityService实例
     */
    private final Map<String, ActivityService> mInstanceMap = new HashMap<>();
    /**
     * 保存各个模块 Dialog的ActivityService实例
     */
    private final Map<String, ActivityService> mHuInstanceMap = new HashMap<>();

    /**
     * 用于配置显示屏幕的类型，0表示显示的是手机，不为0表示车机
     */
    private int mScreenIndex = 0;

    /**
     * 配置屏幕类型
     */
    public void setScreenIndex(int aScreenIndex) {
        this.mScreenIndex = aScreenIndex;
    }

    /**
     * WelinkService初始化入口
     */
    public void initServiceMap(Context aContext) {
        SingletonFactory.getInstance().initSingletonFactory(initWelinkServiceList(), aContext);
    }

    /**
     * WelinkService销毁
     */
    public void onDestroy() {
        unloadModuleService(TYPE_ACTIVITY);
        unloadModuleService(TYPE_DIALOG);
        SingletonFactory.onDestroy();
    }

    /**
     * 调用unloadModuleService方法，释放指定Map内ActivityService拥有的资源，并清除该指定Map
     */
    private void UninitActivityService(Map<String, ActivityService> aActivityServiceMap) {
        for (ActivityService activityService : aActivityServiceMap.values()) {
            activityService.unloadModuleService();
        }
        aActivityServiceMap.clear();
    }

    /**
     * 初始化各模块入口
     *
     * @param type 如果是Activity中使用，调用type={@link #TYPE_ACTIVITY};
     *             如果是Dialog中使用，调用type={@link #TYPE_DIALOG}
     */
    public void initModuleMaps(@ModuleType int type, AppActivity appActivity) {
//        if (type == TYPE_ACTIVITY) {
//            doInitModuleMaps(mInstanceMap, type,appActivity);
//        } else if (type == TYPE_DIALOG) {
//            doInitModuleMaps(mHuInstanceMap, type,appActivity);
//        }
    }

    /**
     * 根据{@link BaseModuleManager#initModuleList}或者{@link BaseModuleManager#initHuModuleList()}配置的各模块对应的map中的key值
     * 以及index获取Page
     *
     * @param moduleKey 配置的各模块对应的map中的key值
     * @param index     index
     */
    public PageObject createPage(String moduleKey, int index) {
        PageObject pageObject = createSinglePage(moduleKey, index);
        if (pageObject == null) {
            //没有找到相应的模块或者页面
            pageObject = createSinglePage("error", 99404);
        }
        return pageObject;
    }

    /**
     * 在指定模块内创建index对应的弹出对话框对象
     *
     * @param aModuleName 模块名
     * @param index       弹出框在模块内对应的index
     * @return 创建的对话框对象
     */
    public BasePopDialog createPopDialog(String aModuleName, int index) throws Exception {
        return createSinglePopDialog(aModuleName, index);
    }

    /**
     * 根据{@link BaseModuleManager#initModuleList}或者{@link BaseModuleManager#initHuModuleList()}配置的各模块对应的map中的key值
     * 以及index获取Page
     *
     * @param moduleKey 配置的各模块对应的map中的key值
     * @param index     index
     */
    private PageObject createSinglePage(String moduleKey, int index) {
        PageObject pageObject = null;
        ActivityService activityService = getActivityService(moduleKey);
        LogManager.i("createPage activityService:" + activityService);
        if (activityService != null) {
            pageObject = activityService.createPage(index);
            if (pageObject != null) {
                pageObject.setModuleName(moduleKey);
            }
        }
        return pageObject;
    }

    /**
     * 在指定模块内创建index对应的弹出对话框对象
     *
     * @param moduleKey 模块名
     * @param index     弹出框在模块内对应的index
     * @return 创建的对话框对象
     */
    private BasePopDialog createSinglePopDialog(String moduleKey, int index) {
        BasePopDialog popDialogObject = null;
        ActivityService activityService = getActivityService(moduleKey);
        if (activityService != null) {
            popDialogObject = activityService.createPopDialog(index);
        }
        return popDialogObject;
    }

    /**
     * 循环调用map中配置的信息，依次初始化各模块入口类
     *
     * @param activityServiceMap 根据配置信息获取并保存实体对象
     * @param type               信息类型
     */
    @SuppressWarnings("unchecked")
    private void doInitModuleMaps(Map<String, ActivityService> activityServiceMap, @ModuleType int type, AppActivity appActivity) {
        Map<String, String> moduleStringMap = getInitList(type);
        if (moduleStringMap == null) {
            throw new IllegalArgumentException("the map that configured for all modules cannot be null");
        }
        activityServiceMap.clear();
        for (Map.Entry<String, String> entry : moduleStringMap.entrySet()) {
            String clazzName = entry.getValue();
            String key = entry.getKey();
            loadModuleService(activityServiceMap, key, clazzName);
        }
    }

    /**
     * 通过模块名加载模块，并保存到指定队列
     *
     * @param aType      队列类型
     * @param aModuleKey 模块名
     * @return 模块对应的实例对象
     */
    public ActivityService loadModuleService(@ModuleType int aType, String aModuleKey) {
        ActivityService activityService = null;
        Map<String, String> mapList = getInitList(aType);
        if (mapList != null && mapList.containsKey(aModuleKey)) {
            activityService = loadModuleService(aType, aModuleKey, mapList.get(aModuleKey));
        }
        return activityService;
    }

    /**
     * 通过模块和类名加载模块，并保存到指定队列
     *
     * @param aType      队列类型
     * @param aModuleKey 模块名
     * @param aClassname 模块的初始化类名
     * @return 模块对应的实例对象
     */
    private ActivityService loadModuleService(@ModuleType int aType, String aModuleKey, String aClassname) {
        ActivityService activityService = null;
        Map<String, ActivityService> activityServiceMap = null;
        if (aType == TYPE_ACTIVITY) {
            activityServiceMap = mInstanceMap;
        } else if (aType == TYPE_DIALOG) {
            activityServiceMap = mHuInstanceMap;
        }
        if (activityServiceMap != null) {
            activityService = loadModuleService(activityServiceMap, aModuleKey, aClassname);
        }
        return activityService;
    }

    /**
     * 通过模块和类名加载模块，并保存到指定队列
     *
     * @param aActivityServiceMap 队列类型
     * @param aModuleKey          模块名
     * @param aClassname          模块的初始化类名
     * @return 模块对应的实例对象
     */
    private ActivityService loadModuleService(Map<String, ActivityService> aActivityServiceMap, String aModuleKey, String aClassname) {
        ActivityService activityService;
        if (!aActivityServiceMap.containsKey(aModuleKey)) {
            activityService = initActivityService(aClassname);
            if (activityService != null) {
                aActivityServiceMap.put(aModuleKey, activityService);
                //加载各个模块手机端资源,并且保证在主线程加载
                ThreadPoolUtil.getInstance().getDefaultExecutor().execute(() -> {
//                    Looper.prepare();
                    aActivityServiceMap.get(aModuleKey).loadModuleService();
                });
            }
        } else {
            activityService = aActivityServiceMap.get(aModuleKey);
        }

        return activityService;
    }

    /**
     * 对指定类名进行实例初始化
     *
     * @param aClassname 需要实例化的类名
     * @return 初始化完成的实例对象
     */
    private ActivityService initActivityService(String aClassname) {
        ActivityService activityService = null;
        //循环放在try catch外部，防止一个反射错误，导致剩余的其他业务模块也加载不起来
        if (aClassname != null && aClassname.length() != 0) {
            Class<?> clazz = null;
            try {
                clazz = Class.forName(aClassname);
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
            if (clazz != null) {
                //优先调用构造方法，此种方法不成功，再去调用getInstance()方法，此处兼容旧版
                try {
                    Method method = clazz.getMethod("getInstance");
                    activityService = (ActivityService) method.invoke(null);
                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    e.printStackTrace();
                }
            }
        }
        return activityService;
    }

    /**
     * 获取指定模块的实例对象，根据当前的使用屏幕
     *
     * @param moduleKey 模块名
     * @return 模块对应的实例对象
     */
    private ActivityService getActivityService(String moduleKey) {
        ActivityService activityService;
        int type;
        if (mScreenIndex == 0) {
            activityService = mInstanceMap.get(moduleKey);
            type = TYPE_ACTIVITY;
        } else {
            activityService = mHuInstanceMap.get(moduleKey);
            type = TYPE_DIALOG;
        }
        if (activityService == null) {
            activityService = loadModuleService(type, moduleKey);
        }
        return activityService;
    }

    /**
     * 根据类型获取指定的初始化配置队列
     *
     * @param type 配置队列对应的类型
     * @return 指定类型对应的配置队列
     */
    private Map<String, String> getInitList(@ModuleType int type) {
        Map<String, String> moduleStringMap = null;
        if (type == TYPE_ACTIVITY) {
            moduleStringMap = initModuleList();
        } else if (type == TYPE_DIALOG) {
            moduleStringMap = initHuModuleList();
        }
        return moduleStringMap;
    }

    /**
     * 删除类型对应已经加载的初始化队列
     *
     * @param type 需要删除的队列类型
     */
    public void unloadModuleService(@ModuleType int type) {
        if (type == TYPE_ACTIVITY) {
            UninitActivityService(mInstanceMap);
        } else if (type == TYPE_DIALOG) {
            UninitActivityService(mHuInstanceMap);
        }
    }

    /**
     * 对指定初始化队列内的对象进行loadModuleService调用
     *
     * @param activityServiceMap 指定的初始化队列
     */
    private void loadModuleService(Map<String, ActivityService> activityServiceMap) {
        for (ActivityService activityService : activityServiceMap.values()) {
            //加载各个模块手机端资源,并且保证在主线程加载
            activityService.loadModuleService();
        }
    }

    /**
     * 配置Module
     *
     * @return 配置的包含各个模块入口Activity class name信息的map
     */
    @NonNull
    protected abstract Map<String, String> initModuleList();

    /**
     * 配置HuModule
     *
     * @return 配置的包含各个模块入口Dialog class name信息的map
     */
    @NonNull
    protected abstract Map<String, String> initHuModuleList();

    /**
     * 返回需要预加载的模块名字符串数组
     */
    public String[] preInitModule() {
        return null;
    }

    /**
     * 配置WelinkService
     *
     * @return 配置的包含WelinkService class name信息的map
     */
    @NonNull
    protected Map<String, String> initWelinkServiceList() {
        Map<String, String> aServiceList = new HashMap<>();
        aServiceList.put(BroadcastService.NAME, BroadcastService.class.getName());
        aServiceList.put(WLPlatformManager.NAME, WLPlatformManager.class.getName());
        aServiceList.put(WLConnectManager.NAME, WLConnectManager.class.getName());
        return aServiceList;
    }
}