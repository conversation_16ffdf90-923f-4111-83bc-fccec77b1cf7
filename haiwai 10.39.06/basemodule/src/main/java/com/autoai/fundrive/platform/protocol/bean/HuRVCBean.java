package com.autoai.fundrive.platform.protocol.bean;

import com.autoai.fundrive.platform.protocol.WLProtocolConfig;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 此协议主要是车机端 rvc 模式 向手机端 launcher发的协议。
 */
public class HuRVCBean extends BaseProtocolBean {
    /**
     * rvcState 类型
     * 1：打开
     * 2：关闭
     */
    private int rvcState;

    public int getRvcState() {
        return rvcState;
    }

    @Override
    public String toString() {
        return "HuRVCBean{" +
                "rvcState=" + rvcState +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }

    @Override
    public void parse(JSONObject extData) throws JSONException {
        this.rvcState = extData.getInt(WLProtocolConfig.HU_PROTOCOL_METHOD_FIELD_RVC);
    }


}
