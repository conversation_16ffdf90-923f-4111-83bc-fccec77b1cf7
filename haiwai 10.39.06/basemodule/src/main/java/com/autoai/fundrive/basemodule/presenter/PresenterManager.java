package com.autoai.fundrive.basemodule.presenter;

import android.content.Context;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;

public class PresenterManager {

	private static final PresenterManager instance = new PresenterManager();

	public static PresenterManager getInstance() {
		return instance;
	}

	private final HashMap<String, IBasePresenter<?>> presenterMaps = new HashMap<>();

	public synchronized <P> P getPresenter(String className, Context context) {
		IBasePresenter<?> presenter = presenterMaps.get(className);
		if (presenter != null) {
			return (P) presenter;
		}

		try {
			Class<?> clazz = Class.forName(className);
			Constructor<?> constructor = clazz.getDeclaredConstructor(Context.class);
			constructor.setAccessible(true);
			presenter = (BasePresenter<?>) constructor.newInstance(context);
			presenterMaps.put(className, presenter);
			return (P) presenter;
		} catch (ClassNotFoundException | InstantiationException | IllegalAccessException
				| SecurityException | IllegalArgumentException | NoSuchMethodException
				| InvocationTargetException e) {
			e.printStackTrace();
		}
		return null;
	}

	public void removePresenter(String className) {
		presenterMaps.remove(className);
	}

}
