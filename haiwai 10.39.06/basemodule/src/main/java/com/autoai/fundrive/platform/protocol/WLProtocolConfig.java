package com.autoai.fundrive.platform.protocol;

import androidx.annotation.Keep;

/**
 * 交互协议常量管理类，主要定义代码使用的常量字段。
 *
 * <AUTHOR>
 */
@Keep
public class WLProtocolConfig {

    private WLProtocolConfig() {}

    public static final int WL_PROTOCOL_VALUE_VERSION = 0;
    public static final String WL_PROTOCOL_VALUE_MODULENAME = "WeLink";
    public static final String WL_PROTOCOL_VALUE_PLATFORM = "android|ios|ce";

    public static final String WL_PROTOCOL_FIELD_MODULENAME = "moduleName";
    public static final String WL_PROTOCOL_FIELD_VERSION = "version";
    public static final String WL_PROTOCOL_FIELD_PLATFORM = "platform";
    public static final String WL_PROTOCOL_FIELD_COMMAND = "command";
    public static final String WL_PROTOCOL_FIELD_METHOD = "method";
    public static final String WL_PROTOCOL_FIELD_EXTDATA = "extData";

    /************************************ 手机发给车机命令 ****************************************/

    /**
     * 手机消息协议
     */
    public static final String MU_PROTOCOL_METHOD_MSGRECEIVE = "onMsgReceive";
    public static final String MU_PROTOCOL_METHOD_WXRESPONSE = "onWeChatResponse";
    public static final String MU_PROTOCOL_METHOD_BLUETOOTH = "onBluetoothState";
    public static final String MU_PROTOCOL_METHOD_MUCHANNEL = "onMuChannel";
    public static final String MU_PROTOCOL_METHOD_EXITWELINK = "onExitWelink";
    public static final String MU_PROTOCOL_METHOD_SCREENCHANGE = "onScreenChange";
    public static final String MU_PROTOCOL_METHOD_AUTOLINK = "setAutoLinkState";
    public static final String MU_PROTOCOL_METHOD_CALLNUMBER = "onCallByNumber";
    public static final String MU_PROTOCOL_METHOD_HOMEKEY = "onHomeKeyState";
    public static final String MU_PROTOCOL_METHOD_STOPPLAY = "onStopPcmPlay";
    public static final String MU_PROTOCOL_METHOD_MUTE = "onMuteState";
    public static final String MU_PROTOCOL_METHOD_STARTMEDIA = "onStartCarMedia";
    public static final String MU_PROTOCOL_METHOD_MEDIASTATE = "onMediaState";
    public static final String MU_PROTOCOL_METHOD_MOBILENAVI = "onMobileNaviData";

    /**
     * 2020-7-28 秦荣昌要求添加
     */
    public static final String MU_PROTOCOL_METHOD_CONTROL_VOLUME = "onHuControlVolume";
    public static final String MU_PROTOCOL_METHOD_MUTE_VOLUME = "onHuMuteVolume";
    public static final String MU_PROTOCOL_METHOD_AIRCON_SWITCH = "onHuAircoSwitch";
    public static final String MU_PROTOCOL_METHOD_AIRCON_TEMP = "onHuAircoTemp";
    public static final String MU_PROTOCOL_METHOD_AIRCON_BLOWINGGRATE = "onHuAircoBlowingrate";

    /**
     * 2020-12-16 于伟海外版要求添加
     */
    public static final String MU_PROTOCOL_METHOD_OPEN_HUBTSETTING = "onOpenHuBTSetting";

    /**
     * 手机消息协议字段
     */

    public static final String MU_PROTOCOL_METHOD_FIELD_DOWNKEY = "pressDownKey";
    public static final String MU_PROTOCOL_METHOD_FIELD_EXIT = "SettingExitWelink";
    public static final String MU_PROTOCOL_METHOD_FIELD_WIDTH = "width";
    public static final String MU_PROTOCOL_METHOD_FIELD_HEIGHT = "height";
    public static final String MU_PROTOCOL_METHOD_FIELD_STATE = "state";
    public static final String MU_PROTOCOL_METHOD_FIELD_PHONE = "phoneNumber";
    public static final String MU_PROTOCOL_METHOD_FIELD_PCMINFO = "pcmInfo";
    public static final String MU_PROTOCOL_METHOD_FIELD_MUTE = "muteState";
    public static final String MU_PROTOCOL_METHOD_FIELD_TYPE = "type";
    public static final String MU_PROTOCOL_METHOD_FIELD_CODE = "code";
    public static final String MU_PROTOCOL_METHOD_FIELD_BLUETOOTH = "bluetoothState";
    public static final String MU_PROTOCOL_METHOD_FIELD_MEDIA = "mediaState";

    public static final String MU_PROTOCOL_METHOD_FIELD_MAP = "miniMapState";
    public static final String MU_PROTOCOL_METHOD_FIELD_POIX = "centerPoix";
    public static final String MU_PROTOCOL_METHOD_FIELD_POIY = "centerPoiy";
    public static final String MU_PROTOCOL_METHOD_FIELD_SHOW_WIDTH = "showwidth";
    public static final String MU_PROTOCOL_METHOD_FIELD_SHOW_HIGH = "showhigh";

    public static final String MU_PROTOCOL_METHOD_FIELD_CONTROL_VOLUME = "controlVolume";
    public static final String MU_PROTOCOL_METHOD_FIELD_REQUEST_TYPE = "requestType";
    public static final String MU_PROTOCOL_METHOD_FIELD_MUTE_VOLUME = "muteVolume";

    public static final String MU_PROTOCOL_METHOD_FIELD_AIRCO_SWITCH = "aircoSwitch";
    public static final String MU_PROTOCOL_METHOD_FIELD_ZONEARR = "zoneArr";
    public static final String MU_PROTOCOL_METHOD_FIELD_AIR_TEMP_TYPE = "airTemperatureType";
    public static final String MU_PROTOCOL_METHOD_FIELD_AIRCO_TEMP= "aircoTemp";
    public static final String MU_PROTOCOL_METHOD_FIELD_CLIMATE_MODE = "climateMode";
    public static final String MU_PROTOCOL_METHOD_FIELD_AIRCO_BLOWINGRATE = "aircoBlowingrate";

    /************************************ 车机发给手机命令 ****************************************/

    /**
     * 车机消息协议
     */
    @Keep
    public static final String HU_PROTOCOL_METHOD_HUCHANNEL = "onHuChannel";
    @Keep
    public static final String HU_PROTOCOL_METHOD_HARDWAREKEY = "onHuHardwareKey";
    @Keep
    public static final String HU_PROTOCOL_METHOD_MININAVI = "onHuMiniNaviChange";
    @Keep
    public static final String HU_PROTOCOL_METHOD_MMSGHSHOW = "onHuDMessageHShow";
    @Keep
    public static final String HU_PROTOCOL_METHOD_BTPHONE = "onHuBTPhone";
    @Keep
    public static final String HU_PROTOCOL_METHOD_BTSTATE = "onHuBTState";
    @Keep
    public static final String HU_PROTOCOL_METHOD_PMODESTATE = "onHuPmodeState";
    @Keep
    public static final String HU_PROTOCOL_METHOD_RVCSTATE = "onHuRVCState";
    @Keep
    public static final String HU_PROTOCOL_METHOD_STANDBYSTATE = "onHuStandbyState";
    @Keep
    public static final String HU_PROTOCOL_METHOD_INTERAUDIO = "onHuInterruptAudioState";
    @Keep
    public static final String HU_PROTOCOL_METHOD_MOSPHERELAMP = "onHuAtmosphereLamp";
    @Keep
    public static final String HU_PROTOCOL_METHOD_NAVISTATE = "onHuNaviState";
    @Keep
    public static final String HU_PROTOCOL_METHOD_VRSTATE = "onHuVRState";

    /**
     * 暂时无用车机消息协议
     */
    @Keep
    public static final String HU_PROTOCOL_METHOD_UPDATEINFO = "onHuUpdateInfo";
    @Keep
    public static final String HU_PROTOCOL_METHOD_RESUMEUPDAE = "onResumeHuUpdate";
    @Keep
    public static final String HU_PROTOCOL_METHOD_STARTTRABS = "onStartTransData2";
    @Keep
    public static final String HU_PROTOCOL_METHOD_CONTROLAUDIO = "onControlMobileAudio";
    @Keep
    public static final String HU_PROTOCOL_METHOD_WXMSGSWITCH = "onHuWechatMessageSwitch";
    @Keep
    public static final String HU_PROTOCOL_METHOD_PLAYWXMSG = "onPlayWechatMsg";

    /**
     * CanData数据协议
     */
    @Keep
    public static final String HU_PROTOCOL_METHOD_CANDATA_BASE = "onStartTransData";
    @Keep
    public static final String HU_PROTOCOL_METHOD_CANDATA_SERVICE = "onSIAServiceData";
    @Keep
    public static final String HU_PROTOCOL_METHOD_CANDATA_SIAOIL = "onSIAOilInspectionData";
    @Keep
    public static final String HU_PROTOCOL_METHOD_CANDATA_FZG = "onFZGStausWarning";
    @Keep
    public static final String HU_PROTOCOL_METHOD_CANDATA_FZGDS = "onListWarningIDsDynValues";

    /**
     * 2020-7-22 于伟要求添加
     */
    @Keep
    public static final String HU_PROTOCOL_METHOD_ENANDCH = "onControlEnAndCh";

    /**
     * 2020-7-28 秦荣昌要求添加
     */
    @Keep
    public static final String HU_PROTOCOL_METHOD_RESPONSE = "onHuResponseCallback";
    @Keep
    public static final String HU_PROTOCOL_METHOD_AIRCONDION = "onHuAircondionData";



    /**
     * 车机消息协议字段
     */

    public static final String HU_PROTOCOL_METHOD_FIELD_VERSION = "version";
    public static final String HU_PROTOCOL_METHOD_FIELD_PACKAGE = "packageName";
    public static final String HU_PROTOCOL_METHOD_FIELD_MODEL = "model";
    public static final String HU_PROTOCOL_METHOD_FIELD_TRANS = "isStartTrans";
    public static final String HU_PROTOCOL_METHOD_FIELD_DOWNKEY = "pressDownKey";
    public static final String HU_PROTOCOL_METHOD_FIELD_AUDIO = "audioState";
    public static final String HU_PROTOCOL_METHOD_FIELD_KEY = "keyState";
    public static final String HU_PROTOCOL_METHOD_FIELD_NAVI = "naviState";
    public static final String HU_PROTOCOL_METHOD_FIELD_PAGE = "pageState";
    public static final String HU_PROTOCOL_METHOD_FIELD_HADDRESS = "haddress";
    public static final String HU_PROTOCOL_METHOD_FIELD_WXMSGSWITCH = "wechatMessageSwitch";
    public static final String HU_PROTOCOL_METHOD_FIELD_WXMSGID = "wechatMsgId";
    public static final String HU_PROTOCOL_METHOD_FIELD_BTPHONE = "btPhoneState";
    public static final String HU_PROTOCOL_METHOD_FIELD_BTSTATE = "btState";
    public static final String HU_PROTOCOL_METHOD_FIELD_BTMACNAME = "btMacName";
    public static final String HU_PROTOCOL_METHOD_FIELD_PMODE = "pmodeState";
    public static final String HU_PROTOCOL_METHOD_FIELD_RVC = "rvcState";
    public static final String HU_PROTOCOL_METHOD_FIELD_STANDBY = "standbyState";
    public static final String HU_PROTOCOL_METHOD_FIELD_AUDIOTYPE = "audioType";
    public static final String HU_PROTOCOL_METHOD_FIELD_VEHICLE = "vehicle_info";
    public static final String HU_PROTOCOL_METHOD_FIELD_PARTNO = "part_no";
    public static final String HU_PROTOCOL_METHOD_FIELD_COLORTYPE = "color_type";
    public static final String HU_PROTOCOL_METHOD_FIELD_COLORRGB = "color_rgb_value";
    public static final String HU_PROTOCOL_METHOD_FIELD_COLORR = "color_r";
    public static final String HU_PROTOCOL_METHOD_FIELD_COLORG = "color_g";
    public static final String HU_PROTOCOL_METHOD_FIELD_COLORB = "color_b";
    public static final String HU_PROTOCOL_METHOD_FIELD_COLORSELECT= "color_select_value";
    public static final String HU_PROTOCOL_METHOD_FIELD_VR = "vrState";
    public static final String HU_PROTOCOL_METHOD_FIELD_CONTROLSTATE = "controlState";

}
