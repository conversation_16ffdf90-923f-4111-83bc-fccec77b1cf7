package com.autoai.fundrive.basemodule.activity;

import com.mapbar.android.model.PageObject;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;

public interface ActivityService {
    /**
     * 初始化各个相关模块
     */
    void loadModuleService();

    /**
     * 获取主APP的Activity或者module在debug时自己的Activity
     */
    AppActivity getActivity();

    /**
     * 创建指定页面
     */
    PageObject createPage(int index);

    /**
     * 创建指定的弹出对话框
     */
    BasePopDialog createPopDialog(int index);

    /**
     * 释放各个模块
     */
    void unloadModuleService();

}