package com.autoai.fundrive.messagebus.bean;

import java.util.List;

public class ParamForResult extends Param {
    private ForResultCallback mForResult;

    public ParamForResult(List<String> mParamList, ForResultCallback mForResult) {
        super(mParamList);
        this.mForResult = mForResult;
    }

    public ForResultCallback getmForResult() {
        return mForResult;
    }

    public interface ForResultCallback<T> {
        void onResult(String tag, T t);
    }
}
