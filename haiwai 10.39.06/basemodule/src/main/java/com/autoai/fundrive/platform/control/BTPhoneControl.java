package com.autoai.fundrive.platform.control;

import com.autoai.fundrive.commontool.LogManager;
import com.autoai.welink.auto.WLBluetoothPhone;

/**
 * 三投三方BTPhone功能接口，主要提供BTPhone实现的基本功能接口。
 *
 * <AUTHOR>
 */
public class BTPhoneControl {

    private final WLBluetoothPhone mWLBluetoothPhone;

    public BTPhoneControl(WLBluetoothPhone mWLBluetoothPhone) {
        this.mWLBluetoothPhone = mWLBluetoothPhone;
    }

    /**
     * 拨打车机蓝牙电话
     */
    public void dialBTPhone(String number, WLBluetoothPhone.Callback callback) {
        LogManager.i("dialBTPhone ------->");
        if (mWLBluetoothPhone == null) {
            return;
        }
        mWLBluetoothPhone.dial(number, callback);
    }

    /**
     * 挂断车机蓝牙电话
     */
    public void hangUpBTPhone() {
        LogManager.i("hangUpBTPhone ------->");
        if (mWLBluetoothPhone == null) {
            return;
        }
        mWLBluetoothPhone.hangUp();
    }

    /**
     * 判断车机是否支持蓝牙电话
     */
    public boolean isSupportBTPhone() {
        LogManager.i("isSupportBTPhone ------->");
        return mWLBluetoothPhone != null;
    }
}
