package com.autoai.fundrive.messagebus.bean;

import java.util.Map;

public class ParamSetForResult extends ParamSet {
    private ParamForResult.ForResultCallback mForResult;

    public ParamSetForResult(Map<String,Object> mParamList, ParamForResult.ForResultCallback mForResult) {
        super(mParamList);
        this.mForResult = mForResult;
    }

    public ParamForResult.ForResultCallback getmForResult() {
        return mForResult;
    }

    public interface ForResultCallback<T> {
        void onResult(String param, T t);
    }
}
