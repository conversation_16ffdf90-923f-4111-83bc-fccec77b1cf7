package com.autoai.fundrive.basemodule.broadcast;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;

import com.autoai.fundrive.basemodule.singleton.SingletonFactory;

/**
 * 广播入口
 *
 * <AUTHOR>
 */
public final class BaseBroadcast extends BroadcastReceiver {

    private PhoneStateListenerInner mPhoneStateListenerInner;

    public BaseBroadcast() {
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        BroadcastService broadcastService = (BroadcastService) SingletonFactory.getInstance().getSingleton(BroadcastService.NAME);
//        LogManager.d("broadcastService[" + broadcastService + "] onReceive action" + action);
        if (TelephonyManager.ACTION_PHONE_STATE_CHANGED.equals(action)) {
            //电话广播事件
            TelephonyManager tm = (TelephonyManager) context
                    .getSystemService(Service.TELEPHONY_SERVICE);
            // 设置一个监听器
            if (mPhoneStateListenerInner == null) {
                mPhoneStateListenerInner = new PhoneStateListenerInner(broadcastService);
            }
            tm.listen(mPhoneStateListenerInner, PhoneStateListener.LISTEN_CALL_STATE);
        } else {
            //其他广播事件
            if (broadcastService != null) {
                broadcastService.actionBroadcastWithIntent(intent);
            }
        }
    }

    static class PhoneStateListenerInner extends PhoneStateListener {

        BroadcastService broadcastService;

        private PhoneStateListenerInner(BroadcastService broadcastService) {
            this.broadcastService = broadcastService;
        }

        @Override
        public void onCallStateChanged(int state, String incomingNumber) {
            super.onCallStateChanged(state, incomingNumber);
            if (broadcastService != null) {
                Intent intent = new Intent();
                intent.putExtra("state", state);
                intent.putExtra("incomingNumber", incomingNumber);
                intent.setAction(TelephonyManager.ACTION_PHONE_STATE_CHANGED);
                broadcastService.actionBroadcastWithIntent(intent);
            }
        }
    }
}
