package com.autoai.fundrive.hidscreen;

import android.app.Activity;
import android.app.Application;
import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.Display;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.autoai.fundrive.basemodule.singleton.SingletonFactory;
import com.autoai.fundrive.commontool.LogManager;
import com.autoai.fundrive.platform.protocol.WLProtocolManager;
import com.autoai.fundrive.platform.WLPlatformManager;
import com.autoai.welink.screen.WLScreen;
import com.autoai.welink.screen.WLScreenListener;
import com.autoai.fundrive.messagebus.MessageCenter;
import com.autoai.fundrive.messagebus.bean.ParamSet;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class ScreenControl {

    private WLScreen mWLScreen;
    private Activity mContext;
    private Notification mNotification;
    private LimitScreenListener mLimitScreenListener;

    private int huScreenWidth;
    private int huScreenHeight;
    private int densityDpi;

    private int mScreenWidth;
    private int mScreenHeight;
    private int phonePointX = -1;
    private int phonePointY = -1;

    private int mDensityDpi;

    private int mOffsetX;
    private int mOffsetY;

    private float base = 1;

    private int mHidAngle;
    //触屏事件区域定义
    private final int[] mRect = new int[4];
    private int[] pHome = new int[4];
    private int[] pBack = new int[4];
    private int[] hHome = new int[4];

    private boolean isStartScreen = false;
    private boolean isLinkFront = false;
    private final Application.ActivityLifecycleCallbacks activityLifecycleCallbacks = new Application.ActivityLifecycleCallbacks() {

        @Override
        public void onActivityStarted(@NonNull Activity activity) {
            LogManager.i("Activity ---- onActivityStarted --->activity:" + activity.getClass().getSimpleName() + ",isStartScreen:" + isStartScreen);
            isLinkFront = true;
            if (isStartScreen) {
                WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
                platformManager.getDisEnableExternal();
                platformManager.setExternalCastScreen(false);

                mRect[0] = 0;
                mRect[1] = 0;
                mRect[2] = 0;
                mRect[3] = 0;
                updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);

                Map<String, Boolean> param = new HashMap<>(4);
                param.put("dialog_masking", false);
                ParamSet<Boolean> paramSet = new ParamSet<>(param);
                MessageCenter.getDefault().post(paramSet, "dialog_masking");

                LogManager.i("ScreenControl ---- onActivityStarted ------------>onLinkStarted");
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onLinkStarted();
                }
            }
        }

        @Override
        public void onActivityStopped(Activity activity) {
            LogManager.i("Activity ---- onActivityStopped ------------>activity:" + activity.getClass().getSimpleName() + ",isStartScreen:" + isStartScreen);
            isLinkFront = false;
            if (isStartScreen) {
                calculateScreen();
                setScreenSurface();

                mRect[0] = mOffsetX;
                mRect[1] = mOffsetY;
                mRect[2] = mScreenWidth;
                mRect[3] = mScreenHeight;
                updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);

                Map<String, Boolean> param = new HashMap<>(4);
                param.put("dialog_masking", true);
                ParamSet<Boolean> paramSet = new ParamSet<>(param);
                MessageCenter.getDefault().post(paramSet, "dialog_masking");

                LogManager.i("ScreenControl ---- onActivityStopped ------------>onLinkStopped");
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onLinkStopped();
                }
            }
        }

        @Override
        public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {

        }

        @Override
        public void onActivityResumed(@NonNull Activity activity) {

        }

        @Override
        public void onActivityPaused(@NonNull Activity activity) {

        }

        @Override
        public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

        }

        @Override
        public void onActivityDestroyed(@NonNull Activity activity) {

        }
    };

    public void setLimitScreenListener(LimitScreenListener listener) {
        mLimitScreenListener = listener;
    }

    public void registerActivityListener(Application mApplication) {
        mApplication.registerActivityLifecycleCallbacks(activityLifecycleCallbacks);
    }

    public void unregisterActivityListener(Application mApplication) {
        mApplication.unregisterActivityLifecycleCallbacks(activityLifecycleCallbacks);
    }

    public void link(Activity context, Notification notification, int huScreenWidth, int huScreenHeight, int densityDpi) {
        LogManager.i("ScreenControl ---- link ------------>");
        mContext = context;
        mNotification = notification;
        this.huScreenWidth = huScreenWidth;
        this.huScreenHeight = huScreenHeight;
        this.densityDpi = densityDpi;

        LogManager.i("LimitScreenListener ---- onLink ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onLink();
        }
    }

    public void unLink() {
        LogManager.i("ScreenControl ---- unLink ------------>");
        stopScreen();
        if (mWLScreen != null) {
            mWLScreen.release(mContext);
            mWLScreen = null;
        }
        LogManager.i("LimitScreenListener ---- onUnLink ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onUnLink();
        }
    }

    public void onTouch(MotionEvent motionEvent) {
        LogManager.i("ScreenControl ---- onTouch ------------>");
        if (mWLScreen == null) {
            return;
        }
        int screenLeft = mOffsetX;
        int screenTop = mOffsetY;
        motionEvent.offsetLocation(-screenLeft, -screenTop);
        float x = motionEvent.getX();
        float y = motionEvent.getY();
        x = x / base;
        y = y / base;
        motionEvent.setLocation(x, y);
        MotionEvent me = MotionEvent.obtain(motionEvent.getDownTime(), motionEvent.getEventTime(), motionEvent.getAction(),
                motionEvent.getX(), motionEvent.getY(), motionEvent.getPressure(), motionEvent.getSize(), motionEvent.getMetaState(),
                motionEvent.getXPrecision(), motionEvent.getYPrecision(), motionEvent.getDeviceId(),
                motionEvent.getEdgeFlags());
        me.setSource(motionEvent.getSource());
//        mWLScreen.touch(me);
    }

    public void startScreen() {
        LogManager.i("ScreenControl ---- startScreen ------------>");
        if (mWLScreen == null) {
            mWLScreen = new WLScreen(mContext, new WLScreenListener(){

                @Override
                public void currentAppPackageName(String packageName) {

                }

                @Override
                public void onRotation(int rotation) {
                    LogManager.i("ScreenControl ---- onRotation ------------>rotation:" + rotation + ",isLinkFront:" + isLinkFront);

                    switch (rotation) {
                        case Surface.ROTATION_0:
                            mHidAngle = 0;
                            break;
                        case Surface.ROTATION_90:
                            mHidAngle = 90;
                            break;
                        case Surface.ROTATION_180:
                            mHidAngle = 180;
                            break;
                        case Surface.ROTATION_270:
                            mHidAngle = 270;
                            break;
                    }

                    calculateScreen();
                    if (!isStartScreen) {
                        return;
                    }
                    if (!isLinkFront) {
                        setScreenSurface();
                        mRect[0] = mOffsetX;
                        mRect[1] = mOffsetY;
                        mRect[2] = mScreenWidth;
                        mRect[3] = mScreenHeight;
                        updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);
                    } else {
                        mRect[0] = 0;
                        mRect[1] = 0;
                        mRect[2] = 0;
                        mRect[3] = 0;
                        updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);
                    }
                }
            }, false, mNotification);
        }
        calculateScreen();
        updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);
        LogManager.i("ScreenControl ---- startScreen ------------>mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi);
        mWLScreen.start(mScreenWidth, mScreenHeight, mDensityDpi);
        LogManager.i("LimitScreenListener ---- onRequestFullScreenLimit ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onRequestFullScreenLimit();
        }
    }

    public void stopScreen() {
        LogManager.i("ScreenControl ---- stopScreen ------------>");
        updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);
        if (mWLScreen != null) {
            WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
            platformManager.getDisEnableExternal();
            platformManager.setExternalCastScreen(false);
            isStartScreen = false;
            mWLScreen.stop();
            LogManager.i("LimitScreenListener ---- onStopScreen ------------>");
            if (mLimitScreenListener != null) {
                mLimitScreenListener.onStopScreen();
            }
        }
    }

    public void pauseScreen() {
        LogManager.i("ScreenControl ---- pauseScreen ------------>");
        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        platformManager.getDisEnableExternal();
        platformManager.setExternalCastScreen(false);

        isStartScreen = false;

        mRect[0] = 0;
        mRect[1] = 0;
        mRect[2] = 0;
        mRect[3] = 0;
        updateStates(false, mHidAngle, mRect, pHome, pBack, hHome);

        Map<String, Boolean> param = new HashMap<>(4);
        param.put("dialog_masking", false);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, "dialog_masking");

        LogManager.i("LimitScreenListener ---- onPauseScreen ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onPauseScreen();
        }
    }

    public void resumeScreen() {
        LogManager.i("ScreenControl ---- resumeScreen ------------>");
        calculateScreen();
        setScreenSurface();

        isStartScreen = true;

        mRect[0] = mOffsetX;
        mRect[1] = mOffsetY;
        mRect[2] = mScreenWidth;
        mRect[3] = mScreenHeight;
        updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);

        Map<String, Boolean> param = new HashMap<>(4);
        param.put("dialog_masking", true);
        ParamSet<Boolean> paramSet = new ParamSet<>(param);
        MessageCenter.getDefault().post(paramSet, "dialog_masking");

        LogManager.i("LimitScreenListener ---- onResumeScreen ------------>");
        if (mLimitScreenListener != null) {
            mLimitScreenListener.onResumeScreen();
        }
    }

    public void updateHid(int[] pHome, int[] pBack, int[] hHome) {
        LogManager.i("ScreenControl ---- updateHid ------------>");
        mRect[0] = mOffsetX;
        mRect[1] = mOffsetY;
        mRect[2] = mScreenWidth;
        mRect[3] = mScreenHeight;
        this.pHome = pHome;
        this.pBack = pBack;
        this.hHome = hHome;

        updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);
    }

    public void activityResult(int requestCode, int resultCode, Intent data) {
        LogManager.i("ScreenControl ---- activityResult ------------>requestCode:" + requestCode + ",resultCode:" + resultCode);
        if (mWLScreen == null) {
            return;
        }

        if (requestCode == WLScreen.SCREEN_CAPTURE_REQUEST_CODE) {
            mWLScreen.onActivityResult(requestCode, resultCode, data);
            if (resultCode == Activity.RESULT_OK) {
                isStartScreen = true;
                if (!isLinkFront) {
                    calculateScreen();

                    mRect[0] = mOffsetX;
                    mRect[1] = mOffsetY;
                    mRect[2] = mScreenWidth;
                    mRect[3] = mScreenHeight;

                    Map<String, Boolean> param = new HashMap<>(4);
                    param.put("dialog_masking", true);
                    ParamSet<Boolean> paramSet = new ParamSet<>(param);
                    MessageCenter.getDefault().post(paramSet, "dialog_masking");
                } else {
                    mRect[0] = 0;
                    mRect[1] = 0;
                    mRect[2] = 0;
                    mRect[3] = 0;
                }
                updateStates(true, mHidAngle, mRect, pHome, pBack, hHome);
                LogManager.i("LimitScreenListener ---- onStartScreen ------------>");
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onStartScreen();
                }
            } else {
                LogManager.i("ScreenControl ---- activityResult ------------>onStopScreen");
                if (mLimitScreenListener != null) {
                    mLimitScreenListener.onStopScreen();
                }
            }
        }
    }


    private void setScreenSurface() {
        LogManager.i("ScreenControl ---- setScreenSurface ------------>");
        if (mWLScreen == null) {
            return;
        }
        Rect rect = new Rect(mOffsetX, mOffsetY, mScreenWidth + mOffsetX, mScreenHeight + mOffsetY);

        WLPlatformManager platformManager = (WLPlatformManager) SingletonFactory.getInstance().getSingleton(WLPlatformManager.NAME);
        //依据范围获取投屏显示容器
        Surface mSurface = platformManager.getEnableExternal(rect);
        mWLScreen.setSurface(mSurface, mScreenWidth, mScreenHeight, mDensityDpi);
        LogManager.i("ScreenControl ---- setScreenSurface ------------>mScreenWidth:" + mScreenWidth + ",mScreenHeight:" + mScreenHeight + ",mDensityDpi:" + mDensityDpi);
        platformManager.setExternalCastScreen(true);
    }

    private void calculateScreen() {
        LogManager.i("ScreenControl ---- calculateScreen ------------>");
        if (phonePointX == -1 || phonePointY == -1) {
            WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
            Display display = wm.getDefaultDisplay();
            Point mPoint = new Point();
            display.getRealSize(mPoint);
            phonePointX = mPoint.x;
            phonePointY = mPoint.y;
        }
        //
        if (mHidAngle == 90 || mHidAngle == 270) {
            mScreenWidth = phonePointY;
            mScreenHeight = phonePointX;
        } else {
            mScreenWidth = phonePointX;
            mScreenHeight = phonePointY;
        }


        float wBase = (float) (huScreenWidth * 1.0 / mScreenWidth);
        float hBase = (float) (huScreenHeight * 1.0 / mScreenHeight);

        base = Math.min(wBase, hBase);

        mScreenWidth = (int) (mScreenWidth * base);
        mScreenHeight = (int) (mScreenHeight * base);

        mOffsetX = (huScreenWidth - mScreenWidth) / 2;
        mOffsetY = (huScreenHeight - mScreenHeight) / 2;
        mDensityDpi = densityDpi;
    }

    private void updateStates(boolean isEnabled, int angle, int[] mRect, int[] pHome, int[] pBack, int[] hHome) {
        LogManager.i("ScreenControl ---- updateStates ------------>");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("moduleName", "WeLink");
            jsonObject.put("version", 0);
            jsonObject.put("platform", "android");

            JSONObject command = new JSONObject();
            command.put("method", "hidTouch");

            JSONObject extData = new JSONObject();
            extData.put("enabled", isEnabled);
            extData.put("angle", angle);
            extData.put("x", mRect[0]);
            extData.put("y", mRect[1]);
            extData.put("w", mRect[2]);
            extData.put("h", mRect[3]);
            command.put("extData", extData);

            JSONObject phoneHome = new JSONObject();
            phoneHome.put("x", pHome[0]);
            phoneHome.put("y", pHome[1]);
            phoneHome.put("w", pHome[2]);
            phoneHome.put("h", pHome[3]);
            command.put("phoneHome", phoneHome);

            JSONObject phoneBack = new JSONObject();
            phoneBack.put("x", pBack[0]);
            phoneBack.put("y", pBack[1]);
            phoneBack.put("w", pBack[2]);
            phoneBack.put("h", pBack[3]);
            command.put("phoneBack", phoneBack);

            JSONObject huHome = new JSONObject();
            huHome.put("x", hHome[0]);
            huHome.put("y", hHome[1]);
            huHome.put("w", hHome[2]);
            huHome.put("h", hHome[3]);
            command.put("huHome", huHome);

            jsonObject.put("command", command);

        } catch (JSONException e) {
            LogManager.e("updateStates exception:" + e.getMessage());
        }
        String hidMessage = jsonObject.toString();
        LogManager.i("ScreenControl ---- updateStates ------------>hidMessage：" + hidMessage);
        WLProtocolManager.getInstance().sendCommondMessage(hidMessage);
    }


}
