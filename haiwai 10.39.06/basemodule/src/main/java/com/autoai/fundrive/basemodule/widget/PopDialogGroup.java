package com.autoai.fundrive.basemodule.widget;

import android.view.ViewGroup;

import com.autoai.fundrive.basemodule.activity.AppActivity;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */
public class PopDialogGroup {
    private final AppActivity mAppActivity;
    protected AOADialog popDialog = null;
    private int showDialogFlagId;
    private final ArrayList<PopDialogIdToListener> dialogArr = new ArrayList<>();
    private int updateDialogPtr = 0;

    public PopDialogGroup(AppActivity aAppActivity) {
        this.mAppActivity = aAppActivity;
    }

    public void AddDialogId(int dialogId, int flagId, DialogType dialogType, PopDialogGroupListener listener) {
        if (!isInArray(flagId)) {
            PopDialogIdToListener dialog = new PopDialogIdToListener();
            dialog.dialogId = dialogId;
            dialog.flagId = flagId;
            dialog.listener = listener;
            dialog.dialogType = dialogType;
            // 将升级相关的对话框放在队列的最后
            if (dialogArr.size() > 0 && (dialogType == DialogType.EUPDATE)) {
                dialogArr.add(dialog);
                updateDialogPtr++;
            } else {
                if (updateDialogPtr > 0) {
                    int offset = dialogArr.size() - updateDialogPtr;
                    if (offset == 0) {
                        dialogArr.add(1, dialog);
                    } else {
                        dialogArr.add(offset, dialog);
                    }
                } else {
                    dialogArr.add(dialog);
                }
            }
        }
        refreshDialog(flagId);
    }

    public void insertDialogId(int index, int dialogId, int flagId, DialogType dialogType, PopDialogGroupListener listener) {
        PopDialogIdToListener dialog = null;
        int containIndex = getDialogIndex(flagId);
        if (containIndex == -1) {
            dialog = new PopDialogIdToListener();
            dialog.dialogId = dialogId;
            dialog.flagId = flagId;
            dialog.listener = listener;
            dialog.dialogType = dialogType;
        } else {
            dialog = dialogArr.remove(containIndex);
        }

        if (dialogArr.size() == 0) {
            dialogArr.add(dialog);
        } else if (index <= dialogArr.size() - 1) {
            dialogArr.add(index, dialog);
        } else {
            dialogArr.add(dialog);
        }

        if (popDialog == null) {
            refreshDialog(flagId);
        } else if (!popDialog.isShowing()) {
            refreshDialog(flagId);
        } else if (index == 0) {
            popDialog.dismiss();
            refreshDialog(flagId);
        }
    }

    public void removeDialogID(int flagId) {
        if (dialogArr.size() < 1) {
            return;
        }
        int index = getDialogIndex(flagId);
        if (index != -1) {
            PopDialogIdToListener dialog = dialogArr.remove(index);
            dialogArr.trimToSize();
            dialog = null;
        }

        if (popDialog != null && !popDialog.isShowing()) {
            refreshTopDialog();
        }
    }

    public void refreshTopDialog() {
        if (dialogArr.size() >= 1) {
            refreshDialog(dialogArr.get(0).flagId);
        }
    }

    public void refreshDialog(int flagId) {
        if (isShowing()) {
            return;
        }
        if (dialogArr.size() > 0) {
            int index = getDialogIndex(flagId);
            if (index != -1) {
                PopDialogIdToListener dialog = dialogArr.get(index);
                createDialog(dialog.dialogId, dialog.flagId, dialog.dialogType, dialog.listener);
            }
        }
    }

    private void createDialog(int dialogId, int flagId, DialogType dialogType, PopDialogGroupListener listener) {
        if (listener == null) {
            return;
        }
        if (popDialog == null) {
            popDialog = new AOADialog(mAppActivity, listener.getRootViewId());
        }
        listener.setAppActivity(mAppActivity);
        if (!isInArray(flagId)) {
            AddDialogId(dialogId, flagId, dialogType, listener);
        }
        listener.initDialog(dialogId, flagId, dialogType, popDialog);
        showDialogFlagId = flagId;
    }

    public boolean isShowing() {
        return popDialog != null && popDialog.isShowing();
    }

    public void dismiss() {
        if (popDialog != null) {
            popDialog.dismiss();
        }
        if (dialogArr.size() >= 1) {
            removeDialogID(dialogArr.get(0).flagId);
        }
    }

    public void dismiss(int flagId) {
        if (popDialog != null && flagId == showDialogFlagId) {
            popDialog.dismiss();
        }
        if (dialogArr.size() >= 1) {
            removeDialogID(flagId);
        }
    }

    private boolean isInArray(int flagId) {
        boolean returnValue = false;
        for (PopDialogIdToListener dialog : dialogArr) {
            if (dialog.flagId == flagId) {
                returnValue = true;
                break;
            }
        }
        return returnValue;
    }

    private int getDialogIndex(int flagId) {
        int index = -1;
        int size = dialogArr.size();
        for (int i = 0; i < size; i++) {
            if (dialogArr.get(i).flagId == flagId) {
                index = i;
                break;
            }
        }
        return index;
    }

    /**
     * 当前提示框类型
     */
    public enum DialogType {
        ENONE,
        /**
         * 错误提示框
         */
        EERROR,
        /**
         * 警告提示框
         */
        EWARN,
        /**
         * 信息提示框
         */
        EINFO,
        /**
         * 网络提示框
         */
        ENET,
        /**
         * 网络变化提示框
         */
        ENET_CHANGE,
        /**
         * 蓝牙提示框
         */
        EBLUETOOTH,
        /**
         * 导航提示框
         */
        ENAVIGATION,
        /**
         * 活动详情提示框
         */
        EACTIONS,
        /**
         * 知道了提示框
         */
        EKNOW,
        /**
         * 知道了提示框
         */
        EONEBUTTON,
        /**
         * 升级提示框
         */
        EUPDATE,
        /**
         * 微信示框
         */
        EWECHAT,
        /**
         * VR 界面
         */
        VR
    }

    public interface PopDialogGroupListener {
        void initDialog(int dialogId, int flagId, DialogType dialogType, AOADialog popDialog);

        int getRootViewId();

        void setDialogSize(ViewGroup.LayoutParams aParams, DialogType dialogType, int dialogId, int flagId);

        void setAppActivity(AppActivity appActivity);
    }

    private static class PopDialogIdToListener {
        public int dialogId;
        public int flagId;
        public DialogType dialogType;
        public PopDialogGroupListener listener;
    }

    public void updateDialog() {
        //解决popdialog解锁后显示一半的问题。
        if (isShowing()) {
            if (dialogArr.size() >= 1) {
                popDialog.dismiss();
                ViewGroup.LayoutParams params = popDialog.getParams();
                dialogArr.get(0).listener.setDialogSize(params, dialogArr.get(0).dialogType, dialogArr.get(0).dialogId, dialogArr.get(0).flagId);
                popDialog.setLayoutParams(params);
                popDialog.show();
            }
        }
    }
}
