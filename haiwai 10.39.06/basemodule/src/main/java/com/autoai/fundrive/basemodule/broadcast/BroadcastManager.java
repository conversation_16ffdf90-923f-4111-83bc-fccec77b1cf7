package com.autoai.fundrive.basemodule.broadcast;

import static android.content.Context.RECEIVER_EXPORTED;

import android.content.Context;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;

import com.autoai.fundrive.basemodule.broadcast.BaseBroadcast;

/**
 * 广播管理
 * 当前只处理注册和反注册
 */
public class BroadcastManager {

    private Context mActivityContext;
    private final BaseBroadcast mBaseBroadcast;

    public BroadcastManager(Context aContext) {
        mActivityContext = aContext;
        mBaseBroadcast = new BaseBroadcast();
    }

    public void registerSystem(String... actions) {
        registerSystem(null, null, 0, actions);
    }

    public void registerSystem(int flags, String... actions) {
        registerSystem(null, null, flags, actions);
    }

    public void registerSystem(String broadcastPermission, Handler scheduler, String... actions) {
        registerSystem(broadcastPermission, scheduler, 0, actions);
    }

    public void registerSystem(String broadcastPermission, Handler scheduler, int flags, String... actions) {
//        LogManager.d("registerSystem Build.VERSION.SDK_INT[" + Build.VERSION.SDK_INT + "]mActivityContext=" + mActivityContext);
        if (mActivityContext == null) {
            throw new RuntimeException("register broadcast receiver activity context null!!!");
        }
        IntentFilter intentFilter = new IntentFilter();
        for (String action : actions) {
            intentFilter.addAction(action);
//            LogManager.d("registerSystem IntentFilter addAction | " +
//                    " broadcastPermission=" + broadcastPermission + " & scheduler=" + scheduler + " & flag=" + flags + "action=" + action);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.TIRAMISU){
                mActivityContext.registerReceiver(mBaseBroadcast, intentFilter, broadcastPermission, scheduler,RECEIVER_EXPORTED);
            }else {
                mActivityContext.registerReceiver(mBaseBroadcast, intentFilter, broadcastPermission, scheduler, flags);
            }

        } else {
            mActivityContext.registerReceiver(mBaseBroadcast, intentFilter, broadcastPermission, scheduler);
        }
    }

    public void unregister() {
        try {
            mActivityContext.unregisterReceiver(mBaseBroadcast);
            mActivityContext = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
//        LogManager.d("BroadcastManager unregister");
    }
}
