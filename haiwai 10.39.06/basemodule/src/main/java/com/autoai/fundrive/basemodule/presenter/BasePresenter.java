package com.autoai.fundrive.basemodule.presenter;

import android.content.Context;

import java.util.List;
import java.util.Vector;

public class BasePresenter<V> implements IBasePresenter<V> {

	protected Vector<V> views = new Vector<>();

	public Context mContext;

	public BasePresenter(Context context) {
		this.mContext = context;
	}

	@Override
    public void attachView(V view) {
		if (!views.contains(view)) {
			views.add(view);
		}
	}

	@Override
	public void detachView(V view, String presenterClassName) {
		views.remove(view);
		if (views.isEmpty()) {
			PresenterManager.getInstance().removePresenter(presenterClassName);
		}
	}

	public List<V> getViews() {
		return views;
	}

}
