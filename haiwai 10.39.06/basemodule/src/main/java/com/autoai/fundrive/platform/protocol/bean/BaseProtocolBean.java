package com.autoai.fundrive.platform.protocol.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 手机交互协议解析器基类。
 *
 * <AUTHOR>
 */
public abstract class BaseProtocolBean {
    private String methodName;

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public abstract void parse(JSONObject extData) throws JSONException;
}
