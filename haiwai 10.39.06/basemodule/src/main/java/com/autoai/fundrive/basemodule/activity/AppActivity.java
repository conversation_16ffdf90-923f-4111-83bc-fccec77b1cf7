package com.autoai.fundrive.basemodule.activity;

import com.mapbar.android.model.ViewManagerInterface;
import com.autoai.fundrive.basemodule.widget.BasePopDialog;

public abstract class AppActivity implements ViewManagerInterface {
    public abstract void showAlert(int i);
    public abstract void showAlert(String s);
    public abstract void showAlert(int i, int aRootViewId);
    public abstract void showAlert(String s, int aRootViewId);
    public abstract void showDialog(BasePopDialog aDialog, int flagId);
    public abstract void closeDialog(BasePopDialog aDialog, int flagId);
    public abstract void closeDialog(int flagId);

    /**
     * 创建指定的弹出对话框
     * @param aModuleName  模块名
     * @param index   对话框index
     * @return  创建成功的对话框对象，如果为NULL，则为创建失败
     */
    public abstract BasePopDialog createPopDialog(String aModuleName, int index);

    public abstract void performThirdApp(String aAppPackageName, String aClaseName, int aAppType);
}
