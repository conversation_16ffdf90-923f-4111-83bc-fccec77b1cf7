<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lnlyt_accredit"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        style="@style/textAppearanceXBig"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/welink_home_accredit_content_title"
        android:textColor="@android:color/white" />

    <TextView
        style="@style/textAppearanceXXLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="@string/welink_home_accredit_content_1"
        android:textColor="@android:color/white" />

    <TextView
        android:id="@+id/tv_permission_tips"
        style="@style/textAppearanceXXLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:lineSpacingExtra="30dp"
        android:text="@string/welink_home_accredit_content_2"
        android:textColor="@android:color/white" />

    <TextView
        android:id="@+id/tv_phone_operation"
        style="@style/textAppearanceXXLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="@string/welink_home_accredit_content_3"
        android:textColor="@color/yellow" />
</LinearLayout>
