<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="260dip"
    android:layout_height="80dip"
    android:background="@drawable/shape_loading"
    android:gravity="center"
    android:orientation="horizontal" >

    <ProgressBar
        android:layout_width="40dip"
        android:layout_height="40dip"
        android:layout_marginStart="10dip" />

    <TextView
        android:id="@+id/loading_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dip"
        android:paddingLeft="24dip"
        android:paddingRight="24dip"
        android:text="@string/qr_code_tips"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        tools:ignore="HardcodedText" />

</LinearLayout>