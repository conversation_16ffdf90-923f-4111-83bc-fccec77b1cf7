<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="#5f5f5f"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingTop="5dp"
        android:paddingRight="15dp"
        android:paddingBottom="5dp"
        android:textColor="#ffffff"
        tools:text="Toast" />
</LinearLayout>