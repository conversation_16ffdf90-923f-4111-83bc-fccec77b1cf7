<resources xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">

    <style name="textAppearanceXLargeM">
        <item name="android:textSize" tools:ignore="SpUsage">19dp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="textAppearanceXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">20dp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="ximatextAppearanceXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">16dp</item>
        <item name="android:textColor">#ffffff</item>
    </style>

    <style name="textAppearanceXBig">
        <item name="android:textSize" tools:ignore="SpUsage">36dp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="navi_ffffff_textAppearanceXXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">19dp</item>
        <item name="android:textColor">#ffffff</item>
    </style>

    <style name="textAppearanceXXLarge">
        <item name="android:textSize" tools:ignore="SpUsage">22dp</item>
        <item name="android:textStyle">normal</item>
    </style>
    <!--PermissonActivity所有需要透明的style-->
    <style name="translucent" parent="android:Theme.Light.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>

    <style name="Translucent_NoTitle" parent="android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

</resources>