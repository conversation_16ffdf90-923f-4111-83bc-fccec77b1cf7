# WeLink手机端架构类图分析

## 概述

本文档基于WeLink手机端代码库深入分析，绘制了包含WeLinkService, WLChannel, WLAOA, WiFi_V1, AOA_V1, AOA2WifiBuffer, Wifi2AOABuffer等核心类的完整架构类图，展示了手机端车机互联系统的设计模式和交互关系。

## 系统架构类图

```mermaid
classDiagram
    %% Android Framework Classes
    class Service {
        <<Android Framework>>
        +onCreate()
        +onStartCommand()
        +onBind()
    }
    
    class UsbManager {
        <<Android Framework>>
        +openAccessory()
        +getAccessoryList()
    }
    
    class UsbAccessory {
        <<Android Framework>>
        +getManufacturer()
        +getModel()
    }

    %% Core Service Class
    class WeLinkService {
        - HeadUnitSpec headUnitSpec
        - WLPCMPolicy wlPCMPolicy  
        - WLCommandParser wlCommandParser
        - WLHardwareHub wlHardwareHub
        - WifiConnectHU wifiConnectHU
        - WLChannel wlChannel
        - long wlChannelSize
        - ExecutorService mExecutorService
        - ExecutorService mTouchExecutorService
        - WLChannelListener wlChannelListener
        
        +onCreate()
        +onStartCommand()
        +onBind() IBinder
        +init()
        +connect()
        +disconnect()
    }

    %% Main Channel Manager
    class WLChannel {
        <<Singleton>>
        - Context context
        - WLChannelListener listener
        - Handler mainHandler
        - WLAOA wlaoa
        - boolean isWifiReady
        - boolean isConnected
        - boolean isAOA2WeLinkConnected
        - WeLinkCB weLinkCB
        
        +getInstance(Context) WLChannel
        +init(WLChannelListener)
        +connect(Intent) boolean
        +disconnect()
        +startRecord()
        +stopRecord()
        +sendH264Data(byte[])
    }

    %% Channel Listener Interface
    class WLChannelListener {
        <<interface>>
        +onConnecting(int, int) Config
        +onConnected(boolean, String, int, boolean, boolean, String, String, String)
        +onDisconnected()
        +onStartSendH264()
        +onStopSendH264()
        +onMotionEvent(byte[])
        +onHardKey(byte[])
        +onMessage(byte[])
        +onCarData(byte[])
        +onMicData(byte[])
        +onAudioPlayStart()
        +onAudioPlayEnds()
        +onReadSize(int)
        +onWriteSize(int)
    }

    %% Config inner class
    class Config {
        +int screenWidth
        +int screenHeight
        +int videoWidth
        +int videoHeight
        +Config(int, int, int, int)
    }

    %% AOA Bridge Class
    class WLAOA {
        - UsbManager usbManager
        - ParcelFileDescriptor fileDescriptor
        - AOA aoa
        - WIFI wifi
        - Wifi2AoaBuffer wifi2AoaBuffer
        - Aoa2WifiBuffer aoa2WifiBuffer
        - StatusCB extStatusCB
        - StatusCB statusCB
        
        +WLAOA(Context)
        +connect(Intent, StatusCB) boolean
        +disconnect()
        +start_proxy(FileDescriptor, StatusCB) boolean
    }

    %% AOA Status Callback
    class StatusCB {
        <<interface>>
        +onConnected()
        +onFailed()
        +onReadSize(int)
        +onWriteSize(int)
    }

    %% AOA Interface and Implementation
    class AOA {
        <<interface>>
        +connect(FileDescriptor, Aoa2WifiBuffer, Wifi2AoaBuffer, ConnectStatus)
        +disconnect()
    }

    class AOA_v1 {
        - int READ_SIZE = 1280 * 1024
        - int A2W_MAX_COUNT = 30
        - FileInputStream inputStream
        - FileOutputStream outputStream
        - ConnectStatus connectStatus
        - boolean firstRead
        - int maxCacheCount
        
        +connect(FileDescriptor, Aoa2WifiBuffer, Wifi2AoaBuffer, ConnectStatus)
        +disconnect()
        -start_proxy_thread()
    }

    %% WiFi Interface and Implementation  
    class WIFI {
        <<interface>>
        +connect(Aoa2WifiBuffer, Wifi2AoaBuffer, ConnectStatus) boolean
        +disconnect()
    }

    class WIFI_v1 {
        - String LOOPBACK_IP = "127.0.0.1"
        - int PORT_DATA = 6821
        - int PORT_MSG = 6805  
        - int PORT_VIDEO = 6802
        - int PORT_HU = 6831
        - int PORT_AUDIO = 6833
        - int W2A_MAX_COUNT = 120
        - HashMap~Integer,Socket~ socketMap
        - HashMap~Integer,InputStream~ inputStreamMap
        - HashMap~Integer,OutputStream~ outputStreamMap
        - ConnectStatus connectStatus
        
        +connect(Aoa2WifiBuffer, Wifi2AoaBuffer, ConnectStatus) boolean
        +disconnect()
        -start_thread(int, Aoa2WifiBuffer, Wifi2AoaBuffer)
    }

    %% Connect Status Interface
    class ConnectStatus {
        <<interface>>
        +onConnectFailed()
        +onReadSize(int)
        +onWriteSize(int)
    }

    %% Buffer Interfaces
    class Aoa2WifiBuffer {
        <<interface>>
        +write(byte[]) boolean
        +read(int) byte[]
        +count() int
        +count(int) int
    }

    class Wifi2AoaBuffer {
        <<interface>>
        +write(int, byte[]) boolean  
        +read() byte[]
        +count() int
    }

    %% Buffer Implementations
    class Aoa2WifiBuffer_v1 {
        - HashMap~Integer,LinkedBlockingQueue~byte[]~~ dataQueueMap
        - int port
        - byte[] buffer
        - int buffer_offset
        - byte[] header
        
        +Aoa2WifiBuffer_v1(int)
        +write(byte[]) boolean
        +read(int) byte[]
        +count() int
        +count(int) int
        -parse_data(byte[])
    }

    class Wifi2AoaBuffer_v1 {
        - int PORT_DATA = 6821
        - int PORT_MSG = 6805
        - int PORT_VIDEO = 6802
        - int PORT_HU = 6831
        - int PORT_AUDIO = 6833
        - HashMap~Integer,byte[]~ bufferMap
        - HashMap~Integer,Integer~ bufferSizeMap
        - HashMap~Integer,byte[]~ headerMap
        - LinkedBlockingQueue~byte[]~ dataQueue
        
        +Wifi2AoaBuffer_v1(int)
        +write(int, byte[]) boolean
        +read() byte[]
        +count() int
        -generate_header(int, int) byte[]
    }

    %% Hardware Hub Listener
    class WLHardwareHubListener {
        <<interface>>
        +onStatusChanged(int, String)
        +onHeartbeat(String, String, int, String)
        +onError(int)
    }

    %% Relationships
    Service <|-- WeLinkService : extends
    WeLinkService *-- WLChannel : contains
    WeLinkService ..|> WLChannelListener : implements
    WLChannel *-- WLAOA : contains
    WLChannel ..|> WLChannelListener : uses
    Config --o WLChannelListener : nested class
    
    WLAOA *-- AOA : contains
    WLAOA *-- WIFI : contains
    WLAOA *-- Aoa2WifiBuffer : contains
    WLAOA *-- Wifi2AoaBuffer : contains
    WLAOA ..|> StatusCB : uses
    WLAOA --> UsbManager : uses
    WLAOA --> UsbAccessory : uses
    
    AOA <|.. AOA_v1 : implements  
    WIFI <|.. WIFI_v1 : implements
    Aoa2WifiBuffer <|.. Aoa2WifiBuffer_v1 : implements
    Wifi2AoaBuffer <|.. Wifi2AoaBuffer_v1 : implements
    
    AOA_v1 ..|> ConnectStatus : uses
    WIFI_v1 ..|> ConnectStatus : uses
    
    AOA_v1 --> Aoa2WifiBuffer : uses
    AOA_v1 --> Wifi2AoaBuffer : uses
    WIFI_v1 --> Aoa2WifiBuffer : uses  
    WIFI_v1 --> Wifi2AoaBuffer : uses
    
    WeLinkService ..|> WLHardwareHubListener : may implement

    %% Notes
    note for WeLinkService "核心服务类，管理所有WeLink功能\n继承自Android Service\n处理连接状态和数据传输"
    note for WLChannel "单例模式的通信通道管理器\n协调AOA和WiFi连接\n处理H264视频流传输"
    note for WLAOA "AOA和WiFi之间的桥梁\n管理USB AOA连接\n协调双向数据传输"
    note for AOA_v1 "AOA连接的具体实现\n处理USB文件描述符\n管理AOA数据读写"
    note for WIFI_v1 "WiFi连接的具体实现\n管理多个TCP Socket端口\n处理WiFi数据传输"
    note for Aoa2WifiBuffer_v1 "AOA到WiFi数据缓冲区\n解析WeLink协议头\n按端口分发数据"
    note for Wifi2AoaBuffer_v1 "WiFi到AOA数据缓冲区\n生成WeLink协议头\n合并多端口数据"
```

## 关键架构特点分析

### 🏗️ **分层架构设计**

1. **Service层**: WeLinkService作为Android服务入口
2. **Channel层**: WLChannel管理通信通道
3. **Protocol层**: WLAOA处理AOA/WiFi协议转换
4. **Transport层**: AOA_v1/WIFI_v1处理具体传输
5. **Buffer层**: 数据缓冲和协议解析

### 🔄 **核心数据流**

#### 手机→车机数据流
```
应用数据 → WIFI_v1 → Wifi2AoaBuffer_v1 → AOA_v1 → USB连接 → 车机
```

#### 车机→手机数据流  
```
车机 → USB连接 → AOA_v1 → Aoa2WifiBuffer_v1 → WIFI_v1 → 应用数据
```

#### 协议转换过程
1. **WLAOA**协调两个方向的数据转换和缓冲
2. **WeLink协议头**解析和生成处理端口路由
3. **多端口TCP连接**实现不同数据类型的分离传输

### 📞 **回调机制详解**

#### WLChannelListener - 主要业务回调
- **连接生命周期**: `onConnecting()`, `onConnected()`, `onDisconnected()`
- **视频流控制**: `onStartSendH264()`, `onStopSendH264()`
- **交互事件**: `onMotionEvent()`, `onHardKey()`, `onMessage()`
- **音频处理**: `onMicData()`, `onAudioPlayStart()`, `onAudioPlayEnds()`
- **数据统计**: `onReadSize()`, `onWriteSize()`

#### StatusCB - AOA连接状态回调
- **连接状态**: `onConnected()`, `onFailed()`
- **数据流量**: `onReadSize()`, `onWriteSize()`

#### ConnectStatus - 传输层连接状态
- **失败处理**: `onConnectFailed()`
- **流量监控**: `onReadSize()`, `onWriteSize()`

#### WLHardwareHubListener - 硬件状态监听
- **状态变化**: `onStatusChanged()` - BLE/WiFi Direct状态跟踪
- **心跳检测**: `onHeartbeat()` - 车机设备发现和连接维护
- **错误处理**: `onError()` - 硬件异常和权限问题

### 🔌 **Android Framework集成**

#### Service架构
- **WeLinkService**继承自`Android Service`，提供后台服务能力
- 支持绑定服务模式，通过AIDL接口与上层应用通信
- 管理服务生命周期和资源释放

#### USB AOA集成
- **UsbManager**管理USB设备访问权限
- **UsbAccessory**处理AOA模式下的设备信息
- **ParcelFileDescriptor**提供文件描述符访问

#### 网络通信集成
- **Socket**实现TCP网络连接
- 支持多端口同时连接（数据、消息、视频、音频、HU控制）
- **InputStream/OutputStream**处理数据流读写

#### 异步处理集成
- **Handler/Looper**实现主线程回调
- **ExecutorService**管理线程池
- **Thread**处理阻塞式数据传输

## 端口映射和数据分类

### TCP端口定义
```java
PORT_DATA = 6821    // 数据传输端口
PORT_MSG = 6805     // 消息传递端口  
PORT_VIDEO = 6802   // 视频流端口
PORT_HU = 6831      // 车机控制端口
PORT_AUDIO = 6833   // 音频流端口
```

### WeLink协议头解析
```java
// AOA到WiFi方向的协议头解析
data[0] == 'W' && data[1] == 'L'  // WeLink协议标识
switch (data[2]) {
    case 8:  -> PORT_VIDEO   // 视频数据
    case 9:  -> PORT_MSG     // 消息数据
    case 12: -> PORT_MSG     // 扩展消息
    case 13: -> PORT_DATA    // 通用数据
    case 18: -> PORT_AUDIO   // 音频数据
    case 20: -> PORT_HU      // 车机控制
}
```

## 性能优化设计

### 缓冲区管理
- **A2W_MAX_COUNT = 30**: AOA到WiFi缓冲区最大队列长度
- **W2A_MAX_COUNT = 120**: WiFi到AOA缓冲区最大队列长度
- **READ_SIZE = 1280 * 1024**: 单次读取缓冲区大小

### 线程模型
- **主线程**: UI回调和状态管理
- **工作线程池**: 数据处理和网络操作
- **专用线程**: USB读写和Socket数据传输
- **Touch线程池**: 触摸事件专用处理

### 数据流控制
- **背压处理**: 缓冲区满时阻塞写入
- **流量统计**: 实时监控读写字节数
- **连接检测**: 心跳机制和超时处理

## 设计模式应用

### 单例模式
- **WLChannel**: 确保全局唯一的通道管理器

### 策略模式  
- **AOA/WIFI接口**: 支持不同版本的协议实现
- **Buffer接口**: 支持不同的数据缓冲策略

### 观察者模式
- **Listener回调**: 事件驱动的状态通知机制

### 代理模式
- **WLAOA**: 作为AOA和WiFi之间的代理桥梁

### 工厂模式
- **版本选择**: 根据协议版本创建对应的实现类

## 总结

该架构设计展现了一个完整的车机互联系统，具有以下突出特点：

1. **清晰的分层架构**：从Android Service到具体传输层的完整抽象
2. **双向数据通道**：同时支持AOA和WiFi两种连接方式
3. **完善的回调机制**：覆盖连接、数据、错误等各种状态
4. **高效的数据处理**：多端口分离和协议解析优化
5. **良好的扩展性**：接口化设计支持新版本协议
6. **Android生态集成**：充分利用Android Framework能力

这个设计为WeLink手机端提供了稳定、高效、可扩展的车机互联基础架构。