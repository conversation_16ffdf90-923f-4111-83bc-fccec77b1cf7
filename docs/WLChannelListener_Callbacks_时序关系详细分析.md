# WLChannelListener_Callbacks 时序关系详细分析

## 概述

本文档详细分析 WLChannelListener_Callbacks 涉及的类时序关系，明确说明各种情况下回调接口的触发条件和调用路径。

## 核心架构层次

```
应用层 (Application)
    ↑ WLChannelListener 回调
WLChannel (主控制器)
    ↑ WeLinkCB 接口        ↑ WLAOA.StatusCB 接口
JNI 原生层               WLAOA (AOA管理器)
    ↑                       ↑
Native C++ WeLink库      AOA/WIFI 实现层
```

## 涉及的核心类

### 1. WLChannel
- **作用**: 主控制器，协调所有回调事件
- **职责**: 状态管理、线程切换、事件分发、连接状态检查

### 2. WLChannelListener (外部接口)
- **作用**: 应用层回调接口
- **职责**: 向应用层通知各种事件和状态变化

### 3. WeLinkCB (内部接口)
- **作用**: JNI 层回调接口
- **职责**: 接收来自 Native 层的事件通知

### 4. WLAOA.StatusCB (AOA 回调接口)
- **作用**: AOA 连接状态回调
- **职责**: 通知 AOA 连接状态和数据传输统计

### 5. CommandSource
- **作用**: 音频命令解析器
- **职责**: 解析音频数据中的控制命令

## 详细时序分析

### 1. 连接生命周期回调

#### onConnecting (正在连接车机)
```
触发路径: JNI层 → WeLinkCB.onConnecting → WLChannel.onConnecting → listener.onConnecting

触发条件:
- JNI 层检测到车机开始连接请求
- 车机发送屏幕尺寸信息

时序步骤:
1. 车机通过 AOA 发起连接
2. JNI 层接收连接请求并获取车机屏幕尺寸
3. WeLinkCB.onConnecting(huWidth, huHeight) 被调用
4. WLChannel 设置 isWifiReady = false
5. 直接调用 listener.onConnecting(huWidth, huHeight)
6. 应用层返回 Config 配置对象
7. WLChannel 调用 config_weLink() 配置屏幕参数

状态变化:
- isWifiReady: true → false
```

#### onConnected (已连接车机)
```
触发路径: JNI层 → WeLinkCB.onConnected → mainHandler.post → listener.onConnected

触发条件:
- JNI 层连接建立完成
- 车机信息交换完成

时序步骤:
1. JNI 层连接握手成功
2. WeLinkCB.onConnected(...) 被调用
3. 切换到主线程执行
4. 更新连接状态标志
5. 调用 listener.onConnected(...)
6. 启动 H264 心跳定时器
7. 执行连接前缓存的任务 (preConnectTasks)

状态变化:
- isConnected: false → true
- isAOA2WeLinkConnected = isAOA2HUConnected
- 启动心跳机制
- 清空预连接任务缓存

参数说明:
- isAOA: 是否通过 AOA 连接
- vehicleType: 车机类型标识
- huFPS: 车机支持的 FPS
- huSupportRecord: 是否支持录音功能
- huSupportBtPhone: 是否支持蓝牙电话
- huBtMacAddress: 车机蓝牙 MAC 地址
- huPlatform: 车机操作系统平台
- vehicleVersion: 车机软件版本
```

#### onDisconnected (连接断开)
```
触发路径 1: JNI层 → WeLinkCB.onDisconnected → mainHandler.post → listener.onDisconnected
触发路径 2: AOA层 → WLAOA.StatusCB.onFailed → mainHandler.post → listener.onDisconnected

触发条件:
1. JNI 层检测到连接断开
2. AOA 连接失败或异常断开
3. USB 配件无效或连接失败

时序步骤:
1. 连接异常检测
2. 主线程中执行断开处理
3. 停止心跳定时器
4. 重置连接状态
5. 调用 reset_weLink(false)
6. 通知 listener.onDisconnected()
7. 清空预连接任务缓存

状态变化:
- isConnected: true → false
- isAOA2HUConnecting: true → false
- 停止心跳机制
- 清空预连接任务
```

### 2. 视频控制回调

#### onStartSendH264 (开始发送H264)
```
触发路径: JNI层 → WeLinkCB.onStartSendH264 → mainHandler.post → listener.onStartSendH264

触发条件:
- 车机请求开始接收视频数据
- 视频传输通道准备完成

时序步骤:
1. 车机发送开始视频传输请求
2. JNI 层接收请求
3. 切换到主线程
4. 检查连接状态 (isConnected)
5. 调用 listener.onStartSendH264()

连接状态检查: ✅ 需要 isConnected = true
```

#### onStopSendH264 (停止发送H264)
```
触发路径: JNI层 → WeLinkCB.onStopSendH264 → mainHandler.post → listener.onStopSendH264

触发条件:
- 车机请求停止接收视频数据
- 视频传输通道关闭

时序步骤:
1. 车机发送停止视频传输请求
2. JNI 层接收请求
3. 切换到主线程
4. 检查连接状态 (isConnected)
5. 调用 listener.onStopSendH264()

连接状态检查: ✅ 需要 isConnected = true
```

### 3. 输入事件回调

#### onMotionEvent (触摸事件)
```
触发路径: JNI层 → WeLinkCB.onMotionEvent → mainHandler.post → listener.onMotionEvent

触发条件:
- 车机屏幕产生触摸事件
- 用户在车机屏幕上进行触摸操作

时序步骤:
1. 车机捕获触摸事件
2. 车机将触摸数据发送给手机
3. JNI 层接收 MotionEvent1 数据
4. 切换到主线程
5. 检查连接状态 (isConnected)
6. 调用 listener.onMotionEvent(data)

数据格式: MotionEvent1 协议格式的 byte[] 数据
连接状态检查: ✅ 需要 isConnected = true
```

#### onHardKey (硬按键事件)
```
触发路径: JNI层 → WeLinkCB.onHardKey → mainHandler.post → listener.onHardKey

触发条件:
- 车机物理按键被按下
- 方向盘控制按键操作

时序步骤:
1. 车机检测到按键事件
2. 车机将按键数据发送给手机
3. JNI 层接收按键数据
4. 切换到主线程
5. 检查连接状态 (isConnected)
6. 调用 listener.onHardKey(data)

数据格式: 按键事件的 byte[] 数据
连接状态检查: ✅ 需要 isConnected = true
```

### 4. 数据接收回调

#### onMessage (接收消息)
```
触发路径: JNI层 → WeLinkCB.onMessage → mainHandler.post → listener.onMessage

触发条件:
- 车机发送业务消息给手机
- 导航、音乐等应用数据交换

时序步骤:
1. 车机发送消息数据
2. JNI 层接收消息
3. 切换到主线程
4. 检查连接状态
   - 已连接: 直接调用 listener.onMessage(data)
   - 未连接: 添加到 preConnectTasks 缓存

特殊机制:
- 连接前缓存: 未连接时会缓存消息，连接成功后统一执行
- 预连接任务: 确保重要消息不丢失

连接状态检查: ⚠️ 未连接时缓存，连接后执行
```

#### onCarData (接收车机数据)
```
触发路径: JNI层 → WeLinkCB.onCarData → mainHandler.post → listener.onCarData

触发条件:
- 车机发送车辆相关数据
- OBD 数据、车辆状态信息等

时序步骤:
1. 车机收集车辆数据
2. 车机发送数据给手机
3. JNI 层接收数据
4. 切换到主线程
5. 检查连接状态
   - 已连接: 直接调用 listener.onCarData(data)
   - 未连接: 添加到 preConnectTasks 缓存

特殊机制:
- 连接前缓存: 与 onMessage 相同的缓存机制
- 注意: 代码中有 Bug，缓存时调用的是 listener.onMessage(data) 而不是 listener.onCarData(data)

连接状态检查: ⚠️ 未连接时缓存，连接后执行
```

#### onMicData (接收麦克风数据)
```
触发路径: JNI层 → WeLinkCB.onMicData → mainHandler.post → listener.onMicData

触发条件:
- 车机麦克风录音数据
- 语音识别、通话录音等

时序步骤:
1. 车机麦克风采集音频
2. 车机发送音频数据给手机
3. JNI 层接收音频数据
4. 切换到主线程
5. 检查连接状态 (isConnected)
6. 调用 listener.onMicData(data)

数据格式: PCM 音频数据 byte[]
连接状态检查: ✅ 需要 isConnected = true
特殊说明: 不支持连接前缓存
```

### 5. 音频控制回调

#### onAudioPlayStart / onAudioPlayEnds (音频播放控制)
```
触发路径: JNI层 → WeLinkCB.onAudioData → CommandSource解析 → listener.onAudioPlayStart/onAudioPlayEnds

触发条件:
- 车机音频系统状态变化
- 音频播放开始或结束

时序步骤:
1. 车机音频系统状态改变
2. 车机发送音频控制数据
3. JNI 层接收数据
4. WeLinkCB.onAudioData(data) 被调用
5. 切换到主线程
6. 检查连接状态 (isConnected)
7. 创建 CommandSource 解析数据
8. 根据命令 ID 分发:
   - COMMAND_TO_PHONE_PCM_START (40010) → listener.onAudioPlayStart()
   - COMMAND_TO_PHONE_PCM_ENDS (40020) → listener.onAudioPlayEnds()

数据解析:
- 数据格式: 包含标识符 0x55AA 的协议数据
- 命令解析: CommandSource.commandID() 提取命令类型
- 错误处理: 无效数据会被忽略

连接状态检查: ✅ 需要 isConnected = true
```

### 6. 数据统计回调

#### onReadSize (读取数据大小统计)
```
触发路径: AOA层 → WLAOA.StatusCB.onReadSize → mainHandler.post → listener.onReadSize

触发条件:
- AOA 从车机读取数据
- 数据传输量统计需求

时序步骤:
1. AOA 层从车机读取数据
2. WLAOA 统计读取字节数
3. WLAOA.StatusCB.onReadSize(size) 被调用
4. 切换到主线程
5. 检查 listener 是否为空
6. 调用 listener.onReadSize(size)

数据来源: AOA_v1.inputStream.read() 的返回值
用途: 网络流量监控、性能分析
连接状态检查: ❌ 无连接状态检查
```

#### onWriteSize (写入数据大小统计)
```
触发路径: AOA层 → WLAOA.StatusCB.onWriteSize → mainHandler.post → listener.onWriteSize

触发条件:
- AOA 向车机写入数据
- 数据传输量统计需求

时序步骤:
1. AOA 层向车机写入数据
2. WLAOA 统计写入字节数
3. WLAOA.StatusCB.onWriteSize(size) 被调用
4. 切换到主线程
5. 检查 listener 是否为空
6. 调用 listener.onWriteSize(size)

数据来源: AOA_v1.outputStream.write() 的参数长度
用途: 网络流量监控、性能分析
连接状态检查: ❌ 无连接状态检查
```

## 特殊机制说明

### 1. 预连接任务缓存 (preConnectTasks)
```
作用: 确保连接建立前的重要数据不丢失
适用回调: onMessage, onCarData
缓存时机: isConnected = false 时
执行时机: onConnected 回调中
实现方式: CopyOnWriteArrayList<Runnable>

代码逻辑:
if (isConnected) {
    listener.onMessage(data);
} else {
    preConnectTasks.add(() -> listener.onMessage(data));
}
```

### 2. 主线程切换机制
```
作用: 确保回调在主线程执行，支持 UI 操作
实现方式: mainHandler.post(() -> { ... })
适用范围: 所有 WeLinkCB 和 WLAOA.StatusCB 回调

目的:
- 避免跨线程访问 UI 导致的异常
- 保证回调执行的线程安全性
```

### 3. 连接状态检查机制
```
检查条件: isConnected = true
适用回调: 大部分 WeLinkCB 回调（除 onReady, onConnecting, onDisconnected）
不检查: onReadSize, onWriteSize (统计类回调)

作用:
- 防止在未连接状态下的无效回调
- 保证数据完整性和状态一致性
```

### 4. 心跳机制
```
启动时机: onConnected 回调中
停止时机: onDisconnected 回调中
心跳间隔: H264_TIMEOUT (600ms)
心跳方式: send_h264(null) 发送空数据作为心跳

作用:
- 保持连接活跃状态
- 及时检测连接异常
```

## 典型调用时序图

### 完整连接流程
```
App                WLChannel              WeLinkCB               JNI/AOA
 |                     |                     |                     |
 |-- init(listener) -->|                     |                     |
 |                     |-- create WeLinkCB->|                     |
 |                     |-- init_weLink() ----------------------->|
 |                     |                     |<-- onReady --------|
 |                     |<-- isWifiReady=true-|                     |
 |                     |                     |                     |
 |-- connect(intent) ->|                     |                     |
 |                     |-- create WLAOA ---->|                     |
 |                     |                     |-- AOA connect ---->|
 |                     |                     |<-- onConnecting ---|
 |<-- onConnecting ----|<--------------------|                     |
 |-- return Config --->|                     |                     |
 |                     |-- config_weLink() ---------------------->|
 |                     |                     |<-- onConnected ----|
 |<-- onConnected -----|<--------------------|                     |
 |                     |-- start heartbeat ->|                     |
 |                     |-- execute preTask ->|                     |
```

### 数据传输流程
```
Car/JNI            WeLinkCB              WLChannel           App
  |                   |                     |                 |
  |-- motion data --->|                     |                 |
  |                   |-- onMotionEvent -->|                 |
  |                   |                     |-- mainHandler ->|
  |                   |                     |                 |<-- onMotionEvent
  |                   |                     |                 |
  |-- message data -->|                     |                 |
  |                   |-- onMessage ------>|                 |
  |                   |                     |-- check conn -->|
  |                   |                     |-- mainHandler ->|<-- onMessage
  |                   |                     |   OR cache      |
```

### 断开流程
```
JNI/AOA            WeLinkCB              WLChannel           App
  |                   |                     |                 |
  |-- disconnect ---->|                     |                 |
  |                   |-- onDisconnected ->|                 |
  |                   |                     |-- stop heart -->|
  |                   |                     |-- reset_weLink->|
  |                   |                     |-- mainHandler ->|
  |                   |                     |                 |<-- onDisconnected
  |                   |                     |-- clear cache ->|
```

## 总结

WLChannelListener_Callbacks 的时序关系涉及多个层次的协调：

1. **双路径设计**: JNI 路径 (控制和数据) + AOA 路径 (连接和统计)
2. **状态管理**: 完善的连接状态检查和生命周期管理
3. **线程安全**: 统一的主线程切换机制
4. **容错机制**: 预连接任务缓存、心跳保活
5. **协议解析**: 智能的音频命令识别和分发

这种设计确保了 AOA 连接的稳定性和数据传输的可靠性，为上层应用提供了简洁而强大的回调接口。