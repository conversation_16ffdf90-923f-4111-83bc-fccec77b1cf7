# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Android automotive application ("Haiwai") for WeLink vehicle connectivity, supporting smartphone-to-car screen mirroring and interaction. The project is built with a modular architecture supporting both local SDK source and remote Maven dependencies.

## Build Commands

### Basic Build
```bash
./gradlew build
```

### Debug Build
```bash
./gradlew assembleDebug
```

### Release Build
```bash
./gradlew assembleRelease
```

### Clean
```bash
./gradlew clean
```

### Running Tests
```bash
./gradlew test
```

## Architecture Overview

### Project Structure
- **app/**: Main application module containing launcher activities and core UI
- **basemodule/**: Core foundation module with base classes, utilities, and platform abstractions
- **link_android/**: WeLink SDK modules (when USE_SDK_SOURCE=true)
  - **wlplatform/**: Platform abstraction layer
  - **wlconnector/**: Connection management
  - **wlserver/**: Server communication
  - **wlchannel/**: Communication channel handling
  - **wlscreen/**: Screen mirroring functionality
  - **wlhardwarehub/**: Hardware integration

### Key Components

1. **Application Entry**: `HaiwaiApplication.java` - Initializes logging, skin manager, and device authentication
2. **Module Management**: `ModuleManager.java` - Configures activity/dialog routing and service initialization
3. **Base Framework**: `BaseApplication.java` - Provides global configuration and module discovery
4. **Protocol Layer**: Extensive protocol handling in `platform/protocol/` for vehicle communication

### SDK Configuration
The project supports two build modes controlled by `USE_SDK_SOURCE` in `gradle.properties`:
- `true`: Uses local SDK source code from `link_android/` directory
- `false`: Uses remote Maven dependencies from AutoAI repositories

### Key Services
- **HaiwaiService**: Main application service
- **WLScreenManager**: Screen mirroring management
- **WLConnectManager**: Connection management

### WeLink Communication Architecture

The WeLink SDK uses a dual-process, dual-communication architecture within the phone for optimal performance:

#### **Process Architecture**
- **WLConnector Process**: Application client that connects to local server
- **WLServer Process**: Local coordination server managing screen mirroring sessions

#### **Communication Channels**
1. **Socket Communication (TCP/IP on 127.0.0.1)**
   - **Purpose**: High-throughput, real-time data streams
   - **Data Types**: Touch events, audio streams, navigation data, connection handshake
   - **Implementation**: `AutoClient.java:113` - Direct TCP connection for low-latency streaming
   - **Protocol**: JSON-based with magic number headers (0x78967896)

2. **AIDL Communication (Inter-Process) - Bidirectional Binding**
   - **Purpose**: Structured service calls and shared memory management  
   - **Data Types**: Surface management, shared memory access, file operations
   - **Implementation**: 
     - `IServerService.aidl` - WLServer provides (Surface, SharedMemory, sendData)
     - `IClientService.aidl` - WLConnector provides (screen control, reverse sendData)
   - **Binding Pattern**: **Bidirectional** - Both sides bind to each other's services
   - **Advantages**: Android-optimized Binder IPC with symmetrical communication

#### **Connection Flow**
1. **WLServer** starts local ServerSocket on random port (127.0.0.1:PORT)
2. **WLConnector** parses connection string: `welink://:PORT/connect?ver=1&check=RANDOM`
3. **Socket handshake** establishes main control channel
4. **Bidirectional AIDL binding** creates dual service channels:
   - WLConnector binds to WLServer's `IServerService` for resource access
   - WLServer binds to WLConnector's `IClientService` for screen control
5. **TouchEventSocket** creates dedicated channel for real-time touch events
6. **Triple data flow**: Streaming via Socket, bidirectional service calls via AIDL

#### **Design Rationale**
- **Performance**: Socket for continuous streams, AIDL for discrete operations
- **Process Isolation**: Screen capture separated from UI for stability
- **Resource Efficiency**: SharedMemory via AIDL, streaming buffers via Socket
- **Multi-App Support**: Single WLServer coordinates multiple app connections

## Development Configuration

### Version Information
- **Compile SDK**: 33
- **Min SDK**: 24
- **Target SDK**: 33
- **Java Version**: 1.8
- **Kotlin Version**: 1.9.0

### Signing Configuration
- **Debug/Release**: Uses `wedrive2024.jks` keystore with alias "wedrive"

### Key Dependencies
- WeLink SDK modules (version controlled by `sdkVersion` in `gradleshell/version.gradle`)
- Retrofit for networking
- Kotlinx Coroutines
- AndroidX libraries
- Custom AutoAI libraries from internal Maven repositories

## Code Conventions

### Language Mix
- Java: Legacy code and main application components
- Kotlin: Newer components, especially networking and data classes

### Package Structure
- `com.a2ra9k.android.launcher.*`: Main application package
- `com.autoai.fundrive.*`: Base module functionality
- `com.autoai.welink.*`: WeLink SDK components

### Logging
Uses custom `LogManager` with configurable debug mode via `DevelopModel`

## Important Notes

- The project contains extensive localization support (Arabic, Chinese, English, Spanish, etc.)
- Includes native libraries for ARM architectures (arm64-v8a, armeabi-v7a)
- Uses ProGuard for code obfuscation in both debug and release builds
- Contains automotive-specific protocols and CAN bus data handling
- Screen mirroring functionality requires specific hardware capabilities