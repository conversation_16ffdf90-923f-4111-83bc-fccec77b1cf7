# WLConnector架构图和时序图详解

## WLConnector主要作用

**WLConnector是WeLink车机互联系统的客户端连接器**，运行在手机App中，主要负责：

### 🎯 核心职责
1. **建立连接** - 与手机端本地WLServer建立Socket连接（127.0.0.1）
2. **协议管理** - 处理8种业务协议的数据传输
3. **状态同步** - 管理连接状态和App生命周期
4. **数据转发** - 在手机App和本地服务器之间转发各种数据
5. **进程通信** - 通过Socket+AIDL与手机WLServer进程通信

简单来说：**WLConnector = 手机端的"本地连接客户端"**

### 🔍 重要说明
**WLConnector连接的是手机端的本地WLServer，而不是车机端服务器！**
- **手机内部**: WLConnector ↔ 手机WLServer（127.0.0.1）
- **车机连接**: 由WLPlatform层负责真正的车机通信

---

## 系统架构图

```mermaid
graph TB
    subgraph "手机App进程"
        App["手机应用层<br/>音乐App/导航App等"]
        
        subgraph "WLConnector模块 (客户端)"
            WLC["WLConnector<br/>(抽象基类)"]
            Conn["Connector<br/>(具体实现)"]
            Trans["Transmisson<br/>(传输层)"]
            PM["ProtocolMgr<br/>(协议管理器)"]
            
            subgraph "业务协议层"
                P1["TouchEventProtocol<br/>(触屏回控)"]
                P2["MusicProtocol<br/>(音乐播放)"]
                P3["BackgroundProtocol<br/>(前后台状态)"]
                P4["CommandProtocol<br/>(车机命令)"]
                P5["其他协议..."]
            end
            
            Client["WLAutoClient<br/>(Socket客户端<br/>连接127.0.0.1)"]
        end
        
        BG["BackgroundNotify<br/>(生命周期监控)"]
    end
    
    subgraph "手机WLServer进程"
        LocalServer["WLServer<br/>(本地服务器)"]
        AutoServer["AutoServer<br/>(Socket服务器)"]
        StateManager["StateManager<br/>(状态管理)"]
        AIDL["双向AIDL服务接口<br/>(IServerService ↔ IClientService)"]
    end
    
    subgraph "WLPlatform层"
        Platform["WLPlatform<br/>(车机通信层)"]
        Hardware["硬件抽象层<br/>(USB/WiFi/蓝牙)"]
    end
    
    subgraph "车机系统"
        CarServer["车机WLServer<br/>(真正服务端)"]
        CarSys["车机各子系统<br/>音响/显示/控制等"]
    end
    
    %% 连接关系
    App --> WLC
    WLC --> Conn
    Conn --> Trans
    Trans --> PM
    PM --> P1
    PM --> P2
    PM --> P3
    PM --> P4
    PM --> P5
    Trans --> Client
    Conn --> BG
    
    %% 手机内部通信
    Client -.->|Socket连接<br/>127.0.0.1| AutoServer
    Client -.->|双向AIDL绑定| AIDL
    AutoServer --> LocalServer
    LocalServer --> StateManager
    
    %% 车机连接
    LocalServer --> Platform
    Platform --> Hardware
    Hardware -.->|物理连接<br/>USB/WiFi/蓝牙| CarServer
    CarServer --> CarSys
    
    %% 样式
    classDef appLayer fill:#e1f5fe
    classDef connectorLayer fill:#f3e5f5
    classDef protocolLayer fill:#e8f5e8
    classDef serverLayer fill:#fff8e1
    classDef platformLayer fill:#f1f8e9
    classDef carLayer fill:#ffebee
    
    class App appLayer
    class WLC,Conn,Trans,PM,Client,BG connectorLayer
    class P1,P2,P3,P4,P5 protocolLayer
    class LocalServer,AutoServer,StateManager,AIDL serverLayer
    class Platform,Hardware platformLayer
    class CarServer,CarSys carLayer
```

---

## 连接建立时序图

```mermaid
sequenceDiagram
    participant App as 手机App
    participant WLC as WLConnector
    participant Conn as Connector
    participant Trans as Transmisson
    participant Client as WLAutoClient
    participant LocalServer as 手机WLServer
    participant AIDL as AIDL服务
    participant Platform as WLPlatform
    
    App->>+WLC: connect(url, listener)
    Note over WLC: 检查isHaveInstance
    WLC->>+Conn: 创建Connector实例
    Conn->>+Trans: 创建Transmisson
    Trans->>+Client: 创建WLAutoClient
    
    Note over Client: 解析URL提取端口号<br/>IP固定为127.0.0.1
    
    Client->>+LocalServer: Socket连接请求(127.0.0.1)
    Note over Client,LocalServer: 手机内部TCP连接建立
    LocalServer-->>-Client: 连接成功
    
    Client->>+AIDL: 绑定AIDL服务(双向)
    Note over Client,AIDL: WLConnector绑定IServerService<br/>WLServer绑定IClientService
    AIDL-->>-Client: 双向服务绑定成功
    
    Client->>+LocalServer: 发送握手包
    Note over LocalServer: 版本验证<br/>能力协商
    LocalServer-->>-Client: 返回配置信息
    
    Client-->>Trans: onConnected(config)
    Trans-->>Conn: onConnected(config)
    
    Note over Conn: 初始化协议管理器<br/>启动生命周期监控
    Conn->>Trans: 初始化ProtocolMgr
    
    Conn-->>-App: onConnected(connector)
    Note over App: 决定是否开始投屏
    App-->>Conn: return true/false
    
    alt 开始投屏
        Conn->>LocalServer: 通知开始服务
        Note over LocalServer: 准备投屏服务<br/>屏幕镜像管理
        LocalServer->>Platform: 可选：建立车机连接
        Note over Platform: 物理连接到车机<br/>(USB/WiFi/蓝牙)
    else 仅连接
        Note over Conn: 仅建立手机内部连接<br/>不启动投屏
    end
```

---

## 数据传输时序图

```mermaid
sequenceDiagram
    participant App as 手机App
    participant Music as MusicProtocol
    participant Trans as Transmisson
    participant Client as WLAutoClient
    participant LocalServer as 手机WLServer
    participant AIDL as AIDL服务
    participant Platform as WLPlatform
    participant CarAudio as 车机音响
    
    %% 音频焦点请求
    App->>+Music: requestFocus()
    Music->>Music: 检查前台状态
    Music->>Trans: sendData(REQUEST_FOCUS)
    Trans->>Client: 发送JSON数据
    Client->>LocalServer: 音频焦点请求(Socket)
    
    LocalServer->>LocalServer: 焦点仲裁
    LocalServer-->>Client: PLAY_SUCCESS
    Client-->>Trans: onReceiveData
    Trans-->>Music: onReceiveData
    Music->>Music: isFocus = true
    Music-->>-App: onEnableFocus()
    
    %% 音乐播放
    App->>+Music: start(totalLen, rate, bit, channel)
    Music->>Trans: sendData(MUSIC_START, 音频参数)
    Trans->>Client: 发送开始播放命令
    Client->>LocalServer: 音频参数配置(Socket)
    LocalServer->>Platform: 转发到车机
    Platform->>CarAudio: 准备音频播放
    
    %% PCM数据传输
    loop 音频数据流
        App->>Music: updatePCM(position, pcmData)
        Music->>AIDL: create(fileName, pcmData)
        Note over AIDL: 通过双向AIDL创建共享文件
        Music->>Trans: sendData(MUSIC_PCM, position+fileName)
        Trans->>Client: 发送PCM位置信息
        Client->>LocalServer: PCM数据包(Socket)
        LocalServer->>AIDL: read(fileName)
        Note over LocalServer: 读取共享文件
        LocalServer->>Platform: 转发PCM数据
        Platform->>CarAudio: 播放PCM数据
        
        Music->>Music: 启动进度线程
        Music-->>App: onTick(position)
    end
    
    %% 音频焦点丢失
    Platform->>LocalServer: MUSIC_CANCEL
    LocalServer->>Client: 焦点丢失通知(Socket)
    Client-->>Trans: onReceiveData
    Trans-->>Music: onReceiveData
    Music->>Music: isFocus = false<br/>停止进度线程
    Music-->>-App: onDisableFocus()
```

---

## 触屏回控时序图

```mermaid
sequenceDiagram
    participant Car as 车机屏幕
    participant Platform as WLPlatform
    participant LocalServer as 手机WLServer
    participant Client as WLAutoClient
    participant Touch as TouchEventProtocol
    participant Dialog as 投屏Dialog
    participant App as 手机App界面
    
    Car->>+Platform: 用户触摸车机屏幕
    Note over Platform: 接收车机触控事件
    
    Platform->>LocalServer: 转发触控数据
    Note over LocalServer: 手机WLServer处理<br/>转换坐标
    
    LocalServer->>Client: 发送触控JSON数据(Socket)
    Note over LocalServer,Client: {"pointerCount":1,<br/>"action":0,<br/>"raw_x":150,<br/>"raw_y":300}
    
    Client-->>+Touch: onReceiveData(touchData)
    Touch->>Touch: 发送到主线程Handler
    
    Note over Touch: 解析JSON数据<br/>提取坐标和动作
    Touch->>Touch: parseMotionEvent(action, x, y)
    
    Touch->>Touch: 构造MotionEvent
    Note over Touch: MotionEvent.obtain(<br/>downTime, eventTime,<br/>action, x, y, pressure...)
    
    Touch->>+Dialog: dispatchTouchEvent(motionEvent)
    Dialog->>App: 触控事件传递
    Note over App: 响应触控操作<br/>更新界面
    Dialog-->>-Touch: 事件处理完成
    Touch-->>-Client: 处理完成
```

---

## 协议处理架构图

```mermaid
graph TB
    subgraph "数据接收流程"
        Receive["接收车机数据"]
        Parse["解析数据包头"]
        Route["ProtocolMgr路由分发"]
    end
    
    subgraph "协议处理层"
        P1["TouchEventProtocol<br/>类型: 3"]
        P2["BackgroundProtocol<br/>类型: 5"]
        P3["MusicProtocol<br/>类型: 11"]
        P4["NaviProtocol<br/>类型: 12"]
        P5["CommandProtocol<br/>类型: 13"]
        P6["ActionProtocol<br/>类型: 14"]
        P7["MicrophoneProtocol<br/>类型: 15"]
        P8["SoundProtocol<br/>类型: 16"]
    end
    
    subgraph "数据发送流程"
        Send["应用调用协议接口"]
        JSON["构造JSON数据"]
        Transmit["Transmisson发送"]
    end
    
    Receive --> Parse
    Parse --> Route
    
    Route --> P1
    Route --> P2
    Route --> P3
    Route --> P4
    Route --> P5
    Route --> P6
    Route --> P7
    Route --> P8
    
    P1 --> Send
    P2 --> Send
    P3 --> Send
    P4 --> Send
    P5 --> Send
    P6 --> Send
    P7 --> Send
    P8 --> Send
    
    Send --> JSON
    JSON --> Transmit
    
    classDef receiveFlow fill:#e3f2fd
    classDef protocolFlow fill:#f1f8e9
    classDef sendFlow fill:#fce4ec
    
    class Receive,Parse,Route receiveFlow
    class P1,P2,P3,P4,P5,P6,P7,P8 protocolFlow
    class Send,JSON,Transmit sendFlow
```

---

## 状态管理流程图

```mermaid
stateDiagram-v2
    [*] --> Disconnected: 初始状态
    
    Disconnected --> Connecting: connect()调用
    Connecting --> Connected: 握手成功
    Connecting --> Error: 连接失败
    
    Connected --> Focused: 获得焦点
    Connected --> Background: App后台
    Connected --> Disconnected: disconnect()
    
    Focused --> Playing: 开始播放/投屏
    Focused --> Connected: 失去焦点
    
    Playing --> Paused: pause()
    Paused --> Playing: resume()
    Playing --> Connected: stop()
    
    Background --> Connected: App前台
    Background --> Disconnected: 连接断开
    
    Error --> Disconnected: 错误恢复
    
    note right of Connected
        已连接状态
        - 协议初始化完成
        - 可发送命令
        - 监控生命周期
    end note
    
    note right of Focused
        获得焦点状态
        - 可控制播放
        - 可发送数据
        - 接收车机回调
    end note
```

---

## 核心设计特点总结

### 🏗️ 架构优势
1. **分层清晰** - 从抽象接口到具体协议，职责分明
2. **协议扩展** - 基于类型分发，易于添加新协议
3. **状态管理** - 多层状态保护，确保操作安全
4. **线程安全** - 主线程统一处理，避免竞态条件

### ⚡ 技术亮点
1. **单例控制** - 确保全局唯一连接
2. **JSON协议** - 结构化数据，便于调试扩展
3. **文件传输** - 大数据通过临时文件，避免内存问题
4. **进度同步** - 精确的播放进度计算和回调

### 🔄 数据流向

**手机内部流向**:
```
手机App ↔ WLConnector ↔ Socket(127.0.0.1) ↔ 手机WLServer ↔ 屏幕镜像管理
```

**完整数据流向**:
```
手机App ↔ WLConnector ↔ 手机WLServer ↔ WLPlatform ↔ 物理连接 ↔ 车机WLServer ↔ 车机系统
```

**双重架构说明**:
- **WLConnector**: 手机端的"本地连接客户端"，专注于协议处理和App交互
- **手机WLServer**: 手机端的"本地服务器"，专注于屏幕镜像和多App管理
- **WLPlatform**: 真正的"车机通信层"，处理物理连接和硬件抽象

这种设计实现了清晰的**进程分离**和**职责解耦**，提高了系统的稳定性和扩展性。