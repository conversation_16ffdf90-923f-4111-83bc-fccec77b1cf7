# WLConnector双向绑定架构补充说明

## 双向AIDL绑定架构详解

### 🔄 双向绑定模式

WLConnector和WLServer采用**双向AIDL绑定**模式，两边都会绑定对方的服务：

```
┌─────────────────────┐           ┌─────────────────────┐
│   WLConnector       │           │     WLServer        │
│   (App进程)         │           │   (独立进程)        │
├─────────────────────┤           ├─────────────────────┤
│ Client → Server     │ ────────> │ IServerService      │
│ (绑定Server服务)     │           │ (提供服务)          │
│                     │           │                     │
│ IClientService      │ <──────── │ Client → Connector  │
│ (提供服务)          │           │ (绑定Client服务)     │
└─────────────────────┘           └─────────────────────┘
```

### 📋 服务接口对比

#### IServerService (WLServer提供)
```java
interface IServerService {
    // 资源管理
    ParcelFileDescriptor getFileDescriptor(String connectStr, String filename);
    SharedMemory getSharedMemory(String connectStr, String filename);
    int length(String connectStr, String filename);
    Surface getSurface(String connectStr);
    
    // 数据传输
    oneway void sendData(String connectStr, int type, in byte[] bytes);
}
```

#### IClientService (WLConnector提供)
```java
interface IClientService {
    // 资源管理
    ParcelFileDescriptor getFileDescriptor(String connectStr, String filename);
    SharedMemory getSharedMemory(String connectStr, String filename);
    int length(String connectStr, String filename);
    
    // 屏幕控制
    void pauseMirrorScreen(String connectStr);
    oneway void resumeMirrorScreen(String connectStr);
    
    // 反向数据传输
    oneway void sendData(String connectStr, int type, in byte[] bytes);
}
```

### 🔧 绑定实现细节

#### WLConnector绑定WLServer
**文件**: `wlconnector/.../Server.java`
```java
public Server(Context context, ...) throws Exception {
    Intent intent = new Intent();
    intent.setAction("com.autoai.welink.autoproxy.server.IServerService");
    intent.setPackage(packageName);
    
    if (!context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)) {
        throw new Exception("SurfaceShared failed");
    }
}
```

#### WLServer绑定WLConnector
**文件**: `wlserver/.../Client.java`
```java
public Client(Context context, String packageName) throws Exception {
    Intent intent = new Intent();
    intent.setAction("com.autoai.welink.auto.client.IClientService");
    intent.setPackage(packageName);
    
    if (!context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)) {
        throw new Exception("MemoryFile failed");
    }
}
```

### 🔀 数据流向

#### 正向数据流 (WLConnector → WLServer)
```
App → WLConnector → Socket(127.0.0.1) → WLServer → WLPlatform → 车机
```

#### 反向数据流 (WLServer → WLConnector)
```
车机 → WLPlatform → WLServer → IClientService.sendData() → WLConnector → App
```

#### 双向AIDL调用
```
WLConnector ←→ IServerService.sendData() ←→ WLServer
WLConnector ←→ IClientService.sendData() ←→ WLServer
```

### 🎯 设计优势

1. **对等通信**: 任何一方都可以主动发起通信
2. **职责分离**: 
   - WLServer: 管理Surface、SharedMemory、状态控制
   - WLConnector: 管理屏幕控制、App生命周期
3. **资源共享**: 双方都可以访问对方的资源
4. **故障恢复**: 任一方断开连接都能被及时感知

### 📡 通信通道总结

WeLink系统使用**三重通信通道**：

1. **Socket通道**: 高频数据流 (音频、触摸、控制命令)
2. **IServerService**: WLServer提供的资源管理服务
3. **IClientService**: WLConnector提供的屏幕控制服务

这种架构实现了**对称式通信**，突破了传统客户端-服务器的单向依赖，创建了更加灵活和强大的进程间协作模式。

### 🔧 同步机制

两边的绑定都使用同步等待机制：

```java
synchronized (constructorSync) {
    if (!context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)) {
        throw new Exception("Bind failed");
    }
    constructorSync.wait(300);  // 等待300ms
}
```

确保服务绑定完成后再继续执行，避免了异步绑定可能带来的时序问题。

### 💡 关键洞察

这种双向绑定架构的核心价值在于：

- **打破了传统C/S架构的限制**
- **实现了真正的对等通信**
- **提供了灵活的资源访问能力**
- **支持复杂的多进程协作场景**

这是一个非常巧妙的架构设计，体现了Android AIDL机制的强大能力。