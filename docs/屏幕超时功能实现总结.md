# 屏幕超时功能实现总结

## 功能概述
成功实现了读取Android系统屏幕超时设置(`Settings.System.SCREEN_OFF_TIMEOUT`)并发送给车机的功能。

## 实现方案

### 1. 扩展现有API
选择扩展现有的 `sendDeviceName` 方法，而不是创建新的协议：
- **原因**：复用现有的 `carHome` 协议和 `phone_device_info` 数据结构
- **优势**：兼容性好，实现简单，逻辑集中

### 2. 核心修改文件

#### 2.1 WLProtocolManager.java (basemodule)
- **新增方法**：`sendDeviceInfo(String model, String deviceName, String deviceBrand, String address, int screenTimeout)`
- **修改方法**：`sendDeviceName` 现在调用新的 `sendDeviceInfo` 方法
- **功能**：在设备信息JSON中添加 `screenTimeout` 字段

#### 2.2 MainActivity.java (basemodule) 
- **新增方法**：`getScreenTimeout()` - 读取系统屏幕超时设置
- **修改方法**：`sendDeviceInfoToCar()` - 调用新的 `sendDeviceInfo` 方法
- **功能**：获取屏幕超时并发送给车机

#### 2.3 HUCommandImpl.java (app)
- **新增方法**：
  - `sendDeviceInfoWithScreenTimeout()` - 发送设备信息包含屏幕超时
  - `getScreenTimeout()` - 读取屏幕超时设置
  - `getBluetoothName()` - 获取蓝牙名称
  - `startScreenTimeoutObserver()` - 启动设置监听器
  - `stopScreenTimeoutObserver()` - 停止设置监听器
- **新增类**：`ScreenTimeoutObserver` - 监听屏幕超时设置变化
- **功能**：实时监听屏幕超时设置变化并自动更新车机

#### 2.4 HaiwaiService.java (app)
- **新增方法**：`sendDeviceInfoToCar()` - 在服务启动时发送设备信息
- **修改方法**：`startServiceImpl()` - 添加发送设备信息的调用
- **功能**：在服务启动时自动发送设备信息给车机

## 技术特性

### 3.1 兼容性处理
- **Android版本**：支持所有Android版本（API Level 1+）
- **权限要求**：无需特殊权限
- **异常处理**：完善的try-catch机制，失败时使用默认值30秒

### 3.2 实时监听
- **ContentObserver**：监听 `Settings.System.SCREEN_OFF_TIMEOUT` 变化
- **自动更新**：设置变化时自动发送新值给车机
- **防抖处理**：500ms延迟发送，避免频繁调用

### 3.3 数据格式
发送给车机的JSON格式：
```json
{
  "moduleName": "WeLink",
  "version": 0,
  "platform": "android",
  "command": {
    "method": "carHome",
    "extData": {
      "key": "phone_device_info",
      "address": "蓝牙地址",
      "model": "设备型号",
      "deviceName": "设备名称", 
      "deviceBrand": "设备品牌",
      "screenTimeout": 30000
    }
  }
}
```

## 调用时机

### 4.1 自动发送
1. **服务启动时**：`HaiwaiService.startServiceImpl()` 延迟1秒发送
2. **设置变化时**：`ScreenTimeoutObserver.onChange()` 延迟500ms发送

### 4.2 手动触发
- 调试模式下可通过按钮手动触发发送

## 日志输出

### 5.1 成功日志
```
getScreenTimeout: 成功读取屏幕超时设置 timeout=30000ms (30秒)
sendDeviceInfo: 添加屏幕超时信息 screenTimeout=30000ms
sendDeviceInfoWithScreenTimeout: 已发送设备信息和屏幕超时设置给车机
```

### 5.2 监听日志
```
startScreenTimeoutObserver: 屏幕超时设置监听器已启动
ScreenTimeoutObserver: 检测到屏幕超时设置变化，重新发送设备信息
```

## 测试建议

### 6.1 功能测试
1. 修改系统屏幕超时设置
2. 观察日志确认自动发送
3. 检查车机端是否收到正确数据

### 6.2 兼容性测试
1. 不同Android版本测试
2. 不同厂商设备测试
3. 权限受限情况测试

## 性能考虑

### 7.1 内存占用
- ContentObserver轻量级，内存占用极小
- 及时注销监听器，避免内存泄漏

### 7.2 CPU占用
- 仅在设置变化时触发，平时无CPU占用
- 延迟发送机制避免频繁调用

## 扩展性

该实现为后续添加更多设备信息提供了良好的基础：
- 可在 `sendDeviceInfo` 方法中添加更多参数
- 可在JSON的 `extData` 中添加更多字段
- 监听器模式可扩展到其他系统设置

## 总结

✅ **实现完成**：成功实现屏幕超时读取和发送功能
✅ **兼容性好**：支持所有Android版本，无权限要求  
✅ **实时监听**：自动检测设置变化并更新车机
✅ **异常处理**：完善的错误处理和默认值机制
✅ **性能优化**：轻量级实现，资源占用极小
✅ **扩展性强**：为后续功能扩展提供良好基础
