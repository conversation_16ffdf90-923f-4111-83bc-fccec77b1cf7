flowchart LR
    subgraph 步骤1[步骤1: 检查和合并header]
        S1A[检查header是否存在] --> S1B{header != null?}
        S1B -->|是| S1C[合并header与新数据]
        S1B -->|否| S1D[直接使用新数据]
    end
    
    subgraph 步骤2[步骤2: 解析数据头部]
        S2A[检查数据长度] --> S2B{data.length >= 8?}
        S2B -->|是| S2C{以'W'和'L'开头?}
        S2C -->|是| S2D[根据data[2]确定端口]
        S2D --> S2E[解析数据长度]
        S2B -->|否| S2F[可能是不完整的头部]
        S2C -->|否| S2G[不是有效的数据包]
    end
    
    subgraph 步骤3[步骤3: 缓冲区管理]
        S3A{buffer == null?} -->|是| S3B[创建新缓冲区]
        S3A -->|否| S3C[使用现有缓冲区]
        S3B --> S3D[计算复制长度]
        S3C --> S3D
        S3D --> S3E[复制数据到缓冲区]
        S3E --> S3F[更新偏移量]
    end
    
    subgraph 步骤4[步骤4: 完整数据包处理]
        S4A{buffer.length == buffer_offset?} -->|是| S4B{port > 0?}
        S4B -->|是| S4C[获取对应端口队列]
        S4C --> S4D[放入队列]
        S4D --> S4E[重置缓冲区状态]
        S4B -->|否| S4E
        S4A -->|否| S4F[等待更多数据]
    end
    
    subgraph 步骤5[步骤5: 处理剩余数据]
        S5A{data.length - length > 0?} -->|是| S5B[创建剩余数据数组]
        S5B --> S5C[复制剩余数据]
        S5C --> S5D[递归处理]
        S5A -->|否| S5E[处理完成]
    end
    
    步骤1 --> 步骤2 --> 步骤3 --> 步骤4 --> 步骤5