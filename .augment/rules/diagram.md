---
type: "agent_requested"
description: "架构分析"
---
我需要对Android项目中的特定模块进行架构分析，请帮我完成以下任务：

1. **模块识别与范围确定**：
   - 首先分析代码库结构，识别主要的功能模块
   - 重点关注核心业务逻辑模块
   - 确定需要分析的模块范围和边界

2. **调用依赖关系分析**：
   - 分析模块间的调用关系和数据流向
   - 识别关键的接口和协议处理逻辑
   - 梳理模块间的依赖层次结构

3. **可视化图表绘制**：
   - 绘制时序图（Sequence Diagram）展示模块间的交互时序
   - 绘制流程图（Flowchart）展示业务流程和决策分支
   - 如果适合，也可以绘制类图或组件图来展示静态结构关系
   - 使用Mermaid格式生成可交互的图表

4. **模块职责分析**：
   - 分析每个模块的核心职责和功能边界
   - 识别是否存在职责不清晰或耦合过紧的问题
   - 提供模块职责的清晰描述和改进建议

每个模块的接口都在时序图上明确。 需要绘制初始化，反初始化，以及各个功能的调用时序和流程。
请先分析代码结构，然后制定详细的分析计划供我确认，确认后再分步骤执行分析和绘图工作。