# Android项目通用接口分析Prompt - 技术概要设计

## 任务概述

你是一名高级Android架构师，需要分析Android项目中供外部调用的公共接口，并将结果整理到Excel技术概要设计表格中。

## 分析目标

识别和整理项目中所有供外部模块调用的公共接口，为技术概要设计提供完整的接口声明和描述。重点关注跨模块的接口边界和对外暴露的API。

## 第一阶段：项目结构分析

### 1.1 代码库扫描
请扫描项目代码库，识别以下类型的接口：
- **公共API类**：对外提供服务的主要入口类
- **接口定义**：interface和abstract class定义的契约
- **回调接口**：用于异步通信的回调定义
- **配置接口**：用于参数配置和初始化的接口
- **监听器接口**：用于事件通知的监听器

### 1.2 模块边界识别
识别项目中的模块边界：
- **应用层模块**：面向最终用户的功能模块
- **服务层模块**：提供核心业务逻辑的服务模块
- **工具层模块**：提供通用功能的工具模块
- **平台层模块**：与Android系统交互的平台模块
- **第三方集成模块**：集成外部SDK或库的模块

## 第二阶段：公共接口识别

### 2.1 接口类型分类
按照以下类型识别和分类接口：

#### 2.1.1 主要API接口
- **初始化接口**：模块或组件的初始化方法
- **核心功能接口**：提供主要业务功能的方法
- **配置接口**：用于参数设置和配置的方法
- **查询接口**：用于状态查询和信息获取的方法
- **销毁接口**：用于资源释放和清理的方法

#### 2.1.2 回调和监听接口
- **事件监听器**：监听状态变化和事件的接口
- **结果回调**：异步操作结果的回调接口
- **进度回调**：长时间操作的进度通知接口
- **错误回调**：异常和错误处理的回调接口

#### 2.1.3 数据交互接口
- **数据输入接口**：接收外部数据的方法
- **数据输出接口**：向外部提供数据的方法
- **数据转换接口**：数据格式转换的方法
- **数据验证接口**：数据有效性检查的方法

#### 2.1.4 控制和管理接口
- **生命周期控制**：启动、暂停、恢复、停止等控制方法
- **状态管理**：状态查询和状态变更的方法
- **权限管理**：权限检查和权限请求的方法
- **资源管理**：资源分配和释放的方法

### 2.2 接口可见性分析
分析每个接口的可见性和访问范围：
- **public接口** - 完全对外开放的接口
- **protected接口** - 继承体系内可访问的接口
- **package接口** - 同包内可访问的接口
- **内部接口** - 模块内部使用的接口

### 2.3 接口调用关系分析
分析每个接口的调用特征：
- **调用方式** - 同步调用、异步调用、回调通知
- **调用频率** - 一次性调用、频繁调用、周期性调用
- **调用时机** - 初始化时、运行时、销毁时
- **调用条件** - 调用的前置条件和约束

## 第三阶段：Excel表格填写规范

### 3.1 Excel表格结构
按照以下列结构填写Excel表格：

| 接口描述 | 请求方法 | 接口定义/请求路径 | 参数定义 ||| 返回值定义 || 类型 ||||
|---------|---------|------------------|---------|---|---|-----------|---|------|---|---|---|
|         |         |                  | 类型 | 定义 | 说明 | 类型 | 说明 | 结构体名称 | 结构体说明 | 成员类型 | 说明 |
|         |         |                  |      |      |      |      |      | 宏名称 | 定义值 | 说明 |  |

### 3.2 字段填写详细说明

#### 接口描述
- 简洁明确地描述接口的主要功能和作用
- 说明接口解决什么问题或提供什么服务
- 示例：`初始化API管理器，设置全局配置参数`
- 示例：`获取用户信息数据`
- 示例：`注册设备连接状态监听器`

#### 请求方法
- 对于Android接口，填写方法的调用方式
- **同步调用** - 直接方法调用，立即返回结果
- **异步调用** - 异步方法调用，通过回调返回结果
- **回调通知** - 作为回调接口被调用
- **事件监听** - 作为监听器接口被触发
- 示例：`同步调用`、`异步调用`、`回调通知`

#### 接口定义/请求路径
- 完整的方法签名，包含类名、方法名、参数类型
- 格式：`完整类名.方法名(参数类型列表)`
- 示例：`com.example.api.ApiManager.initialize(Context, Config)`
- 示例：`com.example.callback.DataCallback.onSuccess(Object)`
- 示例：`com.example.listener.EventListener.onEvent(String, Bundle)`

#### 参数定义
**类型列**：
- 填写参数的Java类型
- 示例：`Context`、`String`、`int`、`boolean`、`Bundle`
- 对于泛型，写明具体类型：`List<String>`、`Map<String, Object>`

**定义列**：
- 填写参数的名称
- 示例：`context`、`config`、`listener`、`callback`

**说明列**：
- 详细说明参数的含义、约束条件、默认值
- 示例：`应用上下文，不能为null`
- 示例：`配置对象，可为null使用默认配置`
- 示例：`事件类型字符串，取值范围：CONNECT/DISCONNECT/ERROR`

#### 返回值定义
**类型列**：
- 填写返回值的Java类型
- 示例：`boolean`、`void`、`String`、`Object`、`List<Data>`
- 对于void方法，填写`void`

**说明列**：
- 说明返回值的含义、可能的取值、特殊情况
- 示例：`true表示初始化成功，false表示失败`
- 示例：`无返回值，结果通过回调通知`
- 示例：`用户数据对象，可能为null`

#### 类型定义
**结构体名称列**：
- 填写自定义类或数据结构的名称
- 示例：`Config`、`UserInfo`、`DeviceStatus`
- 如果是基本类型或系统类型，可以留空

**结构体说明列**：
- 说明自定义类的用途和主要属性
- 示例：`API配置类，包含日志级别、超时时间等配置`
- 示例：`用户信息数据类，包含用户ID、姓名、头像等`

**成员类型列**：
- 填写结构体中主要成员的类型
- 示例：`int logLevel; long timeout; boolean debug`
- 示例：`String userId; String userName; String avatar`

**说明列**：
- 说明结构体成员的含义和用途
- 示例：`logLevel: 日志级别(0-5); timeout: 超时时间(毫秒); debug: 调试模式开关`

**宏名称列**：
- 填写相关的常量或枚举名称
- 示例：`LOG_LEVEL_DEBUG`、`CONNECT_STATE_CONNECTED`
- 示例：`ERROR_CODE_NETWORK`、`DEVICE_TYPE_ANDROID`

**定义值列**：
- 填写常量或枚举的具体值
- 示例：`0`、`1`、`"CONNECTED"`、`1001`

**说明列**：
- 说明常量或枚举的含义和用途
- 示例：`调试级别日志`、`设备已连接状态`
- 示例：`网络错误码`、`Android设备类型`

## 第四阶段：接口分析执行

### 4.1 代码扫描方法
按照以下步骤扫描项目代码：

1. **识别入口类**：找到项目的主要入口类和门面类
2. **扫描public方法**：遍历所有public和protected方法
3. **识别接口定义**：找到所有interface和abstract class
4. **分析回调机制**：识别监听器和回调接口
5. **检查注解标记**：查找@API、@Public等注解标记的接口

### 4.2 接口筛选原则
按照以下原则筛选需要记录的接口：

1. **对外可见性**：只记录public和protected的接口
2. **跨模块调用**：重点关注跨模块边界的接口
3. **业务相关性**：优先记录业务功能相关的接口
4. **稳定性要求**：重点关注需要保持稳定的接口
5. **文档完整性**：确保重要接口都有完整的文档

### 4.3 特殊接口处理
对以下特殊类型的接口需要特别注意：

1. **静态方法**：工具类和单例模式的静态接口
2. **构造方法**：重要类的构造函数和工厂方法
3. **重载方法**：同名不同参数的方法重载
4. **泛型方法**：包含泛型参数的方法
5. **注解方法**：接口中定义的注解方法

## 第五阶段：Android平台特殊性考虑

### 5.1 Android组件相关接口
- **Activity相关**：生命周期方法、Intent处理、结果回调
- **Service相关**：服务绑定、后台任务、进程间通信
- **BroadcastReceiver相关**：广播注册、消息处理
- **ContentProvider相关**：数据访问、权限控制

### 5.2 系统权限相关接口
- **权限检查接口**：运行时权限检查方法
- **权限请求接口**：权限申请和回调处理
- **系统服务接口**：需要特殊权限的系统服务调用

### 5.3 线程和异步处理
- **主线程接口**：必须在UI线程调用的方法
- **后台线程接口**：可以在工作线程调用的方法
- **异步回调接口**：异步操作的结果通知
- **线程安全接口**：支持多线程并发访问的方法

## 输出要求

### 格式要求
- 使用中文进行描述和说明
- 保留英文的类名、方法名、常量名
- 技术术语使用标准的中文翻译

### 完整性要求
- 确保接口声明的完整性和准确性
- 包含所有重要的跨模块接口
- 覆盖主要的业务流程和异常处理

### 一致性要求
- 与现有架构文档保持一致
- 接口命名和描述规范统一
- 模块划分和职责边界清晰

## 示例输出

以下是接口分析的Excel表格示例：

### 示例1：API初始化接口

| 接口描述 | 请求方法 | 接口定义/请求路径 | 参数定义 ||| 返回值定义 || 类型 ||||
|---------|---------|------------------|---------|---|---|-----------|---|------|---|---|---|
| 初始化API管理器，设置全局配置参数 | 同步调用 | com.example.api.ApiManager.initialize(Context, Config) | Context | context | 应用上下文，不能为null | boolean | true表示初始化成功，false表示失败 | Config | API配置类 | int logLevel | 日志级别(0-5) |
|         |         |                  | Config | config | 配置对象，可为null使用默认配置 |      |      |      |      | long timeout | 超时时间(毫秒) |
|         |         |                  |      |      |      |      |      | LOG_LEVEL_DEBUG | 0 | 调试级别日志 |
|         |         |                  |      |      |      |      |      | LOG_LEVEL_INFO | 1 | 信息级别日志 |

### 示例2：异步回调接口

| 接口描述 | 请求方法 | 接口定义/请求路径 | 参数定义 ||| 返回值定义 || 类型 ||||
|---------|---------|------------------|---------|---|---|-----------|---|------|---|---|---|
| 异步操作成功时的回调通知 | 回调通知 | com.example.callback.DataCallback.onSuccess(Object) | Object | data | 返回的数据对象，可能为null | void | 无返回值，在主线程中回调 | UserInfo | 用户信息数据类 | String userId | 用户唯一标识 |
|         |         |                  |      |      |      |      |      |      |      | String userName | 用户姓名 |
|         |         |                  |      |      |      |      |      |      |      | String avatar | 用户头像URL |

### 示例3：事件监听接口

| 接口描述 | 请求方法 | 接口定义/请求路径 | 参数定义 ||| 返回值定义 || 类型 ||||
|---------|---------|------------------|---------|---|---|-----------|---|------|---|---|---|
| 系统事件发生时的通知回调 | 事件监听 | com.example.listener.EventListener.onEvent(String, Bundle) | String | eventType | 事件类型字符串 | void | 无返回值 | EventType | 事件类型枚举 | String | 事件类型常量 |
|         |         |                  | Bundle | params | 事件参数，可为null |      |      |      |      |      |      |
|         |         |                  |      |      |      |      |      | EVENT_CONNECT | "CONNECT" | 设备连接事件 |
|         |         |                  |      |      |      |      |      | EVENT_DISCONNECT | "DISCONNECT" | 设备断开事件 |
|         |         |                  |      |      |      |      |      | EVENT_ERROR | "ERROR" | 错误事件 |

## 第六阶段：关键业务流程接口分析

### 6.1 设备连接流程接口
分析从设备插入到连接建立的完整接口调用链：
```
USB设备插入 → DeviceStatusReceiver.onReceive() → ConnectManager.startCheckConnect()
→ AOACheckDevice.checkUsbPermission() → DataTransfer.startConnect()
→ AOAManager.startAOA() → SdkViewModel.onDeviceConnected()
```

### 6.2 HID校准流程接口
分析iOS设备HID校准的接口调用序列：
```
SdkViewModel.requestCalibration() → HidIosCalibrationUtil.requestCalibration()
→ CalibrationCallBack.onTouchPointRequest() → SdkApi.sendIosHidTouchPoint()
→ HidIosCalibrationManager.checkPoint() → CalibrationStrategy.parseCalibrationPoints()
```

### 6.3 数据传输流程接口
分析视频/音频/触控数据的传输接口：
```
视频数据: Native SDK → SdkViewModel.onVideoData() → Surface渲染
音频数据: Native SDK → SdkViewModel.onAudioData() → AudioAdapter播放
触控数据: UI事件 → HidUtil.onTouch() → BluetoothHidManager.hidSendPointEvent()
```

## 第七阶段：异常处理和边界情况

### 7.1 异常处理接口
- 连接超时处理接口
- 设备异常断开处理接口
- 权限拒绝处理接口
- 校准失败重试接口

### 7.2 边界情况接口
- 多设备同时连接的处理
- 设备快速插拔的处理
- 内存不足时的资源释放
- 系统休眠唤醒的状态恢复

## 第八阶段：性能和优化相关接口

### 8.1 性能监控接口
- 连接建立时间统计
- 数据传输速率监控
- 内存使用情况监控
- CPU使用率监控

### 8.2 优化策略接口
- 连接池管理接口
- 缓存管理接口
- 线程池配置接口
- 资源预加载接口

## 分析执行步骤

### 步骤1：准备阶段
1. 仔细阅读所有相关架构文档
2. 理解项目的整体架构和模块划分
3. 识别核心业务流程和关键技术点

### 步骤2：接口识别阶段
1. 遍历每个模块，识别所有public接口
2. 分析接口的输入输出参数
3. 确定接口的调用方和被调用方
4. 识别接口的调用时机和场景

### 步骤3：关系分析阶段
1. 绘制模块间的依赖关系图
2. 分析数据流向和控制流向
3. 识别关键的接口调用路径
4. 分析异常处理和错误恢复机制

### 步骤4：文档整理阶段
1. 按照Excel表格格式整理接口信息
2. 补充详细的参数和返回值说明
3. 添加调用时机和使用场景描述
4. 标注重要性级别和稳定性评估

### 步骤5：验证和完善阶段
1. 检查接口声明的完整性和准确性
2. 验证与架构文档的一致性
3. 补充遗漏的重要接口
4. 完善异常处理和边界情况

## 质量检查清单

### 完整性检查
- [ ] 是否覆盖了所有核心模块的主要接口
- [ ] 是否包含了完整的业务流程接口
- [ ] 是否考虑了异常处理和边界情况
- [ ] 是否包含了平台特性相关的接口

### 准确性检查
- [ ] 接口签名是否正确
- [ ] 参数和返回值说明是否准确
- [ ] 调用时机描述是否正确
- [ ] 模块归属是否正确

### 一致性检查
- [ ] 命名规范是否统一
- [ ] 描述风格是否一致
- [ ] 与架构文档是否一致
- [ ] 技术术语使用是否规范

### 实用性检查
- [ ] 是否便于开发人员理解和使用
- [ ] 是否有助于系统维护和扩展
- [ ] 是否支持问题排查和调试
- [ ] 是否满足技术概要设计的要求

## 使用示例

### 示例1：分析LinkHost类的所有接口

**输入指令**：
```
请分析LinkHost类的所有公共接口，包括：
1. 该类提供的所有public方法
2. 这些方法被哪些模块调用
3. 方法的详细参数和返回值说明
4. 调用时机和使用场景
```

**期望输出**：
按照Excel表格格式，详细列出LinkHost类的所有接口信息，包括init()、readyForLink()、registerLinkSurface()等方法的完整分析。

### 示例2：分析特定业务流程的接口调用链

**输入指令**：
```
请分析iOS设备HID校准流程中涉及的所有接口调用关系，包括：
1. 完整的调用时序
2. 每个接口的作用和职责
3. 数据传递和状态变化
4. 异常处理机制
```

**期望输出**：
按照业务流程顺序，列出所有相关接口的调用关系和详细信息。

## Excel表格模板

### 主要接口列表模板
| 接口名称 | 所属模块 | 调用方模块 | 接口类型 | 方法签名 | 参数说明 | 返回值说明 | 调用时机 | 重要性级别 | 稳定性 | 备注 |
|---------|---------|-----------|---------|---------|---------|-----------|---------|-----------|--------|------|
| LinkHost.init | linkHost | app | 同步接口 | public static void init(@NonNull Context context, SdkConfig sdkConfig) | context: 应用上下文; sdkConfig: SDK配置参数 | void: 无返回值，通过异常表示失败 | 应用启动时，在使用SDK功能前必须调用 | 核心接口 | 稳定 | 必须在主线程调用，只能调用一次 |
| SdkViewModel.onVideoData | linkHost | native SDK | 异步回调 | public void onVideoData(byte[] data) | data: H.264编码的视频数据 | void: 无返回值 | 收到手机端视频数据时回调 | 核心接口 | 稳定 | 在工作线程中调用，需要解码后渲染 |
| OnLinkStatusChangeListener.onDeviceConnected | linkHost | app | 事件监听 | void onDeviceConnected(int deviceType) | deviceType: 连接的设备类型 | void: 无返回值 | 设备成功连接时触发 | 重要接口 | 稳定 | 在主线程中回调，可用于UI状态更新 |

### 业务流程接口调用链模板
| 序号 | 接口名称 | 调用方 | 被调用方 | 数据流向 | 作用描述 | 异常处理 |
|------|---------|--------|---------|---------|---------|---------|
| 1 | DeviceStatusReceiver.onReceive | Android系统 | DeviceStatusReceiver | Intent → 设备状态 | 接收USB设备插入/拔出广播 | 过滤无效设备类型 |
| 2 | ConnectManager.startCheckConnect | DeviceStatusReceiver | ConnectManager | 设备信息 → 连接检查 | 启动设备连接检查流程 | 检查初始化状态 |
| 3 | AOACheckDevice.checkUsbPermission | ConnectManager | AOACheckDevice | UsbDevice → 权限状态 | 检查USB设备访问权限 | 请求权限或报告失败 |

## 最终交付物要求

### 1. 主接口清单Excel表格
- 包含所有重要的跨模块接口
- 按照模块分组组织
- 详细的接口描述和使用说明

### 2. 业务流程接口调用链文档
- 主要业务流程的完整接口调用序列
- 数据流向和状态变化说明
- 异常处理和错误恢复机制

### 3. 接口设计规范文档
- 接口命名规范
- 参数传递约定
- 返回值和异常处理规范
- 线程安全要求

### 4. 扩展性分析报告
- 新增模块时的接口扩展方案
- 接口版本兼容性考虑
- 未来技术演进的接口适配策略

请按照以上详细要求和模板，对Android项目进行全面的模块间接口调用关系分析，并将结果整理到Excel技术概要设计表格中。
